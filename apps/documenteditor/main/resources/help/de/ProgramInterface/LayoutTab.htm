﻿<!DOCTYPE html>
<html>
	<head>
		<title>Registerkarte Layout</title>
		<meta charset="utf-8" />
        <meta name="description" content="Einführung in die Benutzeroberfläche des Dokumenteneditors – die Registerkarte «Layout»" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Registerkarte Layout</h1>
            <p>Über die Registerkarte <b>Layout</b> im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a>, können Sie die Darstellung des Dokuments ändern: Legen Sie die Seiteneinstellungen fest und definieren Sie die Anordnung der visuellen Elemente.</p>
            <div class="onlineDocumentFeatures">
                <p>Dialogbox Online-Dokumenteneditor:</p>
                <p><img alt="Registerkarte Layout" src="../images/interface/layouttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Dialogbox Desktop-Dokumenteneditor:</p>
                <p><img alt="Registerkarte Layout" src="../images/interface/desktop_layouttab.png" /></p>
            </div>
            <p>Sie können:</p>
            <ul>
                <li><a href="../UsageInstructions/SetPageParameters.htm#margins" onclick="onhyperlinkclick(this)">Seitenränder</a>, <a href="../UsageInstructions/SetPageParameters.htm#orientation" onclick="onhyperlinkclick(this)">Seitenausrichtung</a> und <a href="../UsageInstructions/SetPageParameters.htm#size" onclick="onhyperlinkclick(this)">Seitengröße</a> anpassen,</li>
                <li><a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">Spalten</a> hinzufügen,</li>
                <li><a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">Seitenumbrüche</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">Abschnittsumbrüche</a> und <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">Spaltenumbrüche</a> einfügen,</li>
                <li><a href="../UsageInstructions/InsertLineNumbers.htm" onclick="onhyperlinkclick(this)">Zeilennummern</a> einfügen,</li>
                <li>Objekte ausrichten und anordnen (<a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">Tabellen</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">Bilder</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">Diagramme</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">Formen</a>),</li>
                <li>die <a href="../UsageInstructions/ChangeWrappingStyle.htm" onclick="onhyperlinkclick(this)">Umbruchart</a> ändern und Wrap-Grenze bearbeiten.</li>
                <li>ein <a href="../UsageInstructions/AddWatermark.htm" onclick="onhyperlinkclick(this)">Wasserzeichen</a> hinzufügen.</li>
            </ul>
		</div>
	</body>
</html>