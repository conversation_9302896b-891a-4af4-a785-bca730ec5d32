﻿<!DOCTYPE html>
<html>
<head>
    <title>Formular ausfüllen</title>
    <meta charset="utf-8" />
    <meta name="description" content="Formularvorlage ausfüllen" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Formular ausfüllen</h1>
        <p>Ein ausfüllbares Formular ist eine OFORM-Datei. OFORM ist ein Format zum Ausfüllen von Formularvorlagen und zum Herunterladen oder Ausdrucken des Formulars, nachdem Sie es ausgefüllt haben.</p> 
            <h2>Wie kann man ein Formular ausfüllen:</h2>
                <ol>
                    <li>
                        Öffnen Sie die OFORM-Datei.
                        <p><img alt="oform" src="../images/oform.png" /><p>
                        </li>
                    <li>Füllen Sie alle erforderlichen Felder aus. Die Pflichtfelder sind mit rotem Strich gekennzeichnet. Verwenden Sie <div class="icon icon-arrows_formfilling"></div> oder <div class="icon icon-next_formfilling"></div> <b>Nächstes Feld</b> auf dem obere Symbolleiste, um zwischen den Feldern zu navigieren, oder klicken Sie auf das Feld, das Sie ausfüllen möchten.
<li>Verwenden Sie die Schaltfläche <img alt="Alle Felder löschen" src="../images/clearall_formfilling.png" /> <b>Alle Felder leeren</b>, um alle Eingabefelder zu leeren.</li>
                    <li>Nachdem Sie alle Felder ausgefüllt haben, klicken Sie auf die Schaltfläche <b>Als PDF speichern</b>, um das Formular als PDF-Datei auf Ihrem Computer zu speichern.</li> 
                    <li>
                        Klicken Sie auf <div class = "icon icon-morebutton"></div> in der oberen rechten Ecke der Symbolleiste, um zusätzliche Optionen anzuzeigen. Sie können das Formular <b>Drucken</b>, <b>Als DOCX herunterladen</b> oder <b>Als PDF herunterladen</b>.
                        <p><img alt="Mehr OFORM" src="../images/more_oform.png" /></p>
                        <p>Sie können auch das Thema der <b>Benutzeroberfläche</b> des Formulars ändern, indem Sie <b>Wie im System</b>, <b>Hell</b>, <b>Klassisch Hell</b>, <b>Dunkel</b> oder <b>Dunkler Kontrast</b> auswählen. Sobald das Thema <b>Dunkelmodus</b> aktiviert ist, wird der <b>Dunkelmodus</b> verfügbar.</p>
                        <p><img alt="Dunkelmodus - Formular" src="../images/darkmode_oform.png" /></p>
                        <p>Mit Zoom können Sie die Seite mithilfe der Funktionen <b>Seite anpassen</b>, <b>Breite anpassen</b> und <b>Vergrößern</b> oder <b>Verkleinern</b> skalieren und die Seitengröße ändern.</p>
                        <p><img alt="Zoom" src="../images/pagescaling.png" /></p>
                        <ul>
                            <li><b>Seite anpassen</b> ermöglicht es, die Seite so zu skalieren, dass der Bildschirm die ganze Seite anzeigt.</li>
                            <li><b>Breite anpassen</b> ermöglicht es, die Seite so zu skalieren, dass sie an die Breite des Bildschirms angepasst wird.</li>
                            <li><b>Zoom</b> ermöglicht das Vergrößern und Verkleinern der Seite.</li>
                        </ul>
                        <p><b>Dateispeicherort öffnen</b>, wenn Sie den Ordner durchsuchen müssen, in dem das Formular gespeichert ist.</p>

                    </li> 
                 </ol>
     </div>
</body>
</html>