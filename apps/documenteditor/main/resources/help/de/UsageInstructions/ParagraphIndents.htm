﻿<!DOCTYPE html>
<html>
	<head>
		<title>Absatzeinzüge ändern</title>
		<meta charset="utf-8" />
        <meta name="description" content="Ändern Sie die Absatzeinzüge, nämlich den Einzug der ersten Zeile vom linken Seitenrand und die Absatzeinzüge von links und rechts" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Absatzeinzüge ändern</h1>
			<p>Im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> können Sie den Einzug der ersten Zeile vom linken Seitenrand sowie die Absatzeinzüge von links und rechts einstellen.</p>
			<p>Ändern der Absatzeinzüge:</p>
			<ul>
				<li>
					Stellen Sie die erforderlichen Parameter in der rechten Seitenleiste <b>Absatzeinstellungen</b> im Abschnitt <b>Einzüge</b> ein:
					<ul>
						<li><b>Links</b> - setzen Sie den Absatzabstand von der <b>linken</b> Seite mit einem numerischen Wert.</li>
						<li><b>Rechts</b> - setzen Sie den Absatzabstand von der <b>rechten</b> Seite mit einem numerischen Wert.</li>
						<li><b>Spezial</b> - setzen Sie den Einzug der <b>ersten Linie</b> des Absatzes: Selektieren Sie den entsprechenden Menüpunkt: (<b>(Nichts), Erste Linie, Hängend</b>) und ändern Sie den Standard numerischen Wert entweder für die <b>erste Linie</b> oder <b>Hängend</b>,</li>
					</ul>
				</li>
			</ul>
			<p><img alt="Einzüge - Menü - Rechts" src="../images/indents_right_panel.png" /></p>
			<p>oder</p>
			<ol>
				<li>Positionieren Sie den Zeiger innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus oder markieren Sie den gesamten Text mithilfe der Tastenkombination <b>Strg+A</b>.</li> <!-- Kursor? -->
				<li>Klicken Sie mit der rechten Maustaste und wählen Sie im Kontextmenü die Option <b>Absatz - Erweiterte Einstellungen</b> aus oder nutzen Sie den Link <b>Erweiterte Einstellungen anzeigen</b> in der rechten Seitenleiste.</li>
				<li>Im Fenster <b>Absatz - Erweiterte Einstellungen</b> wechseln Sie zu <b>Einzug & Abstand</b> und setzen Sie die notwendigen Parameter für die Einzug-Sektion: (die Beschreibung der Parameter wird nach oben gegeben).</li>
				<li>
					Klicken Sie auf <b>OK</b>.
					<p><img alt="Absatz - Erweiterte Einstellungen: Einzüge &amp; Position" src="../images/paradvsettings_indents.png" /></p>
				</li>
			</ol>
			<p>Um den Abstand zum linken Seitenrand zu ändern, können Sie auch die entsprechenden Symbole in der Registerkarte <b>Start</b> auf der oberen Symbolleiste benutzen: <b>Einzug verkleinern</b> <span class = "icon icon-decreaseindent"></span> und <b>Einzug vergrößern</b> <span class = "icon icon-increaseindent"></span>.</p>
			<p>Sie können auch das horizontale <b>Lineal</b> nutzen, um Einzüge festzulegen.</p><div class = "big big-indents_ruler"></div><p>Wählen Sie den gewünschten Absatz (Absätze) und ziehen Sie die Einzugsmarken auf dem Lineal in die gewünschte Position.</p>
			<ul>
				<li>Mit der Markierung für den <b>Erstzeileneinzug</b> <div class = "icon icon-firstline_indent"></div> lässt sich der Versatz des Absatzes vom linken Seitenbereich für die erste Zeile eines Absatzes festlegen.</li>
				<li>Mit der Einzugsmarke für den <b>hängenden Einzug</b> <div class = "icon icon-hanging"></div> lässt sich der Versatz vom linken Seitenrand für die zweite Zeile sowie alle Folgezeilen eines Absatzes festlegen.</li>
				<li>Mit der <b>linken Einzugsmarke</b> <div class = "icon icon-leftindent"></div> lässt sich der Versatz des Absatzes vom linken Seitenrand festlegen.</li>
				<li>Mit der <b>rechten Einzugsmarke</b> <div class = "icon icon-right_indent"></div> lässt sich der Versatz des Absatzes vom rechten Seitenrand festlegen.</li>
			</ul>
		</div>
	</body>
</html>