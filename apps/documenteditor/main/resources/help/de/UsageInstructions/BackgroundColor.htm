﻿<!DOCTYPE html>
<html>
	<head>
		<title>Hintergrundfarbe für einen Absatz festlegen</title>
		<meta charset="utf-8" />
		<meta name="description" content="Learn how to select background color for a paragraph" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Hintergrundfarbe für einen Absatz festlegen</h1>
			<p>Im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> der gesamte Absatz, einschließlich Leerzeichen und Zeilenabständen wird mit der gewählten Farbe hinterlegt.</p>
			<p>Einen Absatz mit Farbe hinterlegen oder die aktuelle Farbe ändern:</p>
			<ol>
				<li>Wechseln Sie in die Registerkarte <b>Startseite</b>, klicken Sie auf das Symbol <div class = "icon icon-changecolorscheme"></div> <b>Farbschema ändern</b> und wählen Sie aus den verfügbaren Vorlagen ein Farbschema für Ihr Dokument aus.</li>
				<li>Postitionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus oder markieren Sie den gesamten Text mithilfe der Tastenkombination <b>Strg+A</b>.</li>
				<li>Öffnen Sie die Farbpalette mit den verfügbaren Farben. Sie haben dazu verschiedene Möglichkeiten:
				<ul>
					<li>Klicken Sie auf der Registerkarte <b>Startseite</b> auf das Pfeilsymbol <div class = "icon icon-backgroundcolor"></div> oder</li>
					<li>klicken Sie in der rechten Seitenleiste auf das Farbfeld neben der Überschrift <b>Hintergrundfarbe</b> oder</li>
					<li>klicken Sie in der rechten Seitenleiste auf den Verweis "Erweiterte Einstellungen anzeigen" oder wählen Sie über einen Rechtsklick im Kontextmenü die Option "Absatz Erweiterte Einstellungen“ aus und wechseln Sie dann im Fenster "Absatz - Erweiterte Einstellungen" in die Registerkarte "Rahmen &amp; Füllungen" und klicken Sie auf das Farbfeld neben der Beschriftung <b>Hintergrundfarbe</b>.</li>
				</ul>
				</li>
				<li>Wählen Sie eine beliebige Farbe auf den verfügbaren <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">Paletten</a> aus.</li>
			</ol>
			<p>Nachdem Sie die über das Symbol <span class = "icon icon-backgroundcolor"></span> die gewünschte Farbe ausgewählt haben, können Sie diese Farbe auf jeden ausgewählten Absatz anwenden, indem Sie einfach auf das Symbol <span class = "icon icon-backgroundcolor_selected"></span> klicken (die ausgewählte Farbe wird angezeigt), ohne diese Farbe erneut auf der Palette auswählen zu müssen. Beachten Sie, dass die ausgewählte Farbe nicht für im Schnellzugriff gespeichert wird, wenn Sie die Option <b>Hintergrundfarbe</b> in der rechten Seitenleiste oder im Fenster "Absatz - Erweiterte Einstellungen" verwenden. (Diese Optionen können nützlich sein, wenn Sie über das Symbol <span class = "icon icon-backgroundcolor"></span> eine allgemeine Farbe für das Dokument festgelegt haben und für einen bestimmten Absatz eine andere Hintergrundfarbe auswählen möchten).</p>
			<hr />
			<p>Die Hintergrundfarbe eines bestimmten Absatzes löschen:</p>
			<ol>
				<li>Positionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus oder markieren Sie den gesamten Text mithilfe der Tastenkombination <b>Strg+A</b>.</li>
				<li>Klicken Sie in der rechten Seitenleiste auf das Farbfeld neben der Überschrift <b>Hintergrundfarbe</b>.</li>
				<li>Wählen Sie das Symbol <div class = "icon icon-nofill"></div>.</li>
			</ol>
		</div>
	</body>
</html>