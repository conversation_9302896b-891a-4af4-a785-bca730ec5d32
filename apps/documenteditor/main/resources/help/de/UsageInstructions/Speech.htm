﻿<!DOCTYPE html>
<html>
<head>
    <title>Text laut vorlesen</title>
    <meta charset="utf-8" />
    <meta name="description" content="Die Beschreibung des Sprach-Plugins für ONLYOFFICE Editoren, mit dem der geschriebene Text laut vorgelesen werden kann" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Text laut vorlesen</h1>
        <p>ONLYOFFICE <a href="https://www.onlyoffice.com/de/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> hat ein Plugin, das den Text für Sie vorlesen kann.</p>
        <ol>
            <li>Wählen Sie den vorzulesenden Text aus.</li>
            <li>Öffnen Sie die Registerkarte <b>Plugins</b> und wählen Sie den Menüpunkt <div class = "icon icon-speech"></div> <b>Rede</b> aus.</li>
        </ol>
        <p>Der Text wird nun vorgelesen.</p>
    </div>
</body>
</html>