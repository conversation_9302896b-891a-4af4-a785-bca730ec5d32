﻿<!DOCTYPE html>
<html>
<head>
    <title>Hervorgehobenen Code einfügen</title>
    <meta charset="utf-8" />
    <meta name="description" content="Die Beschreibung des Code-hervorheben-Plugins für ONLYOFFICE Editoren, mit dem Code mit dem bereits angepassten Stil in Dokumente eingefügt werden kann" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Hervorgehobenen Code einfügen</h1>
        <p>Im <a href="https://www.onlyoffice.com/de/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> können Sie hervorgehobenen Code mit dem schon angepassten Stil entsprechend der Programmiersprache und dem Farbstil des von Ihnen ausgewählten Programms einfügen.</p>
        <ol>
            <li>Gehen Sie zu Ihrem Dokument und platzieren Sie den Cursor an der Stelle, an der Sie den Code einfügen möchten.</li>
            <li>Öffnen Sie die Registerkarte <b>Plugins</b> und wählen Sie den Menüpunkt <div class = "icon icon-highlight"></div> <b>Code hervorheben</b> aus.</li>
            <li>Geben Sie die Programmier<b>sprache</b> an.</li>
            <li>Wählen Sie einen Code-<b>Stil</b> aus, der so aussieht, als wäre er in diesem Programm geöffnet.</li>
            <li>Geben Sie an, ob Sie Tabulatoren durch Leerzeichen ersetzen möchten.</li>
            <li>Wählen Sie <b>Hintergrundfarbe</b>. Bewegen Sie dazu den Cursor manuell über die Palette oder fügen Sie den <b>RGB</b>/<b>HSL</b>/<b>HEX</b>-Wert ein.</li>
            <li>Klicken Sie auf <b>OK</b>, um den Code einzufügen.</li>
        </ol>
        <img class="gif" alt="Code hervorheben Plugin - gif" src="../../images/highlight_plugin.gif" width="600" />
    </div>
</body>
</html>