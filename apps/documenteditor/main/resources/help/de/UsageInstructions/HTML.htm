﻿<!DOCTYPE html>
<html>
<head>
    <title>HTML bearbeiten</title>
    <meta charset="utf-8" />
    <meta name="description" content="Die Beschreibung des HTML-Plugins für ONLYOFFICE-Editoren, mit dem der HTML-Code des Textes bearbeitet werden kann" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>HTML bearbeiten</h1>
        <p>Wenn Sie eine Website-Seite in einem Texteditor schreiben und sie als HTML-Code erhalten möchten, verwenden Sie das <b>HTML</b>-Plugin.</p>
        <ol>
            <li>Öffnen Sie die Registerkarte <b>Plugins</b> und klicken Sie auf <b>Get and paste html</b>.</li>
            <li>Wählen Sie die erforderlichen Inhalte aus.</li>
            <li>Der HTML-Code des ausgewählten Absatzes wird im Plugin-Feld auf der linken Seite angezeigt. Sie können den Code bearbeiten, um die Textmerkmale zu ändern, z.B. <em>Schriftgröße</em> oder <em>Schriftfamilie</em> usw.</li>
            <li>Klicken Sie auf <b>Paste into the document</b>, um den Text mit seinem bearbeiteten HTML-Code an der aktuellen Cursorposition in Ihr Dokument einzufügen.</li>
        </ol>
        <p>Sie können auch Ihren eigenen HTML-Code schreiben (ohne einen Dokumentinhalt auszuwählen) und ihn dann in Ihr Dokument einfügen.</p>
        <img class="gif" alt="HTML plugin gif" src="../../images/html_plugin.gif" width="600" />
        <p>Weitere Informationen zum HTML-Plugin und seiner Installation finden Sie auf der <a href="https://www.onlyoffice.com/en/app-directory/html">Plugin-Seite</a> in AppDirectory.</p>
    </div>
</body>
</html>