﻿<!DOCTYPE html>
<html>
<head>
    <title>Wörter zählen</title>
    <meta charset="utf-8" />
    <meta name="description" content="Die Beschreibung des Wortzähler-Plugins für ONLYOFFICE-Editoren, mit dem Wörter, Symbole und Absätze des Textes gezählt werden können" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Wörter zählen</h1>
        <p>Um die genaue Anzahl der Wörter und Symbole mit und ohne Leerzeichen in Ihrem Dokument sowie die Anzahl der Absätze insgesamt zu kennen, verwenden Sie das Wortzähler-Plugin.</p>
        <ol>            
            <li>Öffnen Sie die Registerkarte <b>Plugins</b> und klicken Sie auf die Schaltfläche <b>Wörter und Zeichen zählen</b>.</li>
            <li>Wählen Sie den Text aus.</li>
        </ol>
        <div class="note">Bitte beachten Sie, dass die folgenden Elemente nicht in der Wortzahl enthalten sind:
            <ul>
                <li>Fußnoten-/Endnotensymbole,</li>
                <li>Zahlen aus nummerierten Listen,</li>
                <li>Seitenzahlen.</li>
            </ul>
        </div>
        <img class="gif" alt="Wortzählen-Plugin gif" src="../../images/wordcounter_plugin.gif" width="600" />
        <p>Weitere Informationen zum Wortzähler-Plugin und die Installation finden Sie auf der <a href="https://www.onlyoffice.com/en/app-directory/word-counter">Seite des Plugins</a> im AppDirectory.</p>
    </div>
</body>
</html>