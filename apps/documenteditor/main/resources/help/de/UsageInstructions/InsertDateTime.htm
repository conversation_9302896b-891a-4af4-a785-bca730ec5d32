﻿<!DOCTYPE html>
<html>
<head>
    <title>Datum und Uhrzeit einfügen</title>
    <meta charset="utf-8" />
    <meta name="description" content="Datum und Uhrzeit einfügen" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Datum und Uhrzeit einfügen</h1>
        <p>Um <b>Datum und Uhrzeit</b> einzufügen im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a>,</p>
        <ol>
            <li>positionieren Sie den Textkursor an der Stelle, an der Sie das <b>Datum</b> und die <b>Uhrzeit</b> einfügen wollen,</li>
            <li>öffnen Sie die Registerkarte <b>Einfügen</b>,</li>
            <li>klicken Sie das Symbol <b>Datum & Uhrzeit</b> <div class = "icon icon-date_time_icon"></div> an,</li>
            <li>
                im geöffneten Fenster <b>Datum & Uhrzeit</b> konfigurieren Sie die folgenden Einstellungen:
                <ul>
                    <li>Wählen Sie die Sprache aus.</li>
                    <li>
                        Wählen Sie das Format aus.</li>
                    <li>
                        Markieren Sie das Kästchen <b>Automatisch aktualisieren</b>, damit das Datum und die Uhrzeit immer aktuell sind.
                        <p class="note">
                        Verwenden Sie die Kontextmenüoption <b>Aktualisieren</b>, um das Datum und die Uhrzeit manuell zu ändern.</p>
                    </li>
                    <li>Klicken Sie <b>Als Standard setzen</b> an, um das aktive Format als Standard für diese Sprache zu setzen.</li>
                </ul>
            </li>
            <li>Klicken Sie <b>OK</b> an.</li>
        </ol>
        <p><img alt="Datum und Uhrzeit Fenster" src="../images/date_time.png" /></p>
    </div>
</body>
</html>