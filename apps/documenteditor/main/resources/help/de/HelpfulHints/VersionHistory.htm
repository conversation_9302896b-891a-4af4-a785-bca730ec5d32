﻿<!DOCTYPE html>
<html>
<head>
    <title>Versionshistorie</title>
    <meta charset="utf-8" />
    <meta name="description" content="Tipps zur gemeinsamen Bearbeitung" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Versionshistorie</h1>
        <p>Der <a href="https://www.onlyoffice.com/de/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner <a href="https://helpcenter.onlyoffice.com/de/userguides/groups-tipstricks-documents-document-versions.aspx" onclick="onhyperlinkclick(this)">freigeben</a>; an Dokumenten in Echtzeit <a href="../HelpfulHints/CollaborativeEditing.htm" onclick="onhyperlinkclick(this)">zusammenarbeiten</a>; direkt im Editor <a href="../HelpfulHints/Communicating.htm" onclick="onhyperlinkclick(this)">kommunizieren</a>; bestimmte Teile Ihrer Dokumente, die zusätzliche Eingaben Dritter erfordern, <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">kommentieren</a>; Dokumente <a href="../HelpfulHints/Review.htm" onclick="onhyperlinkclick(this)">überprüfen</a> und Ihre Änderungen hinzufügen, ohne die Datei tatsächlich zu bearbeiten; Dokumente <a href="../HelpfulHints/Comparison.htm" onclick="onhyperlinkclick(this)">vergleichen und zusammenführen</a>, um die Verarbeitung und Bearbeitung zu erleichtern.</p>
        <p>Im <b>Dokumenteneditor</b> können Sie die Versionshistorie des Dokuments anzeigen, an dem Sie mitarbeiten.</p>
        <p><b>Versionshistorie anzeigen:</b></p>
        <p>Um alle am Dokument vorgenommenen Änderungen anzuzeigen:</p>
        <ul>
            <li>Öffnen Sie die Registerkarte <b>Datei</b>.</li>
            <li>Wählen Sie die Option <b>Versionshistorie</b> in der linken Seitenleiste
                <p>oder</p></li>
            <li>Öffnen Sie die Registerkarte <b>Zusammenarbeit</b>.</li>
            <li>Öffnen Sie die Versionshistorie mithilfe des Symbols <div class = "icon icon-versionhistoryicon"></div> <b>Versionshistorie</b> in der oberen Symbolleiste.</li>
        </ul>
        <p>Sie sehen die Liste der Dokumentversionen und -revisionen mit Angabe des Autors jeder Version/Revision sowie Erstellungsdatum und -zeit. Bei Dokumentversionen wird auch die Versionsnummer angegeben (z. B. <em>ver. 2</em>).</p>
        <p><b>Versionen anzeigen:</b></p>
        <p>Um genau zu wissen, welche Änderungen in jeder einzelnen Version/Revision vorgenommen wurden, können Sie die gewünschte Version anzeigen, indem Sie in der linken Seitenleiste darauf klicken. Die vom Autor der Version/Revision vorgenommenen Änderungen werden mit der Farbe gekennzeichnet, die neben dem Namen des Autors in der linken Seitenleiste angezeigt wird.</p>
        <p>Um zur aktuellen Version des Dokuments <b>zurückzukehren</b>, verwenden Sie die Option <b>Historie schließen</b> oben in der Versionsliste.</p>
        <p><b>Versionen wiederherstellen:</b></p>
        <p>Wenn Sie zu einer der vorherigen Versionen des Dokuments zurückkehren müssen, klicken Sie auf den Link <b>Wiederherstellen</b> unter der ausgewählten Version/Revision.</p>
        <p><img alt="Restore" src="../images/versionhistory.png" /></p>
        <p>Um mehr über das Verwalten von Versionen und Zwischenrevisionen sowie das Wiederherstellen früherer Versionen zu erfahren, lesen Sie bitte diesen <a href="https://helpcenter.onlyoffice.com/de/userguides/groups-tipstricks-documents-document-versions.aspx">Artikel</a>.</p>
    </div>
</body>
</html>