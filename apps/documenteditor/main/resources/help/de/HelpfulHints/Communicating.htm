﻿<!DOCTYPE html>
<html>
<head>
    <title>Kommunikation in Echtzeit</title>
    <meta charset="utf-8" />
    <meta name="description" content="Tipps zur gemeinsamen Bearbeitung" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Kommunikation in Echtzeit</h1>
        <p>Der <a href="https://www.onlyoffice.com/de/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner <a href="https://helpcenter.onlyoffice.com/de/userguides/groups-guides-documents-share-documents.aspx" onclick="onhyperlinkclick(this)">freigeben</a>; an Dokumenten in Echtzeit <a href="../HelpfulHints/CollaborativeEditing.htm" onclick="onhyperlinkclick(this)">zusammenarbeiten</a>; bestimmte Teile Ihrer Dokumente, die zusätzliche Eingaben Dritter erfordern, <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">kommentieren</a>; <a href="../HelpfulHints/VersionHistory.htm" onclick="onhyperlinkclick(this)">Dokumentversionen</a> für zukünftige Verwendung speichern; Dokumente <a href="../HelpfulHints/Review.htm" onclick="onhyperlinkclick(this)">überprüfen</a> und Ihre Änderungen hinzufügen, ohne die Datei tatsächlich zu bearbeiten; Dokumente <a href="../HelpfulHints/Comparison.htm" onclick="onhyperlinkclick(this)">vergleichen und zusammenführen</a>, um die Verarbeitung und Bearbeitung zu erleichtern.</p>
        <p>Im <b>Dokumenteneditor</b> können Sie mit Ihren Mitbearbeitern in Echtzeit kommunizieren, indem Sie das integrierte <b>Chat</b>-Tool sowie eine Reihe nützlicher Plugins verwenden, z. B. <a href="../UsageInstructions/CommunicationPlugins.htm" onclick="onhyperlinkclick(this)">Telegram oder Rainbow</a>.</p>
        <p>Um auf das <b>Chat</b>-Tool zuzugreifen und eine Nachricht für andere Benutzer zu hinterlassen:</p>
        <ol>
            <li>
                Klicken Sie im linken Seitenbereich auf das Symbol <div class = "icon icon-chaticon"></div> oder <br />
                wechseln Sie in der oberen Symbolleiste in die Registerkarte <b>Zusammenarbeit</b> und klicken Sie auf die Schaltfläche <div class = "icon icon-chat_toptoolbar"></div> <b>Chat</b>.
            </li>
            <li>Geben Sie Ihren Text in das entsprechende Feld unten ein.</li>
            <li>Klicken Sie auf <b>Senden</b>.</li>
        </ol>
        <p class="note">Die Chat-Nachrichten werden nur während einer Sitzung gespeichert. Um den Inhalt des Dokuments zu diskutieren, ist es besser, <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">Kommentare zu verwenden</a>, die gespeichert werden, bis sie gelöscht werden.</p>
        <p>Alle Nachrichten, die von Benutzern hinterlassen wurden, werden links in der Leiste angezeigt. Liegen ungelesene neue Nachrichten vor, sieht das Chat-Symbol wie folgt aus - <span class = "icon icon-chaticon_new"></span>.</p>
        <p>Um die Leiste mit den Chat-Nachrichten zu schließen, klicken Sie in der linken Seitenleiste auf das Symbol <span class = "icon icon-chaticon"></span> oder klicken Sie in der oberen Symbolleiste erneut auf die Schaltfläche <span class = "icon icon-chat_toptoolbar"></span> <b>Chat</b>.</p>
    </div>
</body>
</html>