﻿<!DOCTYPE html>
<html>
<head>
    <title>Erweiterte Einstellungen des Dokumenteneditors</title>
    <meta charset="utf-8" />
    <meta name="description" content="The advanced settings of Document Editor" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Erweiterte Einstellungen des Dokumenteneditors</h1>
        <p>Der <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> ermöglicht es Ihnen, die erweiterten Einstellungen zu ändern. Um darauf zuzugreifen, öffnen Sie die Registerkarte <b>Datei</b> und wählen Sie die Option <b>Erweiterte Einstellungen</b>.</p>
        <p>Die erweiterten Einstellungen sind wie folgt gruppiert:</p>

        <h3>Bearbeitung und Speicherung</h3>
        <ol>
            <li><span class="onlineDocumentFeatures">Die Option <b>Automatisches speichern</b> wird in der <em>Online-Version</em> verwendet, um das automatische Speichern von Änderungen, die Sie während der Bearbeitung vornehmen, ein-/auszuschalten.</span></li>
            <li><span class="desktopDocumentFeatures">Die Option <b>Wiederherstellen</b> wird in der <em>Desktop-Version</em> verwendet, um die Option ein-/auszuschalten, die die automatische Wiederherstellung von Dokumenten ermöglicht, falls das Programm unerwartet geschlossen wird.</span></li>
            <li><b>Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen</b>. Das entsprechende Symbol wird angezeigt, wenn Sie Inhalte in das Dokument einfügen.</li>
            <li><b>Die Dateien mit älteren MS Word-Versionen kompatibel machen, wenn sie als DOCX gespeichert werden</b>. Die im DOCX-Format gespeicherten Dateien werden mit älteren Versionen von Microsoft Word kompatibel.</li>
        </ol>

        <h3>Zusammenarbeit</h3>
        <ol>
            <li class="onlineDocumentFeatures">
                Im Unterabschnitt <b>Modus "Gemeinsame Bearbeitung"</b> können Sie den bevorzugten Modus zum Anzeigen von Änderungen am Dokument festlegen, wenn Sie gemeinsam arbeiten.
                <ul>
                    <li><b>Schnell</b> (standardmäßig). Die Benutzer, die an der gemeinsamen Bearbeitung von Dokumenten teilnehmen, sehen die Änderungen in Echtzeit, sobald sie von anderen Benutzern vorgenommen wurden.</li>
                    <li><b>Formal</b>. Alle von Mitbearbeitern vorgenommenen Änderungen werden erst angezeigt, nachdem Sie auf das Symbol <b>Speichern</b> <div class="icon icon-saveupdate"></div> geklickt haben, das Sie über neue Änderungen informiert.</li>
                </ul>
            </li>
            <li>
                Im Unterabschnitt <b>Änderungen anzeigen</b> können Sie auswählen, wie neue Änderungen angezeigt werden.
                <ul>
                    <li><b>In Sprechblasen beim Klicken anzeigen</b>. Die Änderung wird in einer Sprechblase angezeigt, wenn Sie auf die nachverfolgte Änderung klicken.</li>
                    <li><b>In Tipps anzeigen</b>. Ein Tooltip wird angezeigt, wenn Sie den Mauszeiger über die nachverfolgte Änderung bewegen.</li>
                </ul>
            </li>
            <li class="onlineDocumentFeatures">
                Im Unterabschnitt <b>Änderungen bei der Echtzeit-Zusammenarbeit zeigen</b> können Sie auswählen, wie neue Änderungen und Kommentare in Echtzeit angezeigt werden.
                <ul>
                    <li><b>Keine</b>. Alle während der aktuellen Sitzung vorgenommenen Änderungen werden nicht hervorgehoben.</li>
                    <li><b>Alle anzeigen</b>. Alle während der aktuellen Sitzung vorgenommenen Änderungen werden hervorgehoben.</li>
                    <li><b>Letzte anzeigen</b>. Alle Änderungen werden hervorgehoben, die Sie vorgenommen haben, seit Sie das letzte Mal das Symbol <b>Speichern</b> <div class="icon icon-saveupdate"></div> angeklickt haben. Diese Option ist nur verfügbar, wenn Sie in der Co-Bearbeitung den Modus <b>Formal</b> ausgewählt haben.</li>
                    <li><b>Kommentare im Text anzeigen</b>. Wenn Sie diese Funktion deaktivieren, werden die kommentierten Passagen nur dann hervorgehoben, wenn Sie auf das Symbol <b>Kommentare</b> <div class="icon icon-commentsicon"></div> in der linken Seitenleiste klicken.</li>
                    <li><b>Gelöste Kommentare anzeigen</b>. Diese Funktion ist standardmäßig deaktiviert, sodass die aufgelösten Kommentare im Dokumenttext ausgeblendet werden. Sie können solche Kommentare nur anzeigen, wenn Sie in der linken Seitenleiste auf das Symbol <b>Kommentare</b> <div class="icon icon-commentsicon"></div> klicken. Aktivieren Sie diese Option, wenn Sie aufgelöste Kommentare im Dokumenttext anzeigen möchten.</li>
                </ul>
            </li>
        </ol>

        <h3>Rechtschreibprüfung</h3>
        <ol>
            <li>Die Option <b>Rechtschreibprüfung</b> wird verwendet, um die Rechtschreibprüfung ein-/auszuschalten.</li>
            <li><b>Wörter in GROSSBUCHSTABEN ignorieren</b>. In Großbuchstaben eingegebene Wörter werden bei der Rechtschreibprüfung ignoriert.</li>
            <li><b>Wörter mit Zahlen ignorieren</b>. Wörter mit Zahlen werden bei der Rechtschreibprüfung ignoriert.</li>
            <li>Über das Menü <b>Automatische Korrekturoptionen</b> können Sie auf die <a href="../UsageInstructions/MathAutoCorrect.htm" onclick="onhyperlinkclick(this)">Autokorrektur-Einstellungen</a> zugreifen, z. B. Text während der Eingabe ersetzen, Funktionen erkennen, automatische Formatierung usw.</li>
        </ol>

        <h3>Arbeitsbereich</h3>
        <ol>
            <li>Die Option <b>Ausrichtungslinien</b> wird zum Ein-/Ausschalten der Ausrichtungshilfslinien verwendet, die beim Verschieben von Objekten angezeigt werden. Sie ermöglicht eine präzisere Objektpositionierung auf der Seite.</li>
            <li>Die Option <b>Hieroglyphen</b> wird verwendet, um die Anzeige von Hieroglyphen ein-/auszuschalten.</li>
            <li>Die Option <b>Verwenden Sie die Alt-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren</b> wird verwendet, um die Verwendung der <em>Alt</em>-Taste in Tastaturkürzeln zu aktivieren.</li>
            <li>
                Die Option <b>Thema der Benutzeroberfläche</b> wird verwendet, um das Farbschema der Benutzeroberfläche des Editors zu ändern.
                <ul>
                    <li>Die Option <b>Wie im System</b> sorgt dafür, dass der Editor dem Oberflächendesign Ihres Systems folgt.</li>
                    <li>Das Farbschema <b>Hell</b> umfasst die Standardfarben Blau, Weiß und Hellgrau mit weniger Kontrast in UI-Elementen, die für die Arbeit tagsüber geeignet sind.</li>
                    <li>Das Farbschema <b>Klassisch Hell</b> umfasst die Standardfarben Blau, Weiß und Hellgrau.</li>
                    <li>Das Farbschema <b>Dunkel</b> umfasst schwarze, dunkelgraue und hellgraue Farben, die für Arbeiten bei Nacht geeignet sind.</li>
                    <li>Das Farbschema <b>Dunkler Kontrast</b> umfasst schwarze, dunkelgraue und weiße Farben mit mehr Kontrast in UI-Elementen, die den Arbeitsbereich der Datei hervorheben.</li>
                    <li>
                        Die Option <b>Dunkelmodus aktivieren</b> wird verwendet, um den Arbeitsbereich dunkler zu machen, wenn der Editor auf das Oberflächendesign <b>Dunkel</b> oder <b>Dunkler Kontrast</b> eingestellt ist. Aktivieren Sie das Kontrollkästchen <b>Dunkelmodus aktivieren</b>, um diese Option zu aktivieren.
                        <p class="note">Abgesehen von den verfügbaren Benutzeroberflächendesigns <b>Hell</b>, <b>Klassisch Hell</b>, <b>Dunkel</b> und <b>Dunkler Kontrast</b> können jetzt ONLYOFFICE-Editoren mit Ihrem eigenen Farbschema angepasst werden. Bitte befolgen Sie <a target="_blank" href="https://helpcenter.onlyoffice.com/installation/docs-developer-change-theme.aspx" onclick="onhyperlinkclick(this)">diese Anleitung</a>, um zu erfahren, wie Sie das tun können.</p>
                    </li>
                </ul>
            </li>
            <li>Die Option <b>Maßeinheit</b> wird verwendet, um anzugeben, welche Einheiten auf den Linealen und in Eigenschaften von Objekten verwendet werden, wenn Parameter wie Breite, Höhe, Abstand, Ränder usw. eingestellt werden. Die verfügbaren Einheiten sind <em>Zentimeter</em>, <em>Punkt</em> und <em>Zoll</em>.</li>
            <li>Die Option <b>Standard-Zoom-Wert</b> wird verwendet, um den Standard-Zoom-Wert festzulegen, indem Sie ihn in der Liste der verfügbaren Optionen zwischen 50 % und 500 % auswählen. Sie können auch die Option <em>Seite anpassen</em> oder <em>Breite anpassen</em> auswählen.</li>
            <li>
                Die Option <b>Schriftglättung</b> wird verwendet, um auszuwählen, wie Schriftarten im Dokumenteneditor angezeigt werden.
                <ul>
                    <li>Wählen Sie <b>Wie Windows</b>, wenn Ihnen die Art gefällt, wie die Schriftarten unter Windows gewöhnlich angezeigt werden, d.h. mit Windows-artigen Hints.</li>
                    <li>Wählen Sie <b>Wie OS X</b>, wenn Ihnen die Art gefällt, wie die Schriftarten auf einem Mac gewöhnlich angezeigt werden, d.h. ohne Hints.</li>
                    <li>Wählen Sie <b>Native</b>, wenn Sie möchten, dass Ihr Text mit den Hints angezeigt wird, die in Schriftartdateien eingebettet sind.</li>
                    <li>
                        <b>Standard-Cache-Modus</b> wird verwendet, um den Cache-Modus für die Schriftzeichen auszuwählen. Es wird nicht empfohlen, es ohne Grund zu wechseln. Es kann nur in manchen Fällen hilfreich sein, beispielsweise wenn ein Problem im Google Chrome-Browser mit aktivierter Hardwarebeschleunigung auftritt.
                        <p>Der Dokumenteneditor verfügt über zwei Cache-Modi:</p>
                        <ol>
                            <li>Im <b>ersten Cache-Modus</b> wird jeder Buchstabe als separates Bild zwischengespeichert.</li>
                            <li>Im <b>zweiten Cache-Modus</b> wird ein Bild einer bestimmten Größe ausgewählt, in dem Buchstaben dynamisch platziert werden, und ein Mechanismus zum Zuweisen/Entfernen von Speicher in diesem Bild wird ebenfalls implementiert. Wenn nicht genügend Speicherplatz vorhanden ist, wird ein zweites Bild erstellt usw.</li>
                        </ol>
                        <p>Die Einstellung <b>Standard-Cache-Modus</b> wendet zwei oben genannte Cache-Modi separat für verschiedene Browser an:</p>
                        <ul>
                            <li>Wenn die Einstellung <b>Standard-Cache-Modus</b> aktiviert ist, verwendet Internet Explorer (Version 9, 10, 11) den <b>zweiten Cache-Modus</b>, andere Browser verwenden den <b>ersten Cache-Modus</b>.</li>
                            <li>Wenn die Einstellung <b>Standard-Cache-Modus</b> deaktiviert ist, verwendet Internet Explorer (Version 9, 10, 11) den <b>ersten Cache-Modus</b>, andere Browser verwenden den <b>zweiten Cache-Modus</b>.</li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li>
                Die Option <b>Einstellungen von Makros</b> wird verwendet, um die Anzeige von Makros mit einer Benachrichtigung einzustellen.
                <ul>
                    <li>Wählen Sie <b>Alle deaktivieren</b>, um alle Makros im Dokument zu deaktivieren.</li>
                    <li>Wählen Sie <b>Benachrichtigung anzeigen</b>, um Benachrichtigungen über Makros im Dokument zu erhalten.</li>
                    <li>Wählen Sie <b>Alle aktivieren</b>, um automatisch alle Makros im Dokument auszuführen.</li>
                </ul>
            </li>
        </ol>
        <p>Um die vorgenommenen Änderungen zu speichern, klicken Sie auf <b>Anwenden</b>.</p>
    </div>
</body>
</html>