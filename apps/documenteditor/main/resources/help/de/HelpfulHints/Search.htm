﻿<!DOCTYPE html>
<html>
	<head>
		<title>Suchen und Ersetzen</title>
		<meta charset="utf-8" />
		<meta name="description" content="Die Beschreibung der Such- und Ersetzungsfunktion für Dokumente im Dokumenteneditor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Suchen und Ersetzen</h1>
			<p>Um nach den erforderlichen Zeichen, Wörtern oder Ausdrücken zu suchen, die im aktuell bearbeiteten Dokument verwendet werden, klicken Sie auf das Symbol <span class="icon icon-searchicon"></span> in der linken Seitenleiste des <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Dokumenteneditors</b></a>, das Symbol <span class="icon icon-search_icon_header"></span> in der oberen rechten Ecke, oder verwenden Sie die Tastenkombination <em>Strg+F</em> (<em>Command+F</em> für MacOS), um das kleine Suchfeld zu öffnen, oder die Tastenkombination <em>Strg+H</em>, um das vollständige Suchfenster zu öffnen.</p>
			<p>Ein kleiner <b>Suchen</b>-Bereich öffnet sich in der oberen rechten Ecke des Arbeitsbereichs.</p>
			<p><img alt="Find small panel" src="../../../../../../common/main/resources/help/de/images/find_small.png" /></p>
			<p>Um auf die erweiterten Einstellungen zuzugreifen, klicken Sie auf das Symbol <span class="icon icon-search_advanced"></span> oder verwenden Sie die Tastenkombination <em>Strg+H</em>.</p>
			<p>Das Fenster <b>Suchen und Ersetzen</b> wird geöffnet:</p>
			<p><img alt="Suchen und Ersetzen Fenster" src="../images/search_window.png" /></p>
			<ol>
				<li>Geben Sie Ihre Anfrage in das entsprechende Dateneingabefeld <b>Suchen</b> ein.</li>
				<li>Wenn Sie ein oder mehrere Vorkommen der gefundenen Zeichen <b>ersetzen</b> müssen, geben Sie den Ersetzungstext in das entsprechende Dateneingabefeld <b>Ersetzen durch</b> ein oder verwenden Sie die Tastenkombination <em>Strg+H</em>. Sie können wählen, ob Sie ein einzelnes derzeit markiertes Vorkommen oder alle Vorkommen ersetzen möchten, indem Sie auf die entsprechenden Schaltflächen <b>Ersetzen</b> und <b>Alle ersetzen</b> klicken.</li>
				<li>Um zwischen den gefundenen Vorkommen zu navigieren, klicken Sie auf eine der Pfeilschaltflächen. Die Schaltfläche <div class="icon icon-searchdownbutton"></div> zeigt das nächste Vorkommen an, während die Schaltfläche <div class="icon icon-searchupbutton"></div> das vorherige Vorkommen anzeigt.</li>
				<li>
					Geben Sie Suchparameter an, indem Sie die erforderlichen Optionen unter den Eingabefeldern aktivieren:
					<ul>
						<li>Die Option <b>Groß-/Kleinschreibung beachten</b> wird verwendet, um nur die Vorkommen zu finden, die in der gleichen Groß-/Kleinschreibung wie Ihre Anfrage eingegeben wurden (z. B. wenn Ihre Anfrage „Editor“ lautet und diese Option ausgewählt ist, werden Wörter wie „Editor“ oder „EDITOR“ usw. nicht gefunden).</li>
						<li>Die Option <b>Nur ganze Wörter</b> wird verwendet, um nur ganze Wörter hervorzuheben.</li>
					</ul>
				</li>
			</ol>
			<p>Alle Vorkommen werden in der Datei hervorgehoben und als Liste im Bereich <b>Suchen</b> auf der linken Seite angezeigt. Verwenden Sie die Liste, um zum gewünschten Vorkommen zu springen, oder verwenden Sie die Navigationsschaltflächen <span class="icon icon-searchupbutton"></span> und <span class="icon icon-searchdownbutton"></span>.</p>
			<p>Der <b>Dokumenteneditor</b> unterstützt die Suche nach Sonderzeichen. Um ein Sonderzeichen zu finden, geben Sie es in das Suchfeld ein.</p>
			<details class="details-example">
				<summary>Die Liste der Sonderzeichen, die in Suchen verwendet werden können</summary>
				<table>
					<tr>
						<td><b>Sonderzeichen</b></td>
						<td><b>Beschreibung</b></td>
					</tr>
					<tr>
						<td>^l</td>
						<td>Zeilenumbruch</td>
					</tr>
					<tr>
						<td>^t</td>
						<td>Tabulator</td>
					</tr>
					<tr>
						<td>^?</td>
						<td>Ein Symbol</td>
					</tr>
					<tr>
						<td>^#</td>
						<td>Eine Ziffer</td>
					</tr>
					<tr>
						<td>^$</td>
						<td>Eine Buchstabe</td>
					</tr>
					<tr>
						<td>^n</td>
						<td>Spaltenbruch</td>
					</tr>
					<tr>
						<td>^e</td>
						<td>Endnote</td>
					</tr>
					<tr>
						<td>^f</td>
						<td>Fußnote</td>
					</tr>
					<tr>
						<td>^g</td>
						<td>Grafisches Element</td>
					</tr>
					<tr>
						<td>^m</td>
						<td>Seitenumbruch</td>
					</tr>
					<tr>
						<td>^~</td>
						<td>Bindestrich</td>
					</tr>
					<tr>
						<td>^s</td>
						<td>Geschütztes Leerzeichen</td>
					</tr>
					<tr>
						<td>^^</td>
						<td>Zirkumflex entkommen</td>
					</tr>
					<tr>
						<td>^w</td>
						<td>Ein Leerzeichen</td>
					</tr>
					<tr>
						<td>^+</td>
						<td>Geviertstrich</td>
					</tr>
					<tr>
						<td>^=</td>
						<td>Gedankenstrich</td>
					</tr>
					<tr>
						<td>^y</td>
						<td>Ein Strich</td>
					</tr>
				</table>
			</details>
			<details class="details-example">
				<summary>Sonderzeichen, die auch zum Ersetzen verwendet werden können:</summary>
				<table>
					<tr>
						<td><b>Sonderzeichen</b></td>
						<td><b>Beschreibung</b></td>
					</tr>
					<tr>
						<td>^l</td>
						<td>Zeilenumbruch</td>
					</tr>
					<tr>
						<td>^t</td>
						<td>Tabulator</td>
					</tr>
					<tr>
						<td>^n</td>
						<td>Spaltenbruch</td>
					</tr>
					<tr>
						<td>^m</td>
						<td>Seitenumbruch</td>
					</tr>
					<tr>
						<td>^~</td>
						<td>Bindestrich</td>
					</tr>
					<tr>
						<td>^s</td>
						<td>Geschütztes Leerzeichen</td>
					</tr>
					<tr>
						<td>^+</td>
						<td>Geviertstrich</td>
					</tr>
					<tr>
						<td>^=</td>
						<td>Gedankenstrich</td>
					</tr>
				</table>
			</details>

		</div>
	</body>
</html>