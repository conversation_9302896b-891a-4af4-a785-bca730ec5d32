﻿<!DOCTYPE html>
<html>
	<head>
		<title>Align your text in a paragraph</title>
		<meta charset="utf-8" />
		<meta name="description" content="Everything that pertains to the text alignment in a paragraph: aligning left, right, justified, center" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Align your text in a paragraph</h1>
			<p>The text is commonly aligned in four ways: left-aligned text, right-aligned text, centered text or justified text. To align the text in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>,</p>
			<ol>
				<li>place the cursor to the position where you want the alignment to be applied (this can be a new line or already entered text),</li>
                <li>switch to the <b>Home</b> tab of the top toolbar,</li>
				<li>select the alignment type you would like to apply:
					<ul>
						<li><b>Left</b> alignment (when the text is lined up to the left side of the page with the right side remaining unaligned) is done by clicking the <b>Align left</b> <div class = "icon icon-alignleft"></div> icon on the top toolbar.</li>
						<li><b>Center</b> alignment (when the text is lined up in the center of the page with the right and the left sides remaining unaligned) is done by clicking the <b>Align center</b> <div class = "icon icon-aligncenter"></div> icon on the top toolbar.</li>
						<li><b>Right</b> alignment (when the text is lined up to the right side of the page with the left side remaining unaligned) is done by clicking the <b>Align right</b> <div class = "icon icon-alignright"></div> icon on the top toolbar.</li>
						<li><b>Justified</b> alignment (when the text is lined up to both the left and the right sides of the page, and additional spacing is added where necessary to keep the alignment) is done by clicking the <b>Justified</b> <div class = "icon icon-onecolumn"></div> icon on the top toolbar.</li>
					</ul>
				</li>
			</ol>
            <p>The alignment parameters are also available in the <b>Paragraph - Advanced Settings</b> window:</p>
            <ol>
                <li>right-click the text and choose the <b>Paragraph - Advanced Settings</b> option from the contextual menu or use the <b>Show advanced settings</b> option on the right sidebar,</li>
                <li>open the <b>Paragraph - Advanced Settings</b> window, switch to the <b>Indents &amp; Spacing</b> tab,</li>
                <li>select one of the alignment types from the <b>Alignment</b> list: <b>Left</b>, <b>Center</b>, <b>Right</b>, <b>Justified</b>,</li>
                <li>click the <b>OK</b> button to apply the changes.</li>
            </ol>
            <p><img alt="Paragraph Advanced Settings - Indents &amp; Spacing" src="../images/paradvsettings_indents.png" /></p>
		</div>
	</body>
</html>