﻿<!DOCTYPE html>
<html>
	<head>
		<title>Change color scheme</title>
		<meta charset="utf-8" />
		<meta name="description" content="Learn how to change color scheme for a document" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Change color scheme</h1>
			<p>Color schemes are applied to the whole document. In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can quickly change the appearance of your document because they define the <b>Theme Colors</b> palette for different document elements (<a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">font</a>, <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)">background</a>, <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tables</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">autoshapes</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">charts</a>). If you applied some <b>Theme Colors</b> to the document elements and then select a different <b>Color Scheme</b>, the applied colors in your document will change correspondingly.</p>
			<p>To change a color scheme, click the downward arrow next to the <b>Change color scheme</b> <span class = "icon icon-changecolorscheme"></span> icon on the <b>Home</b> tab of the top toolbar and select the required color scheme from the list: <b>New Office</b>, <b>Office</b>, <b>Grayscale</b>, <b>Apex</b>, <b>Aspect</b>, <b>Civic</b>, <b>Concourse</b>, <b>Equity</b>, <b>Flow</b>, <b>Foundry</b>, <b>Median</b>, <b>Metro</b>, <b>Module</b>, <b>Odulent</b>, <b>Oriel</b>, <b>Origin</b>, <b>Paper</b>, <b>Solstice</b>, <b>Technic</b>, <b>Trek</b>, <b>Urban</b>, <b>Verve</b>. The selected color scheme will be highlighted in the list.</p>
			<p><img alt="Color Schemes" src="../images/colorscheme.png" /></p>
			<p>Once you select the preferred color scheme, you can select other colors in the color palettes window that corresponds to the document element you want to apply the color to. For most document elements, the color palettes window can be accessed by clicking the colored box on the right sidebar when the required element is selected. For the font, this window can be opened using the downward arrow next to the <b>Font color</b> <span class = "icon icon-fontcolor"></span> icon on the <b>Home</b> tab of the top toolbar. The following palettes are available:</p>
			<p><img alt="Palette" src="../images/palette.png" /></p>
					<ul>
						<li><b>Theme Colors</b> - the colors that correspond to the selected color scheme of the document.</li>
						<li><b>Standard Colors</b> - a set of default colors. The selected color scheme does not affect them.</li>
						<li>
							<b>Custom Color</b> - click this caption if the required color is missing among the available palettes. Select the necessary color range moving the vertical color slider and set a specific color dragging the color picker within the large square color field. Once you select a color with the color picker, the appropriate RGB and sRGB color values will be displayed in the fields on the right. You can also define a color on the base of the RGB color model by entering the corresponding numeric values into the <b>R</b>, <b>G</b>, <b>B</b> (red, green, blue) fields or enter the sRGB hexadecimal code into the field marked with the <b>#</b> sign. The selected color appears in the <b>New</b> preview box. If the object was previously filled with any custom color, this color is displayed in the <b>Current</b> box so you can compare the original and modified colors. When the color is defined, click the <b>Add</b> button:
							<p><img alt="Palette - Custom Color" src="../../../../../../common/main/resources/help/en/images/palette_custom.png" /></p>
							<p>The custom color will be applied to the selected element and added to the <b>Custom color</b> palette.</p>
						</li>
					</ul>
		</div>
	</body>
</html>