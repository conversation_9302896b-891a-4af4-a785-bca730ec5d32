﻿<!DOCTYPE html>
<html>
<head>
    <title>Add caption</title>
    <meta charset="utf-8" />
    <meta name="description" content=">The Caption is a numbered label that you can apply to objects, such as equations, tables, figures and images within your documents" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
   <body>
       <div class="mainpart">
           <div class="search-field">
               <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
           </div>
           <h1>Add captions</h1>
           <p>A <b>caption</b> is a numbered label that can be applied to objects, such as equations, tables, figures, and images in the document.</p>
           <p>A caption allows making a reference in the text - an easily recognizable label on an object.</p>
           <p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can also use captions to create a <a href="../UsageInstructions/AddTableofFigures.htm" onclick="onhyperlinkclick(this)">table of figures</a>.</p>
           <p>To add a caption to an object:</p>
           <ul>
               <li>select the required object to apply a caption;</li>
               <li>switch to the <b>References</b> tab of the top toolbar;</li>
               <li>
                   click the <div class = "icon icon-caption_icon"></div> <b>Caption</b> icon on the top toolbar or right-click on the object and select the <b>Insert Caption</b> option to open the <b>Insert Caption</b> dialogue box
                   <ul>
                       <li>choose the label to use for your caption by clicking the label drop-down and choosing the object. or</li>
                       <li>create a new label by clicking the <b>Add label</b> button to open the <b>Add label</b> dialogue box. Enter a name for the label into the label text box. Then click the <b>OK</b> button to add a new label into the label list;</li>
                   </ul>
               <li>check the <b>Include chapter number</b> checkbox to change the numbering for your caption;</li>
               <li>in <b>Insert</b> drop-down menu choose <b>Before</b> to place the label above the object or <b>After</b> to place it below the object;</li>
               <li>check the <b>Exclude label from caption</b> checkbox to leave only a number for this particular caption in accordance with a sequence number;</li>
               <li>you can then choose how to number your caption by assigning a specific style to the caption and adding a separator;</li>
               <li>to apply the caption click the <b>OK</b> button.</li>
           </ul>
           <p><img alt="Content Control settings window" src="../images/insertcaptionbox.png" /></p>
           <h2>Deleting a label</h2>
           <p>To <b>delete</b> a label you have created, choose the label from the label list within the caption dialogue box then click the <b>Delete label</b> button. The label you created will be immediately deleted.</p>
           <p class="note"><b>Note:</b> You may delete labels you have created but you cannot delete the default labels.</p>
           <h2>Formatting captions</h2>
           <p>As soon as you add a caption, a new style for captions is automatically added to the styles section. To change the style for all captions throughout the document, you should follow these steps:</p>
           <ul>
               <li>select the text to copy a new <b>Caption</b> style;</li>
               <li>search for the <b>Caption</b> style (highlighted in blue by default) in the styles gallery on the <b>Home</b> tab of the top toolbar;</li>
               <li>right-click on it and choose the <b>Update from selection</b> option.</li>
           </ul>
           <p><img alt="Content Control settings window" src="../images/updatefromseleciton.png" /></p>
           <h2>Grouping captions up</h2>
           <p>To move the object and the caption as one unit, you need <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">to group</a> the object and the caption:</p>
           <ul>
               <li>select the object;</li>
               <li>select one of the <b>Wrapping styles</b> using the right sidebar;</li>
               <li>add the caption as it is mentioned above;</li>
               <li>hold down Shift and select the items to be grouped up;</li>
               <li><b>right-click</b> item and choose <b>Arrange</b> > <b>Group</b>.</li>
           </ul>
             <p><img alt="Content Control settings window" src="../images/groupup.png" /></p>
             <p>Now both items will move simultaneously if you drag them somewhere else in the document.</p>
             <p>To unbind the objects, click on <b>Arrange</b> > <b>Ungroup</b> respectively.</p>
       </div>
   </body>
</html>