﻿<!DOCTYPE html>
<html>
<head>
    <title>Insert endnotes</title>
    <meta charset="utf-8" />
    <meta name="description" content="Insert endnotes to provide explanations for some terms or make references to the sources" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Insert endnotes</h1>
        <p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can insert endnotes to add explanations or comments to specific terms or sentences, make references to the sources, etc. that are displayed at end of the document.</p>
        <h2>Inserting endnotes</h2>
        <p>To insert an endnote into your document,</p>
        <ol>
            <li>position the insertion point at the end of the text passage or at the word that you want to add the endnote to,</li>
            <li>switch to the <b>References</b> tab located at the top toolbar,</li>
            <li>
                click the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the top toolbar and select the <b>Insert Endnote</b> option from the menu.<br />
            <p>The endnote mark (i.e. the superscript character that indicates an endnote) appears in the text of the document, and the insertion point moves to the end of the document.</p>
            </li>
            <li>type in the endnote text.</li>
        </ol>
        <p>Repeat the above mentioned operations to add subsequent endnotes for other text passages in the document. The endnotes are numbered automatically: <em>i, ii, iii,</em> etc. by default.</p>
        <p><img alt="Endnotes" src="../images/endnotesadded.png" /></p>
        <h2>Display of endnotes in the document</h2>
        <p>If you hover the mouse pointer over the endnote mark in the document text, a small pop-up window with the endnote text appears.</p>
        <p><img alt="Endnote text" src="../images/endnotetext.png" /></p>
        <h2>Navigating through endnotes</h2>
        <p>To easily navigate through the added endnotes in the text of the document,</p>
        <ol>
            <li>click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the <b>References</b> tab located at the top toolbar,</li>
            <li>in the <b>Go to Endnotes</b> section, use the <div class = "icon icon-previousfootnote"></div> arrow to go to the previous endnote or the <div class = "icon icon-nextfootnote"></div> arrow to go to the next endnote.</li>
        </ol>
        <h2>Editing endnotes</h2>
        <p>To edit the endnotes settings,</p>
        <ol>
            <li>click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the <b>References</b> tab located at the top toolbar,</li>
            <li>select the <b>Notes Settings</b> option from the menu,</li>
            <li>
                change the current parameters in the <b>Notes Settings</b> window that will appear:
                <p><img alt="Endnotes Settings window" src="../images/endnotes_settings.png" /></p>
                <ul>
                    <li>
                        Set the <b>Location</b> of endnotes on the page selecting one of the available options from the drop-down menu to the right:
                        <ul>
                            <li><b>End of section</b> - to position endnotes at the end of the sections.</li>
                            <li><b>End of document</b> - to position endnotes at the end of the document (set by default).</li>
                        </ul>
                    </li>
                    <li>
                        Adjust the endnotes <b>Format</b>:
                        <ul>
                            <li><b>Number Format</b> - select the necessary number format from the available ones: <em>1, 2, 3,...</em>, <em>a, b, c,...</em>, <em>A, B, C,...</em>, <em>i, ii, iii,...</em>, <em>I, II, III,...</em>.</li>
                            <li><b>Start at</b> - use the arrows to set the number or letter you want to start numbering with.</li>
                            <li>
                                <b>Numbering</b> - select a way to number your endnotes:
                                <ul>
                                    <li><b>Continuous</b> - to number endnotes sequentially throughout the document,</li>
                                    <li><b>Restart each section</b> - to start endnote numbering with 1 (or another specified character) at the beginning of each section,</li>
                                    <li><b>Restart each page</b> - to start endnote numbering with 1 (or another specified character) at the beginning of each page.</li>
                                </ul>
                            </li>
                            <li><b>Custom Mark</b> - set a special character or a word you want to use as the endnote mark (e.g. * or Note1). Enter the necessary character/word into the text entry field and click the <b>Insert</b> button at the bottom of the <b>Notes Settings</b> window.</li>
                        </ul>
                    </li>
                    <li>
                        Use the <b>Apply changes to</b> drop-down list if you want to apply the specified notes settings to the <b>Whole document</b> or the <b>Current section</b> only.
                        <p class="note"><b>Note</b>: to use different endnotes formatting in separate parts of the document, you need to add <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">section breaks</a> first.</p>
                    </li>
                </ul>
            </li>
            <li>When you finish, click the <b>Apply</b> button.</li>
        </ol>

        <h2>Removing endnotes</h2>
        <p>To remove a single endnote, position the insertion point directly before the endtnote mark in the text and press <b>Delete</b>. Other endnotes will be renumbered automatically.</p>
        <p>To delete all the endnotes in the document,</p>
        <ol>
            <li>click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the <b>References</b> tab located at the top toolbar,</li>
            <li>select the <b>Delete All Notes</b> option from the menu.</li>
            <li>choose the <b>Delete All Endnotes</b> option in the appeared window and click <b>OK</b>.</li>
        </ol>
    </div>
</body>
</html>