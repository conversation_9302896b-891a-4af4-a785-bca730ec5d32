﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert equations</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insert equations and mathematical symbols." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert equations</h1>
            <p>The <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows you to build equations using the built-in templates, edit them, insert special characters (including mathematical operators, Greek letters, accents, etc.).</p>
            <h3>Add a new equation</h3>
            <p>To insert an equation from the gallery,</p>
			<ol>
				<li>put the cursor within the necessary line,</li>
                <li>switch to the <b>Insert</b> tab of the top toolbar,</li>
				<li>click the arrow next to the <div class = "icon icon-insertequationicon"></div> <b>Equation</b> icon on the top toolbar,</li>
                <li>
                    select the required equation category in the toolbar above the inserted equation,
                    <p>or</p>
                    <p>in the opened drop-down list select the equation category you need.</p>
                    <p>The following categories are currently available: <em>Symbols</em>, <em>Fractions</em>, <em>Scripts</em>, <em>Radicals</em>, <em>Integrals</em>, <em>Large Operators</em>, <em>Brackets</em>, <em>Functions</em>, <em>Accents</em>, <em>Limits and Logarithms</em>, <em>Operators</em>, <em>Matrices</em>,</p>
                </li>
                <li>click the <b>Equation Settings</b> symbol in the toolbar above the inserted equation to access more settings, e.g., <em>Unicode</em> or <em>LaTeX</em>, <em>Professional</em> or <em>Linear</em>, and <em>Change to Inline</em>,</li>
                <li>click the certain symbol/equation in the corresponding set of templates.</li>
			</ol>
            <p><img alt="equation toolbar" src="../images/equationtoolbar.png" /></p>
            <p>The selected symbol/equation box will be inserted at the cursor position. If the selected line is empty, the equation will be centered. To align such an equation to the left or to the right, click on the equation box and use the <span class = "icon icon-alignleft"></span> or <span class = "icon icon-alignright"></span> icon on the <b>Home</b> tab of the top toolbar.</p>
            <div class = "icon icon-insertedequation"></div>
            <p>Each equation template represents a set of slots. A slot is a position for each element that makes up the equation. An empty slot (also called as a placeholder) has a dotted outline <span class = "icon icon-equationplaceholder"></span>. You need to fill in all the placeholders specifying the necessary values.</p>
            <p class="note"><b>Note</b>: to start creating an equation, you can also use the <b>Alt + =</b> keyboard shortcut.</p>
            <p>It's also possible to add a caption to the equation. To learn more on how to work with captions for equations, please refer to <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">this article</a>.</p>
            <h3>Enter values</h3>
            <p>The <b>insertion point</b> specifies where the next character will appear. To position the insertion point precisely, click within the placeholder and use the keyboard arrows to move the insertion point by one character left/right or one line up/down.</p>
            <p>If you need to create a new placeholder below the slot with the insertion point within the selected template, press <b>Enter</b>.</p>
            <div class = "big big-newslot"></div>
            <p>Once the insertion point is positioned, you can fill in the placeholder:
            <ul>
                <li>enter the desired numeric/literal value using the keyboard,</li>
                <li>insert a special character using the <b>Symbols</b> palette from the <div class = "icon icon-insertequationicon"></div> <b>Equation</b> menu on the <b>Insert</b> tab of the top toolbar or typing them from the keyboard (see the <a href="../UsageInstructions/MathAutoCorrect.htm" onclick="onhyperlinkclick(this)"><b>Math AutoСorrect</b></a> option description),</li>
                <li>add another equation template from the palette to create a complex nested equation. The size of the primary equation will be automatically adjusted to fit its content. The size of the nested equation elements depends on the primary equation placeholder size, but it cannot be smaller than the sub-subscript size.</li>                
            </ul>
            </p>
            <p><div class = "big big-nestedfraction"></div></p>
            <p>To add some new equation elements you can also use the <b>right-click menu options</b>:</p>
            <ul>
                <li>To add a new argument that goes before or after the existing one within <em>Brackets</em>, you can right-click on the existing argument and select the <b>Insert argument before/after</b> option from the menu.</li>
                <li>To add a new equation within <em>Cases</em> with several conditions from the <em>Brackets</em> group (or equations of other types, if you've previously added new placeholders by pressing <b>Enter</b>), you can right-click on an empty placeholder or entered equation within it and select the <b>Insert equation before/after</b> option from the menu.</li>
                <li>To add a new row or a column in a <em>Matrix</em>, you can right-click on a placeholder within it, select the <b>Insert</b> option from the menu, then select <b>Row Above/Below</b> or <b>Column Left/Right</b>.</li>
            </ul>
            <p class="note"><b>Note</b>: currently, equations cannot be entered using the linear format, i.e. <b>\sqrt(4&x^3)</b>.</p>
            <p>When entering the values of the mathematical expressions, you do not need to use <b>Spacebar</b> as the spaces between the characters and signs of operations are set automatically.</p>
            <p>If the equation is too long and does not fit a single line, automatic line breaking occurs while typing. You can also insert a line break in a specific position by right-clicking on a mathematical operator and selecting the <b>Insert manual break</b> option from the menu. The selected operator will start a new line. Once the manual line break is added, you can press the <b>Tab</b> key to align the new line to any math operator of the previous line. To delete the added manual line break, right-click on the mathematical operator that starts a new line and select the <b>Delete manual break</b> option.</p>
            <h3>Format equations</h3>
            <p>To increase or decrease the equation <b>font size</b>, click anywhere within the equation box and use the <span class = "icon icon-larger"></span> and <span class = "icon icon-smaller"></span> buttons on the <b>Home</b> tab of the top toolbar or select the necessary font size from the list. All the equation elements will change correspondingly.</p>
            <p>The letters within the equation are italicized by default. If necessary, you can change the <b>font style</b> (<em>bold, italic, strikeout</em>) or <b>color</b> for a whole equation or its part. The <em>underlined</em> style can be applied to the entire equation only, not to individual characters. Select the necessary part of the equation by clicking and dragging it. The selected part will be highlighted in blue. Then use the necessary buttons on the <b>Home</b> tab of the top toolbar to format the selected part. For example, you can remove the italic format for ordinary words that are not variables or constants.</p>
            <div class = "big big-formatastext"></div>
            <p>To modify some equation elements, you can also use the <b>right-click menu options</b>:</p>
            <ul><li>To change the <em>Fractions</em> format, you can right-click on a fraction and select the <b>Change to skewed/linear/stacked fraction</b> option from the menu (the available options differ depending on the selected fraction type). <!--The <b>Remove/Add fraction bar</b> option is also available for stacked fractions.--></li>
                <li>To change the <em>Scripts</em> position relating to text, you can right-click on the equation that includes scripts and select the <b>Scripts before/after text</b> option from the menu.</li>
                <li>To change the argument size for <em>Scripts, Radicals, Integrals, Large Operators, Limits and Logarithms, Operators</em> as well as for overbraces/underbraces and templates with grouping characters from the <em>Accents</em> group, you can right-click on the argument you want to change and select the <b>Increase/Decrease argument size</b> option from the menu.</li>
                <li>To specify whether an empty degree placeholder should be displayed or not for a <em>Radical</em>, you can right-click on the radical and select the <b>Hide/Show degree</b> option from the menu.</li>
                <li>To specify whether an empty limit placeholder should be displayed or not for an <em>Integral</em> or <em>Large Operator</em>, you can right-click on the equation and select the <b>Hide/Show top/bottom limit</b> option from the menu.</li>
                <li>To change the limits position relating to the integral or operator sign for <em>Integrals</em> or <em>Large Operators</em>, you can right-click on the equation and select the <b>Change limits location</b> option from the menu. The limits can be displayed on the right of the operator sign (as subscripts and superscripts) or directly above and below the operator sign.</li>
                <li>To change the limits position relating to text for <em>Limits and Logarithms</em> and templates with grouping characters from the <em>Accents</em> group, you can right-click on the equation and select the <b>Limit over/under text</b> option from the menu.</li>
                <li>To choose which of the <em>Brackets</em> should be displayed, you can right-click on the expression within them and select the <b>Hide/Show opening/closing bracket</b> option from the menu.</li>
                <li>To control the <em>Brackets</em> size, you can right-click on the expression within them. The <b>Stretch brackets</b> option is selected by default so that the brackets can grow according to the expression within them, but you can deselect this option to prevent brackets from stretching. When this option is activated, you can also use the <b>Match brackets to argument height</b> option.</li>
                <li>To change the character position relating to text for overbraces/underbraces or overbars/underbars from the <em>Accents</em> group, you can right-click on the template and select the <b>Char/Bar over/under text</b> option from the menu.</li>
                <li>To choose which borders should be displayed for a <em>Boxed formula</em> from the <em>Accents</em> group, you can right-click on the equation and select the <b>Border properties</b> option from the menu, then select <b>Hide/Show top/bottom/left/right border</b> or <b>Add/Hide horizontal/vertical/diagonal line</b>.</li>
                <li>To specify whether empty placeholders should be displayed or not for a <em>Matrix</em>, you can right-click on it and select the <b>Hide/Show placeholder</b> option from the menu.</li>
            </ul>
            <p>To align some equation elements you can use the <b>right-click menu options</b>:</p>
            <ul>
                <li>To align equations within <em>Cases</em> with several conditions from the <em>Brackets</em> group (or equations of other types, if you've previously added new placeholders by pressing <b>Enter</b>), you can right-click on an equation, select the <b>Alignment</b> option from the menu, then select the alignment type: <b>Top</b>, <b>Center</b>, or <b>Bottom</b>.</li>
                <li>To align a <em>Matrix</em> vertically, you can right-click on the matrix, select the <b>Matrix Alignment</b> option from the menu, then select the alignment type: <b>Top</b>, <b>Center</b>, or <b>Bottom</b>.</li>
                <li>To align elements within a <em>Matrix</em> column horizontally, you can right-click on a placeholder within the column, select the <b>Column Alignment</b> option from the menu, then select the alignment type: <b>Left</b>, <b>Center</b>, or <b>Right</b>.</li>
            </ul>
            <h3>Delete equation elements</h3>
            <p>To delete a part of the equation, select it by dragging the mouse or holding down the <b>Shift</b> key and using the arrow buttons, then press the <b>Delete</b> key on the keyboard.</p>
            <p>A slot can only be deleted together with the template it belongs to.</p>
            <p>To delete the entire equation, select it completely by dragging the mouse or double-clicking on the equation box and press the <b>Delete</b> key on the keyboard.</p>
            <div class = "icon icon-deleteequation"></div>
            <p>To delete some equation elements, you can also use the <b>right-click menu options</b>:</p>
            <ul>
                <li>To delete a <em>Radical</em>, you can right-click on it and select the <b>Delete radical</b> option from the menu.</li>
                <li>To delete a <em>Subscript</em> and/or <em>Superscript</em>, you can right-click on the expression that contains them and select the <b>Remove subscript/superscript</b> option from the menu. If the expression contains scripts that go before text, the <b>Remove scripts</b> option is available.</li>
                <li>To delete <em>Brackets</em>, you can right-click on the expression within them and select the <b>Delete enclosing characters</b> or <b>Delete enclosing characters and separators</b> option from the menu.</li>
                <li>If the expression within <em>Brackets</em> inclides more than one argument, you can right-click on the argument you want to delete and select the <b>Delete argument</b> option from the menu.</li>
                <li>If <em>Brackets</em> enclose more than one equation (i.e. <em>Cases</em> with several conditions), you can right-click on the equation you want to delete and select the <b>Delete equation</b> option from the menu. This option is also available for equations of other types if you've previously added new placeholders by pressing <b>Enter</b>.</li>
                <li>To delete a <em>Limit</em>, you can right-click on it and select the <b>Remove limit</b> option from the menu.</li>
                <li>To delete an <em>Accent</em>, you can right-click on it and select the <b>Remove accent character</b>, <b>Delete char</b> or <b>Remove bar</b> option from the menu (the available options differ depending on the selected accent).</li>
                <li>To delete a row or a column of a <em>Matrix</em>, you can right-click on the placeholder within the row/column you need to delete, select the <b>Delete</b> option from the menu, then select <b>Delete Row/Column</b>.</li>
            </ul>
            <h3 id="convertequation">Convert equations</h3>
            <p>If you open an existing document containing equations which were created with an old version of equation editor (for example, with MS Office versions before 2007), you need to convert these equations to the Office Math ML format to be able to edit them.</p>
            <p>To convert an equation, double-click it. The warning window will appear:</p>
            <p><img alt="Convert equation" src="../images/convertequation.png" /></p>
            <p>To convert the selected equation only, click the <b>Yes</b> button in the warning window. To convert all equations in this document, check the <b>Apply to all equations</b> box and click <b>Yes</b>.</p>
            <p>Once the equation is converted, you can edit it.</p>
		</div>
	</body>
</html>