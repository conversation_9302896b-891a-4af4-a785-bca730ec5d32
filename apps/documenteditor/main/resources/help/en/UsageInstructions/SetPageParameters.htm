﻿<!DOCTYPE html>
<html>
	<head>
		<title>Set page parameters</title>
		<meta charset="utf-8" />
        <meta name="description" content="Set page parameters: change page orientation and size, adjust margins and insert columns" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Set page parameters</h1>
            <p>To change page layout in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, i.e. set page orientation and size, adjust margins and insert columns, use the corresponding icons on the <b>Layout</b> tab of the top toolbar.</p>
            <p class="note"><b>Note</b>: all these parameters are applied to the entire document. If you need to set different page margins, orientation, size, or column number for the separate parts of your document, please refer to <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">this page</a>.</p>
            <h3 id="orientation">Page Orientation</h3>
            <p>Change the current orientation by type clicking the <span class = "icon icon-orientation"></span> <b>Orientation</b> icon. The default orientation type is <b>Portrait</b> that can be switched to <b>Album</b>.</p>
            <h3 id="size">Page Size</h3>
            <p>Change the default A4 format by clicking the <span class = "icon icon-pagesize"></span> <b>Size</b> icon and selecting the required format from the list. The following preset sizes are available:</p>
            <ul>
                <li>US Letter (21,59cm x 27,94cm)</li>
                <li>US Legal (21,59cm x 35,56cm)</li>
                <li>A4 (21cm x 29,7cm)</li>
                <li>A5 (14,81cm x 20,99cm)</li>
                <li>B5 (17,6cm x 25,01cm)</li>
                <li>Envelope #10 (10,48cm x 24,13cm)</li>
                <li>Envelope DL (11,01cm x 22,01cm)</li>
                <li>Tabloid (27,94cm x 43,17cm)</li>
                <li>AЗ (29,7cm x 42,01cm)</li>
                <li>Tabloid Oversize (30,48cm x 45,71cm)</li>
                <li>ROC 16K (19,68cm x 27,3cm)</li>
                <li>Envelope Choukei 3 (11,99cm x 23,49cm)</li>
                <li>Super B/A3 (33,02cm x 48,25cm)</li>
            </ul>
            <p>You can also set a special page size by selecting the <b>Custom Page Size</b> option from the list. The Page Size window will open where you'll be able to select the required <b>Preset</b> (US Letter, US Legal, A4, A5, B5, Envelope #10, Envelope DL, Tabloid, AЗ, Tabloid Oversize, ROC 16K, Envelope Choukei 3, Super B/A3, A0, A1, A2, A6) or set custom <b>Width</b> and <b>Height</b> values. Enter new values into the entry fields or adjust the existing values using the arrow buttons. When you finish, click <b>OK</b> to apply the changes.</p>
            <p><img alt="Custom Page Size" src="../images/custompagesize.png" /></p>
            <h3 id="margins">Page Margins</h3>
            <p>Change the default margins, i.e. the blank space between the left, right, top and bottom page edges and the paragraph text, by clicking the <span class = "icon icon-pagemargins"></span> <b>Margins</b> icon and selecting one of the available presets: <b>Normal</b>, <b>US Normal</b>, <b>Narrow</b>, <b>Moderate</b>, <b>Wide</b>. You can also use the <b>Custom Margins</b> option to set your own values in the Margins window. Enter the required <b>Top</b>, <b>Bottom</b>, <b>Left</b> and <b>Right</b> page margin values into the entry fields or adjust the existing values using arrow buttons.</p>
            <p><img alt="Custom Margins" src="../images/custommargins.png" /></p>
            <p><b>Gutter position</b> is used to set up additional space on the left side of the document or at its top. The <b>Gutter</b> option is helpful to make sure that bookbinding does not cover the text. In the <b>Margins</b> enter the required gutter position into the entry fields and choose where it should be placed in.</p>
            <p class="note"><b>Note</b>: the <b>Gutter position</b> cannot be used when the <b>Mirror margins</b> option is checked.</p>
            <p>In the <b>Multiple pages</b> drop-down menu, choose the <b>Mirror margins</b> option to set up facing pages for double-sided documents. With this option checked, <b>Left</b> and <b>Right</b> margins turn into <b>Inside</b> and <b>Outside</b> margins respectively.</p>
            <p>In <b>Orientation</b> drop-down menu choose from <b>Portrait</b> and <b>Landscape</b> options.</p>
            <p>All applied changes to the document will be displayed in the <b>Preview</b> window.</p>
            <p>When you finish, click <b>OK</b>. The custom margins will be applied to the current document and the <b>Last Custom</b> option with the specified parameters will appear in the <span class = "icon icon-pagemargins"></span> <b>Margins</b> list so that you will be able to apply them to other documents.</p>
            <p>You can also change the margins manually by dragging the border between the grey and white areas on the rulers (the grey areas of the rulers indicate page margins):</p>
            <p><span class = "big big-margins"></span></p>
            <h3 id="columns">Columns</h3>
            <p>Apply a multi-column layout by clicking the <span class = "icon icon-insertcolumns"></span> <b>Columns</b> icon and selecting the necessary column type from the drop-down list. The following options are available:</p>
            <ul>
                <li><b>Two</b> <div class = "icon icon-twocolumns"></div> - to add two columns of the same width,</li>
                <li><b>Three</b> <div class = "icon icon-threecolumns"></div> - to add three columns of the same width,</li>
                <li><b>Left</b> <div class = "icon icon-leftcolumn"></div> - to add two columns: a narrow column on the left and a wide column on the right,</li>
                <li><b>Right</b> <div class = "icon icon-rightcolumn"></div> - to add two columns: a narrow column on the right and a wide column on the left.</li>
            </ul>
            <p>If you want to adjust column settings, select the <b>Custom Columns</b> option from the list. The <b>Columns</b> window will appear, and you'll be able to set the required <b>Number of columns</b> (you can add up to 12 columns) and <b>Spacing between columns</b>. Enter your new values into the entry fields or adjust the existing values using arrow buttons. Check the <b>Column divider</b> box to add a vertical line between the columns. When you finish, click <b>OK</b> to apply the changes.</p>
            <p><img alt="Custom Columns" src="../images/customcolumns.png" /></p>
            <p>To exactly specify where a new column should start, place the cursor before the text that you want to move to the new column, click the <span class = "icon icon-pagebreak1"></span> <b>Breaks</b> icon on the top toolbar and then select the <b>Insert Column Break</b> option. The text will be moved to the next column.</p>
            <p>The inserted column breaks are indicated in your document with a dotted line: <span class = "big big-columnbreak"></span>. If you do not see the inserted column breaks, click the <span class = "icon icon-nonprintingcharacters"></span> icon at the <b>Home</b> tab on the top toolbar to make them visible. To remove a column break select it with the mouse and press the <b>Delete</b> key.</p>
            <p>To manually change the column width and spacing, you can use the horizontal ruler.</p>
            <p><div class = "big big-columnspacing"></div></p>
            <p>To cancel columns and return to a regular single-column layout, click the <span class = "icon icon-insertcolumns"></span> <b>Columns</b> icon on the top toolbar and select the <b>One</b> <span class = "icon icon-onecolumn"></span> option from the list.</p>
        </div>
	</body>
</html>