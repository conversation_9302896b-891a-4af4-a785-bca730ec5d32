﻿<!DOCTYPE html>
<html>
	<head>
		<title>Select background color for a paragraph</title>
		<meta charset="utf-8" />
		<meta name="description" content="Learn how to select background color for a paragraph" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Select a background color for a paragraph</h1>
			<p>A background color is applied to the whole paragraph and completely fills all the paragraph space from the left page margin to the right page margin.</p>
			<p>To apply a background color to a certain paragraph or change the current one in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>,</p>
			<ol>
				<li>select a color scheme for your document from the available ones clicking the <b>Change color scheme</b> <div class = "icon icon-changecolorscheme"></div> icon at the <b>Home</b> tab on the top toolbar</li>
				<li>place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text using the <b>Ctrl+A</b> key combination</li>
				<li>open the color palettes window. You can access it in one of the following ways: 
				<ul>
				<li>click the downward arrow next to the <div class = "icon icon-backgroundcolor"></div> icon on the <b>Home</b> tab of the top toolbar, or</li>
				<li>click the color field next to the <b>Background Color</b> caption on the right sidebar, or</li>
				<li>click the 'Show advanced settings' link on the right sidebar or select the 'Paragraph Advanced Settings' option on the right-click menu, then switch to the 'Borders &amp; Fill' tab within the 'Paragraph - Advanced Settings' window and click the color field next to the <b>Background Color</b> caption.</li>
				</ul>
				</li>
				<li>select any color among the available <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">palettes</a></li>
			</ol>
			<p>After you select the required color by using the <span class = "icon icon-backgroundcolor"></span> icon, you'll be able to apply this color to any selected paragraph just by clicking the <span class = "icon icon-backgroundcolor_selected"></span> icon (it displays the selected color), without having to choose this color in the palette again. If you use the <b>Background Color</b> option on the right sidebar or within the 'Paragraph - Advanced Settings' window, remember that the selected color is not retained for quick access. (These options can be useful if you wish to select a different background color for a specific paragraph and if you are also using some general color selected by clicking the <span class = "icon icon-backgroundcolor"></span> icon).</p>
			<hr />
			<p>To remove the background color from a certain paragraph,</p>
			<ol>
				<li>place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text using the <b>Ctrl+A</b> key combination</li>
				<li>open the color palettes window by clicking the color field next to the <b>Background Color</b> caption on the right sidebar</li>
				<li>select the <div class = "icon icon-nofill"></div> icon.</li>
			</ol>
		</div>
	</body>
</html>