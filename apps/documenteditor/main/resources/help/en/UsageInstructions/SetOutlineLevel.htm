﻿<!DOCTYPE html>
<html>
	<head>
		<title>Set up a paragraph outline level</title>
		<meta charset="utf-8" />
        <meta name="description" content="Set up paragraph outline level" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Set up paragraph outline level</h1>
			
            <p>An outline level is the paragraph level in the document structure. In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, the following levels are available: <em>Basic Text</em>, <em>Level 1</em> - <em>Level 9</em>. The outline level can be specified in different ways, for example, by using <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">heading styles</a>: once you assign a heading style (<em>Heading 1</em> - <em>Heading 9</em>) to a paragraph, it acquires the corresponding outline level. If you assign a level to a paragraph using the paragraph advanced settings, the paragraph acquires the structure level only while its style remains unchanged. The outline level can be also changed in the <a href="../UsageInstructions/CreateTableOfContents.htm#navigation" onclick="onhyperlinkclick(this)"><b>Navigation</b></a> panel on the left using the contextual menu options.</p>
            <p>To change a paragraph outline level using the paragraph advanced settings,</p>
            <ol>
                <li>right-click the text and choose the <b>Paragraph Advanced Settings</b> option from the contextual menu or use the <b>Show advanced settings</b> option on the right sidebar,</li>
                <li>open the <b>Paragraph - Advanced Settings</b> window, switch to the <b>Indents &amp; Spacing</b> tab,</li>
                <li>select the necessary outline level from the <b>Outline level</b> list.</li>
                <li>click the <b>OK</b> button to apply the changes.</li>
            </ol>
            <p><img alt="Paragraph Advanced Settings - Indents &amp; Spacing" src="../images/paradvsettings_indents.png" /></p>
		</div>
	</body>
</html>