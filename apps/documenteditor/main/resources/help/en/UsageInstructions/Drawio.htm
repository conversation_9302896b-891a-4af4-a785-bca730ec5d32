﻿<!DOCTYPE html>
<html>
<head>
    <title>Create and insert diagrams</title>
    <meta charset="utf-8" />
    <meta name="description" content="If you need to create a lot of various and complex diagrams, ONLYOFFICE Document Editor provides you with a draw.io plugin that can create and configure such diagrams" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Create and insert diagrams</h1>
        <p>If you need to create a lot of various and complex diagrams, ONLYOFFICE <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> provides you with a draw.io plugin that can create and configure such diagrams.</p>
        <ol>
            <li>Select the place on the page where you want to insert a diagram.</li>
            <li>Switch to the <b>Plugins</b> tab and click <div class = "icon icon-drawio"></div> <b>draw.io</b>.</li>
            <li>
                <b>draw io</b> window will open containing the following sections:
                <ul>
                    <li><b>Top toolbar</b> contains tools to manage files, configure interface, edit data via <em>File</em>, <em>Edit</em>, <em>View</em>, <em>Arrange</em>, <em>Extras</em>, <em>Help</em> tabs and corresponding options.</li>
                    <li><b>Left sidebar</b> contains various forms to select from: <em>Standard</em>, <em>Software</em>, <em>Networking</em>, <em>Business</em>, <em>Other</em>. To add new shapes to those available by default, click the <b>More Shapes</b> button, select the necessary object types and click <b>Apply</b>.</li>
                    <li>
                        <b>Right sidebar</b> contains tools and settings to customize the worksheet, shapes, charts, sheets, text, and arrows:
                        <ul>
                            <li>
                                <b>Worksheet</b> settings:
                                <ul>
                                    <li><b>View</b>: <em>Grid</em>, its size and color, <em>Page view</em>, <em>Background</em> - you can either select a local image or provide the URL, or choose a suitable color using the color palette, as well as add <em>Shadow</em> effects.</li>
                                    <li><b>Options</b>: <em>Connection Arrows</em>, <em>Connection Points</em>, <em>Guides</em>.</li>
                                    <li><b>Paper size</b>: <em>Portrait</em> or <em>Landscape</em> orientation with specified length and width parameters.</li>
                                </ul>
                            </li>
                            <li>
                                <b>Shape</b> settings:
                                <ul>
                                    <li><b>Color</b>: <em>Fill color</em>, <em>Gradient</em>.</li>
                                    <li><b>Line</b>: <em>Color</em>, <em>Type</em>, <em>Width</em>, <em>Perimeter width</em>.</li>
                                    <li><b>Opacity</b>.</li>
                                </ul>
                            </li>
                            <li>
                                <b>Arrow</b> settings:
                                <ul>
                                    <li><b>Color</b>: <em>Fill color</em>, <em>Gradient</em>.</li>
                                    <li><b>Line</b>: <em>Color</em>, <em>Type</em>, <em>Width</em>, <em>Line end</em>, <em>Line start</em>.</li>
                                    <li><b>Opacity</b>.</li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                    <li><b>Working area</b> to view diagrams, enter and edit data. Here you can move objects, form sequential diagrams, and connect objects with arrows.</li>
                    <li>
                        <b>Status bar</b> contains navigation tools for convenient switching between sheets and managing them.
                        <p><img alt="Draw io diagram" src="../images/drawio_diagram.png" /></p>
                    </li>
                </ul>
            </li>
        </ol>
        <p>Use these tools to create the necessary diagram, edit it, and when it is finished, click the <b>Insert</b> button to add it to the document.</p>
    </div>
</body>
</html>