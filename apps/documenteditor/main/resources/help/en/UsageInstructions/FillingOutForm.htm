﻿<!DOCTYPE html>
<html>
<head>
    <title>Filling Out a Form</title>
    <meta charset="utf-8" />
    <meta name="description" content="Fill out a Form template" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Filling Out a Form</h1>
        <p>A fillable form is an OFORM file. OFORM is a format for filling out template forms and downloading or printing the form after you have filled it out.</p> 
            <h2>How to fill in a form:</h2>
            <ol>
                <li>
                    Open an OFORM file.
                    <p><img alt="oform" src="../images/oform.png" /><p>
                </li>
                <li>Fill in all the required fields. The mandatory fields are marked with red stroke. Use <div class="icon icon-arrows_formfilling"></div> or <div class="icon icon-next_formfilling"></div> <b>Next Field</b> on the top toolbar to navigate between fields, or click the field you wish to fill in.
<li>Use the <b>Clear All Fields</b> button <img alt="Clear all form filling" src="../images/clearall_formfilling.png" /> to empty all input fields.</li>
                <li>After you have filled in all the fields, click the <b>Save as PDF</b> button to save the form to your computer as a PDF file.</li>
                <li>
                    Click <div class = "icon icon-morebutton"></div> in the top right corner of the toolbar for additional options. You can <b>Print</b>, <b>Download as docx</b>, or <b>Download as pdf</b>.
                    <p><img alt="More OFORM" src="../images/more_oform.png" /></p>
                    <p>You can also change the form <b>Interface</b> theme by choosing <b>Same as system</b>, <b>Light</b>, <b>Classic Light</b>, <b>Dark</b> or <b>Contrast Dark</b>. Once the <b>Dark</b> or <b>Contrast Dark</b> interface theme is enabled, the <b>Dark mode</b> becomes available.</p>
                    <p><img alt="Dark Mode Form" src="../images/darkmode_oform.png" /></p>
                    <p><b>Zoom</b> allows to scale and to resize the page using the <b>Fit to page</b>, <b>Fit to width</b> and <b>Zoom in</b> or <b>Zoom out</b> options:</p>
                    <p><img alt="Zoom" src="../images/pagescaling.png" /></p>
                    <ul>
                        <li><b>Fit to page</b> allows to resize the page so that the screen displays the whole page.</li>
                        <li><b>Fit to width</b> allows to resize the page so that the page scales to fit the width of the screen.</li>
                        <li><b>Zoom</b> adjusting tool allows to zoom in and zoom out the page.</li>
                    </ul>
                    <p><b>Open file location</b> when you need to browse the folder where the form is stored.</p>

                </li>
            </ol>
     </div>
</body>
</html>