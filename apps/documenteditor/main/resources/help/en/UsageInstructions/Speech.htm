﻿<!DOCTYPE html>
<html>
<head>
    <title>Read the text out loud</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of Speech plugin for ONLYOFFICE editors, which allows to read the written text out loud" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Read the text out loud</h1>
        <p>ONLYOFFICE <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> has a plugin that can read out the text for you.</p>
        <ol>
            <li>Select the text to be read out.</li>
            <li>Switch to the <b>Plugins</b> tab and choose <div class = "icon icon-speech"></div> <b>Speech</b>.</li>
        </ol>
        <p>The text will now be read out.</p>
    </div>
</body>
</html>