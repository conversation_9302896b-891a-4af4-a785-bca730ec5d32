﻿<!DOCTYPE html>
<html>
	<head>
		<title>Add borders</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add borders to your document selecting their style" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Add borders</h1>
			<p>To add borders to a paragraph, page, or the whole document in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>,</p>
			<ol>
				<li>place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text by pressing the <b>Ctrl+A</b> key combination,</li>
				<li>click the right mouse button and select the <b>Paragraph Advanced Settings</b> option from the menu or use the <b>Show advanced settings</b> link on the right sidebar,</li>
				<li>switch to the <b>Borders &amp; Fill</b> tab in the opened <b>Paragraph - Advanced Settings</b> window,</li>
				<li>set the needed value for <b>Border Size</b> and select a <b>Border Color</b>,</li>
				<li>click within the available diagram or use buttons to select borders and apply the chosen style to them,</li>
				<li>click the <b>OK</b> button.</li>
			</ol>
			<p><img alt="Paragraph Advanced Settings - Borders &amp; Fill" src="../images/paradvsettings_borders.png" /></p>
			<p>After adding the borders, you can also set <b>paddings</b> i.e. distances between the <b>right</b>, <b>left</b>, <b>top</b> and <b>bottom</b> borders and the paragraph.</p>
			<p>To set the necessary values, switch to the <b>Paddings</b> tab of the <b>Paragraph - Advanced Settings</b> window:</p>
			<p><img alt="Paragraph Advanced Settings - Paddings" src="../images/paradvsettings_margins.png" /></p>
		</div>
	</body>
</html>