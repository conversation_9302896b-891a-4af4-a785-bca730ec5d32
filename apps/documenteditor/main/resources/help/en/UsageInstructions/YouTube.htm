﻿<!DOCTYPE html>
<html>
<head>
    <title>Include a video</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of YouTube plugin for ONLYOFFICE editors, which allows to embed videos into documents" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Include a video</h1>
        <p>In the <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can include a video in your document. It will be shown as an image. By double-clicking the image the video dialog opens. Here you can start the video.</p>
        <ol>
            <li>
                Copy the <b>URL</b> of the video you want to include.<br />
                (the complete address shown in the address line of your browser)
            </li>
            <li>Go to your document and place the cursor at the location where you want to include the video.</li>
            <li>Switch to the <b>Plugins</b> tab and choose <div class = "icon icon-youtube"></div> <b>YouTube</b>.</li>
            <li>Paste the <b>URL</b> and click <b>OK</b>.</li>
            <li>Check if it is the correct video and click the <b>OK</b> button below the video.</li>
        </ol>
        <p>The video is now included in your document.</p>
        <img class="gif" alt="Youtube plugin gif" src="../../images/youtube_plugin.gif" width="600" />
</div>
</body>
</html>