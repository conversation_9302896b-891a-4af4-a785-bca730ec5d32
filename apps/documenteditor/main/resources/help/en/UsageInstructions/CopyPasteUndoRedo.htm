﻿<!DOCTYPE html>
<html>
	<head>
		<title>Copy/paste text passages, undo/redo your actions</title>
		<meta charset="utf-8" />
		<meta name="description" content="Perform the basic operations with the document text: copy, paste, undo, redo" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Copy/paste text passages, undo/redo your actions</h1>
            <h3>Use basic clipboard operations</h3>
            <p>To cut, copy and paste text passages and inserted objects (autoshapes, images, charts) in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, select the corresponding options from the right-click menu or click the icons located on any tab of the top toolbar:</p>
			<ul>
                <li><b>Cut</b> – select a text fragment or an object and use the <b>Cut</b> option from the right-click menu, or the <b>Cut</b> <div class="icon icon-cut"></div> icon on the top toolbar to delete the selected text and send it to the computer clipboard memory. <span class="onlineDocumentFeatures">The cut text can be later inserted to another place in the same document.</span></li>
                <li><b>Copy</b> – select a text fragment or an object and use the <b>Copy</b> option from the right-click menu, or the <b>Copy</b> <div class = "icon icon-copy"></div> icon on the top toolbar to copy the selected text to the computer clipboard memory. <span class="onlineDocumentFeatures">The copied text can be later inserted to another place in the same document.</span></li>
                <li><b>Paste</b> – find the place in your document where you need to paste the previously copied text fragment/object and use the the <b>Paste</b> option from the right-click menu, or the <b>Paste</b> <div class = "icon icon-paste"></div> icon on the top toolbar.
                    The copied text/object will be inserted to the current cursor position. <span class="onlineDocumentFeatures">The data can be previously copied from the same document.</span>
                </li>			
			</ul>
            <p><span class="onlineDocumentFeatures">In the <em>online version</em>, the key combinations below are only used to copy or paste data from/into another document or a program.</span> <span class="desktopDocumentFeatures">In the <em>desktop version</em>, both corresponding buttons/menu options and key combinations can be used for any copy/paste operations:</span></p>
            <ul>
                <li><b>Ctrl+X</b> key combination for cutting;</li>
                <li><b>Ctrl+C</b> key combination for copying;</li>
                <li><b>Ctrl+V</b> key combination for pasting.</li>
            </ul>
            <p class="note"><b>Note</b>: instead of cutting and pasting text fragments in the same document, you can just select the required text passage and drag and drop it to the necessary position.</p>
            <h3>Use the Paste Special feature</h3>
            <p class="note"><b>Note</b>: For collaborative editing, the <b>Paste Special</b> feature is available in the <b>Strict</b> co-editing mode only.</p>
            <p>Once the copied text is pasted, the <b>Paste Special</b> <span class="icon icon-pastespecialbutton"></span> button appears next to the inserted text passage. Click this button to select the necessary paste option or use the <em>Ctrl</em> key in combination with the letter key given in the brackets next to the required option.</p>
            <p>When pasting a text paragraph or some text within autoshapes, the following options are available:</p>
            <ul>
                <li><em>Keep source formatting (Ctrl+K)</em> - allows pasting the copied text keeping its original formatting.</li>
                <li><em>Keep text only (Ctrl+T)</em> - allows pasting the text without its original formatting.</li>
            </ul>
            <p>If you copy a table and paste it into an already existing table, the following options are available:</p>
            <ul>
                <li><em>Overwrite cells (Ctrl+O)</em> - allows replacing the contents of the existing table with the copied data. This option is selected by default.</li>
                <li><em>Nest table (Ctrl+N)</em> - allows pasting the copied table as a nested table into the selected cell of the existing table.</li>
                <li><em>Keep text only (Ctrl+T)</em> - allows pasting the table contents as text values separated by the tab character.</li>
            </ul>
            <p>To enable / disable the automatic appearance of the <b>Paste Special</b> button after pasting, go to the <b>File</b> tab > <b>Advanced Settings</b> and check / uncheck the <b>Show the Paste Options button when the content is pasted</b> checkbox.</p>
            <h3>Undo/redo your actions</h3>
            <p>To perform undo/redo operations, click the corresponding icons in the editor header or use the following keyboard shortcuts:</p>
            <ul>
                <li><b>Undo</b> – use the <b>Undo</b> <div class = "icon icon-undo"></div> icon on the left side of the editor header or the <b>Ctrl+Z</b> key combination to undo the last operation you performed.</li>
                <li><b>Redo</b> – use the <b>Redo</b> <div class = "icon icon-redo"></div> icon on the left part of the editor header or the <b>Ctrl+Y</b> key combination to redo the last undone operation.</li>
            </ul>
            <p class="note">
                When you co-edit a document in the <b>Fast</b> mode, the possibility to <b>Redo</b> the last undone operation is not available.
            </p>
		</div>
	</body>
</html>