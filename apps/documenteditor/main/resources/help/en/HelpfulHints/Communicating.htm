﻿<!DOCTYPE html>
<html>
<head>
    <title>Communicating in real time</title>
    <meta charset="utf-8" />
    <meta name="description" content="Tips on collaborative editing" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Communicating in real time</h1>
        <p>The <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows you to maintain constant team-wide approach to work flow: <a href="https://helpcenter.onlyoffice.com/userguides/groups-guides-documents-share-documents.aspx" onclick="onhyperlinkclick(this)">share</a> files and folders, <a href="../HelpfulHints/CollaborativeEditing.htm" onclick="onhyperlinkclick(this)">collaborate</a> on documents in real time, <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">comment</a> certain parts of your documents that require additional third-party input, save <a href="../HelpfulHints/VersionHistory.htm" onclick="onhyperlinkclick(this)">document versions</a> for future use, <a href="../HelpfulHints/Review.htm" onclick="onhyperlinkclick(this)">review</a> documents and add your changes without actually editing the file, <a href="../HelpfulHints/Comparison.htm" onclick="onhyperlinkclick(this)">compare and merge</a> documents to facilitate processing and editing.</p>
        <p>In <b>Document Editor</b> you can communicate with your co-editors in real time using the built-in <b>Chat</b> tool as well as a number of useful plugins, i.e. <a href="../UsageInstructions/CommunicationPlugins.htm" onclick="onhyperlinkclick(this)">Telegram or Rainbow</a>.</p>
        <p>To access the <b>Chat</b> tool and leave a message for other users,</p>
        <ol>
            <li>
                click the <div class = "icon icon-chaticon"></div> icon on the left sidebar, or <br />
                switch to the <b>Collaboration</b> tab of the top toolbar and click the <div class = "icon icon-chat_toptoolbar"></div> <b>Chat</b> button,
            </li>
            <li>enter your text into the corresponding field below,</li>
            <li>press the <b>Send</b> button.</li>
        </ol>
        <p class="note">The chat messages are stored during one session only. To discuss the document content, it is better to <a href="../HelpfulHints/Commenting.htm" onclick="onhyperlinkclick(this)">use comments</a> which are stored until they are deleted.</p>
        <p>All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - <span class = "icon icon-chaticon_new"></span>.</p>
        <p>To close the panel with chat messages, click the <span class = "icon icon-chaticon"></span> icon on the left sidebar or the <span class = "icon icon-chat_toptoolbar"></span> <b>Chat</b> button at the top toolbar once again.</p>
    </div>
</body>
</html>