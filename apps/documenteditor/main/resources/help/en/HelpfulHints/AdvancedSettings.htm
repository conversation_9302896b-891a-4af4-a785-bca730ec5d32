﻿<!DOCTYPE html>
<html>
<head>
    <title>Advanced Settings of the Document Editor</title>
    <meta charset="utf-8" />
    <meta name="description" content="The advanced settings of the Document Editor" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Advanced Settings of the Document Editor</h1>
        <p>The <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows you to change its advanced settings. To access them, open the <b>File</b> tab on the top toolbar and select the <b>Advanced Settings</b> option.</p>
        <p>The advanced settings are grouped as follows:</p>

        <h3>Editing and saving</h3>
        <ol>
            <li><span class="onlineDocumentFeatures"><b>Autosave</b> is used in the <em>online version</em> to turn on/off automatic saving of changes you make while editing.</span></li>
            <li><span class="desktopDocumentFeatures"><b>Autorecover</b> is used in the <em>desktop version</em> to turn on/off the option that allows automatically recovering documents in case the program closes unexpectedly.</span></li>
            <li><b>Show the Paste Options button when the content is pasted</b>. The corresponding icon will appear when you paste content in the document.</li>
            <li><b>Make the files compatible with older MS Word versions when saved as DOCX</b>. The files saved in DOCX format will become compatible with older Microsoft Word versions.</li>
        </ol>

        <h3>Collaboration</h3>
        <ol>
            <li class="onlineDocumentFeatures">
                The <b>Co-editing mode</b> subsection allows you to set the preferable mode for seeing changes made to the document when working in collaboration.
                <ul>
                    <li><b>Fast</b> (by default). The users who take part in the document co-editing will see the changes in real time once they are made by other users.</li>
                    <li><b>Strict</b>. All the changes made by co-editors will be shown only after you click the <b>Save</b> <div class="icon icon-saveupdate"></div> icon that will notify you about new changes.</li>
                </ul>
            </li>
            <li>
                The <b>Show track changes</b> subsection allows you to choose how new changes will be displayed.
                <ul>
                    <li><b>Show by click in balloons</b>. The change is shown in a balloon when you click the tracked change.</li>
                    <li><b>Show by hover in tooltips</b>. A tooltip appears when you hover the mouse pointer over the tracked change.</li>
                </ul>
            </li>
            <li class="onlineDocumentFeatures">
                The <b>Real-time Collaboration Changes</b> subsection allows you to choose how new changes and comments will be displayed in real time.
                <ul>
                    <li><b>View None</b>. All the changes made during the current session will not be highlighted.</li>
                    <li><b>View All</b>. All the changes made during the current session will be highlighted.</li>
                    <li><b>View Last</b>. Only the changes made since you last time clicked the <b>Save</b> <div class="icon icon-saveupdate"></div> icon will be highlighted. This option is only available when the <b>Strict</b> co-editing mode is selected.</li>
                    <li><b>Show changes from other users</b>. This feature allows to see changes made by other users in the document opened for viewing only in the <b>Live Viewer</b> mode.</li>
                    <li><b>Show comments in text</b>. If you disable this feature, the commented passages will be highlighted only if you click the <b>Comments</b> <div class="icon icon-commentsicon"></div> icon on the left sidebar.</li>
                    <li><b>Show resolved comments</b>. This feature is disabled by default so that the resolved comments are hidden in the document text. You can view such comments only if you click the <b>Comments</b> <div class="icon icon-commentsicon"></div> icon on the left sidebar. Enable this option if you want to display resolved comments in the document text.</li>
                </ul>
            </li>
        </ol>

        <h3>Proofing</h3>
        <ol>
            <li>The <b>Spell Checking</b> option is used to turn on/off the spell checking.</li>
            <li><b>Ignore words in UPPERCASE</b>. Words typed in capital letters are ignored during the spell checking.</li>
            <li><b>Ignore words with numbers</b>. Words with numbers in them are ignored during the spell checking.</li>
            <li>The <b>AutoCorrect options...</b> menu allows you to access the <a href="../UsageInstructions/MathAutoCorrect.htm" onclick="onhyperlinkclick(this)">autocorrect settings</a> such as replacing text as you type, recognizing functions, automatic formatting etc.</li>
        </ol>

        <h3>Workspace</h3>
        <ol>
            <li>The <b>Alignment Guides</b> option is used to turn on/off alignment guides that appear when you move objects. It allows for a more precise object positioning on the page.</li>
            <li>The <b>Hieroglyphs</b> option is used to turn on/off the display of hieroglyphs.</li>
            <li>The <b>Use Alt key to navigate the user interface using the keyboard</b> option is used to enable using the <em>Alt</em> / <em>Option</em> key in keyboard shortcuts.</li>
            <li>
                The <b>Interface theme</b> option is used to change the color scheme of the editor’s interface.
                <ul>
                    <li>The <b>Same as system</b> option makes the editor follow the interface theme of your system.</li>
                    <li>The <b>Light</b> color scheme incorporates standard blue, white, and light gray colors with less contrast in UI elements suitable for working during daytime.</li>
                    <li>The <b>Classic Light</b> color scheme incorporates standard blue, white, and light gray colors.</li>
                    <li>The <b>Dark</b> color scheme incorporates black, dark gray, and light gray colors suitable for working during nighttime.</li>
                    <li>The <b>Contrast Dark</b> color scheme incorporates black, dark gray, and white colors with more contrast in UI elements highlighting the working area of the file.</li>
                    <li>
                        The <b>Turn on document dark mode</b> option is used to make the working area darker when the editor is set to <b>Dark</b> or <b>Contrast Dark</b> interface theme. Check the <b>Turn on document dark mode</b> box to enable it.
                        <p class="note"><b>Note</b>: Apart from the available <b>Light</b>, <b>Classic Light</b>, <b>Dark</b>, and <b>Contrast Dark</b> interface themes, ONLYOFFICE editors can now be customized with your own color theme. Please follow <a target="_blank" href="https://helpcenter.onlyoffice.com/installation/docs-developer-change-theme.aspx" onclick="onhyperlinkclick(this)">these instructions</a> to learn how you can do that.</p>
                    </li>
                </ul>
            </li>
            <li>The <b>Unit of Measurement</b> option is used to specify what units are used on the rulers and in properties of objects when setting such parameters as width, height, spacing, margins etc. The available units are <em>Centimeter</em>, <em>Point</em>, and <em>Inch</em>.</li>
            <li>The <b>Default Zoom Value</b> option is used to set the default zoom value, selecting it in the list of available options from 50% to 500%. You can also choose the <em>Fit to Page</em> or <em>Fit to Width</em> option.</li>
            <li>
                The <b>Font Hinting</b> option is used to select how fonts are displayed in the Document Editor.
                <ul>
                    <li>Choose <b>As Windows</b> if you like the way fonts are usually displayed on Windows, i.e. using Windows font hinting.</li>
                    <li>Choose <b>As OS X</b> if you like the way fonts are usually displayed on a Mac, i.e. without any font hinting at all.</li>
                    <li>Choose <b>Native</b> if you want your text to be displayed with the hinting embedded into font files.</li>
                    <li>
                        <b>Default cache mode</b> - used to select the cache mode for the font characters. It’s not recommended to switch it without any reason. It can be helpful in some cases only, for example, when an issue in the Google Chrome browser with the enabled hardware acceleration occurs.
                        <p>The Document Editor has two cache modes:</p>
                        <ol>
                            <li>In the <b>first cache mode</b>, each letter is cached as a separate picture.</li>
                            <li>In the <b>second cache mode</b>, a picture of a certain size is selected where letters are placed dynamically and a mechanism of allocating/removing memory in this picture is also implemented. If there is not enough memory, a second picture is created, etc.</li>
                        </ol>
                        <p>The <b>Default cache mode</b> setting applies two above mentioned cache modes separately for different browsers:</p>
                        <ul>
                            <li>When the <b>Default cache mode</b> setting is enabled, Internet Explorer (v. 9, 10, 11) uses the <b>second cache mode</b>, other browsers use the <b>first cache mode</b>.</li>
                            <li>When the <b>Default cache mode</b> setting is disabled, Internet Explorer (v. 9, 10, 11) uses the <b>first cache mode</b>, other browsers use the <b>second cache mode</b>.</li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li>
                The <b>Macros Settings</b> option is used to set macros display with a notification.
                <ul>
                    <li>Choose <b>Disable All</b> to disable all macros within the document.</li>
                    <li>Choose <b>Show Notification</b> to receive notifications about macros within the document.</li>
                    <li>Choose <b>Enable All</b> to automatically run all macros within the document.</li>
                </ul>
            </li>
        </ol>
        <p>To save the changes you made, click the <b>Apply</b> button.</p>
    </div>
</body>
</html>