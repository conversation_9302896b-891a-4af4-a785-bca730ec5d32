﻿<!DOCTYPE html>
<html>
	<head>
		<title>File tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - File tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>File tab</h1>
            <p>The <b>File</b> tab of the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows performing some basic operations.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Document Editor:</p>
                <p><img alt="File tab" src="../images/interface/filetab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Document Editor:</p>
                <p><img alt="File tab" src="../images/interface/desktop_filetab.png" /></p>
            </div>            
            <p>With this tab, you can use the following options:</p>
            <ul>
                <li><span class="onlineDocumentFeatures">in the <em>online version</em>: <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">save</a> the current file (in case the <b>Autosave</b> option is disabled), save it in the required format on the hard disk drive of your computer with the <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)"><b>Download as</b></a> option, save a copy of the file in the selected format to the portal documents with the <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)"><b>Save copy as</b></a> option, <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">print</a> or <a href="../UsageInstructions/ViewDocInfo.htm" onclick="onhyperlinkclick(this)">rename</a> the current file.</span>
                    <span class="desktopDocumentFeatures">in the <em>desktop version</em>: <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">save</a> the current file without changing its format and location using the <b>Save</b> option, save it changing its name, location or format using the <b>Save as</b> option or <a href="../UsageInstructions/SavePrintDownload.htm" onclick="onhyperlinkclick(this)">print</a> the current file.</span>
                </li>
                <li>protect the file using a <a href="../HelpfulHints/Password.htm" onclick="onhyperlinkclick(this)">password</a>, change or remove the password;</li>
                <li class="desktopDocumentFeatures">protect the file using a digital signature (available in the <em>desktop version</em> only);</li>
                <li class="onlineDocumentFeatures"><a href="../UsageInstructions/OpenCreateNew.htm" onclick="onhyperlinkclick(this)">create</a> a new document or open a recently edited one (available in the <em>online version</em> only),</li>
                <li>view <a href="../UsageInstructions/ViewDocInfo.htm" onclick="onhyperlinkclick(this)">general information</a> about the document or change some file properties,</li>
                <li class="onlineDocumentFeatures">manage <a href="../UsageInstructions/ViewDocInfo.htm" onclick="onhyperlinkclick(this)">access rights</a> (available in the <em>online version</em> only),</li>
                <li class="onlineDocumentFeatures">track <a href="../UsageInstructions/ViewDocInfo.htm" onclick="onhyperlinkclick(this)">version history</a> (available in the <em>online version</em> only),</li>
                <li>access the <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Advanced Settings</a> of the editor,</li>
                <li><span class="desktopDocumentFeatures">in the <em>desktop version</em>, open the folder, where the file is stored, in the <b>File explorer</b> window.</span><span class="onlineDocumentFeatures"> In the <em>online version</em>, open the folder of the <b>Documents</b> module, where the file is stored, in a new browser tab.</span></li>
            </ul>
		</div>
	</body>
</html>