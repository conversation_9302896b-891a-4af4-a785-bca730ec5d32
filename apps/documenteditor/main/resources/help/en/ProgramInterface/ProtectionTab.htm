﻿<!DOCTYPE html>
<html>
	<head>
		<title>Protection tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - Protection tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Protection tab</h1>
            <p>The <b>Protection</b> tab of the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows protecting your documents with a password while setting restricted access rights.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Document Editor:</p>
                <p><img alt="Protection tab" src="../images/interface/protectiontab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Document Editor:</p>
                <p><img alt="Protection tab" src="../images/interface/desktop_protectiontab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li><a href="../HelpfulHints/Password.htm" onclick="onhyperlinkclick(this)">set a password for your document</a>,</li>
                <li>change passwords and delete them,</li>
                <li>set certain types of editing in the protected documents,</li>
                <li>remove document protection altogether.</li>
            </ul>
		</div>
	</body>
</html>