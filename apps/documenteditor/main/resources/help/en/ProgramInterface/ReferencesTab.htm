﻿<!DOCTYPE html>
<html>
	<head>
		<title>References tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - References tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>References tab</h1>
            <p>The <b>References</b> tab of the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows managing different types of references: adding and refreshing tables of contents, creating and editing footnotes, inserting hyperlinks.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Document Editor:</p>
                <p><img alt="References tab" src="../images/interface/referencestab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Document Editor:</p>
                <p><img alt="References tab" src="../images/interface/desktop_referencestab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li>create and automatically update a <a href="../UsageInstructions/CreateTableOfContents.htm" onclick="onhyperlinkclick(this)">table of contents</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertFootnotes.htm" onclick="onhyperlinkclick(this)">footnotes</a> and <a href="../UsageInstructions/InsertEndnotes.htm" onclick="onhyperlinkclick(this)">endnotes</a>,</li>
                <li>insert <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">hyperlinks</a>,</li>
                <li>add <a href="../UsageInstructions/InsertBookmarks.htm" onclick="onhyperlinkclick(this)">bookmarks</a>.</li>
                <li>add <a href="../UsageInstructions/Addcaption.htm" onclick="onhyperlinkclick(this)">captions</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertCrossReference.htm" onclick="onhyperlinkclick(this)">cross-references</a>,</li>
                <li>create a <a href="../UsageInstructions/AddTableofFigures.htm" onclick="onhyperlinkclick(this)">table of figures</a>.</li>
            </ul>
		</div>
	</body>
</html>