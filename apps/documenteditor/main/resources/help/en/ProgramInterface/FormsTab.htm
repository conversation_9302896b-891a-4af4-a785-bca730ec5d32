﻿<!DOCTYPE html>
<html>
<head>
    <title>Forms tab</title>
    <meta charset="utf-8" />
    <meta name="description" content="Introducing the Document Editor user interface - Forms tab" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Forms tab</h1>
        <p class="note"><b>Note</b>: this tab is available with DOCXF files only.</p>
        <p>The <b>Forms</b> tab allows you to create fillable forms such as agreements, applications or surveys. Add, format and configure text and form fields to draft a fillable form no matter how complex you need it to be.</p>
        <div class="onlineDocumentFeatures">
            <p>The corresponding window of the Online Document Editor:</p>
            <p><img alt="Forms tab" src="../images/interface/formstab.png" /></p>
         </div>
         <div class="desktopDocumentFeatures">
            <p>The corresponding window of the Desktop Document Editor:</p>
            <p><img alt="Forms tab" src="../images/interface/desktop_formstab.png" /></p>
        </div>
        <p>Using this tab, you can:</p>
        <ul>
            <li>
                <a href="../UsageInstructions/CreateFillableForms.htm">insert and edit</a>
                <ul>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#textfield">text fields</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#combobox">combo boxes</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#dropdownlist">drop-down lists</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#checkbox">checkboxes</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#radiobutton">radio buttons</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#image">images</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#emailaddress">e-mail addresses</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#phonenumber">phone numbers</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#datetime">date and time</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#zipcode">zip codes</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#creditcard">credit card numbers</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#complexfield">complex fields</a></li>
                </ul>
            <li>clear all fields and highlight settings,</li>
            <li>navigate through form fields using <b>Previous Field</b> and <b>Next Field</b> buttons,</li>
            <li>view the resulting forms in your document,</li>
            <li><a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">manage roles</a>,</li>
            <li>save form as a fillable OFORM file.</li>
        </ul>
    </div>
</body>
</html>