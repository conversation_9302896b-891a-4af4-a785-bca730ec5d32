<table cols="2">
    <tr>
        <td width="50%">
            <label class="header"><%= scope.textRows %></label>
        </td>
        <td width="50%">
            <label class="header"><%= scope.textColumns %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="table-checkbox-header"></div>
        </td>
        <td class="padding-small">
            <div id="table-checkbox-first"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="table-checkbox-total"></div>
        </td>
        <td class="padding-small">
            <div id="table-checkbox-last"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="table-checkbox-banded"></div>
        </td>
        <td class="padding-small">
            <div id="table-checkbox-col-banded"></div>
        </td>
    </tr>
    <tr>
        <td colspan=2>
            <label class="header"><%= scope.textTemplate %></label>
        </td>
    </tr>
    <tr>
        <td colspan=2 class="padding-small">
            <div class="" id="table-btn-template" style="width: 100%; height: 58px;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td colspan=2 class="padding-small">
            <label class="header"><%= scope.textBorders %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div id="table-combo-border-size" style="display: inline-block; vertical-align: middle; width: 93px;"></div>
            <div style="display: inline-block; float:right;vertical-align: middle;">
                <label class="input-label" style="margin-right: 5px;"><%= scope.textBorderColor %></label>
                <div id="table-border-color-btn" style="display: inline-block;"></div>
            </div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <label class="input-label"><%= scope.textSelectBorders %></label>
        </td>
    </tr>
    <tr>
        <td colspan=2 class="padding-small">
            <div id="table-button-border-all" style="display: inline-block;"></div>
            <div id="table-button-border-none" style="display: inline-block;"></div>
            <div id="table-button-border-inner" style="display: inline-block;"></div>
            <div id="table-button-border-outer" style="display: inline-block;"></div>
        </td>
    </tr>
    <tr>
        <td colspan=2 class="padding-small">
            <div id="table-button-border-left" style="display: inline-block;"></div>
            <div id="table-button-border-inner-vert" style="display: inline-block;"></div>
            <div id="table-button-border-right" style="display: inline-block;"></div>
            <div id="table-button-border-top" style="display: inline-block;"></div>
            <div id="table-button-border-inner-hor" style="display: inline-block;"></div>
            <div id="table-button-border-bottom" style="display: inline-block;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2 align="right">
            <label class="input-label" style="margin-right: 5px;"><%= scope.textBackColor %></label>
            <div id="table-back-color-btn" style="display: inline-block;text-align:left;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div id="table-btn-edit" style="width:100%;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td colspan=2>
            <label class="header"><%= scope.textCellSize %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <label style="margin-top: 3px;"><%= scope.textHeight %></label>
            <div id="table-btn-distrub-rows" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="table-spin-cell-height" style="display: inline-block; float:right;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <label style="margin-top: 3px;"><%= scope.textWidth %></label>
            <div id="table-btn-distrub-cols" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="table-spin-cell-width" style="display: inline-block; float:right;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <button type="button" class="btn btn-text-default" id="table-btn-add-formula" style="width:100%;" data-hint="1" data-hint-direction="bottom" data-hint-offset="big"><%= scope.textAddFormula %></button>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div id="table-checkbox-repeat-row"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div id="table-btn-convert-to-text" style="width:100%;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td align="center" colspan=2>
            <label class="link" id="table-advanced-link" data-hint="1" data-hint-direction="bottom" data-hint-offset="medium"><%= scope.textAdvanced %></label>
        </td>
    </tr>
    <tr class="finish-cell"></tr>
</table>