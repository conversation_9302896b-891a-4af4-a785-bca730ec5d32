
<div class="statusbar" style="display:table;">
    <div class="status-group dropup">
        <label id="label-pages" class="status-label dropdown-toggle margin-top-large" style="margin-left: 40px;" data-toggle="dropdown" data-hint="0" data-hint-direction="top"><%= textPageNumber %></label>
        <div id="status-goto-box" class="dropdown-menu">
            <label style="float:left;line-height:22px;"><%= textGotoPage %></label>
            <div id="status-goto-page" style="display:inline-block;"></div>
        </div>
    </div>
    <div class="separator short"></div>
    <div class="status-group">
        <div id="slot-status-btn-info" style="display: inline-block;" class="margin-top-small"></div>
    </div>
    <div class="status-group" style="width:100%; text-align:center;">
        <label id="label-action" class="status-label margin-top-large" data-layout-name="statusBar-actionStatus"></label>
    </div>
    <div class="status-group" style="">
        <div class="separator short el-edit"></div>
        <span id="btn-cnt-lang" class="el-edit margin-top-small" data-layout-name="statusBar-textLang"></span>
        <span id="btn-doc-lang" class="el-edit margin-top-small" data-layout-name="statusBar-docLang"></span>
        <div class="separator short el-edit space"></div>
        <span id="btn-doc-spell" class="el-edit margin-top-small"></span>
        <div class="separator short el-edit el-lang"></div>
        <div id="btn-doc-review" class="el-edit el-review margin-top-small" style="display: inline-block;"></div>
        <div class="separator short el-edit el-review"></div>
        <div class="separator short hide-select-tools"></div>
        <button id="btn-select-tool" type="button" class="btn small btn-toolbar hide-select-tools margin-top-small" data-hint="0" data-hint-direction="top" data-hint-offset="small"><span class="icon toolbar__icon btn-select-tool">&nbsp;</span></button>
        <button id="btn-hand-tool" type="button" class="btn small btn-toolbar hide-select-tools margin-top-small" data-hint="0" data-hint-direction="top" data-hint-offset="small"><span class="icon toolbar__icon btn-hand-tool">&nbsp;</span></button>
        <div class="separator short hide-select-tools"></div>
        <button id="btn-zoom-topage" type="button" class="btn small btn-toolbar margin-top-small" data-hint="0" data-hint-direction="top" data-hint-offset="small"><span class="icon toolbar__icon btn-ic-zoomtopage">&nbsp;</span></button>
        <button id="btn-zoom-towidth" type="button" class="btn small btn-toolbar margin-top-small" data-hint="0" data-hint-direction="top" data-hint-offset="small"><span class="icon toolbar__icon btn-ic-zoomtowidth">&nbsp;</span></button>
        <button id="btn-zoom-down" type="button" class="btn small btn-toolbar margin-top-small"><span class="icon toolbar__icon btn-zoomdown">&nbsp;</span></button>
        <div class="cnt-zoom">
            <div class="dropdown-toggle" data-toggle="dropdown" data-hint="0" data-hint-direction="top">
                <label id="label-zoom" class="status-label">Zoom 100%</label>
            </div>
        </div>
        <button id="btn-zoom-up" type="button" class="btn small btn-toolbar margin-top-small" style="margin-right:40px;"><span class="icon toolbar__icon btn-zoomup">&nbsp;</span></button>
    </div>
</div>
