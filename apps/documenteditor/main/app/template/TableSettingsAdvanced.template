<div id="id-adv-table-width" class="settings-panel active">
    <div class="inner-content">
        <table cols="3">
            <tr>
                <td colspan="3" class="padding-small">
                    <label class="header"><%= scope.textTableSize %></label>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: bottom; padding-bottom: 11px;">
                    <div id="tableadv-checkbox-width"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textWidth %></label>
                    <div id="tableadv-number-width" style="margin-right: 10px;"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textMeasure %></label>
                    <div id="tableadv-cmb-unit" style="width: 115px;"></div>
                </td>
            </tr>
            <tr>
                <td colspan="3" class="padding-large">
                    <div id="tableadv-checkbox-autofit"></div>
                </td>
            </tr>
        </table>
        <div class="padding-small"></div>
        <table cols="2" style="width: 100%;">
            <tr>
                <td colspan=2 class="padding-small">
                    <label class="header"><%= scope.textDefaultMargins %></label>
                </td>
            </tr>
            <tr>
                <td class="padding-small" width="125px">
                    <label class="input-label"><%= scope.textTop %></label>
                    <div id="tableadv-number-margin-table-top"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textLeft %></label>
                    <div id="tableadv-number-margin-table-left"></div>
                </td>
            </tr>
            <tr>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textBottom %></label>
                    <div id="tableadv-number-margin-table-bottom"></div>
                </td>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textRight %></label>
                    <div id="tableadv-number-margin-table-right"></div>
                </td>
            </tr>
        </table>
        <div class="padding-small"></div>
        <table cols="2">
            <tr>
                <td colspan="2" class="padding-small">
                    <label class="header"><%= scope.textOptions %></label>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: bottom; padding-bottom: 19px;">
                    <div id="tableadv-checkbox-spacing"></div>
                </td>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textAllowSpacing %></label>
                    <div id="tableadv-number-spacing"></div>
                </td>
            </tr>
        </table>
    </div>
    <div class="inner-content">
    </div>
</div>
<div id="id-adv-table-cell-props" class="settings-panel">
    <div class="inner-content">
        <table cols="3">
            <tr>
                <td colspan="3" class="padding-small">
                    <label class="header"><%= scope.textCellSize %></label>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: bottom; padding-bottom: 19px;">
                    <div id="tableadv-checkbox-prefwidth"></div>
                </td>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textPrefWidth %></label>
                    <div id="tableadv-number-prefwidth" style="margin-right: 10px;"></div>
                </td>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textMeasure %></label>
                    <div id="tableadv-combo-prefwidth-unit" style="width: 115px;"></div>
                </td>
            </tr>
        </table>
        <div class="padding-small"></div>
        <table cols="2" style="width: 100%;">
            <tr>
                <td colspan=2 class="padding-small">
                    <label class="header"><%= scope.textMargins %></label>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="padding-small">
                    <div id="tableadv-checkbox-margins"></div>
                </td>
            </tr>
            <tr>
                <td class="padding-small" width="125px">
                    <label class="input-label"><%= scope.textTop %></label>
                    <div id="tableadv-number-margin-top"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textLeft %></label>
                    <div id="tableadv-number-margin-left"></div>
                </td>
            </tr>
            <tr>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textBottom %></label>
                    <div id="tableadv-number-margin-bottom"></div>
                </td>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textRight %></label>
                    <div id="tableadv-number-margin-right"></div>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="padding-small"></td>
            </tr>
            <tr>
                <td colspan=2 class="padding-small">
                    <label class="header"><%= scope.textCellOptions %></label>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="padding-small">
                    <div id="tableadv-checkbox-wrap"></div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div id="id-adv-table-wrap" class="settings-panel">
    <div class="inner-content padding-large">
        <label class="header padding-small" style="display: block;"><%= scope.textWrappingStyle %></label>
        <div id="tableadv-button-wrap-none" style="display: inline-block; margin-right:5px;"></div>
        <div id="tableadv-button-wrap-parallel" style="display: inline-block; margin-right:5px;"></div>
    </div>
    <div class="padding-small"></div>
    <div class="inner-content">
        <div id="tableadv-panel-align" style="width: 100%;">
            <label class="header padding-small"><%= scope.textAlign %></label>
            <div class="padding-large">
                <div id="tableadv-button-align-left" style="display: inline-block; margin-right:5px;"></div>
                <div id="tableadv-button-align-center" style="display: inline-block; margin-right:5px;"></div>
                <div id="tableadv-button-align-right" style="display: inline-block; margin-right:5px;"></div>
                <div style="display: inline-block; vertical-align: bottom; margin-left: 20px; margin-bottom: 1px;">
                    <label class="input-label" style="display: block;"><%= scope.textIndLeft %></label>
                    <div id="tableadv-number-indent"></div>
                </div>
            </div>
        </div>
        <div id="tableadv-panel-distance" class="settings-hidden" style="width: 100%;">
            <table cols="2" style="width: 100%;">
                <tr>
                    <td colspan=2 class="padding-small">
                        <label class="header"><%= scope.textDistance %></label>
                    </td>
                </tr>
                <tr>
                    <td class="padding-small" width="125px">
                        <label class="input-label"><%= scope.textTop %></label>
                        <div id="tableadv-number-distance-top"></div>
                    </td>
                    <td class="padding-small">
                        <label class="input-label"><%= scope.textLeft %></label>
                        <div id="tableadv-number-distance-left"></div>
                    </td>
                </tr>
                <tr>
                    <td class="padding-small">
                        <label class="input-label"><%= scope.textBottom %></label>
                        <div id="tableadv-number-distance-bottom"></div>
                    </td>
                    <td class="padding-small">
                        <label class="input-label"><%= scope.textRight %></label>
                        <div id="tableadv-number-distance-right"></div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>
<div id="id-adv-table-borders" class="settings-panel">
    <div class="inner-content padding-large">
        <div style="width: 100%;">
            <label class="input-label"><%= scope.textBorderWidth %></label>
            <div id="tableadv-combo-border-size" style="display: inline-block; vertical-align: middle; width: 93px;"></div>
            <div style="display: inline-block; float:right;vertical-align: middle;">
                <label class="input-label" ><%= scope.textBorderColor %></label>
                <div id="tableadv-border-color-btn" style="display: inline-block;"></div>
            </div>
        </div>
    </div>
    <div class="inner-content">
        <label class="input-label padding-large" style="width: 100%;"><%= scope.textBorderDesc %></label>
        <div id="tableadv-panel-borders" style="width: 100%;" class="padding-large">
            <div id="id-detablestyler" style="display: inline-block; vertical-align: middle; width: 200px; height: 200px;outline: 1px solid #ccc;"></div>
            <div style="display: inline-block; vertical-align: middle; width: 76px; height: 200px; padding-top: 10px; padding-left: 10px;">
                <div id="tableadv-button-border-all"></div>
                <div id="tableadv-button-border-none"></div>
                <div id="tableadv-button-border-inner"></div>
                <div id="tableadv-button-border-outer"></div>
            </div>
        </div>
        <div id="tableadv-panel-borders-spacing" style="width: 100%;" class="padding-large settings-hidden">
            <div id="id-detablestyler-spacing" style="display: inline-block; vertical-align: middle; width: 200px; height: 200px;outline: 1px solid #ccc;"></div>
            <div style="display: inline-block; float:right;vertical-align: middle; width: 76px; height: 200px; padding-top: 10px;">
                <div id="tableadv-button-border-all-none" style="display: inline-block;"></div>
                <div id="tableadv-button-border-all-table" style="display: inline-block;"></div>
                <div id="tableadv-button-border-none-none" style="display: inline-block;"></div>
                <div id="tableadv-button-border-none-table" style="display: inline-block;"></div>
                <div id="tableadv-button-border-inner-none" style="display: inline-block;"></div>
                <div id="tableadv-button-border-inner-table" style="display: inline-block;"></div>
                <div id="tableadv-button-border-outer-none" style="display: inline-block;"></div>
                <div id="tableadv-button-border-outer-table" style="display: inline-block;"></div>
            </div>
        </div>
        <div style="width: 100%;">
            <div id="tableadv-panel-cell-back" style="display: inline-block;">
                <label class="input-label" style="margin-right: 5px;"><%= scope.textBackColor %></label>
                <div id="tableadv-button-back-color" style="display: inline-block;"></div>
            </div>
            <div id="tableadv-panel-table-back" style="display: inline-block;">
                <label class="input-label" style="margin-right: 5px;"><%= scope.textTableBackColor %></label>
                <div id="tableadv-button-table-back-color" style="display: inline-block;"></div>
            </div>
        </div>
    </div>
</div>
<div id="id-adv-table-position" class="settings-panel">
    <div class="inner-content">
        <table cols="3" style="width: 100%;">
            <tr>
                <td colspan="3" class="padding-small">
                    <label class="header"><%= scope.textHorizontal %></label>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: bottom; padding-bottom: 12px;">
                    <div id="tableadv-radio-halign"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textAlignment %></label>
                    <div id="tableadv-combo-halign" style="width: 115px;"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textRelative %></label>
                    <div id="tableadv-combo-hrelative" style="width: 115px;"></div>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: bottom; padding-bottom: 12px;">
                    <div id="tableadv-radio-hposition"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textPosition %></label>
                    <div id="tableadv-spin-x"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textRightOf %></label>
                    <div id="tableadv-combo-hposition" style="width: 115px;"></div>
                </td>
            </tr>
            <tr>
                <td colspan="3" class="padding-small">
                    <label class="header"><%= scope.textVertical %></label>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: bottom; padding-bottom: 12px;">
                    <div id="tableadv-radio-valign"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textAlignment %></label>
                    <div id="tableadv-combo-valign" style="width: 115px;"></div>
                </td>
                <td class="padding-small">
                    <label class="input-label"><%= scope.textRelative %></label>
                    <div id="tableadv-combo-vrelative" style="width: 115px;"></div>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: bottom; padding-bottom: 19px;">
                    <div id="tableadv-radio-vposition"></div>
                </td>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textPosition %></label>
                    <div id="tableadv-spin-y"></div>
                </td>
                <td class="padding-large">
                    <label class="input-label"><%= scope.textBelow %></label>
                    <div id="tableadv-combo-vposition" style="width: 115px;"></div>
                </td>
            </tr>
        </table>
    </div>
    <div class="separator horizontal padding-large"></div>
    <div class="inner-content">
        <table cols="2" style="width: 100%;">
            <tr>
                <td colspan="2" class="padding-small">
                    <label class="header"><%= scope.textOptions %></label>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="tableadv-checkbox-move"></div>
                </td>
                <td>
                    <div id="tableadv-checkbox-overlap"></div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div id="id-adv-table-alttext" class="settings-panel">
    <div class="inner-content">
        <table cols="1" width="100%">
            <tr>
                <td class="padding-large">
                    <label class="header"><%= scope.textAltTitle %></label>
                    <div id="table-advanced-alt-title"></div>
                </td>
            </tr>
            <tr>
                <td>
                    <label class="header"><%= scope.textAltDescription %></label>
                    <textarea id="table-advanced-alt-description" class="form-control" style="width: 100%; height: 120px;"></textarea>
                </td>
            </tr>
            <tr>
                <td>
                    <label><%= scope.textAltTip %></label>
                </td>
            </tr>
        </table>
    </div>
</div>