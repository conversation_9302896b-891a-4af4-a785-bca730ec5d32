<div id="id-adv-paragraph-indents" class="settings-panel active">
    <div class="inner-content">
            <div>
                <div class="padding-large" style="display: inline-block;">
                    <label class="input-label"><%= scope.textAlign %></label>
                    <div id="paragraphadv-spin-text-alignment"></div>
                </div>
                <div class="padding-large text-only" style="float: right;">
                    <label class="input-label"><%= scope.strIndentsOutlinelevel %></label>
                    <div id="paragraphadv-spin-outline-level"></div>
                </div>
            </div>
            <div><label class="header padding-very-small"><%= scope.strIndent %></label></div>
            <div>
                <div class="padding-large" style="display: inline-block;margin-right: 3px;">
                 <label class="input-label"><%= scope.strIndentsLeftText %></label>
                    <div id="paragraphadv-spin-indent-left"></div>
                </div><!--
             --><div class="padding-large" style="display: inline-block;margin-right: 3px;">
                    <label class="input-label"><%= scope.strIndentsRightText %></label>
                    <div id="paragraphadv-spin-indent-right"></div>
                </div><!--
             --><div class="padding-large" style="display: inline-block;vertical-align: top">
                    <div>
                        <label class="input-label"><%= scope.strIndentsSpecial %></label>
                    </div>
                <div>
                        <div id="paragraphadv-spin-special" style="display: inline-block; margin-right: 3px;"></div><!--
                     --><div id="paragraphadv-spin-special-by" style="display: inline-block;"></div>
                    </div>
                </div>
            </div>
            <div><label class="header padding-very-small"><%= scope.strSpacing %></label></div>
            <div>
                <div style="display: inline-block;margin-right: 3px;">
                    <label class="input-label"><%= scope.strIndentsSpacingBefore %></label>
                    <div id="paragraphadv-spin-spacing-before"></div>
                </div><!--
             --><div style="display: inline-block;margin-right: 3px;">
                    <label class="input-label"><%= scope.strIndentsSpacingAfter %></label>
                    <div id="paragraphadv-spin-spacing-after"></div>
                </div><!--
             --><div style="display: inline-block;vertical-align: top">
                    <div>
                        <label class="input-label"><%= scope.strIndentsLineSpacing %></label>
                    </div>
                    <div>
                        <div id="paragraphadv-spin-line-rule" style="display: inline-block;margin-right: 3px;"></div><!--
                     --><div id="paragraphadv-spin-line-height" style="display: inline-block;"></div>
                    </div>
                </div>
            </div>
            <div class="text-only" style="padding-top: 8px;">
                <div id="paragraphadv-checkbox-add-interval"></div>
            </div>
            <div class="padding-large" style="padding-top: 16px; display: none;">
                <div style="border: 1px solid #cbcbcb; width: 350px;">
                    <div id="paragraphadv-indent-preview" style="height: 58px; position: relative;"></div>
                </div>
            </div>
    </div>
</div>
<div id="id-adv-paragraph-line" class="settings-panel">
    <div class="inner-content text-only" style="padding-right: 0px;" >
        <table cols="2" style="width: 100%;">
            <tr>
                <td class="padding-small">
                    <div id="paragraphadv-checkbox-break-before"></div>
                </td>
                <td class="padding-small">
                    <div id="paragraphadv-checkbox-keep-lines"></div>
                </td>
            </tr>
            <tr>
                <td class="padding-small">
                    <div id="paragraphadv-checkbox-orphan"></div>
                </td>
                <td class="padding-small">
                    <div id="paragraphadv-checkbox-keep-next"></div>
                </td>
            </tr>
            <tr>
                <td class="padding-small" colspan="2">
                    <div id="paragraphadv-checkbox-suppress-line-numbers"></div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div id="id-adv-paragraph-borders" class="settings-panel">
    <div class="inner-content">
        <div style="width: 100%;" class="padding-small">
            <label class="input-label"><%= scope.textBorderWidth %></label>
            <div id="paragraphadv-combo-border-size" style="display: inline-block; vertical-align: middle; width: 93px;"></div>
            <div style="display: inline-block; vertical-align: middle; padding-left: 20px;">
                <label class="input-label" ><%= scope.textBorderColor %></label>
                <div id="paragraphadv-border-color-btn" style="display: inline-block;"></div>
            </div>
        </div>
        <label class="input-label padding-small" style="width: 100%;"><%= scope.textBorderDesc %></label>
        <div style="width: 100%;" class="padding-large">
            <div id="id-deparagraphstyler" style="display: inline-block; vertical-align: middle; width: 200px; height: 170px;outline: 1px solid #ccc; margin-top: 2px;"></div>
            <div style="display: inline-block; vertical-align: top; width: 76px; text-align: right; height: 170px; padding-top: 0px; margin-left: 9px;">
                <div id="paragraphadv-button-border-top" style="display: inline-block;"></div>
                <div id="paragraphadv-button-border-inner-hor" style="display: inline-block;"></div>
                <div id="paragraphadv-button-border-bottom" style="display: inline-block;"></div>
                <div id="paragraphadv-button-border-outer" style="display: inline-block;"></div>
                <div id="paragraphadv-button-border-left" style="display: inline-block;"></div>
                <div id="paragraphadv-button-border-all" style="display: inline-block;"></div>
                <div id="paragraphadv-button-border-right" style="display: inline-block;"></div>
                <div id="paragraphadv-button-border-none" style="display: inline-block;"></div>
            </div>
        </div>
        <div style="width: 100%;" class="padding-small">
            <label class="input-label" style="margin-right: 5px;"><%= scope.textBackColor %></label>
            <div id="paragraphadv-back-color-btn" style="display: inline-block;"></div>
        </div>
    </div>
</div>
<div id="id-adv-paragraph-font" class="settings-panel">
    <div class="inner-content" style="width: 100%;">
        <div class="padding-very-small">
            <label class="header"><%= scope.textEffects %></label>
        </div>
        <table cols="2">
            <tr>
                <td class="padding-small">
                    <div id="paragraphadv-checkbox-strike"></div>
                </td>
                <td class="padding-small" style="padding-left: 40px;">
                    <div id="paragraphadv-checkbox-subscript"></div>
                </td>
            </tr>
            <tr>
                <td class="padding-small">
                    <div id="paragraphadv-checkbox-double-strike"></div>
                </td>
                <td class="padding-small" style="padding-left: 40px;">
                    <div id="paragraphadv-checkbox-small-caps"></div>
                </td>
            </tr>
            <tr>
                <td class="padding-large">
                    <div id="paragraphadv-checkbox-superscript"></div>
                </td>
                <td class="padding-large" style="padding-left: 40px;">
                    <div id="paragraphadv-checkbox-all-caps"></div>
                </td>
            </tr>
        </table>
        <div class="padding-very-small">
            <label class="header"><%= scope.textCharacterSpacing %></label>
        </div>
        <div class="padding-large">
            <div style="display: inline-block;">
                <label class="input-label"><%= scope.textSpacing %></label>
                <div id="paragraphadv-spin-spacing"></div>
            </div>
            <div class="text-only" style="display: inline-block; margin-left: 15px;">
                <label class="input-label"><%= scope.textPosition %></label>
                <div id="paragraphadv-spin-position"></div>
            </div>
        </div>
        <div class="padding-very-small">
            <label class="header"><%= scope.textOpenType %></label>
        </div>
        <div class="padding-large">
            <label class="input-label"><%= scope.textLigatures %></label>
            <div id="paragraphadv-cmb-ligatures" style="display: inline-block; vertical-align: baseline; margin-left: 5px;"></div>
        </div>
        <div class="doc-content-color" style="outline: 1px solid #cbcbcb;">
            <div id="paragraphadv-font-img" style="width: 300px; height: 80px; position: relative; margin: 0 auto;"></div>
        </div>
    </div>
</div>
<div id="id-adv-paragraph-tabs" class="settings-panel">
    <div class="inner-content">
            <div class="padding-large">
                <label class="input-label"><%= scope.textDefault %></label>
                <div id="paraadv-spin-default-tab"></div>

            </div>
            <div>
                <div class="padding-large" style="display: inline-block; margin-right: 8px;">
                    <label class="input-label"><%= scope.textTabPosition %></label>
                    <div id="paraadv-spin-tab"></div>
                </div>
                <div class="padding-large" style=" display: inline-block; margin-right: 9px;">
                    <label class="input-label"><%= scope.textAlign %></label>
                    <div id="paraadv-cmb-align"></div>
                </div>
                <div class="padding-large text-only" style="display: inline-block;">
                    <label class="input-label"><%= scope.textLeader %></label>
                    <div id="paraadv-cmb-leader"></div>
                </div>
            </div>
            <div>
                <div colspan=3 class="padding-large">
                     <div id="paraadv-list-tabs" style="width:348px; height: 116px;"></div>
                </div>
            </div>
            <div>
                <button type="button" class="btn btn-text-default" id="paraadv-button-add-tab" style="width:108px;margin-right: 9px; display: inline-block;"><%= scope.textSet %></button>
                <button type="button" class="btn btn-text-default" id="paraadv-button-remove-tab" style="width:108px;margin-right: 9px; display: inline-block;"><%= scope.textRemove %></button>
                <button type="button" class="btn btn-text-default" id="paraadv-button-remove-all" style="width:108px;display: inline-block;"><%= scope.textRemoveAll %></button>
            </div>
    </div>
</div>
<div id="id-adv-paragraph-margins" class="settings-panel">
    <div class="inner-content">
            <div>
                <div class="padding-small" style="display: inline-block;">
                    <label class="input-label"><%= scope.textTop %></label>
                    <div id="paraadv-number-margin-top"></div>
                </div>
                <div class="padding-small" style="display: inline-block; padding-left: 15px;">
                    <label class="input-label"><%= scope.textLeft %></label>
                    <div id="paraadv-number-margin-left"></div>
                </div>
            </div>
            <div>
                <div class="padding-small" style="display: inline-block;">
                    <label class="input-label"><%= scope.textBottom %></label>
                    <div id="paraadv-number-margin-bottom"></div>
                </div>
                <div class="padding-small" style="display: inline-block; padding-left: 15px;">
                    <label class="input-label"><%= scope.textRight %></label>
                    <div id="paraadv-number-margin-right"></div>
                </div>
            </div>
    </div>
</div>