<table cols="2">
    <tr>
        <td colspan=2>
            <label class="header"><%= scope.textSize %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small" width="50%">
            <label id="chart-label-width" class="input-label"><%= scope.textWidth %></label>
        </td>
        <td class="padding-small" width="50%">
            <label id="chart-label-height" class="input-label"><%= scope.textHeight %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td colspan=2 class="padding-small">
            <label class="header"><%= scope.textWrap %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div id="chart-combo-wrap" style="width: 100%;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td colspan=2>
            <label class="header"><%= scope.textChartType %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div id="chart-button-type" style=""></div>
        </td>
    </tr>
    <tr class="not-combined">
        <td class="padding-small" colspan=2>
            <div class="" id="chart-combo-style" style="width: 100%;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small" colspan=2>
            <button type="button" class="btn btn-text-default auto" id="chart-button-edit-data" style="min-width:115px;" data-hint="1" data-hint-direction="bottom" data-hint-offset="big"><%= scope.textEditData %></button>
        </td>
    </tr>
</table>
<table cols="1" id="chart-panel-3d-rotate">
    <tr>
        <td class="padding-small">
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="header"><%= scope.text3dRotation %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="fixed" style="margin-top: 3px;width: 88px;"><%= scope.textX %></label>
            <div id="chart-btn-x-right" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="chart-btn-x-left" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="chart-spin-x" style="display: inline-block; float:right;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="fixed" style="margin-top: 3px;width: 88px;"><%= scope.textY %></label>
            <div id="chart-btn-y-down" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="chart-btn-y-up" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="chart-spin-y" style="display: inline-block; float:right;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="fixed" style="margin-top: 3px;width: 88px;"><%= scope.textPerspective %></label>
            <div id="chart-btn-widen" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="chart-btn-narrow" style="display: inline-block; float:right; margin-left: 4px;"></div>
            <div id="chart-spin-persp" style="display: inline-block; float:right;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="chart-checkbox-right-angle"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="chart-checkbox-autoscale"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="fixed" style="margin-top: 3px;width: 122px;"><%= scope.text3dDepth %></label>
            <div id="chart-spin-3d-depth" style="display: inline-block; float:right;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="fixed" style="margin-top: 3px;width: 122px;"><%= scope.text3dHeight %></label>
            <div id="chart-spin-3d-height" style="display: inline-block; float:right;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="link" id="chart-def-rotate-link" data-hint="1" data-hint-direction="bottom" data-hint-offset="medium"><%= scope.textDefault %></label>
        </td>
    </tr>
</table>
<table cols="2">
    <tr>
        <td class="padding-small" colspan=2>
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td align="center" colspan=2>
            <label class="link" id="chart-advanced-link" data-hint="1" data-hint-direction="bottom" data-hint-offset="medium"><%= scope.textAdvanced %></label>
        </td>
    </tr>
    <tr class="finish-cell"></tr>
</table>