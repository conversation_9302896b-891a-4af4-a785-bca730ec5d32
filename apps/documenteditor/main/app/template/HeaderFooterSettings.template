<table cols="1">
    <tr>
        <td>
            <label class="header"><%= scope.textPosition %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label id="headerfooter-label-position" class="input-label"><%= scope.textHeaderFromTop %></label>
            <div id="headerfooter-spin-position"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="header"><%= scope.textOptions %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="headerfooter-check-diff-first"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="headerfooter-check-diff-odd"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="headerfooter-check-same-as"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="header"><%= scope.textPageNum %></label>
        </td>
    </tr>
    <tr>
        <td>
            <label><%= scope.textTopPage %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="headerfooter-button-top-left" style="display: inline-block; margin-right:5px;"></div>
            <div id="headerfooter-button-top-center" style="display: inline-block; margin-right:5px;"></div>
            <div id="headerfooter-button-top-right" style="display: inline-block; margin-right:5px;"></div>
        </td>
    </tr>
    <tr>
        <td>
            <label><%= scope.textBottomPage %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="headerfooter-button-bottom-left" style="display: inline-block; margin-right:5px;"></div>
            <div id="headerfooter-button-bottom-center" style="display: inline-block; margin-right:5px;"></div>
            <div id="headerfooter-button-bottom-right" style="display: inline-block; margin-right:5px;"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <button type="button" class="btn btn-text-default" id="headerfooter-button-current" style="width:100%;" data-hint="1" data-hint-direction="bottom" data-hint-offset="big"><%= scope.textInsertCurrent %></button>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div class="separator horizontal"></div>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <label class="header"><%= scope.textPageNumbering %></label>
        </td>
    </tr>
    <tr>
        <td class="padding-small">
            <div id="headerfooter-radio-prev"  style="margin-bottom: 5px;"></div>
            <div id="headerfooter-radio-from" style="display: inline-block;vertical-align: middle; margin-right: 2px;"></div>
            <div id="headerfooter-spin-from" style="display: inline-block;vertical-align: middle;"></div>
        </td>
    </tr>
    <tr class="finish-cell"></tr>
</table>