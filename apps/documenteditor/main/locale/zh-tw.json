{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Chat.textEnterMessage": "在這裡輸入您的信息", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名", "Common.Controllers.ExternalDiagramEditor.textClose": "關閉", "Common.Controllers.ExternalDiagramEditor.warningText": "該物件被禁用，因為它正在由另一個帳戶編輯。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalMergeEditor.textAnonymous": "匿名", "Common.Controllers.ExternalMergeEditor.textClose": "關閉", "Common.Controllers.ExternalMergeEditor.warningText": "該物件被禁用，因為它正在由另一個帳戶編輯。", "Common.Controllers.ExternalMergeEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名", "Common.Controllers.ExternalOleEditor.textClose": "關閉", "Common.Controllers.ExternalOleEditor.warningText": "該物件被禁用，因為它正在由另一個帳戶編輯。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "為了比較文檔，所有被跟蹤的更改都將被視為已接受。你想繼續嗎？", "Common.Controllers.ReviewChanges.textAtLeast": "至少", "Common.Controllers.ReviewChanges.textAuto": "自動", "Common.Controllers.ReviewChanges.textBaseline": "基準線", "Common.Controllers.ReviewChanges.textBold": "粗體", "Common.Controllers.ReviewChanges.textBreakBefore": "分頁之前", "Common.Controllers.ReviewChanges.textCaps": "全部大寫", "Common.Controllers.ReviewChanges.textCenter": "居中對齊", "Common.Controllers.ReviewChanges.textChar": "文字水平", "Common.Controllers.ReviewChanges.textChart": "圖表", "Common.Controllers.ReviewChanges.textColor": "字體顏色", "Common.Controllers.ReviewChanges.textContextual": "不要在相同風格的文字段落之間添加間隔", "Common.Controllers.ReviewChanges.textDeleted": "<b>已刪除</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "雙刪除線", "Common.Controllers.ReviewChanges.textEquation": "方程式", "Common.Controllers.ReviewChanges.textExact": "準確", "Common.Controllers.ReviewChanges.textFirstLine": "第一行", "Common.Controllers.ReviewChanges.textFontSize": "字體大小", "Common.Controllers.ReviewChanges.textFormatted": "已格式化", "Common.Controllers.ReviewChanges.textHighlight": "熒光色選", "Common.Controllers.ReviewChanges.textImage": "圖像", "Common.Controllers.ReviewChanges.textIndentLeft": "向左內縮", "Common.Controllers.ReviewChanges.textIndentRight": "向右內縮", "Common.Controllers.ReviewChanges.textInserted": "<b>已插入：</b>", "Common.Controllers.ReviewChanges.textItalic": "斜體", "Common.Controllers.ReviewChanges.textJustify": "對齊", "Common.Controllers.ReviewChanges.textKeepLines": "保持線條一致", "Common.Controllers.ReviewChanges.textKeepNext": "跟著下一個", "Common.Controllers.ReviewChanges.textLeft": "對齊左側", "Common.Controllers.ReviewChanges.textLineSpacing": "行間距: ", "Common.Controllers.ReviewChanges.textMultiple": "多項", "Common.Controllers.ReviewChanges.textNoBreakBefore": "之前沒有分頁符", "Common.Controllers.ReviewChanges.textNoContextual": "在相同風格的段落之間加入間隔", "Common.Controllers.ReviewChanges.textNoKeepLines": "不要保持多個線條在一起", "Common.Controllers.ReviewChanges.textNoKeepNext": "不要與文字保持在一起", "Common.Controllers.ReviewChanges.textNot": "不", "Common.Controllers.ReviewChanges.textNoWidow": "無窗口控制", "Common.Controllers.ReviewChanges.textNum": "變更編號", "Common.Controllers.ReviewChanges.textOff": "{0} 已沒有再使用Track.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} 關閉了所有用戶的Track Changes.", "Common.Controllers.ReviewChanges.textOn": "{0} 已開始使用Track.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} 開啟了所有帳戶的Track Changes.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>段落已刪除</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "段落格式", "Common.Controllers.ReviewChanges.textParaInserted": "<b>段落已插入</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>已下移:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>已上移:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>已移動:<b>", "Common.Controllers.ReviewChanges.textPosition": "職務", "Common.Controllers.ReviewChanges.textRight": "對齊右側", "Common.Controllers.ReviewChanges.textShape": "形狀", "Common.Controllers.ReviewChanges.textShd": "背景顏色", "Common.Controllers.ReviewChanges.textShow": "顯示更改", "Common.Controllers.ReviewChanges.textSmallCaps": "小大寫", "Common.Controllers.ReviewChanges.textSpacing": "間距", "Common.Controllers.ReviewChanges.textSpacingAfter": "間隔後", "Common.Controllers.ReviewChanges.textSpacingBefore": "前間距", "Common.Controllers.ReviewChanges.textStrikeout": "刪除線", "Common.Controllers.ReviewChanges.textSubScript": "下標", "Common.Controllers.ReviewChanges.textSuperScript": "上標", "Common.Controllers.ReviewChanges.textTableChanged": "<b>表格設定已刪除</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>已添加表格行</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>已刪除表格行</b>", "Common.Controllers.ReviewChanges.textTabs": "變更定位", "Common.Controllers.ReviewChanges.textTitleComparison": "比較設定", "Common.Controllers.ReviewChanges.textUnderline": "底線", "Common.Controllers.ReviewChanges.textUrl": "粘貼文檔網址", "Common.Controllers.ReviewChanges.textWidow": "遺留文字控制", "Common.Controllers.ReviewChanges.textWord": "字級", "Common.define.chartData.textArea": "區域", "Common.define.chartData.textAreaStacked": "堆叠面積", "Common.define.chartData.textAreaStackedPer": "100% 堆疊面積圖", "Common.define.chartData.textBar": "槓", "Common.define.chartData.textBarNormal": "劇集柱形", "Common.define.chartData.textBarNormal3d": "3-D 簇狀直式長條圖", "Common.define.chartData.textBarNormal3dPerspective": "3-D 直式長條圖", "Common.define.chartData.textBarStacked": "堆叠柱形", "Common.define.chartData.textBarStacked3d": "3-D 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer": "100% 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer3d": "3-D 100% 堆疊直式長條圖", "Common.define.chartData.textCharts": "圖表", "Common.define.chartData.textColumn": "欄", "Common.define.chartData.textCombo": "組合", "Common.define.chartData.textComboAreaBar": "堆叠面積 - 劇集柱形", "Common.define.chartData.textComboBarLine": "劇集柱形 - 綫", "Common.define.chartData.textComboBarLineSecondary": "劇集柱形 - 副軸綫", "Common.define.chartData.textComboCustom": "自訂組合", "Common.define.chartData.textDoughnut": "甜甜圈圖", "Common.define.chartData.textHBarNormal": "劇集條形", "Common.define.chartData.textHBarNormal3d": "3-D 簇狀橫式長條圖", "Common.define.chartData.textHBarStacked": "堆叠條形", "Common.define.chartData.textHBarStacked3d": "3-D 堆疊橫式長條圖", "Common.define.chartData.textHBarStackedPer": "100% 堆疊橫式長條圖", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% 堆疊橫式長條圖", "Common.define.chartData.textLine": "線", "Common.define.chartData.textLine3d": "3-D 直線圖", "Common.define.chartData.textLineMarker": "直線加標記", "Common.define.chartData.textLineStacked": "堆叠綫", "Common.define.chartData.textLineStackedMarker": "堆積綫及標記", "Common.define.chartData.textLineStackedPer": "100% 堆疊直線圖", "Common.define.chartData.textLineStackedPerMarker": "100% 堆疊直線圖加標記", "Common.define.chartData.textPie": "餅", "Common.define.chartData.textPie3d": "3-D 圓餅圖", "Common.define.chartData.textPoint": "XY（散點圖）", "Common.define.chartData.textScatter": "散佈圖", "Common.define.chartData.textScatterLine": "散佈圖同直線", "Common.define.chartData.textScatterLineMarker": "散佈圖同直線及標記", "Common.define.chartData.textScatterSmooth": "散佈圖同平滑線", "Common.define.chartData.textScatterSmoothMarker": "散佈圖同平滑線及標記", "Common.define.chartData.textStock": "庫存", "Common.define.chartData.textSurface": "表面", "Common.Translation.textMoreButton": "更多", "Common.Translation.warnFileLocked": "該文件正在另一個應用程式中進行編輯。您可以繼續編輯並將其另存為副本。", "Common.Translation.warnFileLockedBtnEdit": "\n建立副本", "Common.Translation.warnFileLockedBtnView": "打開查看", "Common.UI.ButtonColored.textAutoColor": "自動", "Common.UI.ButtonColored.textNewColor": "新增自訂顏色", "Common.UI.Calendar.textApril": "四月", "Common.UI.Calendar.textAugust": "八月", "Common.UI.Calendar.textDecember": "十二月", "Common.UI.Calendar.textFebruary": "二月", "Common.UI.Calendar.textJanuary": "一月", "Common.UI.Calendar.textJuly": "七月", "Common.UI.Calendar.textJune": "六月", "Common.UI.Calendar.textMarch": "三月", "Common.UI.Calendar.textMay": "五月", "Common.UI.Calendar.textMonths": "月", "Common.UI.Calendar.textNovember": "十一月", "Common.UI.Calendar.textOctober": "八邊形", "Common.UI.Calendar.textSeptember": "九月", "Common.UI.Calendar.textShortApril": "Apr", "Common.UI.Calendar.textShortAugust": "八月", "Common.UI.Calendar.textShortDecember": "十二月", "Common.UI.Calendar.textShortFebruary": "二月", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "一月", "Common.UI.Calendar.textShortJuly": "七月", "Common.UI.Calendar.textShortJune": "六月", "Common.UI.Calendar.textShortMarch": "三月", "Common.UI.Calendar.textShortMay": "五月", "Common.UI.Calendar.textShortMonday": "Mo", "Common.UI.Calendar.textShortNovember": "十一月", "Common.UI.Calendar.textShortOctober": "十月", "Common.UI.Calendar.textShortSaturday": "Sa", "Common.UI.Calendar.textShortSeptember": "九月", "Common.UI.Calendar.textShortSunday": "Su", "Common.UI.Calendar.textShortThursday": "th", "Common.UI.Calendar.textShortTuesday": "Tu", "Common.UI.Calendar.textShortWednesday": "我們", "Common.UI.Calendar.textYears": "年", "Common.UI.ComboBorderSize.txtNoBorders": "無邊框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "無邊框", "Common.UI.ComboDataView.emptyComboText": "無風格", "Common.UI.ExtendedColorDialog.addButtonText": "新增", "Common.UI.ExtendedColorDialog.textCurrent": "當前", "Common.UI.ExtendedColorDialog.textHexErr": "輸入的值不正確。<br>請輸入一個介於000000和FFFFFF之間的值。", "Common.UI.ExtendedColorDialog.textNew": "新", "Common.UI.ExtendedColorDialog.textRGBErr": "輸入的值不正確。<br>請輸入0到255之間的數字。", "Common.UI.HSBColorPicker.textNoColor": "無顏色", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "不顯示密碼", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "顯示密碼", "Common.UI.SearchBar.textFind": "尋找", "Common.UI.SearchBar.tipCloseSearch": "關閉搜索", "Common.UI.SearchBar.tipNextResult": "下一個結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "開啟進階設置", "Common.UI.SearchBar.tipPreviousResult": "上一個結果", "Common.UI.SearchDialog.textHighlight": "強調結果", "Common.UI.SearchDialog.textMatchCase": "區分大小寫", "Common.UI.SearchDialog.textReplaceDef": "輸入替換文字", "Common.UI.SearchDialog.textSearchStart": "在這裡輸入您的文字", "Common.UI.SearchDialog.textTitle": "尋找與取代", "Common.UI.SearchDialog.textTitle2": "尋找", "Common.UI.SearchDialog.textWholeWords": "僅全字", "Common.UI.SearchDialog.txtBtnHideReplace": "隱藏替換", "Common.UI.SearchDialog.txtBtnReplace": "取代", "Common.UI.SearchDialog.txtBtnReplaceAll": "取代全部", "Common.UI.SynchronizeTip.textDontShow": "不再顯示此消息", "Common.UI.SynchronizeTip.textSynchronize": "該文檔已被其他帳戶更改。<br>請單擊以儲存更改並重新加載更新。", "Common.UI.ThemeColorPalette.textRecentColors": "近期顏色", "Common.UI.ThemeColorPalette.textStandartColors": "標準顏色", "Common.UI.ThemeColorPalette.textThemeColors": "主題顏色", "Common.UI.Themes.txtThemeClassicLight": "傳統亮色", "Common.UI.Themes.txtThemeContrastDark": "暗色對比", "Common.UI.Themes.txtThemeDark": "暗", "Common.UI.Themes.txtThemeLight": "光亮色系", "Common.UI.Themes.txtThemeSystem": "和系統一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "關閉", "Common.UI.Window.noButtonText": "沒有", "Common.UI.Window.okButtonText": "確定", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "不再顯示此消息", "Common.UI.Window.textError": "錯誤", "Common.UI.Window.textInformation": "資訊", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "公分", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "地址:", "Common.Views.About.txtLicensee": "被許可人", "Common.Views.About.txtLicensor": "許可人", "Common.Views.About.txtMail": "電子郵件：", "Common.Views.About.txtPoweredBy": "於支援", "Common.Views.About.txtTel": "電話: ", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "新增", "Common.Views.AutoCorrectDialog.textApplyText": "鍵入時同時申請", "Common.Views.AutoCorrectDialog.textAutoCorrect": "自動更正", "Common.Views.AutoCorrectDialog.textAutoFormat": "鍵入時自動調整規格", "Common.Views.AutoCorrectDialog.textBulleted": "自動項目符號列表", "Common.Views.AutoCorrectDialog.textBy": "依照", "Common.Views.AutoCorrectDialog.textDelete": "刪除", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "按兩下空白鍵自動增加一個句點(.)符號", "Common.Views.AutoCorrectDialog.textFLCells": "儲存格首字母大寫", "Common.Views.AutoCorrectDialog.textFLSentence": "英文句子第一個字母大寫", "Common.Views.AutoCorrectDialog.textHyperlink": "網絡路徑超連結", "Common.Views.AutoCorrectDialog.textHyphens": "帶連字符（-）的連字符（-）", "Common.Views.AutoCorrectDialog.textMathCorrect": "數學自動更正", "Common.Views.AutoCorrectDialog.textNumbered": "自動編號列表", "Common.Views.AutoCorrectDialog.textQuotes": "\"直引號\"與\"智能引號\"", "Common.Views.AutoCorrectDialog.textRecognized": "公認的功能", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表達式是公認的數學表達式。它們不會自動斜體顯示。", "Common.Views.AutoCorrectDialog.textReplace": "取代", "Common.Views.AutoCorrectDialog.textReplaceText": "鍵入時替換", "Common.Views.AutoCorrectDialog.textReplaceType": "鍵入時替換文字", "Common.Views.AutoCorrectDialog.textReset": "重設", "Common.Views.AutoCorrectDialog.textResetAll": "重置為預設", "Common.Views.AutoCorrectDialog.textRestore": "恢復", "Common.Views.AutoCorrectDialog.textTitle": "自動更正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "公認的函數只能包含字母A到Z，大寫或小寫。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "您新增的所有表達式都將被刪除，被刪除的表達式將被恢復。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnReplace": "％1的自動更正條目已存在。您要更換嗎？", "Common.Views.AutoCorrectDialog.warnReset": "您增加的所有自動更正功能將被刪除，更改後的自動更正將恢復為其原始值。你想繼續嗎？", "Common.Views.AutoCorrectDialog.warnRestore": "％1的自動更正條目將被重置為其原始值。你想繼續嗎？", "Common.Views.Chat.textSend": "傳送", "Common.Views.Comments.mniAuthorAsc": "作者排行A到Z", "Common.Views.Comments.mniAuthorDesc": "作者排行Z到A", "Common.Views.Comments.mniDateAsc": "從最老的", "Common.Views.Comments.mniDateDesc": "從最新的", "Common.Views.Comments.mniFilterGroups": "依群組篩選", "Common.Views.Comments.mniPositionAsc": "從上到下", "Common.Views.Comments.mniPositionDesc": "自下而上", "Common.Views.Comments.textAdd": "新增", "Common.Views.Comments.textAddComment": "發表註解", "Common.Views.Comments.textAddCommentToDoc": "在文檔中新增註解", "Common.Views.Comments.textAddReply": "加入回應", "Common.Views.Comments.textAll": "全部", "Common.Views.Comments.textAnonym": "來賓帳戶", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "關閉", "Common.Views.Comments.textClosePanel": "關閉註解", "Common.Views.Comments.textComments": "註解", "Common.Views.Comments.textEdit": "確定", "Common.Views.Comments.textEnterCommentHint": "在這裡輸入您的註解", "Common.Views.Comments.textHintAddComment": "新增註解", "Common.Views.Comments.textOpenAgain": "重新打開", "Common.Views.Comments.textReply": "回覆", "Common.Views.Comments.textResolve": "解決", "Common.Views.Comments.textResolved": "已解決", "Common.Views.Comments.textSort": "註解分類", "Common.Views.Comments.textViewResolved": "你沒有權限來重新開啟這個註解", "Common.Views.Comments.txtEmpty": "文件裡沒有註解。", "Common.Views.CopyWarningDialog.textDontShow": "不再顯示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用編輯器工具欄按鈕進行[複制]，[剪下]和[貼上]的操作以及內文選單操作僅能在此編輯器中執行。<br> <br>要在“編輯器”之外的應用程式之間進行[複製]或[貼上]，請使用以下鍵盤組合：", "Common.Views.CopyWarningDialog.textTitle": "複製, 剪下, 與貼上之動作", "Common.Views.CopyWarningDialog.textToCopy": "複印", "Common.Views.CopyWarningDialog.textToCut": "切", "Common.Views.CopyWarningDialog.textToPaste": "粘貼", "Common.Views.DocumentAccessDialog.textLoading": "載入中...", "Common.Views.DocumentAccessDialog.textTitle": "分享設定", "Common.Views.ExternalDiagramEditor.textTitle": "圖表編輯器", "Common.Views.ExternalMergeEditor.textTitle": "郵件合併收件人", "Common.Views.ExternalOleEditor.textTitle": "試算表編輯器", "Common.Views.Header.labelCoUsersDescr": "正在編輯文件的帳戶：", "Common.Views.Header.textAddFavorite": "標記為最愛收藏", "Common.Views.Header.textAdvSettings": "進階設定", "Common.Views.Header.textBack": "打開文件所在位置", "Common.Views.Header.textCompactView": "隱藏工具欄", "Common.Views.Header.textHideLines": "隱藏標尺", "Common.Views.Header.textHideStatusBar": "隱藏狀態欄", "Common.Views.Header.textRemoveFavorite": "\n從最愛收藏夾中刪除", "Common.Views.Header.textShare": "分享", "Common.Views.Header.textZoom": "放大", "Common.Views.Header.tipAccessRights": "管理文檔存取權限", "Common.Views.Header.tipDownload": "下載文件", "Common.Views.Header.tipGoEdit": "編輯當前文件", "Common.Views.Header.tipPrint": "列印文件", "Common.Views.Header.tipRedo": "重複", "Common.Views.Header.tipSave": "存檔", "Common.Views.Header.tipSearch": "搜尋", "Common.Views.Header.tipUndo": "復原", "Common.Views.Header.tipUsers": "查看用戶", "Common.Views.Header.tipViewSettings": "查看設定", "Common.Views.Header.tipViewUsers": "查看帳戶並管理文檔存取權限", "Common.Views.Header.txtAccessRights": "變更存取權限", "Common.Views.Header.txtRename": "重新命名", "Common.Views.History.textCloseHistory": "關閉歷史紀錄", "Common.Views.History.textHide": "縮小", "Common.Views.History.textHideAll": "隱藏詳細的更改", "Common.Views.History.textRestore": "恢復", "Common.Views.History.textShow": "擴大", "Common.Views.History.textShowAll": "顯示詳細的更改", "Common.Views.History.textVer": "版本", "Common.Views.ImageFromUrlDialog.textUrl": "粘貼圖片網址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "這是必填欄", "Common.Views.ImageFromUrlDialog.txtNotUrl": "此段落應為“ http://www.example.com”格式的網址", "Common.Views.InsertTableDialog.textInvalidRowsCols": "您需要指定有效的行數和列數。", "Common.Views.InsertTableDialog.txtColumns": "列數", "Common.Views.InsertTableDialog.txtMaxText": "此段落的最大值為{0}。", "Common.Views.InsertTableDialog.txtMinText": "此段落的最小值為{0}。", "Common.Views.InsertTableDialog.txtRows": "行數", "Common.Views.InsertTableDialog.txtTitle": "表格大小", "Common.Views.InsertTableDialog.txtTitleSplit": "分割儲存格", "Common.Views.LanguageDialog.labelSelect": "選擇文件語言", "Common.Views.OpenDialog.closeButtonText": "關閉檔案", "Common.Views.OpenDialog.txtEncoding": "編碼", "Common.Views.OpenDialog.txtIncorrectPwd": "密碼錯誤。", "Common.Views.OpenDialog.txtOpenFile": "輸入檔案密碼", "Common.Views.OpenDialog.txtPassword": "密碼", "Common.Views.OpenDialog.txtPreview": "預覽", "Common.Views.OpenDialog.txtProtected": "輸入密碼並打開文件後，該文件的當前密碼將被重置。", "Common.Views.OpenDialog.txtTitle": "選擇％1個選項", "Common.Views.OpenDialog.txtTitleProtected": "受保護的檔案", "Common.Views.PasswordDialog.txtDescription": "設置密碼以保護此文檔", "Common.Views.PasswordDialog.txtIncorrectPwd": "確認密碼不相同", "Common.Views.PasswordDialog.txtPassword": "密碼", "Common.Views.PasswordDialog.txtRepeat": "重複輸入密碼", "Common.Views.PasswordDialog.txtTitle": "設置密碼", "Common.Views.PasswordDialog.txtWarning": "警告：如果失去密碼，將無法取回。請妥善保存。", "Common.Views.PluginDlg.textLoading": "載入中", "Common.Views.Plugins.groupCaption": "外掛程式", "Common.Views.Plugins.strPlugins": "外掛程式", "Common.Views.Plugins.textClosePanel": "關閉插件", "Common.Views.Plugins.textLoading": "載入中", "Common.Views.Plugins.textStart": "開始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Protection.hintAddPwd": "用密碼加密", "Common.Views.Protection.hintPwd": "變更或刪除密碼", "Common.Views.Protection.hintSignature": "新增數字簽名或簽名行", "Common.Views.Protection.txtAddPwd": "新增密碼", "Common.Views.Protection.txtChangePwd": "變更密碼", "Common.Views.Protection.txtDeletePwd": "刪除密碼", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "新增數字簽名", "Common.Views.Protection.txtSignature": "簽名", "Common.Views.Protection.txtSignatureLine": "新增簽名行", "Common.Views.RenameDialog.textName": "檔案名稱", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "到下一個變化", "Common.Views.ReviewChanges.hintPrev": "到以前的變化", "Common.Views.ReviewChanges.mniFromFile": "檔案裡的文件", "Common.Views.ReviewChanges.mniFromStorage": "儲存巢裡的文件", "Common.Views.ReviewChanges.mniFromUrl": "來自URL裡文件", "Common.Views.ReviewChanges.mniSettings": "比較設定", "Common.Views.ReviewChanges.strFast": "快", "Common.Views.ReviewChanges.strFastDesc": "即時共同編輯。所有更改將自動保存。", "Common.Views.ReviewChanges.strStrict": "嚴格", "Common.Views.ReviewChanges.strStrictDesc": "使用“存檔”按鈕同步您和其他人所做的更改。", "Common.Views.ReviewChanges.textEnable": "啟用", "Common.Views.ReviewChanges.textWarnTrackChanges": "Track Changes將會幫有權限的帳戶開啟。下一次任何帳戶開啟文件時，Track Changes會保持開啟狀態。", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "幫所有使用者開啟Track Changes?", "Common.Views.ReviewChanges.tipAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.tipCoAuthMode": "設定共同編輯模式", "Common.Views.ReviewChanges.tipCommentRem": "刪除註解", "Common.Views.ReviewChanges.tipCommentRemCurrent": "刪除當前註解", "Common.Views.ReviewChanges.tipCommentResolve": "標記註解為已解決", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "將所有的註解標記為已解決", "Common.Views.ReviewChanges.tipCompare": "比較當前文件和另一個文件", "Common.Views.ReviewChanges.tipHistory": "顯示版本歷史", "Common.Views.ReviewChanges.tipRejectCurrent": "拒絕當前變化", "Common.Views.ReviewChanges.tipReview": "跟蹤變化", "Common.Views.ReviewChanges.tipReviewView": "選擇您要顯示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "設定文件語言", "Common.Views.ReviewChanges.tipSetSpelling": "拼字檢查", "Common.Views.ReviewChanges.tipSharing": "管理文檔存取權限", "Common.Views.ReviewChanges.txtAccept": "同意", "Common.Views.ReviewChanges.txtAcceptAll": "同意所有更改", "Common.Views.ReviewChanges.txtAcceptChanges": "同意更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "關閉", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編輯模式", "Common.Views.ReviewChanges.txtCommentRemAll": "刪除所有註解", "Common.Views.ReviewChanges.txtCommentRemCurrent": "刪除當前註解", "Common.Views.ReviewChanges.txtCommentRemMy": "刪除我的註解", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "刪除我當前的註解", "Common.Views.ReviewChanges.txtCommentRemove": "移除", "Common.Views.ReviewChanges.txtCommentResolve": "解決", "Common.Views.ReviewChanges.txtCommentResolveAll": "將所有註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "將註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMy": "將自己所有的註解標記為已解決", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "將自己的註解標記為已解決", "Common.Views.ReviewChanges.txtCompare": "相比", "Common.Views.ReviewChanges.txtDocLang": "語言", "Common.Views.ReviewChanges.txtEditing": "编辑中", "Common.Views.ReviewChanges.txtFinal": "更改已全部接受 {0}", "Common.Views.ReviewChanges.txtFinalCap": "最後", "Common.Views.ReviewChanges.txtHistory": "版本歷史", "Common.Views.ReviewChanges.txtMarkup": "全部的更改{0}", "Common.Views.ReviewChanges.txtMarkupCap": "標記與氣球", "Common.Views.ReviewChanges.txtMarkupSimple": "所有變化{0}<br>無文字通知", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "只改標記的", "Common.Views.ReviewChanges.txtNext": "下一個", "Common.Views.ReviewChanges.txtOff": "給自己關閉", "Common.Views.ReviewChanges.txtOffGlobal": "給自己跟所有使用者關閉", "Common.Views.ReviewChanges.txtOn": "給自己開啟", "Common.Views.ReviewChanges.txtOnGlobal": "給自己跟所有使用者開啟", "Common.Views.ReviewChanges.txtOriginal": "全部更改被拒絕{0}", "Common.Views.ReviewChanges.txtOriginalCap": "原始", "Common.Views.ReviewChanges.txtPrev": "前一個", "Common.Views.ReviewChanges.txtPreview": "預覽", "Common.Views.ReviewChanges.txtReject": "拒絕", "Common.Views.ReviewChanges.txtRejectAll": "拒絕所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "拒絕更改", "Common.Views.ReviewChanges.txtRejectCurrent": "拒絕當前變化", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼字檢查", "Common.Views.ReviewChanges.txtTurnon": "跟蹤變化", "Common.Views.ReviewChanges.txtView": "顯示模式", "Common.Views.ReviewChangesDialog.textTitle": "查看變更", "Common.Views.ReviewChangesDialog.txtAccept": "同意", "Common.Views.ReviewChangesDialog.txtAcceptAll": "同意所有更改", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "同意當前更改", "Common.Views.ReviewChangesDialog.txtNext": "到下一個變化", "Common.Views.ReviewChangesDialog.txtPrev": "到以前的變化", "Common.Views.ReviewChangesDialog.txtReject": "拒絕", "Common.Views.ReviewChangesDialog.txtRejectAll": "拒絕所有更改", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "拒絕當前變化", "Common.Views.ReviewPopover.textAdd": "新增", "Common.Views.ReviewPopover.textAddReply": "加入回應", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "關閉", "Common.Views.ReviewPopover.textEdit": "確定", "Common.Views.ReviewPopover.textFollowMove": "跟隨移動", "Common.Views.ReviewPopover.textMention": "+提及將提供對文檔的存取權限並發送電子郵件", "Common.Views.ReviewPopover.textMentionNotify": "+提及將通過電子郵件通知帳戶", "Common.Views.ReviewPopover.textOpenAgain": "重新打開", "Common.Views.ReviewPopover.textReply": "回覆", "Common.Views.ReviewPopover.textResolve": "解決", "Common.Views.ReviewPopover.textViewResolved": "你沒有權限來重新開啟這個註解", "Common.Views.ReviewPopover.txtAccept": "同意", "Common.Views.ReviewPopover.txtDeleteTip": "刪除", "Common.Views.ReviewPopover.txtEditTip": "編輯", "Common.Views.ReviewPopover.txtReject": "拒絕", "Common.Views.SaveAsDlg.textLoading": "載入中", "Common.Views.SaveAsDlg.textTitle": "儲存文件夾", "Common.Views.SearchPanel.textCaseSensitive": "區分大小寫", "Common.Views.SearchPanel.textCloseSearch": "關閉搜索", "Common.Views.SearchPanel.textFind": "尋找", "Common.Views.SearchPanel.textFindAndReplace": "尋找與取代", "Common.Views.SearchPanel.textMatchUsingRegExp": "用正規表達式進行匹配", "Common.Views.SearchPanel.textNoMatches": "查無匹配", "Common.Views.SearchPanel.textNoSearchResults": "查無搜索结果", "Common.Views.SearchPanel.textReplace": "取代", "Common.Views.SearchPanel.textReplaceAll": "取代全部", "Common.Views.SearchPanel.textReplaceWith": "替換為", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "因數量過多而無法顯示部分結果", "Common.Views.SearchPanel.textWholeWords": "僅使用全字", "Common.Views.SearchPanel.tipNextResult": "下一個結果", "Common.Views.SearchPanel.tipPreviousResult": "上一個結果", "Common.Views.SelectFileDlg.textLoading": "載入中", "Common.Views.SelectFileDlg.textTitle": "選擇資料來源", "Common.Views.SignDialog.textBold": "粗體", "Common.Views.SignDialog.textCertificate": "證書", "Common.Views.SignDialog.textChange": "變更", "Common.Views.SignDialog.textInputName": "輸入簽名者名稱", "Common.Views.SignDialog.textItalic": "斜體", "Common.Views.SignDialog.textNameError": "簽名人姓名不能留空。", "Common.Views.SignDialog.textPurpose": "簽署本文件的目的", "Common.Views.SignDialog.textSelect": "選擇", "Common.Views.SignDialog.textSelectImage": "選擇圖片", "Common.Views.SignDialog.textSignature": "簽名看起來像", "Common.Views.SignDialog.textTitle": "簽署文件", "Common.Views.SignDialog.textUseImage": "或單擊“選擇圖像”以使用圖片作為簽名", "Common.Views.SignDialog.textValid": "從％1到％2有效", "Common.Views.SignDialog.tipFontName": "字體名稱", "Common.Views.SignDialog.tipFontSize": "字體大小", "Common.Views.SignSettingsDialog.textAllowComment": "允許簽名者在簽名對話框中添加註釋", "Common.Views.SignSettingsDialog.textInfoEmail": "電子郵件", "Common.Views.SignSettingsDialog.textInfoName": "名稱", "Common.Views.SignSettingsDialog.textInfoTitle": "簽名人稱號", "Common.Views.SignSettingsDialog.textInstructions": "簽名者說明", "Common.Views.SignSettingsDialog.textShowDate": "在簽名行中顯示簽名日期", "Common.Views.SignSettingsDialog.textTitle": "簽名設置", "Common.Views.SignSettingsDialog.txtEmpty": "這是必填欄", "Common.Views.SymbolTableDialog.textCharacter": "文字", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX 值", "Common.Views.SymbolTableDialog.textCopyright": "版權標誌", "Common.Views.SymbolTableDialog.textDCQuote": "結束雙引號", "Common.Views.SymbolTableDialog.textDOQuote": "開頭雙引號", "Common.Views.SymbolTableDialog.textEllipsis": "水平橢圓", "Common.Views.SymbolTableDialog.textEmDash": "空槓", "Common.Views.SymbolTableDialog.textEmSpace": "空白空間", "Common.Views.SymbolTableDialog.textEnDash": "En 橫槓", "Common.Views.SymbolTableDialog.textEnSpace": "En 空白", "Common.Views.SymbolTableDialog.textFont": "字體", "Common.Views.SymbolTableDialog.textNBHyphen": "不間斷連字符", "Common.Views.SymbolTableDialog.textNBSpace": "不間斷空間", "Common.Views.SymbolTableDialog.textPilcrow": "稻草人標誌", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 空白空間", "Common.Views.SymbolTableDialog.textRange": "範圍", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符號", "Common.Views.SymbolTableDialog.textRegistered": "註冊標誌", "Common.Views.SymbolTableDialog.textSCQuote": "結束單引號", "Common.Views.SymbolTableDialog.textSection": "分區標誌", "Common.Views.SymbolTableDialog.textShortcut": "快捷鍵", "Common.Views.SymbolTableDialog.textSHyphen": "軟連字符", "Common.Views.SymbolTableDialog.textSOQuote": "開單報價", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符號", "Common.Views.SymbolTableDialog.textTitle": "符號", "Common.Views.SymbolTableDialog.textTradeMark": "商標符號", "Common.Views.UserNameDialog.textDontShow": "不要再顯示", "Common.Views.UserNameDialog.textLabel": "標籤：", "Common.Views.UserNameDialog.textLabelError": "標籤不能為空。", "DE.Controllers.LeftMenu.leavePageText": "該文檔中所有未儲存的更改都將丟失。<br>單擊“取消”，然後單擊“存檔”以保存它們。單擊“確定”，放棄所有未儲存的更改。", "DE.Controllers.LeftMenu.newDocumentTitle": "未命名文件", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "DE.Controllers.LeftMenu.requestEditRightsText": "正在請求編輯權限...", "DE.Controllers.LeftMenu.textLoadHistory": "正在載入版本歷史記錄...", "DE.Controllers.LeftMenu.textNoTextFound": "找不到您一直在搜索的數據。請調整您的搜索選項。", "DE.Controllers.LeftMenu.textReplaceSkipped": "替換已完成。 {0}個事件被跳過。", "DE.Controllers.LeftMenu.textReplaceSuccess": "搜索已完成。發生的事件已替換：{0}", "DE.Controllers.LeftMenu.txtCompatible": "該文檔將儲存為新格式。它將允許使用所有編輯器功能，但可能會影響文檔佈局。<br>如果要使文件與舊版MS Word兼容，請使用高級設置的“兼容性”選項。", "DE.Controllers.LeftMenu.txtUntitled": "無標題", "DE.Controllers.LeftMenu.warnDownloadAs": "如果繼續以這種格式保存，則除文本外的所有功能都將丟失。<br>確定要繼續嗎？", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "您的 {0} 將轉換成一份可修改的文件。系統需要處理一段時間。轉換後的文件即可隨之修改，但可能不完全跟您的原 {0} 相同，特別是如果原文件有過多的圖像將會更有差異。", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "如果繼續以這種格式保存，則某些格式可能會丟失。<br>確定要繼續嗎？", "DE.Controllers.LeftMenu.warnReplaceString": "{0}不可用於替換段文字落的有效特殊符號。", "DE.Controllers.Main.applyChangesTextText": "加載更改...", "DE.Controllers.Main.applyChangesTitleText": "加載更改", "DE.Controllers.Main.convertationTimeoutText": "轉換逾時。", "DE.Controllers.Main.criticalErrorExtText": "按“確定”返回文檔列表。", "DE.Controllers.Main.criticalErrorTitle": "錯誤", "DE.Controllers.Main.downloadErrorText": "下載失敗", "DE.Controllers.Main.downloadMergeText": "下載中...", "DE.Controllers.Main.downloadMergeTitle": "下載中", "DE.Controllers.Main.downloadTextText": "文件下載中...", "DE.Controllers.Main.downloadTitleText": "文件下載中", "DE.Controllers.Main.errorAccessDeny": "您嘗試進行未被授權的動作。<br> 請聯繫您的文件伺服器主機的管理者。", "DE.Controllers.Main.errorBadImageUrl": "不正確的圖像 URL", "DE.Controllers.Main.errorCoAuthoringDisconnect": "服務器連接丟失。該文檔目前無法編輯。", "DE.Controllers.Main.errorComboSeries": "如要新增組合圖表，選擇兩個以上的Series資料。", "DE.Controllers.Main.errorCompare": "共同編輯時，“比較文檔”功能不可用。", "DE.Controllers.Main.errorConnectToServer": "無法儲存該文檔。請檢查連接設置或與管理員聯繫。<br>單擊“確定”按鈕時，系統將提示您下載文檔。", "DE.Controllers.Main.errorDatabaseConnection": "外部錯誤。<br>數據庫連接錯誤。如果錯誤仍然存在，請聯繫支持。", "DE.Controllers.Main.errorDataEncrypted": "已收到加密的更改，無法解密。", "DE.Controllers.Main.errorDataRange": "不正確的資料範圍", "DE.Controllers.Main.errorDefaultMessage": "錯誤編號：%1", "DE.Controllers.Main.errorDirectUrl": "請驗證指向文檔的連結。<br>此連結必須是指向要下載文件的直接連結。", "DE.Controllers.Main.errorEditingDownloadas": "在處理文檔期間發生錯誤。<br>使用“下載為”選項將文件備份副本保存到計算機硬碟驅動器中。", "DE.Controllers.Main.errorEditingSaveas": "使用文檔期間發生錯誤。<br>使用“另存為...”選項將文件備份副本保存到硬碟中。", "DE.Controllers.Main.errorEmailClient": "找不到電子郵件客戶端。", "DE.Controllers.Main.errorEmptyTOC": "用選取的文字然後利用樣式庫中的標題風格來創造目錄", "DE.Controllers.Main.errorFilePassProtect": "該文件受密碼保護，無法打開。", "DE.Controllers.Main.errorFileSizeExceed": "此檔案超過這一主機限制的大小<br> 進一步資訊，請聯絡您的文件服務主機的管理者。", "DE.Controllers.Main.errorForceSave": "保存文件時發生錯誤。請使用“下載為”選項將文件保存到電腦硬碟中，或稍後再試。", "DE.Controllers.Main.errorKeyEncrypt": "未知密鑰描述符", "DE.Controllers.Main.errorKeyExpire": "密鑰描述符已過期", "DE.Controllers.Main.errorLoadingFont": "字體未加載。<br>請聯繫文件服務器管理員。", "DE.Controllers.Main.errorMailMergeLoadFile": "文件載入中", "DE.Controllers.Main.errorMailMergeSaveFile": "合併失敗.", "DE.Controllers.Main.errorNoTOC": "沒有目錄需要更新。你可以從引用頁來插入。", "DE.Controllers.Main.errorProcessSaveResult": "儲存失敗", "DE.Controllers.Main.errorServerVersion": "編輯器版本已更新。該頁面將被重新加載以應用更改。", "DE.Controllers.Main.errorSessionAbsolute": "此文件編輯的會期已經過時。請重新載入此頁面。", "DE.Controllers.Main.errorSessionIdle": "此文件已經在編輯狀態有很長時間, 請重新載入此頁面。", "DE.Controllers.Main.errorSessionToken": "與服務器的連接已中斷。請重新加載頁面。", "DE.Controllers.Main.errorSetPassword": "無法重設密碼。", "DE.Controllers.Main.errorStockChart": "不正確的列次序。要建立一推疊圖表, 需要將此表的資料放置為以下的次序<br> 出價, 最高價, 最低價, 節標價。", "DE.Controllers.Main.errorSubmit": "傳送失敗", "DE.Controllers.Main.errorToken": "文檔安全令牌的格式不正確。<br>請與您的Document Server管理員聯繫。", "DE.Controllers.Main.errorTokenExpire": "文檔安全令牌已過期。<br>請與您的Document Server管理員聯繫。", "DE.Controllers.Main.errorUpdateVersion": "文件版本已更改。該頁面將重新加載。", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internet連接已恢復，文件版本已更改。<br>在繼續工作之前，您需要下載文件或複制其內容以確保沒有丟失，然後重新加載此頁面。", "DE.Controllers.Main.errorUserDrop": "目前無法存取該文件。", "DE.Controllers.Main.errorUsersExceed": "超出了定價計劃所允許的帳戶數量", "DE.Controllers.Main.errorViewerDisconnect": "連線失敗。您仍然可以查看該檔案，但在恢復連接並重新加載頁面之前將無法下載或列印該檔案。", "DE.Controllers.Main.leavePageText": "您在此文檔中尚未儲存更改。單擊“保留在此頁面上”，然後單擊“存檔”以儲存更改。單擊“離開此頁面”以放棄所有未儲存的更改。", "DE.Controllers.Main.leavePageTextOnClose": "該文檔中所有未儲存的更改都將遺失。<br>單擊“取消”，然後單擊“存檔”以保存它們。單擊“確定”，放棄所有未儲存的更改。", "DE.Controllers.Main.loadFontsTextText": "加載數據中...", "DE.Controllers.Main.loadFontsTitleText": "加載數據中", "DE.Controllers.Main.loadFontTextText": "加載數據中...", "DE.Controllers.Main.loadFontTitleText": "加載數據中", "DE.Controllers.Main.loadImagesTextText": "正在載入圖片...", "DE.Controllers.Main.loadImagesTitleText": "正在載入圖片", "DE.Controllers.Main.loadImageTextText": "正在載入圖片...", "DE.Controllers.Main.loadImageTitleText": "正在載入圖片", "DE.Controllers.Main.loadingDocumentTextText": "正在載入文件...", "DE.Controllers.Main.loadingDocumentTitleText": "載入文件", "DE.Controllers.Main.mailMergeLoadFileText": "加載數據源...", "DE.Controllers.Main.mailMergeLoadFileTitle": "加載數據源", "DE.Controllers.Main.notcriticalErrorTitle": "警告", "DE.Controllers.Main.openErrorText": "開啟檔案時發生錯誤", "DE.Controllers.Main.openTextText": "開啟文件中...", "DE.Controllers.Main.openTitleText": "開啟文件中", "DE.Controllers.Main.printTextText": "列印文件中...", "DE.Controllers.Main.printTitleText": "列印文件", "DE.Controllers.Main.reloadButtonText": "重新載入頁面", "DE.Controllers.Main.requestEditFailedMessageText": "有人正在編輯此文檔。請稍後再試。", "DE.Controllers.Main.requestEditFailedTitleText": "存取拒絕", "DE.Controllers.Main.saveErrorText": "儲存檔案時發生錯誤", "DE.Controllers.Main.saveErrorTextDesktop": "無法存檔或新增此文件。<br>可能的原因是：<br> 1。該文件是唯獨模式的。 <br> 2。該文件正在由其他帳戶編輯。 <br> 3。磁碟已滿或損壞。", "DE.Controllers.Main.saveTextText": "儲存文件中...", "DE.Controllers.Main.saveTitleText": "儲存文件", "DE.Controllers.Main.scriptLoadError": "連接速度太慢，某些組件無法加載。請重新加載頁面。", "DE.Controllers.Main.sendMergeText": "發送合併中...", "DE.Controllers.Main.sendMergeTitle": "發送合併", "DE.Controllers.Main.splitDividerErrorText": "行數必須是％1的除數。", "DE.Controllers.Main.splitMaxColsErrorText": "列數必須少於％1。", "DE.Controllers.Main.splitMaxRowsErrorText": "行數必須少於％1。", "DE.Controllers.Main.textAnonymous": "匿名", "DE.Controllers.Main.textApplyAll": "適用於所有方程式", "DE.Controllers.Main.textBuyNow": "訪問網站", "DE.Controllers.Main.textChangesSaved": "所有更改已儲存", "DE.Controllers.Main.textClose": "關閉", "DE.Controllers.Main.textCloseTip": "點擊關閉提示", "DE.Controllers.Main.textContactUs": "聯絡業務人員", "DE.Controllers.Main.textConvertEquation": "該方程式是使用不再受支持的方程式編輯器的舊版本創建的。要對其進行編輯，請將等式轉換為Office Math ML格式。<br>立即轉換？", "DE.Controllers.Main.textCustomLoader": "請注意，根據許可條款，您無權更換裝載機。<br>請聯繫我們的銷售部門以獲取報價。", "DE.Controllers.Main.textDisconnect": "失去網路連結", "DE.Controllers.Main.textGuest": "來賓帳戶", "DE.Controllers.Main.textHasMacros": "此檔案包含自動的", "DE.Controllers.Main.textLearnMore": "了解更多", "DE.Controllers.Main.textLoadingDocument": "載入文件", "DE.Controllers.Main.textLongName": "輸入少於128個字符的名稱。", "DE.Controllers.Main.textNoLicenseTitle": "達到許可限制", "DE.Controllers.Main.textPaidFeature": "付費功能", "DE.Controllers.Main.textReconnect": "連線恢復", "DE.Controllers.Main.textRemember": "記住我的選擇", "DE.Controllers.Main.textRememberMacros": "記住我所有巨集的選擇", "DE.Controllers.Main.textRenameError": "使用者名稱無法是空白。", "DE.Controllers.Main.textRenameLabel": "輸入合作名稱", "DE.Controllers.Main.textRequestMacros": "有一個巨集指令要求連結至URL。是否允許該要求至%1?", "DE.Controllers.Main.textShape": "形狀", "DE.Controllers.Main.textStrict": "嚴格模式", "DE.Controllers.Main.textText": "文字", "DE.Controllers.Main.textTryUndoRedo": "快速共同編輯模式禁用了“復原/重複”功能。<br>點擊“嚴格模式”按鈕切換到“嚴格共同編輯”模式以編輯文件而不會受到其他帳戶的干擾，並且僅在保存後發送更改他們。您可以使用編輯器的“進階”設置在共同編輯模式之間切換。", "DE.Controllers.Main.textTryUndoRedoWarn": "在快速共同編輯模式下，復原/重複功能被禁用。", "DE.Controllers.Main.titleLicenseExp": "證件過期", "DE.Controllers.Main.titleServerVersion": "編輯器已更新", "DE.Controllers.Main.titleUpdateVersion": "版本已更改", "DE.Controllers.Main.txtAbove": "以上", "DE.Controllers.Main.txtArt": "在這輸入文字", "DE.Controllers.Main.txtBasicShapes": "基本形狀", "DE.Controllers.Main.txtBelow": "之下", "DE.Controllers.Main.txtBookmarkError": "錯誤！書籤未定義。", "DE.Controllers.Main.txtButtons": "按鈕", "DE.Controllers.Main.txtCallouts": "標註", "DE.Controllers.Main.txtCharts": "圖表", "DE.Controllers.Main.txtChoose": "選擇一個項目", "DE.Controllers.Main.txtClickToLoad": "點此讀取圖片", "DE.Controllers.Main.txtCurrentDocument": "當前文件", "DE.Controllers.Main.txtDiagramTitle": "圖表標題", "DE.Controllers.Main.txtEditingMode": "設定編輯模式...", "DE.Controllers.Main.txtEndOfFormula": "函數意外結束", "DE.Controllers.Main.txtEnterDate": "輸入日期", "DE.Controllers.Main.txtErrorLoadHistory": "歷史記錄加載失敗", "DE.Controllers.Main.txtEvenPage": "雙數頁", "DE.Controllers.Main.txtFiguredArrows": "圖箭", "DE.Controllers.Main.txtFirstPage": "第一頁", "DE.Controllers.Main.txtFooter": "頁尾", "DE.Controllers.Main.txtFormulaNotInTable": "函數不在表格中", "DE.Controllers.Main.txtHeader": "標頭", "DE.Controllers.Main.txtHyperlink": "超連結", "DE.Controllers.Main.txtIndTooLarge": "索引太大", "DE.Controllers.Main.txtLines": "線數", "DE.Controllers.Main.txtMainDocOnly": "錯誤！僅主文檔。", "DE.Controllers.Main.txtMath": "數學", "DE.Controllers.Main.txtMissArg": "遺失論點", "DE.Controllers.Main.txtMissOperator": "缺少運算符", "DE.Controllers.Main.txtNeedSynchronize": "您有更新", "DE.Controllers.Main.txtNone": "無", "DE.Controllers.Main.txtNoTableOfContents": "該文件中沒有標題。將標題風格應用於內文，以便出現在目錄中。", "DE.Controllers.Main.txtNoTableOfFigures": "沒有圖表目錄項目可用。", "DE.Controllers.Main.txtNoText": "錯誤！指定的風格文件中沒有文字。", "DE.Controllers.Main.txtNotInTable": "不在表中", "DE.Controllers.Main.txtNotValidBookmark": "錯誤！不是有效的書籤自參考。", "DE.Controllers.Main.txtOddPage": "奇數頁", "DE.Controllers.Main.txtOnPage": "在頁面上", "DE.Controllers.Main.txtRectangles": "長方形", "DE.Controllers.Main.txtSameAsPrev": "與上一個相同", "DE.Controllers.Main.txtSection": "-部分", "DE.Controllers.Main.txtSeries": "系列", "DE.Controllers.Main.txtShape_accentBorderCallout1": "線路標註1（邊框和強調欄）", "DE.Controllers.Main.txtShape_accentBorderCallout2": "線路標註2（邊框和強調欄）", "DE.Controllers.Main.txtShape_accentBorderCallout3": "線路標註3（邊框和強調欄）", "DE.Controllers.Main.txtShape_accentCallout1": "線路標註1（強調欄）", "DE.Controllers.Main.txtShape_accentCallout2": "線路標註2（強調欄）", "DE.Controllers.Main.txtShape_accentCallout3": "線路標註3（強調欄）", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "後退或上一步按鈕", "DE.Controllers.Main.txtShape_actionButtonBeginning": "開始按鈕", "DE.Controllers.Main.txtShape_actionButtonBlank": "空白按鈕", "DE.Controllers.Main.txtShape_actionButtonDocument": "文件按鈕", "DE.Controllers.Main.txtShape_actionButtonEnd": "結束按鈕", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "前進或後退按鈕", "DE.Controllers.Main.txtShape_actionButtonHelp": "幫助按鈕", "DE.Controllers.Main.txtShape_actionButtonHome": "首頁按鈕", "DE.Controllers.Main.txtShape_actionButtonInformation": "信息按鈕", "DE.Controllers.Main.txtShape_actionButtonMovie": "電影按鈕", "DE.Controllers.Main.txtShape_actionButtonReturn": "返回按鈕", "DE.Controllers.Main.txtShape_actionButtonSound": "聲音按鈕", "DE.Controllers.Main.txtShape_arc": "弧", "DE.Controllers.Main.txtShape_bentArrow": "彎曲箭頭", "DE.Controllers.Main.txtShape_bentConnector5": "彎頭接頭", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "彎頭箭頭連接器", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "彎頭雙箭頭連接器", "DE.Controllers.Main.txtShape_bentUpArrow": "向上彎曲箭頭", "DE.Controllers.Main.txtShape_bevel": "斜角", "DE.Controllers.Main.txtShape_blockArc": "圓弧", "DE.Controllers.Main.txtShape_borderCallout1": "線路標註1", "DE.Controllers.Main.txtShape_borderCallout2": "線路標註2", "DE.Controllers.Main.txtShape_borderCallout3": "線路標註3", "DE.Controllers.Main.txtShape_bracePair": "雙括號", "DE.Controllers.Main.txtShape_callout1": "線路標註1（無邊框）", "DE.Controllers.Main.txtShape_callout2": "線路標註2（無邊框）", "DE.Controllers.Main.txtShape_callout3": "線路標註3（無邊框）", "DE.Controllers.Main.txtShape_can": "罐狀", "DE.Controllers.Main.txtShape_chevron": "雪佛龍V形", "DE.Controllers.Main.txtShape_chord": "弦", "DE.Controllers.Main.txtShape_circularArrow": "圓形箭頭", "DE.Controllers.Main.txtShape_cloud": "雲", "DE.Controllers.Main.txtShape_cloudCallout": "雲標註", "DE.Controllers.Main.txtShape_corner": "角", "DE.Controllers.Main.txtShape_cube": "立方體", "DE.Controllers.Main.txtShape_curvedConnector3": "彎曲連接器", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "彎曲箭頭連接器", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "彎曲雙箭頭連接器", "DE.Controllers.Main.txtShape_curvedDownArrow": "彎曲的向下箭頭", "DE.Controllers.Main.txtShape_curvedLeftArrow": "彎曲的左箭頭", "DE.Controllers.Main.txtShape_curvedRightArrow": "彎曲的右箭頭", "DE.Controllers.Main.txtShape_curvedUpArrow": "彎曲的向上箭頭", "DE.Controllers.Main.txtShape_decagon": "十邊形", "DE.Controllers.Main.txtShape_diagStripe": "斜條紋", "DE.Controllers.Main.txtShape_diamond": "鑽石", "DE.Controllers.Main.txtShape_dodecagon": "十二邊形", "DE.Controllers.Main.txtShape_donut": "甜甜圈", "DE.Controllers.Main.txtShape_doubleWave": "雙波", "DE.Controllers.Main.txtShape_downArrow": "下箭頭", "DE.Controllers.Main.txtShape_downArrowCallout": "向下箭頭標註", "DE.Controllers.Main.txtShape_ellipse": "橢圓", "DE.Controllers.Main.txtShape_ellipseRibbon": "彎下絲帶", "DE.Controllers.Main.txtShape_ellipseRibbon2": "向上彎曲絲帶", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程圖：替代過程", "DE.Controllers.Main.txtShape_flowChartCollate": "流程圖：整理", "DE.Controllers.Main.txtShape_flowChartConnector": "流程圖：連接器", "DE.Controllers.Main.txtShape_flowChartDecision": "流程圖：決策", "DE.Controllers.Main.txtShape_flowChartDelay": "流程圖：延遲", "DE.Controllers.Main.txtShape_flowChartDisplay": "流程圖：顯示", "DE.Controllers.Main.txtShape_flowChartDocument": "流程圖：文件", "DE.Controllers.Main.txtShape_flowChartExtract": "流程圖：提取", "DE.Controllers.Main.txtShape_flowChartInputOutput": "流程圖：數據", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "流程圖：內部存儲", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程圖：磁碟", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程圖：直接存取存儲", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "流程圖：順序存取存儲", "DE.Controllers.Main.txtShape_flowChartManualInput": "流程圖：手動輸入", "DE.Controllers.Main.txtShape_flowChartManualOperation": "流程圖：手動操作", "DE.Controllers.Main.txtShape_flowChartMerge": "流程圖：合併", "DE.Controllers.Main.txtShape_flowChartMultidocument": "流程圖：多文檔", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程圖：頁外連接器", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程圖：存儲的數據", "DE.Controllers.Main.txtShape_flowChartOr": "流程圖：或", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程圖：預定義流程", "DE.Controllers.Main.txtShape_flowChartPreparation": "流程圖：準備", "DE.Controllers.Main.txtShape_flowChartProcess": "流程圖：流程", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "流程圖：卡", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "流程圖：穿孔紙帶", "DE.Controllers.Main.txtShape_flowChartSort": "流程圖：排序", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "流程圖：求和結點", "DE.Controllers.Main.txtShape_flowChartTerminator": "流程圖：終結者", "DE.Controllers.Main.txtShape_foldedCorner": "折角", "DE.Controllers.Main.txtShape_frame": "框", "DE.Controllers.Main.txtShape_halfFrame": "半框", "DE.Controllers.Main.txtShape_heart": "心", "DE.Controllers.Main.txtShape_heptagon": "七邊形", "DE.Controllers.Main.txtShape_hexagon": "六邊形", "DE.Controllers.Main.txtShape_homePlate": "五角形", "DE.Controllers.Main.txtShape_horizontalScroll": "水平滾動", "DE.Controllers.Main.txtShape_irregularSeal1": "爆炸1", "DE.Controllers.Main.txtShape_irregularSeal2": "爆炸2", "DE.Controllers.Main.txtShape_leftArrow": "左箭頭", "DE.Controllers.Main.txtShape_leftArrowCallout": "向左箭頭標註", "DE.Controllers.Main.txtShape_leftBrace": "左括號", "DE.Controllers.Main.txtShape_leftBracket": "左括號", "DE.Controllers.Main.txtShape_leftRightArrow": "左右箭頭", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭頭標註", "DE.Controllers.Main.txtShape_leftRightUpArrow": "左右上箭頭", "DE.Controllers.Main.txtShape_leftUpArrow": "左上箭頭", "DE.Controllers.Main.txtShape_lightningBolt": "閃電", "DE.Controllers.Main.txtShape_line": "線", "DE.Controllers.Main.txtShape_lineWithArrow": "箭頭", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "雙箭頭", "DE.Controllers.Main.txtShape_mathDivide": "分裂", "DE.Controllers.Main.txtShape_mathEqual": "等於", "DE.Controllers.Main.txtShape_mathMinus": "減去", "DE.Controllers.Main.txtShape_mathMultiply": "乘", "DE.Controllers.Main.txtShape_mathNotEqual": "不平等", "DE.Controllers.Main.txtShape_mathPlus": "加", "DE.Controllers.Main.txtShape_moon": "月亮", "DE.Controllers.Main.txtShape_noSmoking": "\"否\"符號", "DE.Controllers.Main.txtShape_notchedRightArrow": "缺口右箭頭", "DE.Controllers.Main.txtShape_octagon": "八邊形", "DE.Controllers.Main.txtShape_parallelogram": "平行四邊形", "DE.Controllers.Main.txtShape_pentagon": "五角形", "DE.Controllers.Main.txtShape_pie": "餅", "DE.Controllers.Main.txtShape_plaque": "簽名", "DE.Controllers.Main.txtShape_plus": "加", "DE.Controllers.Main.txtShape_polyline1": "塗", "DE.Controllers.Main.txtShape_polyline2": "自由形式", "DE.Controllers.Main.txtShape_quadArrow": "四箭頭", "DE.Controllers.Main.txtShape_quadArrowCallout": "四箭頭標註", "DE.Controllers.Main.txtShape_rect": "長方形", "DE.Controllers.Main.txtShape_ribbon": "下絨帶", "DE.Controllers.Main.txtShape_ribbon2": "上色帶", "DE.Controllers.Main.txtShape_rightArrow": "右箭頭", "DE.Controllers.Main.txtShape_rightArrowCallout": "右箭頭標註", "DE.Controllers.Main.txtShape_rightBrace": "右括號", "DE.Controllers.Main.txtShape_rightBracket": "右括號", "DE.Controllers.Main.txtShape_round1Rect": "圓形單角矩形", "DE.Controllers.Main.txtShape_round2DiagRect": "圓斜角矩形", "DE.Controllers.Main.txtShape_round2SameRect": "圓同一邊角矩形", "DE.Controllers.Main.txtShape_roundRect": "圓角矩形", "DE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "DE.Controllers.Main.txtShape_smileyFace": "笑臉", "DE.Controllers.Main.txtShape_snip1Rect": "剪斷單角矩形", "DE.Controllers.Main.txtShape_snip2DiagRect": "剪裁對角線矩形", "DE.Controllers.Main.txtShape_snip2SameRect": "剪斷同一邊角矩形", "DE.Controllers.Main.txtShape_snipRoundRect": "剪斷和圓形單角矩形", "DE.Controllers.Main.txtShape_spline": "曲線", "DE.Controllers.Main.txtShape_star10": "十點星", "DE.Controllers.Main.txtShape_star12": "十二點星", "DE.Controllers.Main.txtShape_star16": "十六點星", "DE.Controllers.Main.txtShape_star24": "24點星", "DE.Controllers.Main.txtShape_star32": "32點星", "DE.Controllers.Main.txtShape_star4": "4點星", "DE.Controllers.Main.txtShape_star5": "5點星", "DE.Controllers.Main.txtShape_star6": "6點星", "DE.Controllers.Main.txtShape_star7": "7點星", "DE.Controllers.Main.txtShape_star8": "8點星", "DE.Controllers.Main.txtShape_stripedRightArrow": "條紋右箭頭", "DE.Controllers.Main.txtShape_sun": "太陽", "DE.Controllers.Main.txtShape_teardrop": "淚珠", "DE.Controllers.Main.txtShape_textRect": "文字框", "DE.Controllers.Main.txtShape_trapezoid": "梯形", "DE.Controllers.Main.txtShape_triangle": "三角形", "DE.Controllers.Main.txtShape_upArrow": "向上箭頭", "DE.Controllers.Main.txtShape_upArrowCallout": "向上箭頭標註", "DE.Controllers.Main.txtShape_upDownArrow": "上下箭頭", "DE.Controllers.Main.txtShape_uturnArrow": "掉頭箭頭", "DE.Controllers.Main.txtShape_verticalScroll": "垂直滾動", "DE.Controllers.Main.txtShape_wave": "波", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "橢圓形標註", "DE.Controllers.Main.txtShape_wedgeRectCallout": "矩形標註", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圓角矩形標註", "DE.Controllers.Main.txtStarsRibbons": "星星和絲帶", "DE.Controllers.Main.txtStyle_Caption": "標題", "DE.Controllers.Main.txtStyle_endnote_text": "尾註文", "DE.Controllers.Main.txtStyle_footnote_text": "註腳文字", "DE.Controllers.Main.txtStyle_Heading_1": "標題 1", "DE.Controllers.Main.txtStyle_Heading_2": "標題 2", "DE.Controllers.Main.txtStyle_Heading_3": "標題 3", "DE.Controllers.Main.txtStyle_Heading_4": "標題 4", "DE.Controllers.Main.txtStyle_Heading_5": "標題 5", "DE.Controllers.Main.txtStyle_Heading_6": "標題 6", "DE.Controllers.Main.txtStyle_Heading_7": "標題 7", "DE.Controllers.Main.txtStyle_Heading_8": "標題 8", "DE.Controllers.Main.txtStyle_Heading_9": "標題 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "激烈的報價", "DE.Controllers.Main.txtStyle_List_Paragraph": "段落列表", "DE.Controllers.Main.txtStyle_No_Spacing": "沒有間距", "DE.Controllers.Main.txtStyle_Normal": "標準", "DE.Controllers.Main.txtStyle_Quote": "引用", "DE.Controllers.Main.txtStyle_Subtitle": "副標題", "DE.Controllers.Main.txtStyle_Title": "標題", "DE.Controllers.Main.txtSyntaxError": "語法錯誤", "DE.Controllers.Main.txtTableInd": "表索引不能為零", "DE.Controllers.Main.txtTableOfContents": "目錄", "DE.Controllers.Main.txtTableOfFigures": "圖表目錄", "DE.Controllers.Main.txtTOCHeading": "目錄標題", "DE.Controllers.Main.txtTooLarge": "數字太大而無法格式化", "DE.Controllers.Main.txtTypeEquation": "在此處輸入方程式。", "DE.Controllers.Main.txtUndefBookmark": "未定義的書籤", "DE.Controllers.Main.txtXAxis": "X軸", "DE.Controllers.Main.txtYAxis": "Y軸", "DE.Controllers.Main.txtZeroDivide": "零分度", "DE.Controllers.Main.unknownErrorText": "未知錯誤。", "DE.Controllers.Main.unsupportedBrowserErrorText": "不支援您的瀏覽器", "DE.Controllers.Main.uploadDocExtMessage": "未知的文件格式。", "DE.Controllers.Main.uploadDocFileCountMessage": "沒有文件上傳。", "DE.Controllers.Main.uploadDocSizeMessage": "超出最大文檔大小限制。", "DE.Controllers.Main.uploadImageExtMessage": "圖片格式未知。", "DE.Controllers.Main.uploadImageFileCountMessage": "沒有上傳圖片。", "DE.Controllers.Main.uploadImageSizeMessage": "圖像超出最大大小限制。最大大小為25MB。", "DE.Controllers.Main.uploadImageTextText": "正在上傳圖片...", "DE.Controllers.Main.uploadImageTitleText": "上載圖片", "DE.Controllers.Main.waitText": "請耐心等待...", "DE.Controllers.Main.warnBrowserIE9": "該應用程序在IE9上具有較低的功能。使用IE10或更高版本", "DE.Controllers.Main.warnBrowserZoom": "瀏覽器當前的縮放設置不受完全支持。請按Ctrl + 0重置為預設縮放。", "DE.Controllers.Main.warnLicenseExceeded": "您的系統已經達到同時編輯連線的 %1 編輯器。只能以檢視模式開啟此文件。<br> 欲得知進一步訊息, 請聯絡您的帳號管理者。", "DE.Controllers.Main.warnLicenseExp": "您的授權證已過期.<br>請更新您的授權證並重新整理頁面。", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "授權過期<br>您已沒有編輯文件功能的授權<br> 請與您的管理者聯繫。", "DE.Controllers.Main.warnLicenseLimitedRenewed": "授權證書需要更新<br> 您只有部分的文件編輯功能的存取權限<br>請與您的管理者聯繫來取得完整的存取權限。", "DE.Controllers.Main.warnLicenseUsersExceeded": "您已達到％1個編輯器限制。請聯絡你的帳號管理員以了解更多資訊。", "DE.Controllers.Main.warnNoLicense": "您的系統已經達到同時編輯連線的 %1 編輯器。只能以檢視模式開啟此文件。<br> 請聯繫 %1 銷售團隊來取得個人升級的需求。", "DE.Controllers.Main.warnNoLicenseUsers": "您已達到％1個編輯器限制。請聯絡％1業務部以了解更多的升級條款及方案。", "DE.Controllers.Main.warnProcessRightsChange": "您被拒絕編輯文件的權利。", "DE.Controllers.Navigation.txtBeginning": "文件的開頭", "DE.Controllers.Navigation.txtGotoBeginning": "轉到文檔的開頭", "DE.Controllers.Search.notcriticalErrorTitle": "警告", "DE.Controllers.Search.textNoTextFound": "找不到您在搜索的數據。請調整您的搜索選項。", "DE.Controllers.Search.textReplaceSkipped": "替換已完成。 {0}個事件被跳過。", "DE.Controllers.Search.textReplaceSuccess": "搜尋完成。 {0}個符合結果已被取代", "DE.Controllers.Search.warnReplaceString": "{0}不是有效的字元", "DE.Controllers.Statusbar.textDisconnect": "<b>連線失敗</b><br>正在嘗試連線。請檢查網路連線設定。", "DE.Controllers.Statusbar.textHasChanges": "跟蹤了新的變化", "DE.Controllers.Statusbar.textSetTrackChanges": "您現在是在Track Changes模式", "DE.Controllers.Statusbar.textTrackChanges": "在啟用“修訂”模式的情況下打開文檔", "DE.Controllers.Statusbar.tipReview": "跟蹤變化", "DE.Controllers.Statusbar.zoomText": "放大{0}%", "DE.Controllers.Toolbar.confirmAddFontName": "您要儲存的字型在目前的設備上無法使用。<br>字型風格將使用其中一種系統字體顯示，儲存的字體將在可用時啟用。<br>您要繼續嗎？", "DE.Controllers.Toolbar.dataUrl": "粘貼數據 URL", "DE.Controllers.Toolbar.notcriticalErrorTitle": "警告", "DE.Controllers.Toolbar.textAccent": "口音", "DE.Controllers.Toolbar.textBracket": "括號", "DE.Controllers.Toolbar.textEmptyImgUrl": "您必須輸入圖檔的URL.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "你必須指定URL", "DE.Controllers.Toolbar.textFontSizeErr": "輸入的值不正確。<br>請輸入1到300之間的數字值", "DE.Controllers.Toolbar.textFraction": "分數", "DE.Controllers.Toolbar.textFunction": "功能", "DE.Controllers.Toolbar.textGroup": "群組", "DE.Controllers.Toolbar.textInsert": "插入", "DE.Controllers.Toolbar.textIntegral": "積分", "DE.Controllers.Toolbar.textLargeOperator": "大型運營商", "DE.Controllers.Toolbar.textLimitAndLog": "極限和對數", "DE.Controllers.Toolbar.textMatrix": "矩陣", "DE.Controllers.Toolbar.textOperator": "經營者", "DE.Controllers.Toolbar.textRadical": "激進單數", "DE.Controllers.Toolbar.textRecentlyUsed": "最近使用", "DE.Controllers.Toolbar.textScript": "腳本", "DE.Controllers.Toolbar.textSymbols": "符號", "DE.Controllers.Toolbar.textTabForms": "表格", "DE.Controllers.Toolbar.textWarning": "警告", "DE.Controllers.Toolbar.txtAccent_Accent": "尖銳", "DE.Controllers.Toolbar.txtAccent_ArrowD": "上方的左右箭頭", "DE.Controllers.Toolbar.txtAccent_ArrowL": "上方的向左箭頭", "DE.Controllers.Toolbar.txtAccent_ArrowR": "上方向右箭頭", "DE.Controllers.Toolbar.txtAccent_Bar": "槓", "DE.Controllers.Toolbar.txtAccent_BarBot": "底橫槓", "DE.Controllers.Toolbar.txtAccent_BarTop": "橫槓", "DE.Controllers.Toolbar.txtAccent_BorderBox": "盒裝公式（帶佔位符）", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "盒裝函數（範例）", "DE.Controllers.Toolbar.txtAccent_Check": "檢查", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "底括號", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "大括號", "DE.Controllers.Toolbar.txtAccent_Custom_1": "向量A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "帶橫線的ABC", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x X或y的橫槓", "DE.Controllers.Toolbar.txtAccent_DDDot": "三點", "DE.Controllers.Toolbar.txtAccent_DDot": "雙點", "DE.Controllers.Toolbar.txtAccent_Dot": "點", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "雙橫槓", "DE.Controllers.Toolbar.txtAccent_Grave": "墓", "DE.Controllers.Toolbar.txtAccent_GroupBot": "下面的分組字符", "DE.Controllers.Toolbar.txtAccent_GroupTop": "上面的分組字符", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "上方的向左魚叉", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "右上方的魚叉", "DE.Controllers.Toolbar.txtAccent_Hat": "帽子", "DE.Controllers.Toolbar.txtAccent_Smile": "短音符", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "括號", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "帶分隔符的括號", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "帶分隔符的括號", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Curve": "括號", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "帶分隔符的括號", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Custom_1": "案件（兩件條件）", "DE.Controllers.Toolbar.txtBracket_Custom_2": "案件（三件條件）", "DE.Controllers.Toolbar.txtBracket_Custom_3": "堆疊物件", "DE.Controllers.Toolbar.txtBracket_Custom_4": "堆疊物件", "DE.Controllers.Toolbar.txtBracket_Custom_5": "案件例子", "DE.Controllers.Toolbar.txtBracket_Custom_6": "二項式係數", "DE.Controllers.Toolbar.txtBracket_Custom_7": "二項式係數", "DE.Controllers.Toolbar.txtBracket_Line": "括號", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_LineDouble": "括號", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_LowLim": "括號", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "單括號", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Round": "括號", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "帶分隔符的括號", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Square": "括號", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括號", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括號", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括號", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "括號", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "單括號", "DE.Controllers.Toolbar.txtBracket_UppLim": "括號", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "單括號", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "單括號", "DE.Controllers.Toolbar.txtFractionDiagonal": "偏斜分數", "DE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "DE.Controllers.Toolbar.txtFractionHorizontal": "線性分數", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi超過2", "DE.Controllers.Toolbar.txtFractionSmall": "小分數", "DE.Controllers.Toolbar.txtFractionVertical": "堆積分數", "DE.Controllers.Toolbar.txtFunction_1_Cos": "反餘弦函數", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "雙曲餘弦函數", "DE.Controllers.Toolbar.txtFunction_1_Cot": "反正切函數", "DE.Controllers.Toolbar.txtFunction_1_Coth": "雙曲反正切函數", "DE.Controllers.Toolbar.txtFunction_1_Csc": "餘割函數反", "DE.Controllers.Toolbar.txtFunction_1_Csch": "雙曲反餘割函數", "DE.Controllers.Toolbar.txtFunction_1_Sec": "反割線功能", "DE.Controllers.Toolbar.txtFunction_1_Sech": "雙曲反正割函數", "DE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦函數", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "雙曲反正弦函數", "DE.Controllers.Toolbar.txtFunction_1_Tan": "反正切函數", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "雙曲反正切函數", "DE.Controllers.Toolbar.txtFunction_Cos": "Cosine 函數", "DE.Controllers.Toolbar.txtFunction_Cosh": "雙曲餘弦函數", "DE.Controllers.Toolbar.txtFunction_Cot": "Cotangent 函數", "DE.Controllers.Toolbar.txtFunction_Coth": "雙曲餘切函數", "DE.Controllers.Toolbar.txtFunction_Csc": "餘割函數", "DE.Controllers.Toolbar.txtFunction_Csch": "雙曲餘割函數", "DE.Controllers.Toolbar.txtFunction_Custom_1": "正弦波", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "切線函數", "DE.Controllers.Toolbar.txtFunction_Sec": "正割功能", "DE.Controllers.Toolbar.txtFunction_Sech": "雙曲正割函數", "DE.Controllers.Toolbar.txtFunction_Sin": "正弦函數", "DE.Controllers.Toolbar.txtFunction_Sinh": "雙曲正弦函數", "DE.Controllers.Toolbar.txtFunction_Tan": "切線公式", "DE.Controllers.Toolbar.txtFunction_Tanh": "雙曲正切函數", "DE.Controllers.Toolbar.txtIntegral": "積分", "DE.Controllers.Toolbar.txtIntegral_dtheta": "微分θ", "DE.Controllers.Toolbar.txtIntegral_dx": "差分　x", "DE.Controllers.Toolbar.txtIntegral_dy": "差分　y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "積分", "DE.Controllers.Toolbar.txtIntegralDouble": "雙積分", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "雙積分", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "雙積分", "DE.Controllers.Toolbar.txtIntegralOriented": "輪廓積分", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "輪廓積分", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "表面積分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "表面積分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "表面積分", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "輪廓積分", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "體積積分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "體積積分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "體積積分", "DE.Controllers.Toolbar.txtIntegralSubSup": "積分", "DE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重積分", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重積分", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "楔", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "聯產品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "聯產品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "聯產品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "聯產品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "聯產品", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交叉點", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "產品", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "求和", "DE.Controllers.Toolbar.txtLargeOperator_Union": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "聯合", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "聯合", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "限制例子", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大例子", "DE.Controllers.Toolbar.txtLimitLog_Lim": "限制", "DE.Controllers.Toolbar.txtLimitLog_Ln": "自然對數", "DE.Controllers.Toolbar.txtLimitLog_Log": "對數", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "對數", "DE.Controllers.Toolbar.txtLimitLog_Max": "最大", "DE.Controllers.Toolbar.txtLimitLog_Min": "最低", "DE.Controllers.Toolbar.txtMarginsH": "對於給定的頁面高度，上下邊距太高", "DE.Controllers.Toolbar.txtMarginsW": "給定頁面寬度，左右頁邊距太寬", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2的空矩陣", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "帶括號的空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "帶括號的空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "帶括號的空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "帶括號的空矩陣", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空矩陣", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空矩陣", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空矩陣", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空矩陣", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基準點", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "中線點", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "對角點", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直點", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "稀疏矩陣", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "稀疏矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 單位矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 單位矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 單位矩陣", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 單位矩陣", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "下方的左右箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "上方的左右箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "下方的向左箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "上方的向左箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下方向右箭頭", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "上方向右箭頭", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "冒號相等", "DE.Controllers.Toolbar.txtOperator_Custom_1": "產量", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta 收益", "DE.Controllers.Toolbar.txtOperator_Definition": "等同於定義", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta 等於", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "下方的左右箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "上方的左右箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "下方的向左箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "上方的向左箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下方向右箭頭", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "上方向右箭頭", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "等於 等於", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "負等於", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "加等於", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "測量者", "DE.Controllers.Toolbar.txtRadicalCustom_1": "激進", "DE.Controllers.Toolbar.txtRadicalCustom_2": "激進", "DE.Controllers.Toolbar.txtRadicalRoot_2": "平方根", "DE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "DE.Controllers.Toolbar.txtRadicalRoot_n": "自由基度", "DE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "DE.Controllers.Toolbar.txtScriptCustom_1": "腳本", "DE.Controllers.Toolbar.txtScriptCustom_2": "腳本", "DE.Controllers.Toolbar.txtScriptCustom_3": "腳本", "DE.Controllers.Toolbar.txtScriptCustom_4": "腳本", "DE.Controllers.Toolbar.txtScriptSub": "下標", "DE.Controllers.Toolbar.txtScriptSubSup": "下標-上標", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "左下標-上標", "DE.Controllers.Toolbar.txtScriptSup": "上標", "DE.Controllers.Toolbar.txtSymbol_about": "大約", "DE.Controllers.Toolbar.txtSymbol_additional": "補充", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_approx": "幾乎等於", "DE.Controllers.Toolbar.txtSymbol_ast": "星號運算符", "DE.Controllers.Toolbar.txtSymbol_beta": "測試版", "DE.Controllers.Toolbar.txtSymbol_beth": "賭注", "DE.Controllers.Toolbar.txtSymbol_bullet": "項目點操作者", "DE.Controllers.Toolbar.txtSymbol_cap": "交叉點", "DE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "DE.Controllers.Toolbar.txtSymbol_cdots": "中線水平省略號", "DE.Controllers.Toolbar.txtSymbol_celsius": "攝氏度", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "大約等於", "DE.Controllers.Toolbar.txtSymbol_cup": "聯合", "DE.Controllers.Toolbar.txtSymbol_ddots": "右下斜省略號", "DE.Controllers.Toolbar.txtSymbol_degree": "度", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "分裂標誌", "DE.Controllers.Toolbar.txtSymbol_downarrow": "下箭頭", "DE.Controllers.Toolbar.txtSymbol_emptyset": "空組集", "DE.Controllers.Toolbar.txtSymbol_epsilon": "厄普西隆", "DE.Controllers.Toolbar.txtSymbol_equals": "等於", "DE.Controllers.Toolbar.txtSymbol_equiv": "相同", "DE.Controllers.Toolbar.txtSymbol_eta": "和", "DE.Controllers.Toolbar.txtSymbol_exists": "存在", "DE.Controllers.Toolbar.txtSymbol_factorial": "階乘", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏度", "DE.Controllers.Toolbar.txtSymbol_forall": "對所有人", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "大於或等於", "DE.Controllers.Toolbar.txtSymbol_gg": "比大得多", "DE.Controllers.Toolbar.txtSymbol_greater": "更佳", "DE.Controllers.Toolbar.txtSymbol_in": "元素", "DE.Controllers.Toolbar.txtSymbol_inc": "增量", "DE.Controllers.Toolbar.txtSymbol_infinity": "無限", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "拉姆達", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭頭", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭頭", "DE.Controllers.Toolbar.txtSymbol_leq": "小於或等於", "DE.Controllers.Toolbar.txtSymbol_less": "少於", "DE.Controllers.Toolbar.txtSymbol_ll": "遠遠少於", "DE.Controllers.Toolbar.txtSymbol_minus": "減去", "DE.Controllers.Toolbar.txtSymbol_mp": "減加", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "不等於", "DE.Controllers.Toolbar.txtSymbol_ni": "包含為成員", "DE.Controllers.Toolbar.txtSymbol_not": "不簽名", "DE.Controllers.Toolbar.txtSymbol_notexists": "不存在", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "DE.Controllers.Toolbar.txtSymbol_percent": "百分比", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "加", "DE.Controllers.Toolbar.txtSymbol_pm": "加減", "DE.Controllers.Toolbar.txtSymbol_propto": "成比例", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "第四根", "DE.Controllers.Toolbar.txtSymbol_qed": "證明結束", "DE.Controllers.Toolbar.txtSymbol_rddots": "右上斜省略號", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭頭", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "激進標誌", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "因此", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "乘法符號", "DE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭頭", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "厄普西隆變體", "DE.Controllers.Toolbar.txtSymbol_varphi": "Phi 變體", "DE.Controllers.Toolbar.txtSymbol_varpi": "Pi變體", "DE.Controllers.Toolbar.txtSymbol_varrho": "Rho變體", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma 變體", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Theta變體", "DE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略號", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "切合至頁面", "DE.Controllers.Viewport.textFitWidth": "切合至寬度", "DE.Controllers.Viewport.txtDarkMode": "夜間模式", "DE.Views.AddNewCaptionLabelDialog.textLabel": "標籤：", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "標籤不能為空。", "DE.Views.BookmarksDialog.textAdd": "新增", "DE.Views.BookmarksDialog.textBookmarkName": "書籤名", "DE.Views.BookmarksDialog.textClose": "關閉", "DE.Views.BookmarksDialog.textCopy": "複製", "DE.Views.BookmarksDialog.textDelete": "刪除", "DE.Views.BookmarksDialog.textGetLink": "獲取連結", "DE.Views.BookmarksDialog.textGoto": "去", "DE.Views.BookmarksDialog.textHidden": "隱藏的書籤", "DE.Views.BookmarksDialog.textLocation": "位置", "DE.Views.BookmarksDialog.textName": "名稱", "DE.Views.BookmarksDialog.textSort": "排序方式", "DE.Views.BookmarksDialog.textTitle": "書籤", "DE.Views.BookmarksDialog.txtInvalidName": "書籤名稱只能包含字母，數字和下劃線，並且應以字母開頭", "DE.Views.CaptionDialog.textAdd": "新增標籤", "DE.Views.CaptionDialog.textAfter": "之後", "DE.Views.CaptionDialog.textBefore": "之前", "DE.Views.CaptionDialog.textCaption": "標題", "DE.Views.CaptionDialog.textChapter": "本章始於風格", "DE.Views.CaptionDialog.textChapterInc": "包括章節編號", "DE.Views.CaptionDialog.textColon": "冒號", "DE.Views.CaptionDialog.textDash": "長划", "DE.Views.CaptionDialog.textDelete": "刪除標籤", "DE.Views.CaptionDialog.textEquation": "方程式", "DE.Views.CaptionDialog.textExamples": "示例：表2-A，圖像1.IV", "DE.Views.CaptionDialog.textExclude": "從標題中排除標籤", "DE.Views.CaptionDialog.textFigure": "數字", "DE.Views.CaptionDialog.textHyphen": "連字號", "DE.Views.CaptionDialog.textInsert": "插入", "DE.Views.CaptionDialog.textLabel": "標籤", "DE.Views.CaptionDialog.textLongDash": "長破折號", "DE.Views.CaptionDialog.textNumbering": "編號", "DE.Views.CaptionDialog.textPeriod": "區間", "DE.Views.CaptionDialog.textSeparator": "使用分隔符", "DE.Views.CaptionDialog.textTable": "表格", "DE.Views.CaptionDialog.textTitle": "插入標題", "DE.Views.CellsAddDialog.textCol": "欄", "DE.Views.CellsAddDialog.textDown": "游標下方", "DE.Views.CellsAddDialog.textLeft": "靠左", "DE.Views.CellsAddDialog.textRight": "靠右", "DE.Views.CellsAddDialog.textRow": "行列", "DE.Views.CellsAddDialog.textTitle": "插入多個", "DE.Views.CellsAddDialog.textUp": "游標上方", "DE.Views.ChartSettings.textAdvanced": "顯示進階設定", "DE.Views.ChartSettings.textChartType": "變更圖表類型", "DE.Views.ChartSettings.textEditData": "編輯資料", "DE.Views.ChartSettings.textHeight": "\n高度", "DE.Views.ChartSettings.textOriginalSize": "實際大小", "DE.Views.ChartSettings.textSize": "大小", "DE.Views.ChartSettings.textStyle": "風格", "DE.Views.ChartSettings.textUndock": "從面板上卸下", "DE.Views.ChartSettings.textWidth": "寬度", "DE.Views.ChartSettings.textWrap": "包覆風格", "DE.Views.ChartSettings.txtBehind": "文字在後", "DE.Views.ChartSettings.txtInFront": "文字在前", "DE.Views.ChartSettings.txtInline": "與文字排列", "DE.Views.ChartSettings.txtSquare": "正方形", "DE.Views.ChartSettings.txtThrough": "通過", "DE.Views.ChartSettings.txtTight": "緊", "DE.Views.ChartSettings.txtTitle": "圖表", "DE.Views.ChartSettings.txtTopAndBottom": "頂部和底部", "DE.Views.ControlSettingsDialog.strGeneral": "一般", "DE.Views.ControlSettingsDialog.textAdd": "新增", "DE.Views.ControlSettingsDialog.textAppearance": "外貌", "DE.Views.ControlSettingsDialog.textApplyAll": "全部應用", "DE.Views.ControlSettingsDialog.textBox": "邊界框", "DE.Views.ControlSettingsDialog.textChange": "編輯", "DE.Views.ControlSettingsDialog.textCheckbox": "複選框", "DE.Views.ControlSettingsDialog.textChecked": "選中的符號", "DE.Views.ControlSettingsDialog.textColor": "顏色", "DE.Views.ControlSettingsDialog.textCombobox": "組合框", "DE.Views.ControlSettingsDialog.textDate": "日期格式", "DE.Views.ControlSettingsDialog.textDelete": "刪除", "DE.Views.ControlSettingsDialog.textDisplayName": "顯示名稱", "DE.Views.ControlSettingsDialog.textDown": "下", "DE.Views.ControlSettingsDialog.textDropDown": "下拉選單", "DE.Views.ControlSettingsDialog.textFormat": "以此顯示日期", "DE.Views.ControlSettingsDialog.textLang": "語言", "DE.Views.ControlSettingsDialog.textLock": "鎖定", "DE.Views.ControlSettingsDialog.textName": "標題", "DE.Views.ControlSettingsDialog.textNone": "無", "DE.Views.ControlSettingsDialog.textPlaceholder": "佔位符", "DE.Views.ControlSettingsDialog.textShowAs": "顯示為", "DE.Views.ControlSettingsDialog.textSystemColor": "系統", "DE.Views.ControlSettingsDialog.textTag": "標籤", "DE.Views.ControlSettingsDialog.textTitle": "內容控制設定", "DE.Views.ControlSettingsDialog.textUnchecked": "未經檢查的符號", "DE.Views.ControlSettingsDialog.textUp": "上", "DE.Views.ControlSettingsDialog.textValue": "值", "DE.Views.ControlSettingsDialog.tipChange": "變更符號", "DE.Views.ControlSettingsDialog.txtLockDelete": "內容控制無法刪除", "DE.Views.ControlSettingsDialog.txtLockEdit": "內容無法編輯", "DE.Views.CrossReferenceDialog.textAboveBelow": "上/下", "DE.Views.CrossReferenceDialog.textBookmark": "書籤", "DE.Views.CrossReferenceDialog.textBookmarkText": "書籤文字", "DE.Views.CrossReferenceDialog.textCaption": "整個標題", "DE.Views.CrossReferenceDialog.textEmpty": "請求引用為空。", "DE.Views.CrossReferenceDialog.textEndnote": "尾註", "DE.Views.CrossReferenceDialog.textEndNoteNum": "尾註編號", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "尾註編號（格式化）", "DE.Views.CrossReferenceDialog.textEquation": "方程式", "DE.Views.CrossReferenceDialog.textFigure": "數字", "DE.Views.CrossReferenceDialog.textFootnote": "註腳", "DE.Views.CrossReferenceDialog.textHeading": "標題", "DE.Views.CrossReferenceDialog.textHeadingNum": "標題編號", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "標題編號（全文）", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "標題編號（無內容）", "DE.Views.CrossReferenceDialog.textHeadingText": "標題文字", "DE.Views.CrossReferenceDialog.textIncludeAbove": "包括上方/下方", "DE.Views.CrossReferenceDialog.textInsert": "插入", "DE.Views.CrossReferenceDialog.textInsertAs": "用超連結插入", "DE.Views.CrossReferenceDialog.textLabelNum": "僅標籤和編號", "DE.Views.CrossReferenceDialog.textNoteNum": "腳註編號", "DE.Views.CrossReferenceDialog.textNoteNumForm": "腳註編號（格式化）", "DE.Views.CrossReferenceDialog.textOnlyCaption": "僅字幕文字", "DE.Views.CrossReferenceDialog.textPageNum": "頁碼", "DE.Views.CrossReferenceDialog.textParagraph": "編號項目", "DE.Views.CrossReferenceDialog.textParaNum": "段落編號", "DE.Views.CrossReferenceDialog.textParaNumFull": "段落編號（全文）", "DE.Views.CrossReferenceDialog.textParaNumNo": "段落編號（無上下文）", "DE.Views.CrossReferenceDialog.textSeparate": "用分隔數字", "DE.Views.CrossReferenceDialog.textTable": "表格", "DE.Views.CrossReferenceDialog.textText": "段落文字", "DE.Views.CrossReferenceDialog.textWhich": "對於哪個標題", "DE.Views.CrossReferenceDialog.textWhichBookmark": "給哪個書籤", "DE.Views.CrossReferenceDialog.textWhichEndnote": "對於哪個尾註", "DE.Views.CrossReferenceDialog.textWhichHeading": "對於哪個標題", "DE.Views.CrossReferenceDialog.textWhichNote": "對於哪個腳註", "DE.Views.CrossReferenceDialog.textWhichPara": "對於哪個編號項目", "DE.Views.CrossReferenceDialog.txtReference": "插入對", "DE.Views.CrossReferenceDialog.txtTitle": "相互參照", "DE.Views.CrossReferenceDialog.txtType": "參考類型", "DE.Views.CustomColumnsDialog.textColumns": "列數", "DE.Views.CustomColumnsDialog.textSeparator": "欄位分隔線", "DE.Views.CustomColumnsDialog.textSpacing": "欄之前的距離", "DE.Views.CustomColumnsDialog.textTitle": "欄", "DE.Views.DateTimeDialog.confirmDefault": "設置{0}的預設格式：“ {1}”", "DE.Views.DateTimeDialog.textDefault": "設為預設", "DE.Views.DateTimeDialog.textFormat": "格式", "DE.Views.DateTimeDialog.textLang": "語言", "DE.Views.DateTimeDialog.textUpdate": "自動更新", "DE.Views.DateTimeDialog.txtTitle": "日期和時間", "DE.Views.DocumentHolder.aboveText": "以上", "DE.Views.DocumentHolder.addCommentText": "新增註解", "DE.Views.DocumentHolder.advancedDropCapText": "首字大寫設定", "DE.Views.DocumentHolder.advancedFrameText": "框的進階設置", "DE.Views.DocumentHolder.advancedParagraphText": "段落進階設置", "DE.Views.DocumentHolder.advancedTableText": "表格進階設定", "DE.Views.DocumentHolder.advancedText": "進階設定", "DE.Views.DocumentHolder.alignmentText": "對齊", "DE.Views.DocumentHolder.belowText": "之下", "DE.Views.DocumentHolder.breakBeforeText": "分頁之前", "DE.Views.DocumentHolder.bulletsText": "項目符和編號", "DE.Views.DocumentHolder.cellAlignText": "儲存格垂直對齊", "DE.Views.DocumentHolder.cellText": "儲存格", "DE.Views.DocumentHolder.centerText": "中心", "DE.Views.DocumentHolder.chartText": "圖表進階設置", "DE.Views.DocumentHolder.columnText": "欄", "DE.Views.DocumentHolder.deleteColumnText": "刪除欄位", "DE.Views.DocumentHolder.deleteRowText": "刪除行列", "DE.Views.DocumentHolder.deleteTableText": "刪除表格", "DE.Views.DocumentHolder.deleteText": "刪除", "DE.Views.DocumentHolder.direct270Text": "向上旋轉文字", "DE.Views.DocumentHolder.direct90Text": "向下旋轉文字", "DE.Views.DocumentHolder.directHText": "水平", "DE.Views.DocumentHolder.directionText": "文字方向", "DE.Views.DocumentHolder.editChartText": "編輯資料", "DE.Views.DocumentHolder.editFooterText": "編輯頁腳", "DE.Views.DocumentHolder.editHeaderText": "編輯標題", "DE.Views.DocumentHolder.editHyperlinkText": "編輯超連結", "DE.Views.DocumentHolder.guestText": "來賓帳戶", "DE.Views.DocumentHolder.hyperlinkText": "超連結", "DE.Views.DocumentHolder.ignoreAllSpellText": "忽略所有", "DE.Views.DocumentHolder.ignoreSpellText": "忽視", "DE.Views.DocumentHolder.imageText": "圖像進階設置", "DE.Views.DocumentHolder.insertColumnLeftText": "欄位以左", "DE.Views.DocumentHolder.insertColumnRightText": "欄位以右", "DE.Views.DocumentHolder.insertColumnText": "插入欄位", "DE.Views.DocumentHolder.insertRowAboveText": "上行", "DE.Views.DocumentHolder.insertRowBelowText": "下行", "DE.Views.DocumentHolder.insertRowText": "插入行", "DE.Views.DocumentHolder.insertText": "插入", "DE.Views.DocumentHolder.keepLinesText": "保持線條一致", "DE.Views.DocumentHolder.langText": "選擇語言", "DE.Views.DocumentHolder.leftText": "左", "DE.Views.DocumentHolder.loadSpellText": "正在加載變體...", "DE.Views.DocumentHolder.mergeCellsText": "合併儲存格", "DE.Views.DocumentHolder.moreText": "更多變體...", "DE.Views.DocumentHolder.noSpellVariantsText": "沒有變體", "DE.Views.DocumentHolder.notcriticalErrorTitle": "警告", "DE.Views.DocumentHolder.originalSizeText": "實際大小", "DE.Views.DocumentHolder.paragraphText": "段落", "DE.Views.DocumentHolder.removeHyperlinkText": "刪除超連結", "DE.Views.DocumentHolder.rightText": "右", "DE.Views.DocumentHolder.rowText": "行", "DE.Views.DocumentHolder.saveStyleText": "新增風格", "DE.Views.DocumentHolder.selectCellText": "選擇儲存格", "DE.Views.DocumentHolder.selectColumnText": "選擇欄", "DE.Views.DocumentHolder.selectRowText": "選擇列", "DE.Views.DocumentHolder.selectTableText": "選擇表格", "DE.Views.DocumentHolder.selectText": "選擇", "DE.Views.DocumentHolder.shapeText": "形狀進階設定", "DE.Views.DocumentHolder.spellcheckText": "拼字檢查", "DE.Views.DocumentHolder.splitCellsText": "分割儲存格...", "DE.Views.DocumentHolder.splitCellTitleText": "分割儲存格", "DE.Views.DocumentHolder.strDelete": "刪除簽名", "DE.Views.DocumentHolder.strDetails": "簽名細節", "DE.Views.DocumentHolder.strSetup": "簽名設置", "DE.Views.DocumentHolder.strSign": "簽名", "DE.Views.DocumentHolder.styleText": "轉換為風格", "DE.Views.DocumentHolder.tableText": "表格", "DE.Views.DocumentHolder.textAccept": "同意更新", "DE.Views.DocumentHolder.textAlign": "對齊", "DE.Views.DocumentHolder.textArrange": "安排", "DE.Views.DocumentHolder.textArrangeBack": "傳送到背景", "DE.Views.DocumentHolder.textArrangeBackward": "向後發送", "DE.Views.DocumentHolder.textArrangeForward": "向前進", "DE.Views.DocumentHolder.textArrangeFront": "移到前景", "DE.Views.DocumentHolder.textCells": "儲存格", "DE.Views.DocumentHolder.textCol": "刪除整列", "DE.Views.DocumentHolder.textContentControls": "內容控制", "DE.Views.DocumentHolder.textContinueNumbering": "繼續編號", "DE.Views.DocumentHolder.textCopy": "複製", "DE.Views.DocumentHolder.textCrop": "剪裁", "DE.Views.DocumentHolder.textCropFill": "填入", "DE.Views.DocumentHolder.textCropFit": "切合", "DE.Views.DocumentHolder.textCut": "剪下", "DE.Views.DocumentHolder.textDistributeCols": "分配列", "DE.Views.DocumentHolder.textDistributeRows": "分配行", "DE.Views.DocumentHolder.textEditControls": "內容控制設置", "DE.Views.DocumentHolder.textEditPoints": "編輯點", "DE.Views.DocumentHolder.textEditWrapBoundary": "編輯包裝邊界", "DE.Views.DocumentHolder.textFlipH": "水平翻轉", "DE.Views.DocumentHolder.textFlipV": "垂直翻轉", "DE.Views.DocumentHolder.textFollow": "跟隨移動", "DE.Views.DocumentHolder.textFromFile": "從檔案", "DE.Views.DocumentHolder.textFromStorage": "從存儲", "DE.Views.DocumentHolder.textFromUrl": "從 URL", "DE.Views.DocumentHolder.textJoinList": "加入上一個列表", "DE.Views.DocumentHolder.textLeft": "儲存格並向左移", "DE.Views.DocumentHolder.textNest": "套疊表格", "DE.Views.DocumentHolder.textNextPage": "下一頁", "DE.Views.DocumentHolder.textNumberingValue": "編號值", "DE.Views.DocumentHolder.textPaste": "貼上", "DE.Views.DocumentHolder.textPrevPage": "前一頁", "DE.Views.DocumentHolder.textRefreshField": "更新段落", "DE.Views.DocumentHolder.textReject": "駁回更新", "DE.Views.DocumentHolder.textRemCheckBox": "刪除複選框", "DE.Views.DocumentHolder.textRemComboBox": "刪除組合框", "DE.Views.DocumentHolder.textRemDropdown": "刪除下拉菜單", "DE.Views.DocumentHolder.textRemField": "刪除文字欄位", "DE.Views.DocumentHolder.textRemove": "移除", "DE.Views.DocumentHolder.textRemoveControl": "刪除內容控制", "DE.Views.DocumentHolder.textRemPicture": "移除圖片", "DE.Views.DocumentHolder.textRemRadioBox": "刪除單選按鈕", "DE.Views.DocumentHolder.textReplace": "取代圖片", "DE.Views.DocumentHolder.textRotate": "旋轉", "DE.Views.DocumentHolder.textRotate270": "逆時針旋轉90°", "DE.Views.DocumentHolder.textRotate90": "順時針旋轉90°", "DE.Views.DocumentHolder.textRow": "刪除整行", "DE.Views.DocumentHolder.textSeparateList": "單獨的清單", "DE.Views.DocumentHolder.textSettings": "設定", "DE.Views.DocumentHolder.textSeveral": "多行/多列", "DE.Views.DocumentHolder.textShapeAlignBottom": "底部對齊", "DE.Views.DocumentHolder.textShapeAlignCenter": "居中對齊", "DE.Views.DocumentHolder.textShapeAlignLeft": "對齊左側", "DE.Views.DocumentHolder.textShapeAlignMiddle": "中央對齊", "DE.Views.DocumentHolder.textShapeAlignRight": "對齊右側", "DE.Views.DocumentHolder.textShapeAlignTop": "上方對齊", "DE.Views.DocumentHolder.textStartNewList": "開始新清單", "DE.Views.DocumentHolder.textStartNumberingFrom": "設定編號值", "DE.Views.DocumentHolder.textTitleCellsRemove": "刪除儲存格", "DE.Views.DocumentHolder.textTOC": "目錄", "DE.Views.DocumentHolder.textTOCSettings": "目錄設置", "DE.Views.DocumentHolder.textUndo": "復原", "DE.Views.DocumentHolder.textUpdateAll": "更新整個表格", "DE.Views.DocumentHolder.textUpdatePages": "只更新頁碼", "DE.Views.DocumentHolder.textUpdateTOC": "更新目錄", "DE.Views.DocumentHolder.textWrap": "包覆風格", "DE.Views.DocumentHolder.tipIsLocked": "該元素當前正在由另一個帳戶編輯。", "DE.Views.DocumentHolder.toDictionaryText": "新增到字典", "DE.Views.DocumentHolder.txtAddBottom": "新增底部邊框", "DE.Views.DocumentHolder.txtAddFractionBar": "新增分數欄", "DE.Views.DocumentHolder.txtAddHor": "新增水平線", "DE.Views.DocumentHolder.txtAddLB": "新增左邊框", "DE.Views.DocumentHolder.txtAddLeft": "新增左邊框", "DE.Views.DocumentHolder.txtAddLT": "新增左頂行", "DE.Views.DocumentHolder.txtAddRight": "加入右邊框", "DE.Views.DocumentHolder.txtAddTop": "加入上邊框", "DE.Views.DocumentHolder.txtAddVer": "加入垂直線", "DE.Views.DocumentHolder.txtAlignToChar": "與角色對齊", "DE.Views.DocumentHolder.txtBehind": "文字在後", "DE.Views.DocumentHolder.txtBorderProps": "邊框屬性", "DE.Views.DocumentHolder.txtBottom": "底部", "DE.Views.DocumentHolder.txtColumnAlign": "欄位對準", "DE.Views.DocumentHolder.txtDecreaseArg": "減小參數大小", "DE.Views.DocumentHolder.txtDeleteArg": "刪除參數", "DE.Views.DocumentHolder.txtDeleteBreak": "刪除手動的斷行", "DE.Views.DocumentHolder.txtDeleteChars": "刪除封閉字符", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "刪除括起來的字符和分隔符", "DE.Views.DocumentHolder.txtDeleteEq": "刪除方程式", "DE.Views.DocumentHolder.txtDeleteGroupChar": "刪除字元", "DE.Views.DocumentHolder.txtDeleteRadical": "刪除部首", "DE.Views.DocumentHolder.txtDistribHor": "水平分佈", "DE.Views.DocumentHolder.txtDistribVert": "垂直分佈", "DE.Views.DocumentHolder.txtEmpty": "（空）", "DE.Views.DocumentHolder.txtFractionLinear": "變更為線性分數", "DE.Views.DocumentHolder.txtFractionSkewed": "變更為傾斜分數", "DE.Views.DocumentHolder.txtFractionStacked": "變更為堆積分數", "DE.Views.DocumentHolder.txtGroup": "群組", "DE.Views.DocumentHolder.txtGroupCharOver": "文字上的Char", "DE.Views.DocumentHolder.txtGroupCharUnder": "文字下的Char", "DE.Views.DocumentHolder.txtHideBottom": "隱藏底部邊框", "DE.Views.DocumentHolder.txtHideBottomLimit": "隱藏下限", "DE.Views.DocumentHolder.txtHideCloseBracket": "隱藏右括號", "DE.Views.DocumentHolder.txtHideDegree": "隱藏度", "DE.Views.DocumentHolder.txtHideHor": "隱藏水平線", "DE.Views.DocumentHolder.txtHideLB": "隱藏左底線", "DE.Views.DocumentHolder.txtHideLeft": "隱藏左邊框", "DE.Views.DocumentHolder.txtHideLT": "隱藏左頂行", "DE.Views.DocumentHolder.txtHideOpenBracket": "隱藏開口支架", "DE.Views.DocumentHolder.txtHidePlaceholder": "隱藏佔位符", "DE.Views.DocumentHolder.txtHideRight": "隱藏右邊框", "DE.Views.DocumentHolder.txtHideTop": "隱藏頂部邊框", "DE.Views.DocumentHolder.txtHideTopLimit": "隱藏最高限額", "DE.Views.DocumentHolder.txtHideVer": "隱藏垂直線", "DE.Views.DocumentHolder.txtIncreaseArg": "增加參數大小", "DE.Views.DocumentHolder.txtInFront": "文字在前", "DE.Views.DocumentHolder.txtInline": "與文字排列", "DE.Views.DocumentHolder.txtInsertArgAfter": "在後面插入參數", "DE.Views.DocumentHolder.txtInsertArgBefore": "在前面插入參數", "DE.Views.DocumentHolder.txtInsertBreak": "插入手動中斷", "DE.Views.DocumentHolder.txtInsertCaption": "插入標題", "DE.Views.DocumentHolder.txtInsertEqAfter": "在後面插入方程式", "DE.Views.DocumentHolder.txtInsertEqBefore": "在前面插入方程式", "DE.Views.DocumentHolder.txtKeepTextOnly": "僅保留文字", "DE.Views.DocumentHolder.txtLimitChange": "變更限制位置", "DE.Views.DocumentHolder.txtLimitOver": "文字限制", "DE.Views.DocumentHolder.txtLimitUnder": "文字下的限制", "DE.Views.DocumentHolder.txtMatchBrackets": "將括號匹配到參數高度", "DE.Views.DocumentHolder.txtMatrixAlign": "矩陣對齊", "DE.Views.DocumentHolder.txtOverbar": "槓覆蓋文字", "DE.Views.DocumentHolder.txtOverwriteCells": "覆蓋儲存格", "DE.Views.DocumentHolder.txtPasteSourceFormat": "保持源格式", "DE.Views.DocumentHolder.txtPressLink": "按{0}並單擊連結", "DE.Views.DocumentHolder.txtPrintSelection": "列印選擇", "DE.Views.DocumentHolder.txtRemFractionBar": "刪除分數欄", "DE.Views.DocumentHolder.txtRemLimit": "取消限制", "DE.Views.DocumentHolder.txtRemoveAccentChar": "刪除強調字符", "DE.Views.DocumentHolder.txtRemoveBar": "移除欄", "DE.Views.DocumentHolder.txtRemoveWarning": "確定移除此簽名?<br>這動作無法復原.", "DE.Views.DocumentHolder.txtRemScripts": "刪除腳本", "DE.Views.DocumentHolder.txtRemSubscript": "刪除下標", "DE.Views.DocumentHolder.txtRemSuperscript": "刪除上標", "DE.Views.DocumentHolder.txtScriptsAfter": "文字後的文字", "DE.Views.DocumentHolder.txtScriptsBefore": "文字前的腳本", "DE.Views.DocumentHolder.txtShowBottomLimit": "顯示底限", "DE.Views.DocumentHolder.txtShowCloseBracket": "顯示結束括號", "DE.Views.DocumentHolder.txtShowDegree": "顯示程度", "DE.Views.DocumentHolder.txtShowOpenBracket": "顯示開口支架", "DE.Views.DocumentHolder.txtShowPlaceholder": "顯示佔位符", "DE.Views.DocumentHolder.txtShowTopLimit": "顯示最高限額", "DE.Views.DocumentHolder.txtSquare": "正方形", "DE.Views.DocumentHolder.txtStretchBrackets": "延伸括號", "DE.Views.DocumentHolder.txtThrough": "通過", "DE.Views.DocumentHolder.txtTight": "緊", "DE.Views.DocumentHolder.txtTop": "上方", "DE.Views.DocumentHolder.txtTopAndBottom": "頂部和底部", "DE.Views.DocumentHolder.txtUnderbar": "槓至文字底下", "DE.Views.DocumentHolder.txtUngroup": "解開組合", "DE.Views.DocumentHolder.txtWarnUrl": "這連結可能對您的設備和資料造成損害。<br> 您確定要繼續嗎？", "DE.Views.DocumentHolder.updateStyleText": "更新％1風格", "DE.Views.DocumentHolder.vertAlignText": "垂直對齊", "DE.Views.DropcapSettingsAdvanced.strBorders": "邊框和添入", "DE.Views.DropcapSettingsAdvanced.strDropcap": "首字大寫", "DE.Views.DropcapSettingsAdvanced.strMargins": "邊框", "DE.Views.DropcapSettingsAdvanced.textAlign": "對齊", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "至少", "DE.Views.DropcapSettingsAdvanced.textAuto": "自動", "DE.Views.DropcapSettingsAdvanced.textBackColor": "背景顏色", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "邊框顏色", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "點擊圖表或使用按鈕選擇邊框", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "邊框大小", "DE.Views.DropcapSettingsAdvanced.textBottom": "底部", "DE.Views.DropcapSettingsAdvanced.textCenter": "中心", "DE.Views.DropcapSettingsAdvanced.textColumn": "欄", "DE.Views.DropcapSettingsAdvanced.textDistance": "與文字的距離", "DE.Views.DropcapSettingsAdvanced.textExact": "準確", "DE.Views.DropcapSettingsAdvanced.textFlow": "流框", "DE.Views.DropcapSettingsAdvanced.textFont": "字體", "DE.Views.DropcapSettingsAdvanced.textFrame": "框", "DE.Views.DropcapSettingsAdvanced.textHeight": "高度", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "水平", "DE.Views.DropcapSettingsAdvanced.textInline": "內聯框架", "DE.Views.DropcapSettingsAdvanced.textInMargin": "在邊框內", "DE.Views.DropcapSettingsAdvanced.textInText": "字段內", "DE.Views.DropcapSettingsAdvanced.textLeft": "左", "DE.Views.DropcapSettingsAdvanced.textMargin": "邊框", "DE.Views.DropcapSettingsAdvanced.textMove": "與文字移動", "DE.Views.DropcapSettingsAdvanced.textNone": "無", "DE.Views.DropcapSettingsAdvanced.textPage": "頁面", "DE.Views.DropcapSettingsAdvanced.textParagraph": "段落", "DE.Views.DropcapSettingsAdvanced.textParameters": "參量", "DE.Views.DropcapSettingsAdvanced.textPosition": "位置", "DE.Views.DropcapSettingsAdvanced.textRelative": "關係到", "DE.Views.DropcapSettingsAdvanced.textRight": "右", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "行高", "DE.Views.DropcapSettingsAdvanced.textTitle": "首字大寫-進階設定", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "框-進階設置", "DE.Views.DropcapSettingsAdvanced.textTop": "上方", "DE.Views.DropcapSettingsAdvanced.textVertical": "垂直", "DE.Views.DropcapSettingsAdvanced.textWidth": "寬度", "DE.Views.DropcapSettingsAdvanced.tipFontName": "字體", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "無邊框", "DE.Views.EditListItemDialog.textDisplayName": "顯示名稱", "DE.Views.EditListItemDialog.textNameError": "顯示名稱不能為空。", "DE.Views.EditListItemDialog.textValue": "值", "DE.Views.EditListItemDialog.textValueError": "具有相同值的項目已存在。", "DE.Views.FileMenu.btnBackCaption": "打開文件所在位置", "DE.Views.FileMenu.btnCloseMenuCaption": "關閉選單", "DE.Views.FileMenu.btnCreateNewCaption": "新增", "DE.Views.FileMenu.btnDownloadCaption": "下載為", "DE.Views.FileMenu.btnExitCaption": "關閉", "DE.Views.FileMenu.btnFileOpenCaption": "開啟", "DE.Views.FileMenu.btnHelpCaption": "幫助", "DE.Views.FileMenu.btnHistoryCaption": "版本歷史", "DE.Views.FileMenu.btnInfoCaption": "文件資訊", "DE.Views.FileMenu.btnPrintCaption": "列印", "DE.Views.FileMenu.btnProtectCaption": "保護", "DE.Views.FileMenu.btnRecentFilesCaption": "打開最近", "DE.Views.FileMenu.btnRenameCaption": "改名", "DE.Views.FileMenu.btnReturnCaption": "返回到文件", "DE.Views.FileMenu.btnRightsCaption": "存取權限", "DE.Views.FileMenu.btnSaveAsCaption": "另存為", "DE.Views.FileMenu.btnSaveCaption": "存檔", "DE.Views.FileMenu.btnSaveCopyAsCaption": "另存新檔為", "DE.Views.FileMenu.btnSettingsCaption": "進階設定", "DE.Views.FileMenu.btnToEditCaption": "編輯文件", "DE.Views.FileMenu.textDownload": "下載", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "空白文檔", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新增", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "套用", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "新增作者", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "新增文字", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "應用程式", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "變更存取權限", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "註解", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已建立", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "快速Web預覽", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "載入中...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最後修改者", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上一次更改", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "沒有", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "擁有者", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "頁", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "頁面大小", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "段落", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF產生器", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "已標記為PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF版本", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "有權利的人", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "帶空格的符號", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "統計", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主旨", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "符號", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "標題", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "\n已上傳", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "文字", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "是", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "變更存取權限", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "有權利的人", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "帶密碼", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "受保護的文件", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "帶簽名", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "編輯文件", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編輯將刪除文檔中的簽名。<br>確定要繼續嗎？", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "本文件已受密碼保護", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "該文件需要簽名。", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效簽名已增加到文件檔中。該文件檔受到保護，無法編輯。", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "文檔中的某些數字簽名無效或無法驗證。該文檔受到保護，無法編輯。", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "查看簽名", "DE.Views.FileMenuPanels.Settings.okButtonText": "套用", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同編輯模式", "DE.Views.FileMenuPanels.Settings.strFast": "快", "DE.Views.FileMenuPanels.Settings.strFontRender": "字體提示", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "忽略大寫單詞", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "忽略帶數字的單詞", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "巨集設定", "DE.Views.FileMenuPanels.Settings.strPasteButton": "粘貼內容時顯示“粘貼選項”按鈕", "DE.Views.FileMenuPanels.Settings.strShowChanges": "即時共同編輯設定更新", "DE.Views.FileMenuPanels.Settings.strShowComments": "在內文中顯示註釋", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "顯示其他用戶的更改", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "顯示已解決的註釋", "DE.Views.FileMenuPanels.Settings.strStrict": "嚴格", "DE.Views.FileMenuPanels.Settings.strTheme": "介面主題", "DE.Views.FileMenuPanels.Settings.strUnit": "測量單位", "DE.Views.FileMenuPanels.Settings.strZoom": "預設縮放", "DE.Views.FileMenuPanels.Settings.text10Minutes": "每10分鐘", "DE.Views.FileMenuPanels.Settings.text30Minutes": "每30分鐘", "DE.Views.FileMenuPanels.Settings.text5Minutes": "每5分鐘", "DE.Views.FileMenuPanels.Settings.text60Minutes": "每一小時", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "對齊指南", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "自動恢復", "DE.Views.FileMenuPanels.Settings.textAutoSave": "自動存檔", "DE.Views.FileMenuPanels.Settings.textDisabled": "已停用", "DE.Views.FileMenuPanels.Settings.textForceSave": "儲存所有歷史版本到伺服器", "DE.Views.FileMenuPanels.Settings.textMinute": "每一分鐘", "DE.Views.FileMenuPanels.Settings.textOldVersions": "儲存為DOCX時，使文件與舊版MS Word兼容", "DE.Views.FileMenuPanels.Settings.txtAll": "查看全部", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "自動更正選項...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "預設緩存模式", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "點擊氣球而展示", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "懸停在工具提示而展示", "DE.Views.FileMenuPanels.Settings.txtCm": "公分", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "共同編輯", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "開啟文件夜間模式", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "編輯並儲存", "DE.Views.FileMenuPanels.Settings.txtFastTip": "實時共同編輯，所有變更將自動儲存。", "DE.Views.FileMenuPanels.Settings.txtFitPage": "切合至頁面", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "切合至寬度", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "特殊符號", "DE.Views.FileMenuPanels.Settings.txtInch": "吋", "DE.Views.FileMenuPanels.Settings.txtLast": "查看最後", "DE.Views.FileMenuPanels.Settings.txtMac": "作為OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "本機", "DE.Views.FileMenuPanels.Settings.txtNone": "查看無", "DE.Views.FileMenuPanels.Settings.txtProofing": "打樣", "DE.Views.FileMenuPanels.Settings.txtPt": "點", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "全部啟用", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "不用提示啟用全部巨集", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "顯示追蹤更改", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "拼字檢查", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "全部停用", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "不用提示停用全部巨集", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "使用儲存鍵來同步你和其他用戶的變更", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "使用Alt鍵來操控用戶介面", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "使用Option鍵來操控用戶介面", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "顯示通知", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "以提示停用全部巨集", "DE.Views.FileMenuPanels.Settings.txtWin": "作為Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "工作空間", "DE.Views.FormSettings.textAlways": "永遠", "DE.Views.FormSettings.textAspect": "鎖定寬高比", "DE.Views.FormSettings.textAtLeast": "至少", "DE.Views.FormSettings.textAuto": "自動", "DE.Views.FormSettings.textAutofit": "自動調整", "DE.Views.FormSettings.textBackgroundColor": "背景顏色", "DE.Views.FormSettings.textCheckbox": "複選框", "DE.Views.FormSettings.textColor": "邊框顏色", "DE.Views.FormSettings.textComb": "文字組合", "DE.Views.FormSettings.textCombobox": "組合框", "DE.Views.FormSettings.textConnected": "段落已連結", "DE.Views.FormSettings.textDelete": "刪除", "DE.Views.FormSettings.textDigits": "數字", "DE.Views.FormSettings.textDisconnect": "斷線", "DE.Views.FormSettings.textDropDown": "下拉式", "DE.Views.FormSettings.textExact": "準確的", "DE.Views.FormSettings.textField": "文字段落", "DE.Views.FormSettings.textFixed": "固定欄位大小", "DE.Views.FormSettings.textFormat": "格式", "DE.Views.FormSettings.textFromFile": "從檔案", "DE.Views.FormSettings.textFromStorage": "從存儲", "DE.Views.FormSettings.textFromUrl": "從 URL", "DE.Views.FormSettings.textGroupKey": "組密鑰", "DE.Views.FormSettings.textImage": "圖像", "DE.Views.FormSettings.textKey": "鍵", "DE.Views.FormSettings.textLock": "鎖", "DE.Views.FormSettings.textMaxChars": "文字數限制", "DE.Views.FormSettings.textMulti": "多行文字欄位", "DE.Views.FormSettings.textNever": "永不", "DE.Views.FormSettings.textNoBorder": "無邊界", "DE.Views.FormSettings.textNone": "無", "DE.Views.FormSettings.textPlaceholder": "佔位符", "DE.Views.FormSettings.textRadiobox": "收音機按鈕", "DE.Views.FormSettings.textRequired": "必要", "DE.Views.FormSettings.textScale": "何時縮放", "DE.Views.FormSettings.textSelectImage": "選擇圖片", "DE.Views.FormSettings.textTag": "標籤", "DE.Views.FormSettings.textTip": "頂點", "DE.Views.FormSettings.textTipAdd": "增加新值", "DE.Views.FormSettings.textTipDelete": "刪除值", "DE.Views.FormSettings.textTipDown": "下移", "DE.Views.FormSettings.textTipUp": "上移", "DE.Views.FormSettings.textTooBig": "圖像過大", "DE.Views.FormSettings.textTooSmall": "圖像過小", "DE.Views.FormSettings.textUnlock": "開鎖", "DE.Views.FormSettings.textValue": "值選項", "DE.Views.FormSettings.textWidth": "儲存格寬度", "DE.Views.FormsTab.capBtnCheckBox": "複選框", "DE.Views.FormsTab.capBtnComboBox": "組合框", "DE.Views.FormsTab.capBtnDownloadForm": "下載為oform", "DE.Views.FormsTab.capBtnDropDown": "下拉式", "DE.Views.FormsTab.capBtnEmail": "電子郵件地址", "DE.Views.FormsTab.capBtnImage": "圖像", "DE.Views.FormsTab.capBtnNext": "下一欄位", "DE.Views.FormsTab.capBtnPhone": "電話號碼", "DE.Views.FormsTab.capBtnPrev": "上一欄位", "DE.Views.FormsTab.capBtnRadioBox": "收音機按鈕", "DE.Views.FormsTab.capBtnSaveForm": "另存oform檔", "DE.Views.FormsTab.capBtnSubmit": "傳送", "DE.Views.FormsTab.capBtnText": "文字段落", "DE.Views.FormsTab.capBtnView": "查看表格", "DE.Views.FormsTab.textClear": "清除欄位", "DE.Views.FormsTab.textClearFields": "清除所有段落", "DE.Views.FormsTab.textCreateForm": "新增文字段落並建立一個可填寫的 OFORM 文件", "DE.Views.FormsTab.textGotIt": "我瞭解了", "DE.Views.FormsTab.textHighlight": "強調顯示設置", "DE.Views.FormsTab.textNoHighlight": "沒有突出顯示", "DE.Views.FormsTab.textRequired": "填寫所有必填欄位以發送表單。", "DE.Views.FormsTab.textSubmited": "表格傳送成功", "DE.Views.FormsTab.tipCheckBox": "插入複選框", "DE.Views.FormsTab.tipComboBox": "插入組合框", "DE.Views.FormsTab.tipDownloadForm": "下載成可編輯OFORM文件", "DE.Views.FormsTab.tipDropDown": "插入下拉列表", "DE.Views.FormsTab.tipImageField": "插入圖片", "DE.Views.FormsTab.tipNextForm": "移至下一欄位", "DE.Views.FormsTab.tipPrevForm": "移至上一欄位", "DE.Views.FormsTab.tipRadioBox": "插入收音機按鈕", "DE.Views.FormsTab.tipSaveForm": "儲存一份可以填寫的 OFORM 檔案", "DE.Views.FormsTab.tipSubmit": "傳送表格", "DE.Views.FormsTab.tipTextField": "插入文字欄位", "DE.Views.FormsTab.tipViewForm": "查看表格", "DE.Views.FormsTab.txtUntitled": "無標題", "DE.Views.HeaderFooterSettings.textBottomCenter": "底部中間", "DE.Views.HeaderFooterSettings.textBottomLeft": "左下方", "DE.Views.HeaderFooterSettings.textBottomPage": "頁底", "DE.Views.HeaderFooterSettings.textBottomRight": "右下方", "DE.Views.HeaderFooterSettings.textDiffFirst": "首頁不同", "DE.Views.HeaderFooterSettings.textDiffOdd": "單/雙數頁不同", "DE.Views.HeaderFooterSettings.textFrom": "開始", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "底部的頁腳", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "標頭從上方", "DE.Views.HeaderFooterSettings.textInsertCurrent": "插入到當前位置", "DE.Views.HeaderFooterSettings.textOptions": "選項", "DE.Views.HeaderFooterSettings.textPageNum": "插入頁碼", "DE.Views.HeaderFooterSettings.textPageNumbering": "頁編碼", "DE.Views.HeaderFooterSettings.textPosition": "位置", "DE.Views.HeaderFooterSettings.textPrev": "從上個部份繼續", "DE.Views.HeaderFooterSettings.textSameAs": "連接到上一個", "DE.Views.HeaderFooterSettings.textTopCenter": "頂部中心", "DE.Views.HeaderFooterSettings.textTopLeft": "左上方", "DE.Views.HeaderFooterSettings.textTopPage": "頁面頂部", "DE.Views.HeaderFooterSettings.textTopRight": "右上", "DE.Views.HyperlinkSettingsDialog.textDefault": "所選文字片段", "DE.Views.HyperlinkSettingsDialog.textDisplay": "顯示", "DE.Views.HyperlinkSettingsDialog.textExternal": "外部連結", "DE.Views.HyperlinkSettingsDialog.textInternal": "放置在文件中", "DE.Views.HyperlinkSettingsDialog.textTitle": "超連結設置", "DE.Views.HyperlinkSettingsDialog.textTooltip": "屏幕提示文字", "DE.Views.HyperlinkSettingsDialog.textUrl": "連結至", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "文件的開頭", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "書籤", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "這是必填欄", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "標題", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "此段落應為“ http://www.example.com”格式的網址", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此欄位限2083字符", "DE.Views.ImageSettings.textAdvanced": "顯示進階設定", "DE.Views.ImageSettings.textCrop": "剪裁", "DE.Views.ImageSettings.textCropFill": "填入", "DE.Views.ImageSettings.textCropFit": "切合", "DE.Views.ImageSettings.textCropToShape": "剪裁成圖形", "DE.Views.ImageSettings.textEdit": "編輯", "DE.Views.ImageSettings.textEditObject": "編輯物件", "DE.Views.ImageSettings.textFitMargins": "切合至邊框", "DE.Views.ImageSettings.textFlip": "翻轉", "DE.Views.ImageSettings.textFromFile": "從檔案", "DE.Views.ImageSettings.textFromStorage": "從存儲", "DE.Views.ImageSettings.textFromUrl": "從 URL", "DE.Views.ImageSettings.textHeight": "高度", "DE.Views.ImageSettings.textHint270": "逆時針旋轉90°", "DE.Views.ImageSettings.textHint90": "順時針旋轉90°", "DE.Views.ImageSettings.textHintFlipH": "水平翻轉", "DE.Views.ImageSettings.textHintFlipV": "垂直翻轉", "DE.Views.ImageSettings.textInsert": "取代圖片", "DE.Views.ImageSettings.textOriginalSize": "實際大小", "DE.Views.ImageSettings.textRecentlyUsed": "最近使用", "DE.Views.ImageSettings.textRotate90": "旋轉90°", "DE.Views.ImageSettings.textRotation": "旋轉", "DE.Views.ImageSettings.textSize": "大小", "DE.Views.ImageSettings.textWidth": "寬度", "DE.Views.ImageSettings.textWrap": "包覆風格", "DE.Views.ImageSettings.txtBehind": "文字在後", "DE.Views.ImageSettings.txtInFront": "文字在前", "DE.Views.ImageSettings.txtInline": "與文字排列", "DE.Views.ImageSettings.txtSquare": "正方形", "DE.Views.ImageSettings.txtThrough": "通過", "DE.Views.ImageSettings.txtTight": "緊", "DE.Views.ImageSettings.txtTopAndBottom": "頂部和底部", "DE.Views.ImageSettingsAdvanced.strMargins": "文字填充", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "絕對", "DE.Views.ImageSettingsAdvanced.textAlignment": "對齊", "DE.Views.ImageSettingsAdvanced.textAlt": "替代文字", "DE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "DE.Views.ImageSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "DE.Views.ImageSettingsAdvanced.textAltTitle": "標題", "DE.Views.ImageSettingsAdvanced.textAngle": "角度", "DE.Views.ImageSettingsAdvanced.textArrows": "箭頭", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "鎖定寬高比", "DE.Views.ImageSettingsAdvanced.textAutofit": "自動調整", "DE.Views.ImageSettingsAdvanced.textBeginSize": "開始大小", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "開始風格", "DE.Views.ImageSettingsAdvanced.textBelow": "之下", "DE.Views.ImageSettingsAdvanced.textBevel": "斜角", "DE.Views.ImageSettingsAdvanced.textBottom": "底部", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "底邊距", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "文字包裝", "DE.Views.ImageSettingsAdvanced.textCapType": "Cap 類型", "DE.Views.ImageSettingsAdvanced.textCenter": "中心", "DE.Views.ImageSettingsAdvanced.textCharacter": "文字", "DE.Views.ImageSettingsAdvanced.textColumn": "欄", "DE.Views.ImageSettingsAdvanced.textDistance": "與文字的距離", "DE.Views.ImageSettingsAdvanced.textEndSize": "端部尺寸", "DE.Views.ImageSettingsAdvanced.textEndStyle": "結束風格", "DE.Views.ImageSettingsAdvanced.textFlat": "平面", "DE.Views.ImageSettingsAdvanced.textFlipped": "已翻轉", "DE.Views.ImageSettingsAdvanced.textHeight": "高度", "DE.Views.ImageSettingsAdvanced.textHorizontal": "水平", "DE.Views.ImageSettingsAdvanced.textHorizontally": "水平地", "DE.Views.ImageSettingsAdvanced.textJoinType": "加入類型", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "比例不變", "DE.Views.ImageSettingsAdvanced.textLeft": "左", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "左邊距", "DE.Views.ImageSettingsAdvanced.textLine": "線", "DE.Views.ImageSettingsAdvanced.textLineStyle": "線型", "DE.Views.ImageSettingsAdvanced.textMargin": "邊框", "DE.Views.ImageSettingsAdvanced.textMiter": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMove": "用文字移動對象", "DE.Views.ImageSettingsAdvanced.textOptions": "選項", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "實際大小", "DE.Views.ImageSettingsAdvanced.textOverlap": "允許重疊", "DE.Views.ImageSettingsAdvanced.textPage": "頁面", "DE.Views.ImageSettingsAdvanced.textParagraph": "段落", "DE.Views.ImageSettingsAdvanced.textPosition": "位置", "DE.Views.ImageSettingsAdvanced.textPositionPc": "相對位置", "DE.Views.ImageSettingsAdvanced.textRelative": "關係到", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "相對的", "DE.Views.ImageSettingsAdvanced.textResizeFit": "調整形狀以適合文本", "DE.Views.ImageSettingsAdvanced.textRight": "右", "DE.Views.ImageSettingsAdvanced.textRightMargin": "右邊距", "DE.Views.ImageSettingsAdvanced.textRightOf": "在 - 的右邊", "DE.Views.ImageSettingsAdvanced.textRotation": "旋轉", "DE.Views.ImageSettingsAdvanced.textRound": "圓", "DE.Views.ImageSettingsAdvanced.textShape": "形狀設定", "DE.Views.ImageSettingsAdvanced.textSize": "大小", "DE.Views.ImageSettingsAdvanced.textSquare": "正方形", "DE.Views.ImageSettingsAdvanced.textTextBox": "文字框", "DE.Views.ImageSettingsAdvanced.textTitle": "圖像-進階設置", "DE.Views.ImageSettingsAdvanced.textTitleChart": "圖表-進階設置", "DE.Views.ImageSettingsAdvanced.textTitleShape": "形狀 - 進階設定", "DE.Views.ImageSettingsAdvanced.textTop": "上方", "DE.Views.ImageSettingsAdvanced.textTopMargin": "頂部邊框", "DE.Views.ImageSettingsAdvanced.textVertical": "垂直", "DE.Views.ImageSettingsAdvanced.textVertically": "垂直", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "重量和箭頭", "DE.Views.ImageSettingsAdvanced.textWidth": "寬度", "DE.Views.ImageSettingsAdvanced.textWrap": "包覆風格", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "文字在後", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "文字在前", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "與文字排列", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "正方形", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "通過", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "緊", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "頂部和底部", "DE.Views.LeftMenu.tipAbout": "關於", "DE.Views.LeftMenu.tipChat": "聊天", "DE.Views.LeftMenu.tipComments": "註解", "DE.Views.LeftMenu.tipNavigation": "導航", "DE.Views.LeftMenu.tipOutline": "標題", "DE.Views.LeftMenu.tipPlugins": "外掛程式", "DE.Views.LeftMenu.tipSearch": "搜尋", "DE.Views.LeftMenu.tipSupport": "反饋與支持", "DE.Views.LeftMenu.tipTitles": "標題", "DE.Views.LeftMenu.txtDeveloper": "開發者模式", "DE.Views.LeftMenu.txtEditor": "文字編輯器", "DE.Views.LeftMenu.txtLimit": "限制存取", "DE.Views.LeftMenu.txtTrial": "試用模式", "DE.Views.LeftMenu.txtTrialDev": "試用開發人員模式", "DE.Views.LineNumbersDialog.textAddLineNumbering": "添加行號", "DE.Views.LineNumbersDialog.textApplyTo": "套用更改", "DE.Views.LineNumbersDialog.textContinuous": "連續", "DE.Views.LineNumbersDialog.textCountBy": "計數", "DE.Views.LineNumbersDialog.textDocument": "整個文檔", "DE.Views.LineNumbersDialog.textForward": "這一點向前", "DE.Views.LineNumbersDialog.textFromText": "來自文字", "DE.Views.LineNumbersDialog.textNumbering": "編號項目", "DE.Views.LineNumbersDialog.textRestartEachPage": "重新開始每一頁", "DE.Views.LineNumbersDialog.textRestartEachSection": "重新開始每個部分", "DE.Views.LineNumbersDialog.textSection": "當前部分", "DE.Views.LineNumbersDialog.textStartAt": "開始", "DE.Views.LineNumbersDialog.textTitle": "行號", "DE.Views.LineNumbersDialog.txtAutoText": "自動", "DE.Views.Links.capBtnAddText": "添加文字", "DE.Views.Links.capBtnBookmarks": "書籤", "DE.Views.Links.capBtnCaption": "標題", "DE.Views.Links.capBtnContentsUpdate": "更新表格", "DE.Views.Links.capBtnCrossRef": "相互參照", "DE.Views.Links.capBtnInsContents": "目錄", "DE.Views.Links.capBtnInsFootnote": "註腳", "DE.Views.Links.capBtnInsLink": "超連結", "DE.Views.Links.capBtnTOF": "圖表", "DE.Views.Links.confirmDeleteFootnotes": "您要刪除所有腳註嗎？", "DE.Views.Links.confirmReplaceTOF": "您要替換選定的數字表嗎？", "DE.Views.Links.mniConvertNote": "轉換所有筆記", "DE.Views.Links.mniDelFootnote": "刪除所有筆記", "DE.Views.Links.mniInsEndnote": "插入尾註", "DE.Views.Links.mniInsFootnote": "插入註腳", "DE.Views.Links.mniNoteSettings": "筆記設置", "DE.Views.Links.textContentsRemove": "刪除目錄", "DE.Views.Links.textContentsSettings": "設定", "DE.Views.Links.textConvertToEndnotes": "將所有腳註轉換為尾註", "DE.Views.Links.textConvertToFootnotes": "將所有尾註轉換為腳註", "DE.Views.Links.textGotoEndnote": "轉到尾註", "DE.Views.Links.textGotoFootnote": "轉到腳註", "DE.Views.Links.textSwapNotes": "交換腳註和尾註", "DE.Views.Links.textUpdateAll": "更新整個表格", "DE.Views.Links.textUpdatePages": "只更新頁碼", "DE.Views.Links.tipAddText": "包含標題在目錄裡", "DE.Views.Links.tipBookmarks": "創建一個書籤", "DE.Views.Links.tipCaption": "插入標題", "DE.Views.Links.tipContents": "插入目錄", "DE.Views.Links.tipContentsUpdate": "更新目錄", "DE.Views.Links.tipCrossRef": "插入交叉參考", "DE.Views.Links.tipInsertHyperlink": "新增超連結", "DE.Views.Links.tipNotes": "插入或編輯腳註", "DE.Views.Links.tipTableFigures": "插入圖表", "DE.Views.Links.tipTableFiguresUpdate": "更新目錄圖", "DE.Views.Links.titleUpdateTOF": "更新目錄圖", "DE.Views.Links.txtDontShowTof": "不要顯示在目錄", "DE.Views.Links.txtLevel": "等級", "DE.Views.ListSettingsDialog.textAuto": "自動", "DE.Views.ListSettingsDialog.textCenter": "中心", "DE.Views.ListSettingsDialog.textLeft": "左", "DE.Views.ListSettingsDialog.textLevel": "水平", "DE.Views.ListSettingsDialog.textPreview": "預覽", "DE.Views.ListSettingsDialog.textRight": "右", "DE.Views.ListSettingsDialog.txtAlign": "對齊", "DE.Views.ListSettingsDialog.txtBullet": "項目點", "DE.Views.ListSettingsDialog.txtColor": "顏色", "DE.Views.ListSettingsDialog.txtFont": "字體和符號", "DE.Views.ListSettingsDialog.txtLikeText": "像文字", "DE.Views.ListSettingsDialog.txtNewBullet": "新子彈點", "DE.Views.ListSettingsDialog.txtNone": "無", "DE.Views.ListSettingsDialog.txtSize": "大小", "DE.Views.ListSettingsDialog.txtSymbol": "符號", "DE.Views.ListSettingsDialog.txtTitle": "清單設定", "DE.Views.ListSettingsDialog.txtType": "類型", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF格式", "DE.Views.MailMergeEmailDlg.okButtonText": "傳送", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "主題", "DE.Views.MailMergeEmailDlg.textAttachDocx": "附加為DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "附件為PDF", "DE.Views.MailMergeEmailDlg.textFileName": "檔案名稱", "DE.Views.MailMergeEmailDlg.textFormat": "郵件格式", "DE.Views.MailMergeEmailDlg.textFrom": "自", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "訊息", "DE.Views.MailMergeEmailDlg.textSubject": "主題行", "DE.Views.MailMergeEmailDlg.textTitle": "傳送到電子郵件", "DE.Views.MailMergeEmailDlg.textTo": "到", "DE.Views.MailMergeEmailDlg.textWarning": "警告！", "DE.Views.MailMergeEmailDlg.textWarningMsg": "請注意，單擊“發送”按鈕後就無法停止郵寄。", "DE.Views.MailMergeSettings.downloadMergeTitle": "合併", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "合併失敗.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "警告", "DE.Views.MailMergeSettings.textAddRecipients": "首先將一些收件人添加到列表中", "DE.Views.MailMergeSettings.textAll": "所有記錄", "DE.Views.MailMergeSettings.textCurrent": "當前記錄", "DE.Views.MailMergeSettings.textDataSource": "數據源", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "下載", "DE.Views.MailMergeSettings.textEditData": "編輯收件人列表", "DE.Views.MailMergeSettings.textEmail": "電子郵件", "DE.Views.MailMergeSettings.textFrom": "自", "DE.Views.MailMergeSettings.textGoToMail": "轉到郵件", "DE.Views.MailMergeSettings.textHighlight": "突出顯示合併字段", "DE.Views.MailMergeSettings.textInsertField": "插入合併字段", "DE.Views.MailMergeSettings.textMaxRecepients": "最多100個收件人。", "DE.Views.MailMergeSettings.textMerge": "合併", "DE.Views.MailMergeSettings.textMergeFields": "合併欄位", "DE.Views.MailMergeSettings.textMergeTo": "合併到", "DE.Views.MailMergeSettings.textPdf": "PDF格式", "DE.Views.MailMergeSettings.textPortal": "存檔", "DE.Views.MailMergeSettings.textPreview": "預覽結果", "DE.Views.MailMergeSettings.textReadMore": "瞭解更多", "DE.Views.MailMergeSettings.textSendMsg": "所有郵件均已準備就緒，將在一段時間內發送出去。<br>郵件的發送速度取決於您的郵件服務。<br>您可以繼續使用文檔或將其關閉。操作結束後，通知將發送到您的註冊電子郵件地址。", "DE.Views.MailMergeSettings.textTo": "到", "DE.Views.MailMergeSettings.txtFirst": "到第一個記錄", "DE.Views.MailMergeSettings.txtFromToError": "\"從\"值必須小於\"到\"值", "DE.Views.MailMergeSettings.txtLast": "到最後記錄", "DE.Views.MailMergeSettings.txtNext": "到下一條記錄", "DE.Views.MailMergeSettings.txtPrev": "到之前的紀錄", "DE.Views.MailMergeSettings.txtUntitled": "無標題", "DE.Views.MailMergeSettings.warnProcessMailMerge": "開始合併失敗", "DE.Views.Navigation.strNavigate": "標題", "DE.Views.Navigation.txtClosePanel": "關閉標題", "DE.Views.Navigation.txtCollapse": "全部收縮", "DE.Views.Navigation.txtDemote": "降級", "DE.Views.Navigation.txtEmpty": "文件中沒有標題。<br>應用一個標題風格，以便出現在目錄中。", "DE.Views.Navigation.txtEmptyItem": "空標題", "DE.Views.Navigation.txtEmptyViewer": "文件中沒有標題。", "DE.Views.Navigation.txtExpand": "展開全部", "DE.Views.Navigation.txtExpandToLevel": "擴展到水平", "DE.Views.Navigation.txtFontSize": "字體大小", "DE.Views.Navigation.txtHeadingAfter": "之後的新標題", "DE.Views.Navigation.txtHeadingBefore": "之前的新標題", "DE.Views.Navigation.txtLarge": "大", "DE.Views.Navigation.txtMedium": "中", "DE.Views.Navigation.txtNewHeading": "新副標題", "DE.Views.Navigation.txtPromote": "促進", "DE.Views.Navigation.txtSelect": "選擇內容", "DE.Views.Navigation.txtSettings": "標題設置", "DE.Views.Navigation.txtSmall": "小", "DE.Views.Navigation.txtWrapHeadings": "包覆長的標題", "DE.Views.NoteSettingsDialog.textApply": "套用", "DE.Views.NoteSettingsDialog.textApplyTo": "套用更改", "DE.Views.NoteSettingsDialog.textContinue": "連續", "DE.Views.NoteSettingsDialog.textCustom": "自定標記", "DE.Views.NoteSettingsDialog.textDocEnd": "文件結尾", "DE.Views.NoteSettingsDialog.textDocument": "整個文檔", "DE.Views.NoteSettingsDialog.textEachPage": "重新開始每一頁", "DE.Views.NoteSettingsDialog.textEachSection": "重新開始每個部分", "DE.Views.NoteSettingsDialog.textEndnote": "尾註", "DE.Views.NoteSettingsDialog.textFootnote": "註腳", "DE.Views.NoteSettingsDialog.textFormat": "格式", "DE.Views.NoteSettingsDialog.textInsert": "插入", "DE.Views.NoteSettingsDialog.textLocation": "位置", "DE.Views.NoteSettingsDialog.textNumbering": "編號", "DE.Views.NoteSettingsDialog.textNumFormat": "數字格式", "DE.Views.NoteSettingsDialog.textPageBottom": "頁底", "DE.Views.NoteSettingsDialog.textSectEnd": "本節結束", "DE.Views.NoteSettingsDialog.textSection": "當前部分", "DE.Views.NoteSettingsDialog.textStart": "開始", "DE.Views.NoteSettingsDialog.textTextBottom": "文字之下", "DE.Views.NoteSettingsDialog.textTitle": "筆記設置", "DE.Views.NotesRemoveDialog.textEnd": "刪除所有尾註", "DE.Views.NotesRemoveDialog.textFoot": "刪除所有腳註", "DE.Views.NotesRemoveDialog.textTitle": "刪除筆記", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "警告", "DE.Views.PageMarginsDialog.textBottom": "底部", "DE.Views.PageMarginsDialog.textGutter": "溝", "DE.Views.PageMarginsDialog.textGutterPosition": "溝位置", "DE.Views.PageMarginsDialog.textInside": "內", "DE.Views.PageMarginsDialog.textLandscape": "橫向方向", "DE.Views.PageMarginsDialog.textLeft": "左", "DE.Views.PageMarginsDialog.textMirrorMargins": "對應邊距", "DE.Views.PageMarginsDialog.textMultiplePages": "多頁", "DE.Views.PageMarginsDialog.textNormal": "標準", "DE.Views.PageMarginsDialog.textOrientation": "方向", "DE.Views.PageMarginsDialog.textOutside": "外", "DE.Views.PageMarginsDialog.textPortrait": "直向方向", "DE.Views.PageMarginsDialog.textPreview": "預覽", "DE.Views.PageMarginsDialog.textRight": "右", "DE.Views.PageMarginsDialog.textTitle": "邊框", "DE.Views.PageMarginsDialog.textTop": "上方", "DE.Views.PageMarginsDialog.txtMarginsH": "對於給定的頁面高度，上下邊距太高", "DE.Views.PageMarginsDialog.txtMarginsW": "給定頁面寬度，左右頁邊距太寬", "DE.Views.PageSizeDialog.textHeight": "高度", "DE.Views.PageSizeDialog.textPreset": "預設值", "DE.Views.PageSizeDialog.textTitle": "頁面大小", "DE.Views.PageSizeDialog.textWidth": "寬度", "DE.Views.PageSizeDialog.txtCustom": "自訂", "DE.Views.PageThumbnails.textClosePanel": "關閉預覽圖", "DE.Views.PageThumbnails.textHighlightVisiblePart": "色彩醒目提示頁面可見部份", "DE.Views.PageThumbnails.textPageThumbnails": "頁面預覽圖", "DE.Views.PageThumbnails.textThumbnailsSettings": "預覽圖設定", "DE.Views.PageThumbnails.textThumbnailsSize": "預覽圖大小", "DE.Views.ParagraphSettings.strIndent": "縮進", "DE.Views.ParagraphSettings.strIndentsLeftText": "左", "DE.Views.ParagraphSettings.strIndentsRightText": "右", "DE.Views.ParagraphSettings.strIndentsSpecial": "特殊", "DE.Views.ParagraphSettings.strLineHeight": "行間距", "DE.Views.ParagraphSettings.strParagraphSpacing": "段落間距", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "不要在相同風格的文字段落之間添加間隔", "DE.Views.ParagraphSettings.strSpacingAfter": "之後", "DE.Views.ParagraphSettings.strSpacingBefore": "之前", "DE.Views.ParagraphSettings.textAdvanced": "顯示進階設定", "DE.Views.ParagraphSettings.textAt": "在", "DE.Views.ParagraphSettings.textAtLeast": "至少", "DE.Views.ParagraphSettings.textAuto": "多項", "DE.Views.ParagraphSettings.textBackColor": "背景顏色", "DE.Views.ParagraphSettings.textExact": "準確", "DE.Views.ParagraphSettings.textFirstLine": "第一行", "DE.Views.ParagraphSettings.textHanging": "懸掛式", "DE.Views.ParagraphSettings.textNoneSpecial": "（空）", "DE.Views.ParagraphSettings.txtAutoText": "自動", "DE.Views.ParagraphSettingsAdvanced.noTabs": "指定的標籤將出現在此段落中", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大寫", "DE.Views.ParagraphSettingsAdvanced.strBorders": "邊框和添入", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "分頁之前", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "雙刪除線", "DE.Views.ParagraphSettingsAdvanced.strIndent": "縮進", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行間距", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "大綱級別", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "之後", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "之前", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "保持線條一致", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "跟著下一個", "DE.Views.ParagraphSettingsAdvanced.strMargins": "填充物", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "孤兒控制", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字體", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "縮進和間距", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "換行和分頁符", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "放置", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小大寫", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "不要在相同風格的文字段落之間添加間隔", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "間距", "DE.Views.ParagraphSettingsAdvanced.strStrike": "刪除線", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "下標", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "上標", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "禁止行號", "DE.Views.ParagraphSettingsAdvanced.strTabs": "標籤", "DE.Views.ParagraphSettingsAdvanced.textAlign": "對齊", "DE.Views.ParagraphSettingsAdvanced.textAll": "全部", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "至少", "DE.Views.ParagraphSettingsAdvanced.textAuto": "多項", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "背景顏色", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "基本文字", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "邊框顏色", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "點擊圖或使用按鈕選擇邊框並將選定的樣式應用於邊框", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "邊框大小", "DE.Views.ParagraphSettingsAdvanced.textBottom": "底部", "DE.Views.ParagraphSettingsAdvanced.textCentered": "置中", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "文字間距", "DE.Views.ParagraphSettingsAdvanced.textContext": "上下文的", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "上下文的與任用的", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "上下文、歷史與任用的", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "上下文與歷史的", "DE.Views.ParagraphSettingsAdvanced.textDefault": "預設分頁", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "任用的", "DE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "DE.Views.ParagraphSettingsAdvanced.textExact": "準確", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "第一行", "DE.Views.ParagraphSettingsAdvanced.textHanging": "懸掛式", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "歷史的", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "歷史與任用的", "DE.Views.ParagraphSettingsAdvanced.textJustified": "合理的", "DE.Views.ParagraphSettingsAdvanced.textLeader": "領導", "DE.Views.ParagraphSettingsAdvanced.textLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textLevel": "水平", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "連字符號", "DE.Views.ParagraphSettingsAdvanced.textNone": "無", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（空）", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType 功能", "DE.Views.ParagraphSettingsAdvanced.textPosition": "位置", "DE.Views.ParagraphSettingsAdvanced.textRemove": "移除", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "移除所有", "DE.Views.ParagraphSettingsAdvanced.textRight": "右", "DE.Views.ParagraphSettingsAdvanced.textSet": "指定", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "間距", "DE.Views.ParagraphSettingsAdvanced.textStandard": "僅限標準", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "標準與上下文的", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "標準、上下文與任用的", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "標準、上下文與歷史的", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "標準和任意的", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "標準，歷史和任意的", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "標準和歷史的", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "標籤位置", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "DE.Views.ParagraphSettingsAdvanced.textTitle": "段落-進階設置", "DE.Views.ParagraphSettingsAdvanced.textTop": "上方", "DE.Views.ParagraphSettingsAdvanced.tipAll": "設置外邊界和所有內線", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "僅設置底部邊框", "DE.Views.ParagraphSettingsAdvanced.tipInner": "僅設置水平內線", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "僅設置左邊框", "DE.Views.ParagraphSettingsAdvanced.tipNone": "設置無邊界", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "僅設置外部框線", "DE.Views.ParagraphSettingsAdvanced.tipRight": "僅設置右邊框", "DE.Views.ParagraphSettingsAdvanced.tipTop": "僅設置頂部邊框", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "自動", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "無邊框", "DE.Views.RightMenu.txtChartSettings": "圖表設定", "DE.Views.RightMenu.txtFormSettings": "表格設定", "DE.Views.RightMenu.txtHeaderFooterSettings": "標頭和頁腳設置", "DE.Views.RightMenu.txtImageSettings": "影像設定", "DE.Views.RightMenu.txtMailMergeSettings": "郵件合併設置", "DE.Views.RightMenu.txtParagraphSettings": "段落設定", "DE.Views.RightMenu.txtShapeSettings": "形狀設定", "DE.Views.RightMenu.txtSignatureSettings": "簽名設置", "DE.Views.RightMenu.txtTableSettings": "表格設定", "DE.Views.RightMenu.txtTextArtSettings": "文字藝術設定", "DE.Views.ShapeSettings.strBackground": "背景顏色", "DE.Views.ShapeSettings.strChange": "變更自動形狀", "DE.Views.ShapeSettings.strColor": "顏色", "DE.Views.ShapeSettings.strFill": "填入", "DE.Views.ShapeSettings.strForeground": "前景色", "DE.Views.ShapeSettings.strPattern": "模式", "DE.Views.ShapeSettings.strShadow": "顯示陰影", "DE.Views.ShapeSettings.strSize": "大小", "DE.Views.ShapeSettings.strStroke": "筆鋒", "DE.Views.ShapeSettings.strTransparency": "透明度", "DE.Views.ShapeSettings.strType": "輸入", "DE.Views.ShapeSettings.textAdvanced": "顯示進階設定", "DE.Views.ShapeSettings.textAngle": "角度", "DE.Views.ShapeSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入0 pt至1584 pt之間的值。", "DE.Views.ShapeSettings.textColor": "填充顏色", "DE.Views.ShapeSettings.textDirection": "方向", "DE.Views.ShapeSettings.textEmptyPattern": "無模式", "DE.Views.ShapeSettings.textFlip": "翻轉", "DE.Views.ShapeSettings.textFromFile": "從檔案", "DE.Views.ShapeSettings.textFromStorage": "從存儲", "DE.Views.ShapeSettings.textFromUrl": "從 URL", "DE.Views.ShapeSettings.textGradient": "漸變點", "DE.Views.ShapeSettings.textGradientFill": "漸層填充", "DE.Views.ShapeSettings.textHint270": "逆時針旋轉90°", "DE.Views.ShapeSettings.textHint90": "順時針旋轉90°", "DE.Views.ShapeSettings.textHintFlipH": "水平翻轉", "DE.Views.ShapeSettings.textHintFlipV": "垂直翻轉", "DE.Views.ShapeSettings.textImageTexture": "圖片或紋理", "DE.Views.ShapeSettings.textLinear": "線性的", "DE.Views.ShapeSettings.textNoFill": "沒有填充", "DE.Views.ShapeSettings.textPatternFill": "模式", "DE.Views.ShapeSettings.textPosition": "位置", "DE.Views.ShapeSettings.textRadial": "徑向的", "DE.Views.ShapeSettings.textRecentlyUsed": "最近使用", "DE.Views.ShapeSettings.textRotate90": "旋轉90°", "DE.Views.ShapeSettings.textRotation": "旋轉", "DE.Views.ShapeSettings.textSelectImage": "選擇圖片", "DE.Views.ShapeSettings.textSelectTexture": "選擇", "DE.Views.ShapeSettings.textStretch": "延伸", "DE.Views.ShapeSettings.textStyle": "風格", "DE.Views.ShapeSettings.textTexture": "從紋理", "DE.Views.ShapeSettings.textTile": "磚瓦", "DE.Views.ShapeSettings.textWrap": "包覆風格", "DE.Views.ShapeSettings.tipAddGradientPoint": "新增漸變點", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "刪除漸變點", "DE.Views.ShapeSettings.txtBehind": "文字在後", "DE.Views.ShapeSettings.txtBrownPaper": "牛皮紙", "DE.Views.ShapeSettings.txtCanvas": "畫布", "DE.Views.ShapeSettings.txtCarton": "紙箱", "DE.Views.ShapeSettings.txtDarkFabric": "深色面料", "DE.Views.ShapeSettings.txtGrain": "紋", "DE.Views.ShapeSettings.txtGranite": "花崗岩", "DE.Views.ShapeSettings.txtGreyPaper": "灰紙", "DE.Views.ShapeSettings.txtInFront": "文字在前", "DE.Views.ShapeSettings.txtInline": "與文字排列", "DE.Views.ShapeSettings.txtKnit": "編織", "DE.Views.ShapeSettings.txtLeather": "皮革", "DE.Views.ShapeSettings.txtNoBorders": "無線條", "DE.Views.ShapeSettings.txtPapyrus": "紙莎草紙", "DE.Views.ShapeSettings.txtSquare": "正方形", "DE.Views.ShapeSettings.txtThrough": "通過", "DE.Views.ShapeSettings.txtTight": "緊", "DE.Views.ShapeSettings.txtTopAndBottom": "頂部和底部", "DE.Views.ShapeSettings.txtWood": "木頭", "DE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "DE.Views.SignatureSettings.strDelete": "刪除簽名", "DE.Views.SignatureSettings.strDetails": "簽名細節", "DE.Views.SignatureSettings.strInvalid": "無效的簽名", "DE.Views.SignatureSettings.strRequested": "要求的簽名", "DE.Views.SignatureSettings.strSetup": "簽名設置", "DE.Views.SignatureSettings.strSign": "簽名", "DE.Views.SignatureSettings.strSignature": "簽名", "DE.Views.SignatureSettings.strSigner": "簽名者", "DE.Views.SignatureSettings.strValid": "有效簽名", "DE.Views.SignatureSettings.txtContinueEditing": "仍要編輯", "DE.Views.SignatureSettings.txtEditWarning": "編輯將刪除文檔中的簽名。<br>是否確定繼續？", "DE.Views.SignatureSettings.txtRemoveWarning": "確定移除此簽名?<br>這動作無法復原.", "DE.Views.SignatureSettings.txtRequestedSignatures": "該文件需要簽名。", "DE.Views.SignatureSettings.txtSigned": "有效簽名已添加到文檔中。該文檔受到保護，無法編輯。", "DE.Views.SignatureSettings.txtSignedInvalid": "文檔中的某些數字簽名無效或無法驗證。該文檔受到保護，無法編輯。", "DE.Views.Statusbar.goToPageText": "轉到頁面", "DE.Views.Statusbar.pageIndexText": "第{0}頁，共{1}頁", "DE.Views.Statusbar.tipFitPage": "切合至頁面", "DE.Views.Statusbar.tipFitWidth": "切合至寬度", "DE.Views.Statusbar.tipHandTool": "移動工具", "DE.Views.Statusbar.tipSelectTool": "選取工具", "DE.Views.Statusbar.tipSetLang": "設定文字語言", "DE.Views.Statusbar.tipZoomFactor": "放大", "DE.Views.Statusbar.tipZoomIn": "放大", "DE.Views.Statusbar.tipZoomOut": "縮小", "DE.Views.Statusbar.txtPageNumInvalid": "頁碼無效", "DE.Views.StyleTitleDialog.textHeader": "新增風格", "DE.Views.StyleTitleDialog.textNextStyle": "下一段風格", "DE.Views.StyleTitleDialog.textTitle": "標題", "DE.Views.StyleTitleDialog.txtEmpty": "這是必填欄", "DE.Views.StyleTitleDialog.txtNotEmpty": "段落不能為空", "DE.Views.StyleTitleDialog.txtSameAs": "與新增風格相同", "DE.Views.TableFormulaDialog.textBookmark": "粘貼書籤", "DE.Views.TableFormulaDialog.textFormat": "數字格式", "DE.Views.TableFormulaDialog.textFormula": "函數", "DE.Views.TableFormulaDialog.textInsertFunction": "粘貼功能", "DE.Views.TableFormulaDialog.textTitle": "函數設定", "DE.Views.TableOfContentsSettings.strAlign": "右對齊頁碼", "DE.Views.TableOfContentsSettings.strFullCaption": "包括標籤和編號", "DE.Views.TableOfContentsSettings.strLinks": "將目錄格式設置為連結", "DE.Views.TableOfContentsSettings.strLinksOF": "調數字目錄格式為鏈接", "DE.Views.TableOfContentsSettings.strShowPages": "顯示頁碼", "DE.Views.TableOfContentsSettings.textBuildTable": "從中建立目錄", "DE.Views.TableOfContentsSettings.textBuildTableOF": "從建立數據表", "DE.Views.TableOfContentsSettings.textEquation": "方程式", "DE.Views.TableOfContentsSettings.textFigure": "數字", "DE.Views.TableOfContentsSettings.textLeader": "領導", "DE.Views.TableOfContentsSettings.textLevel": "水平", "DE.Views.TableOfContentsSettings.textLevels": "層次", "DE.Views.TableOfContentsSettings.textNone": "無", "DE.Views.TableOfContentsSettings.textRadioCaption": "標題", "DE.Views.TableOfContentsSettings.textRadioLevels": "大綱級別", "DE.Views.TableOfContentsSettings.textRadioStyle": "風格", "DE.Views.TableOfContentsSettings.textRadioStyles": "選擇風格", "DE.Views.TableOfContentsSettings.textStyle": "風格", "DE.Views.TableOfContentsSettings.textStyles": "風格", "DE.Views.TableOfContentsSettings.textTable": "表格", "DE.Views.TableOfContentsSettings.textTitle": "目錄", "DE.Views.TableOfContentsSettings.textTitleTOF": "圖表", "DE.Views.TableOfContentsSettings.txtCentered": "置中", "DE.Views.TableOfContentsSettings.txtClassic": "經典", "DE.Views.TableOfContentsSettings.txtCurrent": "當前", "DE.Views.TableOfContentsSettings.txtDistinctive": "獨特的", "DE.Views.TableOfContentsSettings.txtFormal": "正式", "DE.Views.TableOfContentsSettings.txtModern": "現代", "DE.Views.TableOfContentsSettings.txtOnline": "上線", "DE.Views.TableOfContentsSettings.txtSimple": "簡單", "DE.Views.TableOfContentsSettings.txtStandard": "標準", "DE.Views.TableSettings.deleteColumnText": "刪除欄位", "DE.Views.TableSettings.deleteRowText": "刪除行列", "DE.Views.TableSettings.deleteTableText": "刪除表格", "DE.Views.TableSettings.insertColumnLeftText": "向左插入列", "DE.Views.TableSettings.insertColumnRightText": "向右插入列", "DE.Views.TableSettings.insertRowAboveText": "在上方插入行", "DE.Views.TableSettings.insertRowBelowText": "在下方插入行", "DE.Views.TableSettings.mergeCellsText": "合併儲存格", "DE.Views.TableSettings.selectCellText": "選擇儲存格", "DE.Views.TableSettings.selectColumnText": "選擇欄", "DE.Views.TableSettings.selectRowText": "選擇列", "DE.Views.TableSettings.selectTableText": "選擇表格", "DE.Views.TableSettings.splitCellsText": "分割儲存格...", "DE.Views.TableSettings.splitCellTitleText": "分割儲存格", "DE.Views.TableSettings.strRepeatRow": "在每一頁頂部重複作為標題行", "DE.Views.TableSettings.textAddFormula": "插入函數", "DE.Views.TableSettings.textAdvanced": "顯示進階設定", "DE.Views.TableSettings.textBackColor": "背景顏色", "DE.Views.TableSettings.textBanded": "帶狀", "DE.Views.TableSettings.textBorderColor": "顏色", "DE.Views.TableSettings.textBorders": "邊框風格", "DE.Views.TableSettings.textCellSize": "行和列大小", "DE.Views.TableSettings.textColumns": "欄", "DE.Views.TableSettings.textConvert": "轉換表格至文字", "DE.Views.TableSettings.textDistributeCols": "分配列", "DE.Views.TableSettings.textDistributeRows": "分配行", "DE.Views.TableSettings.textEdit": "行和列", "DE.Views.TableSettings.textEmptyTemplate": "\n沒有模板", "DE.Views.TableSettings.textFirst": "第一", "DE.Views.TableSettings.textHeader": "標頭", "DE.Views.TableSettings.textHeight": "高度", "DE.Views.TableSettings.textLast": "最後", "DE.Views.TableSettings.textRows": "行列", "DE.Views.TableSettings.textSelectBorders": "選擇您要更改上面選擇的應用風格的邊框", "DE.Views.TableSettings.textTemplate": "從範本中選擇", "DE.Views.TableSettings.textTotal": "總計", "DE.Views.TableSettings.textWidth": "寬度", "DE.Views.TableSettings.tipAll": "設置外邊界和所有內線", "DE.Views.TableSettings.tipBottom": "僅設置外底邊框", "DE.Views.TableSettings.tipInner": "僅設置內線", "DE.Views.TableSettings.tipInnerHor": "僅設置水平內線", "DE.Views.TableSettings.tipInnerVert": "僅設置垂直內線", "DE.Views.TableSettings.tipLeft": "僅設置左外邊框", "DE.Views.TableSettings.tipNone": "設置無邊界", "DE.Views.TableSettings.tipOuter": "僅設置外部框線", "DE.Views.TableSettings.tipRight": "僅設置右外框", "DE.Views.TableSettings.tipTop": "僅設置外部頂部邊框", "DE.Views.TableSettings.txtNoBorders": "無邊框", "DE.Views.TableSettings.txtTable_Accent": "口音", "DE.Views.TableSettings.txtTable_Colorful": "七彩", "DE.Views.TableSettings.txtTable_Dark": "暗", "DE.Views.TableSettings.txtTable_GridTable": "網格表", "DE.Views.TableSettings.txtTable_Light": "光", "DE.Views.TableSettings.txtTable_ListTable": "列表表", "DE.Views.TableSettings.txtTable_PlainTable": "普通表", "DE.Views.TableSettings.txtTable_TableGrid": "表格網格", "DE.Views.TableSettingsAdvanced.textAlign": "對齊", "DE.Views.TableSettingsAdvanced.textAlignment": "對齊", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "儲存格間距", "DE.Views.TableSettingsAdvanced.textAlt": "替代文字", "DE.Views.TableSettingsAdvanced.textAltDescription": "描述", "DE.Views.TableSettingsAdvanced.textAltTip": "視覺對象信息的替代基於文本的表示形式，將向有視力或認知障礙的人讀取，以幫助他們更好地理解圖像，自動成型，圖表或表格中包含的信息。", "DE.Views.TableSettingsAdvanced.textAltTitle": "標題", "DE.Views.TableSettingsAdvanced.textAnchorText": "文字", "DE.Views.TableSettingsAdvanced.textAutofit": "自動調整大小以適合內容", "DE.Views.TableSettingsAdvanced.textBackColor": "儲存格背景", "DE.Views.TableSettingsAdvanced.textBelow": "之下", "DE.Views.TableSettingsAdvanced.textBorderColor": "邊框顏色", "DE.Views.TableSettingsAdvanced.textBorderDesc": "點擊圖或使用按鈕選擇邊框並將選定的樣式應用於邊框", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "邊框和背景", "DE.Views.TableSettingsAdvanced.textBorderWidth": "邊框大小", "DE.Views.TableSettingsAdvanced.textBottom": "底部", "DE.Views.TableSettingsAdvanced.textCellOptions": "儲存格選項", "DE.Views.TableSettingsAdvanced.textCellProps": "儲存格", "DE.Views.TableSettingsAdvanced.textCellSize": "儲存格大小", "DE.Views.TableSettingsAdvanced.textCenter": "中心", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "中心", "DE.Views.TableSettingsAdvanced.textCheckMargins": "使用預設邊距", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "預設儲存格邊距", "DE.Views.TableSettingsAdvanced.textDistance": "與文字的距離", "DE.Views.TableSettingsAdvanced.textHorizontal": "水平", "DE.Views.TableSettingsAdvanced.textIndLeft": "從左縮進", "DE.Views.TableSettingsAdvanced.textLeft": "左", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "左", "DE.Views.TableSettingsAdvanced.textMargin": "邊框", "DE.Views.TableSettingsAdvanced.textMargins": "儲存格邊距", "DE.Views.TableSettingsAdvanced.textMeasure": "測量", "DE.Views.TableSettingsAdvanced.textMove": "用文字移動對象", "DE.Views.TableSettingsAdvanced.textOnlyCells": "僅適用於選取的儲存格", "DE.Views.TableSettingsAdvanced.textOptions": "選項", "DE.Views.TableSettingsAdvanced.textOverlap": "允許重疊", "DE.Views.TableSettingsAdvanced.textPage": "頁面", "DE.Views.TableSettingsAdvanced.textPosition": "位置", "DE.Views.TableSettingsAdvanced.textPrefWidth": "首選寬度", "DE.Views.TableSettingsAdvanced.textPreview": "預覽", "DE.Views.TableSettingsAdvanced.textRelative": "關係到", "DE.Views.TableSettingsAdvanced.textRight": "右", "DE.Views.TableSettingsAdvanced.textRightOf": "在 - 的右邊", "DE.Views.TableSettingsAdvanced.textRightTooltip": "右", "DE.Views.TableSettingsAdvanced.textTable": "表格", "DE.Views.TableSettingsAdvanced.textTableBackColor": "表格背景", "DE.Views.TableSettingsAdvanced.textTablePosition": "表格位置", "DE.Views.TableSettingsAdvanced.textTableSize": "表格大小", "DE.Views.TableSettingsAdvanced.textTitle": "表格 - 進階設定", "DE.Views.TableSettingsAdvanced.textTop": "上方", "DE.Views.TableSettingsAdvanced.textVertical": "垂直", "DE.Views.TableSettingsAdvanced.textWidth": "寬度", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "寬度與間距", "DE.Views.TableSettingsAdvanced.textWrap": "文字包裝", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "內聯表", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "流量表", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "包覆風格", "DE.Views.TableSettingsAdvanced.textWrapText": "包覆文字", "DE.Views.TableSettingsAdvanced.tipAll": "設置外邊界和所有內線", "DE.Views.TableSettingsAdvanced.tipCellAll": "設置邊框僅用於內部儲存格", "DE.Views.TableSettingsAdvanced.tipCellInner": "只為內部儲存格設置垂直和水平線", "DE.Views.TableSettingsAdvanced.tipCellOuter": "為內部儲存格設置外部邊界", "DE.Views.TableSettingsAdvanced.tipInner": "僅設置內線", "DE.Views.TableSettingsAdvanced.tipNone": "設置無邊界", "DE.Views.TableSettingsAdvanced.tipOuter": "僅設置外部框線", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": " 設置外部邊框和所有內部儲存格的邊框", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "設置內部儲存格的外部邊界以及垂直和水平線", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "設置表格的外部邊框和內部儲存格的外部邊框", "DE.Views.TableSettingsAdvanced.txtCm": "公分", "DE.Views.TableSettingsAdvanced.txtInch": "吋", "DE.Views.TableSettingsAdvanced.txtNoBorders": "無邊框", "DE.Views.TableSettingsAdvanced.txtPercent": "百分", "DE.Views.TableSettingsAdvanced.txtPt": "點", "DE.Views.TableToTextDialog.textEmpty": "你必須輸入至少一個自訂的分隔字元", "DE.Views.TableToTextDialog.textNested": "轉換套疊表格", "DE.Views.TableToTextDialog.textOther": "其它", "DE.Views.TableToTextDialog.textPara": "段落標記", "DE.Views.TableToTextDialog.textSemicolon": "分號", "DE.Views.TableToTextDialog.textSeparator": "文字分隔用", "DE.Views.TableToTextDialog.textTab": "標籤", "DE.Views.TableToTextDialog.textTitle": "轉換表格至文字", "DE.Views.TextArtSettings.strColor": "顏色", "DE.Views.TextArtSettings.strFill": "填入", "DE.Views.TextArtSettings.strSize": "大小", "DE.Views.TextArtSettings.strStroke": "筆鋒", "DE.Views.TextArtSettings.strTransparency": "透明度", "DE.Views.TextArtSettings.strType": "輸入", "DE.Views.TextArtSettings.textAngle": "角度", "DE.Views.TextArtSettings.textBorderSizeErr": "輸入的值不正確。<br>請輸入0 pt至1584 pt之間的值。", "DE.Views.TextArtSettings.textColor": "填充顏色", "DE.Views.TextArtSettings.textDirection": "方向", "DE.Views.TextArtSettings.textGradient": "漸變點", "DE.Views.TextArtSettings.textGradientFill": "漸層填充", "DE.Views.TextArtSettings.textLinear": "線性的", "DE.Views.TextArtSettings.textNoFill": "沒有填充", "DE.Views.TextArtSettings.textPosition": "位置", "DE.Views.TextArtSettings.textRadial": "徑向的", "DE.Views.TextArtSettings.textSelectTexture": "選擇", "DE.Views.TextArtSettings.textStyle": "風格", "DE.Views.TextArtSettings.textTemplate": "樣板", "DE.Views.TextArtSettings.textTransform": "轉變", "DE.Views.TextArtSettings.tipAddGradientPoint": "新增漸變點", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "刪除漸變點", "DE.Views.TextArtSettings.txtNoBorders": "無線條", "DE.Views.TextToTableDialog.textAutofit": "自動調整欄寬", "DE.Views.TextToTableDialog.textColumns": "欄", "DE.Views.TextToTableDialog.textContents": "自動調整欄寬至內容", "DE.Views.TextToTableDialog.textEmpty": "你必須輸入至少一個自訂的分隔字元", "DE.Views.TextToTableDialog.textFixed": "固定欄寬", "DE.Views.TextToTableDialog.textOther": "其它", "DE.Views.TextToTableDialog.textPara": "段落", "DE.Views.TextToTableDialog.textRows": "行列", "DE.Views.TextToTableDialog.textSemicolon": "分號", "DE.Views.TextToTableDialog.textSeparator": "文字分隔從", "DE.Views.TextToTableDialog.textTab": "標籤", "DE.Views.TextToTableDialog.textTableSize": "表格大小", "DE.Views.TextToTableDialog.textTitle": "轉換文字至表格", "DE.Views.TextToTableDialog.textWindow": "自動調整欄寬至視窗", "DE.Views.TextToTableDialog.txtAutoText": "自動", "DE.Views.Toolbar.capBtnAddComment": "新增註解", "DE.Views.Toolbar.capBtnBlankPage": "空白頁面", "DE.Views.Toolbar.capBtnColumns": "欄", "DE.Views.Toolbar.capBtnComment": "註解", "DE.Views.Toolbar.capBtnDateTime": "日期和時間", "DE.Views.Toolbar.capBtnInsChart": "圖表", "DE.Views.Toolbar.capBtnInsControls": "內容控制", "DE.Views.Toolbar.capBtnInsDropcap": "首字大寫", "DE.Views.Toolbar.capBtnInsEquation": "方程式", "DE.Views.Toolbar.capBtnInsHeader": "頁首/頁尾", "DE.Views.Toolbar.capBtnInsImage": "圖像", "DE.Views.Toolbar.capBtnInsPagebreak": "段落隔斷", "DE.Views.Toolbar.capBtnInsShape": "形狀", "DE.Views.Toolbar.capBtnInsSymbol": "符號", "DE.Views.Toolbar.capBtnInsTable": "表格", "DE.Views.Toolbar.capBtnInsTextart": "文字藝術", "DE.Views.Toolbar.capBtnInsTextbox": "文字框", "DE.Views.Toolbar.capBtnLineNumbers": "行號", "DE.Views.Toolbar.capBtnMargins": "邊框", "DE.Views.Toolbar.capBtnPageOrient": "方向", "DE.Views.Toolbar.capBtnPageSize": "大小", "DE.Views.Toolbar.capBtnWatermark": "浮水印", "DE.Views.Toolbar.capImgAlign": "對齊", "DE.Views.Toolbar.capImgBackward": "向後發送", "DE.Views.Toolbar.capImgForward": "向前進", "DE.Views.Toolbar.capImgGroup": "群組", "DE.Views.Toolbar.capImgWrapping": "包覆", "DE.Views.Toolbar.mniCapitalizeWords": "每個單字字首大寫", "DE.Views.Toolbar.mniCustomTable": "插入自訂表格", "DE.Views.Toolbar.mniDrawTable": "畫表", "DE.Views.Toolbar.mniEditControls": "控制設定", "DE.Views.Toolbar.mniEditDropCap": "首字大寫設定", "DE.Views.Toolbar.mniEditFooter": "編輯頁腳", "DE.Views.Toolbar.mniEditHeader": "編輯標題", "DE.Views.Toolbar.mniEraseTable": "擦除表", "DE.Views.Toolbar.mniFromFile": "從檔案", "DE.Views.Toolbar.mniFromStorage": "從存儲", "DE.Views.Toolbar.mniFromUrl": "從 URL", "DE.Views.Toolbar.mniHiddenBorders": "隱藏表格邊框", "DE.Views.Toolbar.mniHiddenChars": "非印刷字符", "DE.Views.Toolbar.mniHighlightControls": "強調顯示設置", "DE.Views.Toolbar.mniImageFromFile": "圖片來自文件", "DE.Views.Toolbar.mniImageFromStorage": "來自存儲的圖像", "DE.Views.Toolbar.mniImageFromUrl": "來自網址的圖片", "DE.Views.Toolbar.mniInsertSSE": "插入計算表", "DE.Views.Toolbar.mniLowerCase": "小寫", "DE.Views.Toolbar.mniRemoveFooter": "移除頁腳", "DE.Views.Toolbar.mniRemoveHeader": "移除頁首", "DE.Views.Toolbar.mniSentenceCase": "大寫句子頭", "DE.Views.Toolbar.mniTextToTable": "轉換文字至表格", "DE.Views.Toolbar.mniToggleCase": "轉換大小寫", "DE.Views.Toolbar.mniUpperCase": "大寫", "DE.Views.Toolbar.strMenuNoFill": "沒有填充", "DE.Views.Toolbar.textAutoColor": "自動", "DE.Views.Toolbar.textBold": "粗體", "DE.Views.Toolbar.textBottom": "底部：", "DE.Views.Toolbar.textChangeLevel": "變更清單層級", "DE.Views.Toolbar.textCheckboxControl": "複選框", "DE.Views.Toolbar.textColumnsCustom": "自定欄", "DE.Views.Toolbar.textColumnsLeft": "左", "DE.Views.Toolbar.textColumnsOne": "一", "DE.Views.Toolbar.textColumnsRight": "右", "DE.Views.Toolbar.textColumnsThree": "三", "DE.Views.Toolbar.textColumnsTwo": "二", "DE.Views.Toolbar.textComboboxControl": "組合框", "DE.Views.Toolbar.textContinuous": "連續", "DE.Views.Toolbar.textContPage": "連續頁面", "DE.Views.Toolbar.textCustomLineNumbers": "行號選項", "DE.Views.Toolbar.textDateControl": "日期", "DE.Views.Toolbar.textDropdownControl": "下拉選單", "DE.Views.Toolbar.textEditWatermark": "自定浮水印", "DE.Views.Toolbar.textEvenPage": "雙數頁", "DE.Views.Toolbar.textInMargin": "在邊框內", "DE.Views.Toolbar.textInsColumnBreak": "插入分欄符", "DE.Views.Toolbar.textInsertPageCount": "插入頁數", "DE.Views.Toolbar.textInsertPageNumber": "插入頁碼", "DE.Views.Toolbar.textInsPageBreak": "插入分頁符", "DE.Views.Toolbar.textInsSectionBreak": "插入分節符", "DE.Views.Toolbar.textInText": "字段內", "DE.Views.Toolbar.textItalic": "斜體", "DE.Views.Toolbar.textLandscape": "橫向方向", "DE.Views.Toolbar.textLeft": "左：", "DE.Views.Toolbar.textListSettings": "清單設定", "DE.Views.Toolbar.textMarginsLast": "最後自訂", "DE.Views.Toolbar.textMarginsModerate": "中等", "DE.Views.Toolbar.textMarginsNarrow": "狹窄", "DE.Views.Toolbar.textMarginsNormal": "標準", "DE.Views.Toolbar.textMarginsUsNormal": "美國普通", "DE.Views.Toolbar.textMarginsWide": "寬", "DE.Views.Toolbar.textNewColor": "新增自訂顏色", "DE.Views.Toolbar.textNextPage": "下一頁", "DE.Views.Toolbar.textNoHighlight": "沒有突出顯示", "DE.Views.Toolbar.textNone": "無", "DE.Views.Toolbar.textOddPage": "奇數頁", "DE.Views.Toolbar.textPageMarginsCustom": "自定邊距", "DE.Views.Toolbar.textPageSizeCustom": "自定頁面大小", "DE.Views.Toolbar.textPictureControl": "圖片", "DE.Views.Toolbar.textPlainControl": "純文本", "DE.Views.Toolbar.textPortrait": "直向方向", "DE.Views.Toolbar.textRemoveControl": "刪除內容控制", "DE.Views.Toolbar.textRemWatermark": "刪除水印", "DE.Views.Toolbar.textRestartEachPage": "重新開始每一頁", "DE.Views.Toolbar.textRestartEachSection": "重新開始每個部分", "DE.Views.Toolbar.textRichControl": "富文本", "DE.Views.Toolbar.textRight": "右: ", "DE.Views.Toolbar.textStrikeout": "刪除線", "DE.Views.Toolbar.textStyleMenuDelete": "刪除風格", "DE.Views.Toolbar.textStyleMenuDeleteAll": "刪除所有自定風格", "DE.Views.Toolbar.textStyleMenuNew": "精選新風格", "DE.Views.Toolbar.textStyleMenuRestore": "恢復為預設值", "DE.Views.Toolbar.textStyleMenuRestoreAll": "恢復全部為預設風格", "DE.Views.Toolbar.textStyleMenuUpdate": "選擇更新", "DE.Views.Toolbar.textSubscript": "下標", "DE.Views.Toolbar.textSuperscript": "上標", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "禁止當前段落", "DE.Views.Toolbar.textTabCollaboration": "共同編輯", "DE.Views.Toolbar.textTabFile": "檔案", "DE.Views.Toolbar.textTabHome": "首頁", "DE.Views.Toolbar.textTabInsert": "插入", "DE.Views.Toolbar.textTabLayout": "佈局", "DE.Views.Toolbar.textTabLinks": "參考文獻", "DE.Views.Toolbar.textTabProtect": "保護", "DE.Views.Toolbar.textTabReview": "評論;回顧", "DE.Views.Toolbar.textTabView": "檢視", "DE.Views.Toolbar.textTitleError": "錯誤", "DE.Views.Toolbar.textToCurrent": "到當前位置", "DE.Views.Toolbar.textTop": "頂部: ", "DE.Views.Toolbar.textUnderline": "底線", "DE.Views.Toolbar.tipAlignCenter": "居中對齊", "DE.Views.Toolbar.tipAlignJust": "合理的", "DE.Views.Toolbar.tipAlignLeft": "對齊左側", "DE.Views.Toolbar.tipAlignRight": "對齊右側", "DE.Views.Toolbar.tipBack": "返回", "DE.Views.Toolbar.tipBlankPage": "插入空白頁", "DE.Views.Toolbar.tipChangeCase": "改大小寫", "DE.Views.Toolbar.tipChangeChart": "變更圖表類型", "DE.Views.Toolbar.tipClearStyle": "清晰的風格", "DE.Views.Toolbar.tipColorSchemas": "變更配色方案", "DE.Views.Toolbar.tipColumns": "插入欄", "DE.Views.Toolbar.tipControls": "插入內容控件", "DE.Views.Toolbar.tipCopy": "複製", "DE.Views.Toolbar.tipCopyStyle": "複製風格", "DE.Views.Toolbar.tipCut": "剪下", "DE.Views.Toolbar.tipDateTime": "插入當前日期和時間", "DE.Views.Toolbar.tipDecFont": "減少字體大小", "DE.Views.Toolbar.tipDecPrLeft": "減少縮進", "DE.Views.Toolbar.tipDropCap": "插入下蓋", "DE.Views.Toolbar.tipEditHeader": "編輯頁眉或頁腳", "DE.Views.Toolbar.tipFontColor": "字體顏色", "DE.Views.Toolbar.tipFontName": "字體", "DE.Views.Toolbar.tipFontSize": "字體大小", "DE.Views.Toolbar.tipHighlightColor": "熒光色選", "DE.Views.Toolbar.tipImgAlign": "對齊物件", "DE.Views.Toolbar.tipImgGroup": "組對象", "DE.Views.Toolbar.tipImgWrapping": "包覆文字", "DE.Views.Toolbar.tipIncFont": "增量字體大小", "DE.Views.Toolbar.tipIncPrLeft": "增加縮進", "DE.Views.Toolbar.tipInsertChart": "插入圖表", "DE.Views.Toolbar.tipInsertEquation": "插入方程式", "DE.Views.Toolbar.tipInsertImage": "插入圖片", "DE.Views.Toolbar.tipInsertNum": "插入頁碼", "DE.Views.Toolbar.tipInsertShape": "插入自動形狀", "DE.Views.Toolbar.tipInsertSymbol": "插入符號", "DE.Views.Toolbar.tipInsertTable": "插入表格", "DE.Views.Toolbar.tipInsertText": "插入文字框", "DE.Views.Toolbar.tipInsertTextArt": "插入文字藝術", "DE.Views.Toolbar.tipLineNumbers": "顯示行號", "DE.Views.Toolbar.tipLineSpace": "段落行距", "DE.Views.Toolbar.tipMailRecepients": "郵件合併", "DE.Views.Toolbar.tipMarkers": "項目符號", "DE.Views.Toolbar.tipMarkersArrow": "箭頭項目符號", "DE.Views.Toolbar.tipMarkersCheckmark": "核取記號項目符號", "DE.Views.Toolbar.tipMarkersDash": "連字號項目符號", "DE.Views.Toolbar.tipMarkersFRhombus": "實心菱形項目符號", "DE.Views.Toolbar.tipMarkersFRound": "實心圓項目符號", "DE.Views.Toolbar.tipMarkersFSquare": "實心方形項目符號", "DE.Views.Toolbar.tipMarkersHRound": "空心圓項目符號", "DE.Views.Toolbar.tipMarkersStar": "星星項目符號", "DE.Views.Toolbar.tipMultiLevelNumbered": "多層次數字清單", "DE.Views.Toolbar.tipMultilevels": "多級清單", "DE.Views.Toolbar.tipMultiLevelSymbols": "多層次符號清單", "DE.Views.Toolbar.tipMultiLevelVarious": "多層次數字清單", "DE.Views.Toolbar.tipNumbers": "編號", "DE.Views.Toolbar.tipPageBreak": "插入分頁符或分節符", "DE.Views.Toolbar.tipPageMargins": "頁邊距", "DE.Views.Toolbar.tipPageOrient": "頁面方向", "DE.Views.Toolbar.tipPageSize": "頁面大小", "DE.Views.Toolbar.tipParagraphStyle": "段落風格", "DE.Views.Toolbar.tipPaste": "貼上", "DE.Views.Toolbar.tipPrColor": "段落背景顏色", "DE.Views.Toolbar.tipPrint": "列印", "DE.Views.Toolbar.tipRedo": "重複", "DE.Views.Toolbar.tipSave": "存檔", "DE.Views.Toolbar.tipSaveCoauth": "儲存您的更改，以供其他帳戶查看。", "DE.Views.Toolbar.tipSelectAll": "全選", "DE.Views.Toolbar.tipSendBackward": "向後發送", "DE.Views.Toolbar.tipSendForward": "向前進", "DE.Views.Toolbar.tipShowHiddenChars": "非印刷字符", "DE.Views.Toolbar.tipSynchronize": "該文檔已被其他帳戶更改。請單擊以儲存您的更改並重新加載更新。", "DE.Views.Toolbar.tipUndo": "復原", "DE.Views.Toolbar.tipWatermark": "編輯水印", "DE.Views.Toolbar.txtDistribHor": "水平分佈", "DE.Views.Toolbar.txtDistribVert": "垂直分佈", "DE.Views.Toolbar.txtMarginAlign": "對齊頁邊距", "DE.Views.Toolbar.txtObjectsAlign": "對齊所選物件", "DE.Views.Toolbar.txtPageAlign": "對齊頁面", "DE.Views.Toolbar.txtScheme1": "辦公室", "DE.Views.Toolbar.txtScheme10": "中位數", "DE.Views.Toolbar.txtScheme11": " 地鐵", "DE.Views.Toolbar.txtScheme12": "模組", "DE.Views.Toolbar.txtScheme13": "豐富的", "DE.Views.Toolbar.txtScheme14": "Oriel", "DE.Views.Toolbar.txtScheme15": "起源", "DE.Views.Toolbar.txtScheme16": "紙", "DE.Views.Toolbar.txtScheme17": "冬至", "DE.Views.Toolbar.txtScheme18": "技術", "DE.Views.Toolbar.txtScheme19": "跋涉", "DE.Views.Toolbar.txtScheme2": "灰階", "DE.Views.Toolbar.txtScheme20": "市區", "DE.Views.Toolbar.txtScheme21": "感染力", "DE.Views.Toolbar.txtScheme22": "新的Office", "DE.Views.Toolbar.txtScheme3": "頂尖", "DE.Views.Toolbar.txtScheme4": "方面", "DE.Views.Toolbar.txtScheme5": "Civic", "DE.Views.Toolbar.txtScheme6": "大堂", "DE.Views.Toolbar.txtScheme7": "產權", "DE.Views.Toolbar.txtScheme8": "流程", "DE.Views.Toolbar.txtScheme9": "鑄造廠", "DE.Views.ViewTab.textAlwaysShowToolbar": "永遠顯示工具欄", "DE.Views.ViewTab.textDarkDocument": "夜間模式文件", "DE.Views.ViewTab.textFitToPage": "配合紙張大小", "DE.Views.ViewTab.textFitToWidth": "配合寬度", "DE.Views.ViewTab.textInterfaceTheme": "介面主題", "DE.Views.ViewTab.textNavigation": "導航", "DE.Views.ViewTab.textOutline": "標題", "DE.Views.ViewTab.textRulers": "尺規", "DE.Views.ViewTab.textStatusBar": "狀態欄", "DE.Views.ViewTab.textZoom": "放大", "DE.Views.ViewTab.tipDarkDocument": "夜間模式文件", "DE.Views.ViewTab.tipFitToPage": "配合紙張大小", "DE.Views.ViewTab.tipFitToWidth": "配合寬度", "DE.Views.ViewTab.tipHeadings": "標題", "DE.Views.ViewTab.tipInterfaceTheme": "介面主題", "DE.Views.WatermarkSettingsDialog.textAuto": "自動", "DE.Views.WatermarkSettingsDialog.textBold": "粗體", "DE.Views.WatermarkSettingsDialog.textColor": "文字顏色", "DE.Views.WatermarkSettingsDialog.textDiagonal": "對角線", "DE.Views.WatermarkSettingsDialog.textFont": "字體", "DE.Views.WatermarkSettingsDialog.textFromFile": "從檔案", "DE.Views.WatermarkSettingsDialog.textFromStorage": "從存儲", "DE.Views.WatermarkSettingsDialog.textFromUrl": "從 URL", "DE.Views.WatermarkSettingsDialog.textHor": "水平", "DE.Views.WatermarkSettingsDialog.textImageW": "圖像水印", "DE.Views.WatermarkSettingsDialog.textItalic": "斜體", "DE.Views.WatermarkSettingsDialog.textLanguage": "語言", "DE.Views.WatermarkSettingsDialog.textLayout": "佈局", "DE.Views.WatermarkSettingsDialog.textNone": "無", "DE.Views.WatermarkSettingsDialog.textScale": "尺度", "DE.Views.WatermarkSettingsDialog.textSelect": "選擇圖片", "DE.Views.WatermarkSettingsDialog.textStrikeout": "淘汰", "DE.Views.WatermarkSettingsDialog.textText": "文字", "DE.Views.WatermarkSettingsDialog.textTextW": "文字水印", "DE.Views.WatermarkSettingsDialog.textTitle": "浮水印設定", "DE.Views.WatermarkSettingsDialog.textTransparency": "半透明", "DE.Views.WatermarkSettingsDialog.textUnderline": "底線", "DE.Views.WatermarkSettingsDialog.tipFontName": "字體名稱", "DE.Views.WatermarkSettingsDialog.tipFontSize": "字體大小"}