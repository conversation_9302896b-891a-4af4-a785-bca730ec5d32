{"Common.Controllers.Chat.notcriticalErrorTitle": "Avertissement", "Common.Controllers.Chat.textEnterMessage": "Entrez votre message ici", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonyme", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "L'objet est désactivé car il est en cours de modification par un autre utilisateur.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Avertissement", "Common.Controllers.ExternalMergeEditor.textAnonymous": "Anonyme", "Common.Controllers.ExternalMergeEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.warningText": "L'objet est désactivé car il est en cours de modification par un autre utilisateur.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Avertissement", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonyme", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "L'objet est désactivé, car il est en cours de modification par un autre utilisateur.", "Common.Controllers.ExternalOleEditor.warningTitle": "Avertissement", "Common.Controllers.History.notcriticalErrorTitle": "Avertissement", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "Pour comparer les documents, toutes les modifications apportées seront considérées comme acceptées. Voulez-vous continuer ?", "Common.Controllers.ReviewChanges.textAtLeast": "au moins ", "Common.Controllers.ReviewChanges.textAuto": "auto", "Common.Controllers.ReviewChanges.textBaseline": "Ligne de base", "Common.Controllers.ReviewChanges.textBold": "Gras", "Common.Controllers.ReviewChanges.textBreakBefore": "Saut de page avant", "Common.Controllers.ReviewChanges.textCaps": "Toutes en majuscules", "Common.Controllers.ReviewChanges.textCenter": "Aligner au centre", "Common.Controllers.ReviewChanges.textChar": "Niveau des caractères", "Common.Controllers.ReviewChanges.textChart": "Graphique", "Common.Controllers.ReviewChanges.textColor": "Couleur de police", "Common.Controllers.ReviewChanges.textContextual": "Ne pas ajouter d'intervalle entre paragraphes du même style", "Common.Controllers.ReviewChanges.textDeleted": "<b>Supprimé :</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Barré double", "Common.Controllers.ReviewChanges.textEquation": "Équation", "Common.Controllers.ReviewChanges.textExact": "exactement", "Common.Controllers.ReviewChanges.textFirstLine": "Première ligne", "Common.Controllers.ReviewChanges.textFontSize": "Taille de la police", "Common.Controllers.ReviewChanges.textFormatted": "Formaté", "Common.Controllers.ReviewChanges.textHighlight": "<PERSON><PERSON><PERSON> de surlignage", "Common.Controllers.ReviewChanges.textImage": "Image", "Common.Controllers.ReviewChanges.textIndentLeft": "Retrait à gauche", "Common.Controllers.ReviewChanges.textIndentRight": "Retrait à droite", "Common.Controllers.ReviewChanges.textInserted": "<b>Inséré :</b>", "Common.Controllers.ReviewChanges.textItalic": "Italique", "Common.Controllers.ReviewChanges.textJustify": "Justifier ", "Common.Controllers.ReviewChanges.textKeepLines": "Lignes solidaires", "Common.Controllers.ReviewChanges.textKeepNext": "Paragraphes solidaires", "Common.Controllers.ReviewChanges.textLeft": "<PERSON><PERSON><PERSON> à gauche", "Common.Controllers.ReviewChanges.textLineSpacing": "Interligne:", "Common.Controllers.ReviewChanges.textMultiple": "multiple ", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Pas de saut de page avant", "Common.Controllers.ReviewChanges.textNoContextual": "Ajouter un intervalle entre les paragraphes du même style", "Common.Controllers.ReviewChanges.textNoKeepLines": "Ne gardez pas de lignes ensemble", "Common.Controllers.ReviewChanges.textNoKeepNext": "Ne gardez pas avec la prochaine", "Common.Controllers.ReviewChanges.textNot": "Non", "Common.Controllers.ReviewChanges.textNoWidow": "Pas de contrôle des veuves", "Common.Controllers.ReviewChanges.textNum": "Changer la numérotation", "Common.Controllers.ReviewChanges.textOff": "{0} n'utilise plus le suivi des modifications. ", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} a désactivé le suivi des modifications pour tout le monde.", "Common.Controllers.ReviewChanges.textOn": "{0} utilise désormais le suivi des modifications.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} a activé le suivi des modifications pour tout le monde.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>Paragraphe supprimé</b> ", "Common.Controllers.ReviewChanges.textParaFormatted": "Paragraphe formaté", "Common.Controllers.ReviewChanges.textParaInserted": "<b>Paragraphe inséré</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b><PERSON>é<PERSON><PERSON><PERSON> vers le bas :</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b><PERSON>ép<PERSON><PERSON> vers le haut :</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Déplacé :</b>", "Common.Controllers.ReviewChanges.textPosition": "Position", "Common.Controllers.ReviewChanges.textRight": "<PERSON><PERSON><PERSON> d<PERSON>", "Common.Controllers.ReviewChanges.textShape": "Forme", "Common.Controllers.ReviewChanges.textShd": "Couleur d'arrière-plan", "Common.Controllers.ReviewChanges.textShow": "Afficher les modifications au", "Common.Controllers.ReviewChanges.textSmallCaps": "Petites majuscules", "Common.Controllers.ReviewChanges.textSpacing": "Espacement", "Common.Controllers.ReviewChanges.textSpacingAfter": "Espacement après", "Common.Controllers.ReviewChanges.textSpacingBefore": "Espacement avant", "Common.Controllers.ReviewChanges.textStrikeout": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSubScript": "Indice", "Common.Controllers.ReviewChanges.textSuperScript": "Exposant", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Paramètres du tableau modifiés</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b><PERSON>gnes de tableau ajoutées</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>Lignes de tableau supprimées</b>", "Common.Controllers.ReviewChanges.textTabs": "Changer les tabulations", "Common.Controllers.ReviewChanges.textTitleComparison": "Paramètres de comparaison", "Common.Controllers.ReviewChanges.textUnderline": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textUrl": "Coller l'adresse URL du document", "Common.Controllers.ReviewChanges.textWidow": "Contrôle des veuves", "Common.Controllers.ReviewChanges.textWord": "Niveau des mots", "Common.define.chartData.textArea": "Zone", "Common.define.chartData.textAreaStacked": "Zone empilée", "Common.define.chartData.textAreaStackedPer": "Aire Empilée 100%", "Common.define.chartData.textBar": "En barre", "Common.define.chartData.textBarNormal": "colonne groupée ", "Common.define.chartData.textBarNormal3d": "Barres groupées en 3D", "Common.define.chartData.textBarNormal3dPerspective": "Colonne 3D", "Common.define.chartData.textBarStacked": "Colonne empi<PERSON>", "Common.define.chartData.textBarStacked3d": "Histogramme empilé en 3D", "Common.define.chartData.textBarStackedPer": "100% colonne empilée", "Common.define.chartData.textBarStackedPer3d": "3-D 100% colonne empilée", "Common.define.chartData.textCharts": "Graphiques", "Common.define.chartData.textColumn": "Colonne", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Zone empilée - colonne en cluster ", "Common.define.chartData.textComboBarLine": "Colonne - ligne groupée", "Common.define.chartData.textComboBarLineSecondary": "Colonne ligne groupée sur le second axe", "Common.define.chartData.textComboCustom": "Combinaison personnalisée", "Common.define.chartData.textDoughnut": "Donut", "Common.define.chartData.textHBarNormal": "barre groupée ", "Common.define.chartData.textHBarNormal3d": "Barres groupées en 3D", "Common.define.chartData.textHBarStacked": "Barre empilée", "Common.define.chartData.textHBarStacked3d": "Barres empilées en 3D", "Common.define.chartData.textHBarStackedPer": "100% barre empilée", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% barre empilée", "Common.define.chartData.textLine": "Graphique en ligne", "Common.define.chartData.textLine3d": "ligne 3D", "Common.define.chartData.textLineMarker": "Ligne avec marqueurs", "Common.define.chartData.textLineStacked": "ligne empilée", "Common.define.chartData.textLineStackedMarker": "ligne empilée avec marqueurs", "Common.define.chartData.textLineStackedPer": "100% ligne empilée", "Common.define.chartData.textLineStackedPerMarker": "100% ligne empilée avec marqueurs", "Common.define.chartData.textPie": "Graphiques à secteurs", "Common.define.chartData.textPie3d": "<PERSON>mbert 3D", "Common.define.chartData.textPoint": "Nuages de points (XY)", "Common.define.chartData.textScatter": "Disperser", "Common.define.chartData.textScatterLine": "disperser avec des lignes droites ", "Common.define.chartData.textScatterLineMarker": "disperser avec des lignes droites", "Common.define.chartData.textScatterSmooth": "disperser avec des lignes lisses ", "Common.define.chartData.textScatterSmoothMarker": "disperser avec des lignes lisses et marqueurs", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "Surface", "Common.define.smartArt.textAccentedPicture": "Image accentuée", "Common.define.smartArt.textAccentProcess": "Processus accentué", "Common.define.smartArt.textAlternatingFlow": "Flux interactif", "Common.define.smartArt.textAlternatingHexagons": "Hexagones alternés", "Common.define.smartArt.textAlternatingPictureBlocks": "Blocs d'images alternées", "Common.define.smartArt.textAlternatingPictureCircles": "Cercles d'images alternées", "Common.define.smartArt.textArchitectureLayout": "Disposition architecture", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON> fl<PERSON>", "Common.define.smartArt.textAscendingPictureAccentProcess": "Processus accentué dans un ordre croissant avec images", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Processus en lacet simple", "Common.define.smartArt.textBasicBlockList": "Liste de blocs simple", "Common.define.smartArt.textBasicChevronProcess": "Processus en chevrons simple", "Common.define.smartArt.textBasicCycle": "Cycle simple", "Common.define.smartArt.textBasicMatrix": "Matrice simple", "Common.define.smartArt.textBasicPie": "Graphique en secteurs simple", "Common.define.smartArt.textBasicProcess": "Processus simple", "Common.define.smartArt.textBasicPyramid": "Pyramide simple", "Common.define.smartArt.textBasicRadial": "Radial simple", "Common.define.smartArt.textBasicTarget": "Cible simple", "Common.define.smartArt.textBasicTimeline": "Chronologie simple", "Common.define.smartArt.textBasicVenn": "Venn simple", "Common.define.smartArt.textBendingPictureAccentList": "Liste accentuée en lacet avec images", "Common.define.smartArt.textBendingPictureBlocks": "Blocs en lacet avec images", "Common.define.smartArt.textBendingPictureCaption": "Images en lacet avec légendes", "Common.define.smartArt.textBendingPictureCaptionList": "Liste d’images en lacet avec légendes", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Texte semi-transparent en lacet avec images", "Common.define.smartArt.textBlockCycle": "Cycle en blocs", "Common.define.smartArt.textBubblePictureList": "Liste d’images avec bulles", "Common.define.smartArt.textCaptionedPictures": "Images avec légende", "Common.define.smartArt.textChevronAccentProcess": "Processus accentué en chevrons", "Common.define.smartArt.textChevronList": "Liste de chevrons", "Common.define.smartArt.textCircleAccentTimeline": "Barre de planning accentuée avec cercles", "Common.define.smartArt.textCircleArrowProcess": "Processus en flèches circulaires", "Common.define.smartArt.textCirclePictureHierarchy": "Hiérarchie avec images rondes", "Common.define.smartArt.textCircleProcess": "Processus en cercles", "Common.define.smartArt.textCircleRelationship": "Relation circulaire", "Common.define.smartArt.textCircularBendingProcess": "Processus en lacets avec bulles", "Common.define.smartArt.textCircularPictureCallout": "Images circulaires avec légende", "Common.define.smartArt.textClosedChevronProcess": "Processus en chevrons fermés", "Common.define.smartArt.textContinuousArrowProcess": "Processus en flèche continue", "Common.define.smartArt.textContinuousBlockProcess": "Processus en bloc continu", "Common.define.smartArt.textContinuousCycle": "Cycle continu", "Common.define.smartArt.textContinuousPictureList": "Liste continue avec images", "Common.define.smartArt.textConvergingArrows": "Flèches convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergeant", "Common.define.smartArt.textConvergingText": "Texte convergent", "Common.define.smartArt.textCounterbalanceArrows": "Flèches d’équilibrage", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "<PERSON>rice de <PERSON>", "Common.define.smartArt.textDescendingBlockList": "Liste de blocs décroissante", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDivergingArrows": "Flèches divergentes", "Common.define.smartArt.textDivergingRadial": "Radial convergeant", "Common.define.smartArt.textEquation": "Équation", "Common.define.smartArt.textFramedTextPicture": "Images avec texte en encadré", "Common.define.smartArt.textFunnel": "Entonnoir", "Common.define.smartArt.textGear": "Engrenage", "Common.define.smartArt.textGridMatrix": "Matrice avec grille", "Common.define.smartArt.textGroupedList": "Liste groupée", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organigramme avec demi-cercles", "Common.define.smartArt.textHexagonCluster": "Groupe d’hexagones", "Common.define.smartArt.textHexagonRadial": "Hexagone radial", "Common.define.smartArt.textHierarchy": "Hiéra<PERSON>ie", "Common.define.smartArt.textHierarchyList": "Liste hiérarchique", "Common.define.smartArt.textHorizontalBulletList": "Liste à puces horizontale", "Common.define.smartArt.textHorizontalHierarchy": "Hiérarchie horizontale", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hiérarchie horizontale avec étiquettes", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hiérarchie horizontale à plusieurs niveaux", "Common.define.smartArt.textHorizontalOrganizationChart": "Organigramme horizontal", "Common.define.smartArt.textHorizontalPictureList": "Liste horizontale avec images", "Common.define.smartArt.textIncreasingArrowProcess": "Processus en flèches croissant", "Common.define.smartArt.textIncreasingCircleProcess": "Processus ascendant avec cercles", "Common.define.smartArt.textInterconnectedBlockProcess": "Processus en blocs interconnectés", "Common.define.smartArt.textInterconnectedRings": "Anneaux interconnectés", "Common.define.smartArt.textInvertedPyramid": "Pyramide inversée", "Common.define.smartArt.textLabeledHierarchy": "Hiérarchie libellée", "Common.define.smartArt.textLinearVenn": "Venn lin<PERSON>", "Common.define.smartArt.textLinedList": "Liste alignée", "Common.define.smartArt.textList": "Liste", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Cycle multidirectionnel", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigramme avec titre et nom", "Common.define.smartArt.textNestedTarget": "Cible imbriquée", "Common.define.smartArt.textNondirectionalCycle": "Cycle non directionnel", "Common.define.smartArt.textOpposingArrows": "Flèches opposées", "Common.define.smartArt.textOpposingIdeas": "Idées opposées", "Common.define.smartArt.textOrganizationChart": "Organigramme", "Common.define.smartArt.textOther": "<PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Processus à phases", "Common.define.smartArt.textPicture": "Image", "Common.define.smartArt.textPictureAccentBlocks": "Blocs accentués avec images", "Common.define.smartArt.textPictureAccentList": "Liste accentuée avec images", "Common.define.smartArt.textPictureAccentProcess": "Processus accentué avec images", "Common.define.smartArt.textPictureCaptionList": "Liste de légendes d'images", "Common.define.smartArt.textPictureFrame": "Cadre de l’image", "Common.define.smartArt.textPictureGrid": "Grille d’images", "Common.define.smartArt.textPictureLineup": "Alignement d’images", "Common.define.smartArt.textPictureOrganizationChart": "Organigramme avec images", "Common.define.smartArt.textPictureStrips": "Bandes avec images", "Common.define.smartArt.textPieProcess": "Processus à secteurs", "Common.define.smartArt.textPlusAndMinus": "Plus et moins", "Common.define.smartArt.textProcess": "Processus", "Common.define.smartArt.textProcessArrows": "Processus en flèches", "Common.define.smartArt.textProcessList": "Liste de processus", "Common.define.smartArt.textPyramid": "Pyramide", "Common.define.smartArt.textPyramidList": "Liste pyramidale", "Common.define.smartArt.textRadialCluster": "Groupe en rayon", "Common.define.smartArt.textRadialCycle": "Cycle radial", "Common.define.smartArt.textRadialList": "Liste radiale", "Common.define.smartArt.textRadialPictureList": "Liste radiale avec images", "Common.define.smartArt.textRadialVenn": "Venn radial", "Common.define.smartArt.textRandomToResultProcess": "Processus d’idées aléatoires avec résultat", "Common.define.smartArt.textRelationship": "Relation", "Common.define.smartArt.textRepeatingBendingProcess": "Processus en lacets", "Common.define.smartArt.textReverseList": "Liste inversée", "Common.define.smartArt.textSegmentedCycle": "Cycle segmenté", "Common.define.smartArt.textSegmentedProcess": "Processus segmenté", "Common.define.smartArt.textSegmentedPyramid": "Pyramide segmentée", "Common.define.smartArt.textSnapshotPictureList": "Liste d’images instantanées", "Common.define.smartArt.textSpiralPicture": "Images en spirale", "Common.define.smartArt.textSquareAccentList": "Liste accentuée avec carrés", "Common.define.smartArt.textStackedList": "Liste empilée", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepDownProcess": "Processus descendant", "Common.define.smartArt.textStepUpProcess": "Processus ascendant", "Common.define.smartArt.textSubStepProcess": "Processus avec sous-étapes", "Common.define.smartArt.textTabbedArc": "Arc à onglets", "Common.define.smartArt.textTableHierarchy": "Hiérarchie de tables", "Common.define.smartArt.textTableList": "Liste de tables", "Common.define.smartArt.textTabList": "Liste des onglets", "Common.define.smartArt.textTargetList": "Liste cible", "Common.define.smartArt.textTextCycle": "Cycle de texte", "Common.define.smartArt.textThemePictureAccent": "Images de thème accentué", "Common.define.smartArt.textThemePictureAlternatingAccent": "Images de thème alternées accentué", "Common.define.smartArt.textThemePictureGrid": "Grille d’images de thème", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON> avec titres", "Common.define.smartArt.textTitledPictureAccentList": "Liste accentuée avec images et titre", "Common.define.smartArt.textTitledPictureBlocks": "Blocs d’images avec titre", "Common.define.smartArt.textTitlePictureLineup": "Alignement d’images avec titre", "Common.define.smartArt.textTrapezoidList": "Liste trapézo<PERSON>dale", "Common.define.smartArt.textUpwardArrow": "Flèche vers le haut", "Common.define.smartArt.textVaryingWidthList": "Liste à largeur variable", "Common.define.smartArt.textVerticalAccentList": "Liste accentuée verticale", "Common.define.smartArt.textVerticalArrowList": "Liste verticale avec flèches", "Common.define.smartArt.textVerticalBendingProcess": "Processus vertical en lacet", "Common.define.smartArt.textVerticalBlockList": "Liste de blocs verticale", "Common.define.smartArt.textVerticalBoxList": "Liste de zones verticale", "Common.define.smartArt.textVerticalBracketList": "Liste de crochets verticale", "Common.define.smartArt.textVerticalBulletList": "Liste à puces verticale", "Common.define.smartArt.textVerticalChevronList": "Liste de chevrons verticale", "Common.define.smartArt.textVerticalCircleList": "Liste de cercles verticale", "Common.define.smartArt.textVerticalCurvedList": "Liste courbe verticale", "Common.define.smartArt.textVerticalEquation": "Équation verticale", "Common.define.smartArt.textVerticalPictureAccentList": "Liste accentuée verticale avec images", "Common.define.smartArt.textVerticalPictureList": "Liste d’images verticale", "Common.define.smartArt.textVerticalProcess": "Processus vertical", "Common.Translation.textMoreButton": "Plus", "Common.Translation.tipFileLocked": "Le document est verrouillé pour l'édition. Vous pouvez apporter des modifications et l'enregistrer comme copie locale ultérieurement.", "Common.Translation.tipFileReadOnly": "Le fichier est disponible en lecture seule. Pour sauvegarder vos modifications, enregistrez le fichier sous un nouveau nom ou à un autre endroit.", "Common.Translation.warnFileLocked": "Vous ne pouvez pas modifier ce fichier car il a été modifié avec une autre application.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>er une copie", "Common.Translation.warnFileLockedBtnView": "Ouvrir pour visualisation", "Common.UI.ButtonColored.textAutoColor": "Automatique", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> person<PERSON>", "Common.UI.Calendar.textApril": "Avril", "Common.UI.Calendar.textAugust": "Août", "Common.UI.Calendar.textDecember": "décembre", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "janvier", "Common.UI.Calendar.textJuly": "juillet", "Common.UI.Calendar.textJune": "juin", "Common.UI.Calendar.textMarch": "mars", "Common.UI.Calendar.textMay": "mai", "Common.UI.Calendar.textMonths": "mois", "Common.UI.Calendar.textNovember": "novembre", "Common.UI.Calendar.textOctober": "octobre", "Common.UI.Calendar.textSeptember": "septembre", "Common.UI.Calendar.textShortApril": "Avr.", "Common.UI.Calendar.textShortAugust": "Août", "Common.UI.Calendar.textShortDecember": "déc.", "Common.UI.Calendar.textShortFebruary": "févr.", "Common.UI.Calendar.textShortFriday": "ven.", "Common.UI.Calendar.textShortJanuary": "janv.", "Common.UI.Calendar.textShortJuly": "juill.", "Common.UI.Calendar.textShortJune": "juin", "Common.UI.Calendar.textShortMarch": "mars", "Common.UI.Calendar.textShortMay": "mai", "Common.UI.Calendar.textShortMonday": "lun.", "Common.UI.Calendar.textShortNovember": "nov.", "Common.UI.Calendar.textShortOctober": "oct.", "Common.UI.Calendar.textShortSaturday": "sam.", "Common.UI.Calendar.textShortSeptember": "sept.", "Common.UI.Calendar.textShortSunday": "dim.", "Common.UI.Calendar.textShortThursday": "jeu.", "Common.UI.Calendar.textShortTuesday": "mar.", "Common.UI.Calendar.textShortWednesday": "mer.", "Common.UI.Calendar.textYears": "ann<PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Pas de bordures", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Pas de bordures", "Common.UI.ComboDataView.emptyComboText": "Aucun style", "Common.UI.ExtendedColorDialog.addButtonText": "Ajouter", "Common.UI.ExtendedColorDialog.textCurrent": "Actuelle", "Common.UI.ExtendedColorDialog.textHexErr": "La valeur saisie est incorrecte. <br>Entrez une valeur de 000000 à FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nouvelle", "Common.UI.ExtendedColorDialog.textRGBErr": "La valeur saisie est incorrecte. <br>Entrez une valeur numérique de 0 à 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> de couleur", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Masquer le mot de passe", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Afficher le mot de passe", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON>er la recherche", "Common.UI.SearchBar.tipNextResult": "Résultat suivant", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres avancés", "Common.UI.SearchBar.tipPreviousResult": "Résultat précédent", "Common.UI.SearchDialog.textHighlight": "Surligner les résultats", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON> sensible", "Common.UI.SearchDialog.textReplaceDef": "Saisis<PERSON>z le texte de remplacement", "Common.UI.SearchDialog.textSearchStart": "Entrez votre texte ici", "Common.UI.SearchDialog.textTitle": "Re<PERSON><PERSON> et remplacer", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Seulement les mots entiers", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON>lace<PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Remplacer tout", "Common.UI.SynchronizeTip.textDontShow": "Ne plus afficher ce message", "Common.UI.SynchronizeTip.textSynchronize": "Le document a été modifié par un autre utilisateur.<br>Cliquez pour enregistrer vos modifications et recharger les mises à jour.", "Common.UI.ThemeColorPalette.textRecentColors": "Couleurs récentes", "Common.UI.ThemeColorPalette.textStandartColors": "Couleurs standard", "Common.UI.ThemeColorPalette.textThemeColors": "Couleurs de thème", "Common.UI.Themes.txtThemeClassicLight": "Classique clair", "Common.UI.Themes.txtThemeContrastDark": "Contraste élevé sombre", "Common.UI.Themes.txtThemeDark": "Sombre", "Common.UI.Themes.txtThemeLight": "<PERSON>", "Common.UI.Themes.txtThemeSystem": "Identique à système", "Common.UI.Window.cancelButtonText": "Annuler", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Non", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Ne plus afficher ce message", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Avertissement", "Common.UI.Window.yesButtonText": "O<PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "adresse: ", "Common.Views.About.txtLicensee": "CESSIONNAIRE", "Common.Views.About.txtLicensor": "CONCÉDANT", "Common.Views.About.txtMail": "émail: ", "Common.Views.About.txtPoweredBy": "Réalisation", "Common.Views.About.txtTel": "tél.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Ajouter", "Common.Views.AutoCorrectDialog.textApplyText": "Appliquer pendant la frappe", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Correction automatique de texte", "Common.Views.AutoCorrectDialog.textAutoFormat": "Mise en forme automatique au cours de la frappe", "Common.Views.AutoCorrectDialog.textBulleted": "Listes à puces automatiques", "Common.Views.AutoCorrectDialog.textBy": "Par", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Ajouter un point avec un double espace", "Common.Views.AutoCorrectDialog.textFLCells": "Mettre la première lettre des cellules du tableau en majuscule", "Common.Views.AutoCorrectDialog.textFLSentence": "Majuscule en début de phrase", "Common.Views.AutoCorrectDialog.textHyperlink": "Adresses Internet et réseau avec des liens hypertextes", "Common.Views.AutoCorrectDialog.textHyphens": "Traits d’union (--) avec tiret (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "AutoMaths", "Common.Views.AutoCorrectDialog.textNumbered": "Listes numérotées automatiquement", "Common.Views.AutoCorrectDialog.textQuotes": "Guillemets typographiques par des guillemets", "Common.Views.AutoCorrectDialog.textRecognized": "Fonctions reconnues", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Les expressions suivantes sont les expressions mathématiques reconnues. Elles ne seront pas mises en italique automatiquement.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON>lace<PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Remplacer pendant la frappe", "Common.Views.AutoCorrectDialog.textReplaceType": "Remp<PERSON><PERSON> le texte au cours de la frappe", "Common.Views.AutoCorrectDialog.textReset": "Réinitialiser", "Common.Views.AutoCorrectDialog.textResetAll": "Rétablir paramètres par défaut", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Correction automatique", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Fonctions reconnues doivent contenir seulement des lettres de A à Z, majuscules et minuscules.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Toute expression que vous avez ajoutée sera supprimée et toute expression que vous avez supprimée sera restaurée. Voulez-vous continuer ?", "Common.Views.AutoCorrectDialog.warnReplace": "Ce symbol pour 1% existe déjà. Voulez-vous le remplacer ?", "Common.Views.AutoCorrectDialog.warnReset": "Toutes les autocorrections que vous avez ajouté seront supprimées et celles que vous avez modifiées seront réinitialisées à leurs valeurs originales. Voulez-vous continuer ?", "Common.Views.AutoCorrectDialog.warnRestore": "Ce symbol d'autocorrection pour 1% sera réinitialisé à sa valeur initiale. Voulez-vous continuer ?", "Common.Views.Chat.textSend": "Envoyer", "Common.Views.Comments.mniAuthorAsc": "Auteur de A à Z", "Common.Views.Comments.mniAuthorDesc": "Auteur de Z à A", "Common.Views.Comments.mniDateAsc": "Plus ancien", "Common.Views.Comments.mniDateDesc": "Plus récent", "Common.Views.Comments.mniFilterGroups": "Filtrer par groupe", "Common.Views.Comments.mniPositionAsc": "Du haut", "Common.Views.Comments.mniPositionDesc": "Du bas", "Common.Views.Comments.textAdd": "Ajouter", "Common.Views.Comments.textAddComment": "Ajouter", "Common.Views.Comments.textAddCommentToDoc": "Ajouter un commentaire au document", "Common.Views.Comments.textAddReply": "Ajouter une réponse", "Common.Views.Comments.textAll": "<PERSON>ut", "Common.Views.Comments.textAnonym": "Invi<PERSON>", "Common.Views.Comments.textCancel": "Annuler", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> les commentaires", "Common.Views.Comments.textComments": "Commentaires", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Entrez votre commentaire ici", "Common.Views.Comments.textHintAddComment": "Ajouter un commentaire", "Common.Views.Comments.textOpenAgain": "Ouvrir à nouveau", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "R<PERSON>ol<PERSON>", "Common.Views.Comments.textSort": "Trier les commentaires", "Common.Views.Comments.textViewResolved": "Vous n'avez pas la permission de rouvrir le commentaire", "Common.Views.Comments.txtEmpty": "Il n'y a pas de commentaires dans le document.", "Common.Views.CopyWarningDialog.textDontShow": "Ne plus afficher ce message", "Common.Views.CopyWarningDialog.textMsg": "Vous pouvez réaliser les actions de copier, couper et coller en utilisant les boutons de la barre d'outils et à l'aide du menu contextuel à partir de cet onglet uniquement.<br><br>Pour copier ou coller de / vers les applications en dehors de l'onglet de l'éditeur, utilisez les combinaisons de touches suivantes :", "Common.Views.CopyWarningDialog.textTitle": "Actions copier, couper et coller", "Common.Views.CopyWarningDialog.textToCopy": "pour <PERSON><PERSON>r", "Common.Views.CopyWarningDialog.textToCut": "pour Couper", "Common.Views.CopyWarningDialog.textToPaste": "pour <PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "Chargement en cours...", "Common.Views.DocumentAccessDialog.textTitle": "Paramètres de partage", "Common.Views.ExternalDiagramEditor.textTitle": "Éditeur de graphique", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Enregistrer et quitter", "Common.Views.ExternalMergeEditor.textTitle": "Destinataires de fusion et publipostage", "Common.Views.ExternalOleEditor.textTitle": "Tableur", "Common.Views.Header.labelCoUsersDescr": "Le document est en cours de modification par les utilisateurs suivants :", "Common.Views.Header.textAddFavorite": "Marquer en tant que favori", "Common.Views.Header.textAdvSettings": "Paramètres avancés", "Common.Views.Header.textBack": "<PERSON>u<PERSON><PERSON>r l'emplacement du fichier", "Common.Views.Header.textCompactView": "Masquer la barre d'outils", "Common.Views.Header.textHideLines": "Masquer les règles", "Common.Views.Header.textHideStatusBar": "Masquer la barre d'état", "Common.Views.Header.textReadOnly": "Lecture seule", "Common.Views.Header.textRemoveFavorite": "Enlever des favoris", "Common.Views.Header.textShare": "Partager", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON>rer les droits d'accès au document", "Common.Views.Header.tipDownload": "Télécharger le fichier", "Common.Views.Header.tipGoEdit": "Modifier le fichier courant", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON> le fi<PERSON>er", "Common.Views.Header.tipPrintQuick": "Impression rapide", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Enregistrer", "Common.Views.Header.tipSearch": "Recherche", "Common.Views.Header.tipUndo": "Annuler", "Common.Views.Header.tipUsers": "Afficher les utilisateurs", "Common.Views.Header.tipViewSettings": "Paramètres d'affichage", "Common.Views.Header.tipViewUsers": "Afficher les utilisateurs et gérer les droits d'accès aux documents", "Common.Views.Header.txtAccessRights": "Modifier les droits d'accès", "Common.Views.Header.txtRename": "<PERSON>mmer", "Common.Views.History.textCloseHistory": "Fermer l'historique", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Masquer les modifications détaillées", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Développer", "Common.Views.History.textShowAll": "Afficher les modifications détaillées", "Common.Views.History.textVer": "ver. ", "Common.Views.ImageFromUrlDialog.textUrl": "Coller URL d'image", "Common.Views.ImageFromUrlDialog.txtEmpty": "Ce champ est obligatoire", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ce champ doit être une URL au format \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Specifiez les lignes valides et le total des colonnes.", "Common.Views.InsertTableDialog.txtColumns": "Nombre de colonnes", "Common.Views.InsertTableDialog.txtMaxText": "La valeur maximale pour ce champ est {0}.", "Common.Views.InsertTableDialog.txtMinText": "La valeur minimale pour ce champ est {0}.", "Common.Views.InsertTableDialog.txtRows": "Nombre de lignes", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON>au", "Common.Views.InsertTableDialog.txtTitleSplit": "Fractionner la cellule", "Common.Views.LanguageDialog.labelSelect": "Sélectionner la langue du document", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>er", "Common.Views.OpenDialog.txtEncoding": "Codage ", "Common.Views.OpenDialog.txtIncorrectPwd": "Le mot de passe est incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Entrer le mot de passe pour ouvrir le fichier", "Common.Views.OpenDialog.txtPassword": "Mot de passe", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Une fois le mot de passe saisi et le fichier ouvert, le mot de passe actuel de fichier sera réinitialisé.", "Common.Views.OpenDialog.txtTitle": "Choisir %1 des options ", "Common.Views.OpenDialog.txtTitleProtected": "Fichier protégé", "Common.Views.PasswordDialog.txtDescription": "Indiquez un mot de passe pour protéger ce document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Le mot de passe de confirmation n'est pas identique", "Common.Views.PasswordDialog.txtPassword": "Mot de passe", "Common.Views.PasswordDialog.txtRepeat": "Confirmer le mot de passe", "Common.Views.PasswordDialog.txtTitle": "Définir un mot de passe", "Common.Views.PasswordDialog.txtWarning": "Attention : si vous oubliez ou perdez votre mot de passe, il sera impossible de le récupérer. Conservez-le en lieu sûr.", "Common.Views.PluginDlg.textLoading": "Chargement", "Common.Views.Plugins.groupCaption": "Modules complémentaires", "Common.Views.Plugins.strPlugins": "Plug-ins", "Common.Views.Plugins.textClosePanel": "Ferm<PERSON> le plugin", "Common.Views.Plugins.textLoading": "Chargement", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Chiffrer avec mot de passe", "Common.Views.Protection.hintDelPwd": "Supprimer le mot de passe", "Common.Views.Protection.hintPwd": "Modifier ou supprimer le mot de passe", "Common.Views.Protection.hintSignature": "Ajouter une signature numérique ou une ligne de signature", "Common.Views.Protection.txtAddPwd": "Ajouter un mot de passe", "Common.Views.Protection.txtChangePwd": "Modifier le mot de passe", "Common.Views.Protection.txtDeletePwd": "Supprimer le mot de passe", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Ajouter une signature numérique", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Ajouter la zone de signature", "Common.Views.RenameDialog.textName": "Nom de fi<PERSON>er", "Common.Views.RenameDialog.txtInvalidName": "Un nom de fichier ne peut pas contenir les caractères suivants :", "Common.Views.ReviewChanges.hintNext": "À la modification suivante", "Common.Views.ReviewChanges.hintPrev": "À la modification précédente", "Common.Views.ReviewChanges.mniFromFile": "Document à partir d'un fichier", "Common.Views.ReviewChanges.mniFromStorage": "Document à partir de stockage", "Common.Views.ReviewChanges.mniFromUrl": "Document à partir d'une URL", "Common.Views.ReviewChanges.mniSettings": "Paramètres de comparaison", "Common.Views.ReviewChanges.strFast": "Rapide", "Common.Views.ReviewChanges.strFastDesc": "Co-édition en temps réel. Tous les changements sont enregistrés automatiquement.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Utilisez le bouton \"Enregistrer\" pour synchroniser les modifications que vous et d'autres personnes faites.", "Common.Views.ReviewChanges.textEnable": "Activé", "Common.Views.ReviewChanges.textWarnTrackChanges": "Le suivi des modifications sera activé pour tous les utilisateurs avec un accès complet. La prochaine fois que quelqu'un ouvrira le document, le suivi des modifications restera activé. ", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Activer le suivi des changements pour tout le monde", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accepter la modification actuelle", "Common.Views.ReviewChanges.tipCoAuthMode": "Définir le mode de co-édition", "Common.Views.ReviewChanges.tipCommentRem": "Supprimer les commentaires", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Supprimer les commentaires existants", "Common.Views.ReviewChanges.tipCommentResolve": "Résoudre les commentaires", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Résoudre les commentaires actuels", "Common.Views.ReviewChanges.tipCompare": "Comparer le document actif avec un autre document", "Common.Views.ReviewChanges.tipHistory": "Afficher versions", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeter cette modification", "Common.Views.ReviewChanges.tipReview": "Suivi des modifications", "Common.Views.ReviewChanges.tipReviewView": "Sélectionner le mode souhaité.", "Common.Views.ReviewChanges.tipSetDocLang": "Définir la langue du document", "Common.Views.ReviewChanges.tipSetSpelling": "Vérification de l'orthographe", "Common.Views.ReviewChanges.tipSharing": "<PERSON><PERSON>rer les droits d'accès au document", "Common.Views.ReviewChanges.txtAccept": "Accepter", "Common.Views.ReviewChanges.txtAcceptAll": "Accepter toutes les modifications", "Common.Views.ReviewChanges.txtAcceptChanges": "Accepter les modifications", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accepter la modification actuelle", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Mode de co-édition ", "Common.Views.ReviewChanges.txtCommentRemAll": "Supprimer tous les commentaires", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Supprimer les commentaires actuels", "Common.Views.ReviewChanges.txtCommentRemMy": "Supprimer mes commentaires", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Supprimer mes commentaires actuels", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON> tous les commentaires comme résolus", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Marquer les commentaires actuels comme résolus", "Common.Views.ReviewChanges.txtCommentResolveMy": "Marquer mes commentaires comme résolus", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Marquer mes commentaires comme résolus", "Common.Views.ReviewChanges.txtCompare": "Comparer", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtEditing": "Modifier", "Common.Views.ReviewChanges.txtFinal": "Toutes les modifications acceptées {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historique des versions", "Common.Views.ReviewChanges.txtMarkup": "Toutes les modifications {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Balisage et bulles", "Common.Views.ReviewChanges.txtMarkupSimple": "Tous les changements {0}<br><PERSON><PERSON> <PERSON> ballons", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Balisage uniquement", "Common.Views.ReviewChanges.txtNext": "Suivante", "Common.Views.ReviewChanges.txtOff": "OFF pour moi ", "Common.Views.ReviewChanges.txtOffGlobal": "OFF pour moi et tout le monde", "Common.Views.ReviewChanges.txtOn": "ON pour moi ", "Common.Views.ReviewChanges.txtOnGlobal": "ON pour moi et tout le monde", "Common.Views.ReviewChanges.txtOriginal": "Toutes les modifications rejetées {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Précédente", "Common.Views.ReviewChanges.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Refuser tous les changements", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeter les modifications", "Common.Views.ReviewChanges.txtRejectCurrent": "Refuser des changements actuels", "Common.Views.ReviewChanges.txtSharing": "Partage", "Common.Views.ReviewChanges.txtSpelling": "Vérification de l'orthographe", "Common.Views.ReviewChanges.txtTurnon": "Suivi des modifications", "Common.Views.ReviewChanges.txtView": "Mode d'affichage", "Common.Views.ReviewChangesDialog.textTitle": "<PERSON>é<PERSON><PERSON> les <PERSON>", "Common.Views.ReviewChangesDialog.txtAccept": "Accepter", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Accepter toutes les modifications", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Accepter la modification actuelle", "Common.Views.ReviewChangesDialog.txtNext": "À la modification suivante", "Common.Views.ReviewChangesDialog.txtPrev": "À la modification précédente", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "Refuser toutes les modifications", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Refuser la modification actuelle", "Common.Views.ReviewPopover.textAdd": "Ajouter", "Common.Views.ReviewPopover.textAddReply": "Ajouter une réponse", "Common.Views.ReviewPopover.textCancel": "Annuler", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Entrez votre commentaire ici", "Common.Views.ReviewPopover.textFollowMove": "Suivre le Mouvement", "Common.Views.ReviewPopover.textMention": "+mention donne l'accès au document et notifie par courriel  ", "Common.Views.ReviewPopover.textMentionNotify": "+mention notifie l'utilisateur par courriel", "Common.Views.ReviewPopover.textOpenAgain": "Ouvrir à nouveau", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Vous n'avez pas la permission de rouvrir le commentaire", "Common.Views.ReviewPopover.txtAccept": "Accepter", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Modifier", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Chargement", "Common.Views.SaveAsDlg.textTitle": "Dossier pour enregistrement", "Common.Views.SearchPanel.textCaseSensitive": "Sensible à la casse", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON>er la recherche", "Common.Views.SearchPanel.textContentChanged": "Document modifié.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Re<PERSON><PERSON> et remplacer", "Common.Views.SearchPanel.textMatchUsingRegExp": "Correspondance à l'aide d'expressions régulières", "Common.Views.SearchPanel.textNoMatches": "Aucune correspondance", "Common.Views.SearchPanel.textNoSearchResults": "Aucun résultat de recherche", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON>lace<PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Remplacer tout", "Common.Views.SearchPanel.textReplaceWith": "Remplacer par", "Common.Views.SearchPanel.textSearchAgain": "{0}Effectuer une nouvelle recherche{1} pour obtenir des résultats précis.", "Common.Views.SearchPanel.textSearchHasStopped": "La recherche a été arrêtée", "Common.Views.SearchPanel.textSearchResults": "Résultats de la recherche : {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Il y a trop de résultats pour les montrer ici", "Common.Views.SearchPanel.textWholeWords": "Mots entiers uniquement", "Common.Views.SearchPanel.tipNextResult": "Résultat suivant", "Common.Views.SearchPanel.tipPreviousResult": "Résultat précédent", "Common.Views.SelectFileDlg.textLoading": "Chargement", "Common.Views.SelectFileDlg.textTitle": "Sélectionner la source de données", "Common.Views.SignDialog.textBold": "Gras", "Common.Views.SignDialog.textCertificate": "Certificat", "Common.Views.SignDialog.textChange": "Modifier", "Common.Views.SignDialog.textInputName": "Saisir le nom du signataire", "Common.Views.SignDialog.textItalic": "Italique", "Common.Views.SignDialog.textNameError": "Veuillez indiquer le nom du signataire.", "Common.Views.SignDialog.textPurpose": "But de la signature du document", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Sélectionner une image", "Common.Views.SignDialog.textSignature": "L'aspect de votre signature", "Common.Views.SignDialog.textTitle": "Signer le document", "Common.Views.SignDialog.textUseImage": "ou cliquez sur \"Sélectionner une image\" afin d'utiliser une image en tant que signature", "Common.Views.SignDialog.textValid": "Valide de %1 à %2", "Common.Views.SignDialog.tipFontName": "Nom de police", "Common.Views.SignDialog.tipFontSize": "Taille de police", "Common.Views.SignSettingsDialog.textAllowComment": "Autoriser le signataire à ajouter un commentaire dans la boîte de dialogue de la signature", "Common.Views.SignSettingsDialog.textDefInstruction": "Avant de signer un document, vérifiez que le contenu que vous signez est correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail du signataire suggéré", "Common.Views.SignSettingsDialog.textInfoName": "Signataire suggéré", "Common.Views.SignSettingsDialog.textInfoTitle": "Titre du signataire suggéré", "Common.Views.SignSettingsDialog.textInstructions": "Instructions pour les signataires", "Common.Views.SignSettingsDialog.textShowDate": "Afficher la date de signature à côté de la ligne de signature", "Common.Views.SignSettingsDialog.textTitle": "Configuration de signature", "Common.Views.SignSettingsDialog.txtEmpty": "Ce champ est obligatoire.", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valeur hexadécimale Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Symbole de copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON><PERSON> double fermant", "Common.Views.SymbolTableDialog.textDOQuote": "Double guillemet ouvrant", "Common.Views.SymbolTableDialog.textEllipsis": "Points de suspension", "Common.Views.SymbolTableDialog.textEmDash": "Tiret cadratin", "Common.Views.SymbolTableDialog.textEmSpace": "Espace cadratin", "Common.Views.SymbolTableDialog.textEnDash": "Tiret demi-cadratin", "Common.Views.SymbolTableDialog.textEnSpace": "Espace demi-cadratin", "Common.Views.SymbolTableDialog.textFont": "Police", "Common.Views.SymbolTableDialog.textNBHyphen": "Trait d’union insécable", "Common.Views.SymbolTableDialog.textNBSpace": "Espace insécable", "Common.Views.SymbolTableDialog.textPilcrow": "Pied-de-mouche", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Espace cadratin", "Common.Views.SymbolTableDialog.textRange": "Plage", "Common.Views.SymbolTableDialog.textRecent": "Caractères spéciaux récemment utilisés", "Common.Views.SymbolTableDialog.textRegistered": "Symbole de marque déposée", "Common.Views.SymbolTableDialog.textSCQuote": "G<PERSON><PERSON>et simple fermant", "Common.Views.SymbolTableDialog.textSection": "Paragraphe", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON>e <PERSON> r<PERSON>ci", "Common.Views.SymbolTableDialog.textSHyphen": "Trait d'union conditionnel", "Common.Views.SymbolTableDialog.textSOQuote": "G<PERSON><PERSON><PERSON> simple ouvrant", "Common.Views.SymbolTableDialog.textSpecial": "symboles spéciaux", "Common.Views.SymbolTableDialog.textSymbols": "Symboles", "Common.Views.SymbolTableDialog.textTitle": "Symbole", "Common.Views.SymbolTableDialog.textTradeMark": "Symbole de marque", "Common.Views.UserNameDialog.textDontShow": "Ne plus me demander à nouveau", "Common.Views.UserNameDialog.textLabel": "Étiquette :", "Common.Views.UserNameDialog.textLabelError": "Étiquette ne doit pas être vide", "DE.Controllers.DocProtection.txtIsProtectedComment": "Le document est protégé. V<PERSON> pouvez uniquement laisser des commentaires dans ce document.", "DE.Controllers.DocProtection.txtIsProtectedForms": "Le document est protégé. V<PERSON> pouvez uniquement remplir les formulaires de ce document.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "Le document est protégé. Vous pouvez modifier ce document, mais toutes les modifications seront suivies.", "DE.Controllers.DocProtection.txtIsProtectedView": "Le document est protégé. Vous pouvez uniquement consulter ce document.", "DE.Controllers.DocProtection.txtWasProtectedComment": "Le document a été protégé par un autre utilisateur.\nV<PERSON> pouvez uniquement laisser des commentaires dans ce document.", "DE.Controllers.DocProtection.txtWasProtectedForms": "Le document a été protégé par un autre utilisateur.\nV<PERSON> pouvez uniquement remplir les formulaires de ce document.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "Le document a été protégé par un autre utilisateur.\nVous pouvez modifier ce document, mais toutes les modifications seront suivies.", "DE.Controllers.DocProtection.txtWasProtectedView": "Le document a été protégé par un autre utilisateur.\nVous pouvez uniquement afficher ce document.", "DE.Controllers.DocProtection.txtWasUnprotected": "La protection du document a été retirée.", "DE.Controllers.LeftMenu.leavePageText": "Toutes les modifications non enregistrées dans ce document seront perdus.<br> <PERSON><PERSON><PERSON> sur \"Annuler\", puis \"Enregistrer\" pour les sauver. Cliquez sur \"OK\" pour annuler toutes les modifications non enregistrées.", "DE.Controllers.LeftMenu.newDocumentTitle": "Document sans nom", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Avertissement", "DE.Controllers.LeftMenu.requestEditRightsText": "Demande des droits de modification...", "DE.Controllers.LeftMenu.textLoadHistory": "Chargement de l'historique des versions...", "DE.Controllers.LeftMenu.textNoTextFound": "Votre recherche n'a donné aucun résultat.S'il vous plaît, modifiez vos critères de recherche.", "DE.Controllers.LeftMenu.textReplaceSkipped": "Le remplacement est fait. {0} occurrences ont été ignorées.", "DE.Controllers.LeftMenu.textReplaceSuccess": "La recherche est effectuée. Occurrences ont été remplacées:{0}", "DE.Controllers.LeftMenu.txtCompatible": "Le fichier sera enregistré au nouveau format. Toutes les fonctionnalités des éditeurs vous seront disponibles, mais cela peut affecter la mise en page du document.<br>Activez l'option \" Compatibilité \" dans les paramètres avancés pour rendre votre fichier compatible avec les anciennes versions de MS Word.   ", "DE.Controllers.LeftMenu.txtUntitled": "Sans titre", "DE.Controllers.LeftMenu.warnDownloadAs": "Si vous continuez à enregistrer dans ce format toutes les fonctions sauf le texte seront perdues.<br>Êtes-vous sûr de vouloir continuer ?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "Votre {0} sera converti en un format modifiable. Cette opération peut prendre quelque temps. Le document résultant sera optimisé pour l'édition de texte, il peut donc être différent de l'original {0}, surtout si le fichier original contient de nombreux éléments graphiques.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Si vous continuer à sauvegarder dans ce format une partie de la mise en forme peut être supprimée <br>Êtes-vous sûr de vouloir continuer?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} n'est pas un caractère spécial valide pour le champ de remplacement.", "DE.Controllers.Main.applyChangesTextText": "Chargement des changemets...", "DE.Controllers.Main.applyChangesTitleText": "Chargement des changemets", "DE.Controllers.Main.confirmMaxChangesSize": "La taille des actions dépasse la limitation fixée pour votre serveur.<br><PERSON><PERSON><PERSON><PERSON> sur \"Annuler\" pour annuler votre dernière action ou sur \"Continuer\" pour maintenir l'action en local (vous devez télécharger le fichier ou copier son contenu pour vous assurer que rien n'est perdu).", "DE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON> de conversion expiré.", "DE.Controllers.Main.criticalErrorExtText": "Cliquez sur \"OK\" pour revenir à la liste des documents.", "DE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.downloadErrorText": "Échec du téléchargement.", "DE.Controllers.Main.downloadMergeText": "Téléchargement en cours...", "DE.Controllers.Main.downloadMergeTitle": "Téléchargement en cours", "DE.Controllers.Main.downloadTextText": "Téléchargement du document...", "DE.Controllers.Main.downloadTitleText": "Téléchargement du document", "DE.Controllers.Main.errorAccessDeny": "Vous tentez d'exéсuter une action pour laquelle vous ne disposez pas des droits.<br><PERSON><PERSON><PERSON>z contacter l'administrateur de Document Server.", "DE.Controllers.Main.errorBadImageUrl": "L'URL de l'image est incorrecte", "DE.Controllers.Main.errorCannotPasteImg": "Il n'est pas possible de coller cette image à partir du Presse-papiers, mais vous pouvez l'enregistrer sur votre appareil et l'insérer à partir de là, ou vous pouvez copier l'image sans texte et la coller dans le document.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Connexion au serveur perdue. Le document ne peut être modifié en ce moment.", "DE.Controllers.Main.errorComboSeries": "Pour créer un graphique combiné, sélectionnez au moins deux séries de données. ", "DE.Controllers.Main.errorCompare": "La fonctionnalité «‎ Comparaison des documents » n'est pas disponible lors de co-édition.", "DE.Controllers.Main.errorConnectToServer": "Impossible d'enregistrer le document. Veuillez vérifier vos paramètres de connexion ou contactez l'administrateur.<br><PERSON><PERSON><PERSON> vous cliquez sur le bouton 'OK', vous serez invité à télécharger le document.", "DE.Controllers.Main.errorDatabaseConnection": "Erreur externe.<br>Erreur de connexion à la base de données. Si l'erreur persiste veillez contactez l'assistance technique.", "DE.Controllers.Main.errorDataEncrypted": "Les modifications chiffrées ont été reçues, mais ne peuvent pas être déchiffrées.", "DE.Controllers.Main.errorDataRange": "Plage de donn<PERSON>.", "DE.Controllers.Main.errorDefaultMessage": "Code d'erreur: %1", "DE.Controllers.Main.errorDirectUrl": "Vérifiez le lien vers le document.<br>Assurez-vous que c'est un lien de téléchargement direct. ", "DE.Controllers.Main.errorEditingDownloadas": "Une erreur s'est produite lors du travail avec le document.<br>Utilisez l'option 'Télécharger comme' pour enregistrer une copie de sauvegarde du fichier sur le disque dur de votre ordinateur.", "DE.Controllers.Main.errorEditingSaveas": "Une erreur s'est produite lors du travail avec le document.<br>Utilisez l'option 'Télécharger comme...' pour enregistrer une copie de sauvegarde sur le disque dur de votre ordinateur. ", "DE.Controllers.Main.errorEmailClient": "Pas de client messagerie trouvé", "DE.Controllers.Main.errorEmptyTOC": "Commencez à créer une table des matières en appliquant un style de titres de la galerie Styles au texte sélectionné.", "DE.Controllers.Main.errorFilePassProtect": "Le fichier est protégé par le mot de passe et ne peut être ouvert.", "DE.Controllers.Main.errorFileSizeExceed": "La taille du fichier dépasse les limites établies sur votre serveur.<br><PERSON><PERSON><PERSON><PERSON> contacter votre administrateur de Document Server pour obtenir plus d'information.  ", "DE.Controllers.Main.errorForceSave": "Une erreur est survenue lors de l'enregistrement du fichier. Veuillez utiliser l'option \"Télécharger comme\" pour enregistrer le fichier sur le disque dur de votre ordinateur ou réessayer plus tard.", "DE.Controllers.Main.errorInconsistentExt": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier ne correspond pas à l'extension du fichier.", "DE.Controllers.Main.errorInconsistentExtDocx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des documents texte (par exemple docx), mais le fichier a une extension incohérente : %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à l'un des formats suivants : pdf/djvu/xps/oxps, mais le fichier a l'extension incohérente : %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des présentations (par exemple pptx), mais le fichier a une extension incohérente : %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des feuilles de calcul (par exemple xlsx), mais le fichier a une extension incohérente : %1.", "DE.Controllers.Main.errorKeyEncrypt": "Descripteur de clés inconnu", "DE.Controllers.Main.errorKeyExpire": "Descripteur de clés expiré", "DE.Controllers.Main.errorLoadingFont": "Les polices ne sont pas téléchargées.<br><PERSON><PERSON>illez contacter l'administrateur de Document Server.", "DE.Controllers.Main.errorMailMergeLoadFile": "Échec de chargement du document. Merci de choisir un autre fichier.", "DE.Controllers.Main.errorMailMergeSaveFile": "Fusion a échoué.", "DE.Controllers.Main.errorNoTOC": "Il n'y a pas de table des matières à mettre à jour. V<PERSON> pouvez en insérer une à partir de l'onglet Références.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "Le mot de passe que vous avez fourni n'est pas correct.<br>V<PERSON>ri<PERSON>z que la touche CAPS LOCK est désactivée et assurez-vous d'utiliser la bonne majuscule.", "DE.Controllers.Main.errorProcessSaveResult": "Échec de l'enregistrement", "DE.Controllers.Main.errorServerVersion": "La version de l'éditeur a été mise à jour. La page sera rechargée pour appliquer les modifications.", "DE.Controllers.Main.errorSessionAbsolute": "Votre session a expiré. Veuillez recharger la page.", "DE.Controllers.Main.errorSessionIdle": "Le document n'a pas été modifié depuis trop longtemps. Veuillez recharger la page.", "DE.Controllers.Main.errorSessionToken": "La connexion au serveur a été interrompue. Veuillez recharger la page.", "DE.Controllers.Main.errorSetPassword": "Le mot de passe ne peut pas être configuré", "DE.Controllers.Main.errorStockChart": "Ordre lignes incorrect. <PERSON><PERSON> créer un diagramme boursier, positionnez les données sur la feuille de calcul dans l'ordre suivant :<br>cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "DE.Controllers.Main.errorSubmit": "Échec de soumission", "DE.Controllers.Main.errorTextFormWrongFormat": "La valeur saisie ne correspond pas au format du champ.", "DE.Controllers.Main.errorToken": "Le jeton de sécurité du document n’était pas formé correctement.<br>V<PERSON>illez contacter l'administrateur de Document Server.", "DE.Controllers.Main.errorTokenExpire": "Le jeton de sécurité du document a expiré.<br>Veuillez contactez l'administrateur de votre Document Server.", "DE.Controllers.Main.errorUpdateVersion": "La version du fichier a été changée. La page sera rechargée.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "La connexion a été rétablie et la version du fichier a été modifiée.<br>Avant de pouvoir continuer à travailler, vous devez télécharger le fichier ou copier son contenu pour vous assurer que rien n'est perdu, puis recharger cette page.", "DE.Controllers.Main.errorUserDrop": "Impossible d'accéder au fichier", "DE.Controllers.Main.errorUsersExceed": "Le nombre d'utilisateurs autorisés par le plan tarifaire a été dépassé", "DE.Controllers.Main.errorViewerDisconnect": "La connexion a été perdue. Vous pouvez toujours afficher le document,<br>mais ne pouvez pas le télécharger ou l'imprimer jusqu'à ce que la connexion soit rétablie et que la page soit rafraichit.", "DE.Controllers.Main.leavePageText": "Vous avez des modifications non enregistrées dans ce document. Cliquez sur 'Rester sur cette page', ensuite sur 'Enregistrer' pour enregistrer les modifications. Cliquez sur 'Quitter cette page' pour annuler toutes les modifications non enregistrées.", "DE.Controllers.Main.leavePageTextOnClose": "Toutes les modifications non enregistrées dans ce document seront perdues.<br> <PERSON><PERSON><PERSON> sur \"Annuler\", puis \"Enregistrer\" pour les sauvegarder. Cliquez sur \"OK\" pour annuler toutes les modifications non enregistrées.", "DE.Controllers.Main.loadFontsTextText": "Chargement des données...", "DE.Controllers.Main.loadFontsTitleText": "Chargement des données", "DE.Controllers.Main.loadFontTextText": "Chargement des données...", "DE.Controllers.Main.loadFontTitleText": "Chargement des données", "DE.Controllers.Main.loadImagesTextText": "Chargement des images...", "DE.Controllers.Main.loadImagesTitleText": "Chargement des images", "DE.Controllers.Main.loadImageTextText": "Chargement d'une image...", "DE.Controllers.Main.loadImageTitleText": "Chargement d'une image", "DE.Controllers.Main.loadingDocumentTextText": "Chargement du document...", "DE.Controllers.Main.loadingDocumentTitleText": "Chargement du document", "DE.Controllers.Main.mailMergeLoadFileText": "Chargement de la source des données...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Chargement de la source des données", "DE.Controllers.Main.notcriticalErrorTitle": "Avertissement", "DE.Controllers.Main.openErrorText": "Une erreur s’est produite lors de l’ouverture du fichier", "DE.Controllers.Main.openTextText": "Ouverture du document...", "DE.Controllers.Main.openTitleText": "Ouverture du document", "DE.Controllers.Main.printTextText": "Impression d'un document...", "DE.Controllers.Main.printTitleText": "Impression du document", "DE.Controllers.Main.reloadButtonText": "Recharger la page", "DE.Controllers.Main.requestEditFailedMessageText": "Quelqu'un est en train de modifier ce document. Veuillez réessayer plus tard.", "DE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>ès refusé", "DE.Controllers.Main.saveErrorText": "Une erreur s'est produite lors de l'enregistrement du fichier", "DE.Controllers.Main.saveErrorTextDesktop": "Le fichier ne peut pas être sauvé ou créé.<br>Les raisons possible sont :<br>1. Le fichier est en lecture seule. <br>2. Les fichier est en cours d'éditions par d'autres utilisateurs. <br>3. Le disque dur est plein ou corrompu.", "DE.Controllers.Main.saveTextText": "Enregistrement du document...", "DE.Controllers.Main.saveTitleText": "Enregistrement du document", "DE.Controllers.Main.scriptLoadError": "La connexion est trop lente, certains  éléments ne peuvent pas être chargés. Veuillez recharger la page.", "DE.Controllers.Main.sendMergeText": "Envoie du résultat de la fusion...", "DE.Controllers.Main.sendMergeTitle": "Envoie du résultat de la fusion", "DE.Controllers.Main.splitDividerErrorText": "Le nombre de lignes doit être un diviseur de %1.", "DE.Controllers.Main.splitMaxColsErrorText": "Le nombre de colonnes doivent être inférieure à %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "Le nombre de lignes doit être inférieure à %1.", "DE.Controllers.Main.textAnonymous": "Anonyme", "DE.Controllers.Main.textAnyone": "<PERSON>ut le monde", "DE.Controllers.Main.textApplyAll": "Appliquer à toutes les équations", "DE.Controllers.Main.textBuyNow": "Visiter le site web", "DE.Controllers.Main.textChangesSaved": "Toutes les modifications ont été enregistrées", "DE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textCloseTip": "Cliquez pour fermer le conseil", "DE.Controllers.Main.textContactUs": "Contacter l'équipe de ventes", "DE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textConvertEquation": "Cette équation a été créée avec une ancienne version de l'éditeur des équations qui n'est plus disponible. Pour modifier cette équation, convertissez-la au format Office Math ML.<br>Convertir maintenant ?", "DE.Controllers.Main.textCustomLoader": "Veuillez noter que conformément aux clauses du contrat de licence vous n'êtes pas autorisé à changer le chargeur.<br>Veuillez contacter notre Service des Ventes pour obtenir le devis.", "DE.Controllers.Main.textDisconnect": "La connexion est perdue", "DE.Controllers.Main.textGuest": "Invi<PERSON>", "DE.Controllers.Main.textHasMacros": "Le fichier contient des macros automatiques.<br><PERSON><PERSON><PERSON><PERSON><PERSON>vous exécuter les macros ?", "DE.Controllers.Main.textLearnMore": "En savoir plus", "DE.Controllers.Main.textLoadingDocument": "Chargement du document", "DE.Controllers.Main.textLongName": "Entrez un nom inférieur à 128 caractères.", "DE.Controllers.Main.textNoLicenseTitle": "La limite de la licence est atteinte", "DE.Controllers.Main.textPaidFeature": "Fonction payante", "DE.Controllers.Main.textReconnect": "La connexion est restaurée", "DE.Controllers.Main.textRemember": "Se souvenir de mon choix pour tous les fichiers", "DE.Controllers.Main.textRememberMacros": "Retenir mes choix pour toutes les macros", "DE.Controllers.Main.textRenameError": "Le nom d'utilisateur ne peut être vide.", "DE.Controllers.Main.textRenameLabel": "Entrez un nom à utiliser pour la collaboration", "DE.Controllers.Main.textRequestMacros": "Une macro fait une demande à l'URL. Voulez-vous autoriser la demande à l'adresse %1 ?", "DE.Controllers.Main.textShape": "Forme", "DE.Controllers.Main.textStrict": "Mode strict", "DE.Controllers.Main.textText": "Texte", "DE.Controllers.Main.textTryQuickPrint": "Vous avez sélectionné Impression rapide : l'ensemble du document sera imprimé sur la dernière imprimante sélectionnée ou celle par défaut.<br><PERSON><PERSON><PERSON><PERSON>-vous continuer ?", "DE.Controllers.Main.textTryUndoRedo": "Les fonctions annuler/rétablir sont désactivées pour le mode de co-édition rapide.<br><PERSON><PERSON><PERSON> sur le bouton \"Mode strict\" pour passer au mode de la co-édition stricte pour modifier le fichier sans interférence d'autres utilisateurs et envoyer vos modifications seulement après que vous les enregistrez. Vous pouvez basculer entre les modes de co-édition à l'aide de paramètres avancés d'éditeur.", "DE.Controllers.Main.textTryUndoRedoWarn": "Les fonctions Annuler/Rétablir sont désactivées pour le mode de co-édition rapide.", "DE.Controllers.Main.textUndo": "Annuler", "DE.Controllers.Main.titleLicenseExp": "Licence expirée", "DE.Controllers.Main.titleServerVersion": "L'éditeur est mis à jour", "DE.Controllers.Main.titleUpdateVersion": "Version a été modifiée", "DE.Controllers.Main.txtAbove": "Au-dessus", "DE.Controllers.Main.txtArt": "Votre texte ici", "DE.Controllers.Main.txtBasicShapes": "Formes de base", "DE.Controllers.Main.txtBelow": "En dessous", "DE.Controllers.Main.txtBookmarkError": "Erreur! Marque-page non défini.", "DE.Controllers.Main.txtButtons": "Boutons", "DE.Controllers.Main.txtCallouts": "Légendes", "DE.Controllers.Main.txtCharts": "Graphiques", "DE.Controllers.Main.txtChoose": "Choisir un élément", "DE.Controllers.Main.txtClickToLoad": "Cliquez pour charger une image", "DE.Controllers.Main.txtCurrentDocument": "Document actuel", "DE.Controllers.Main.txtDiagramTitle": "Titre du graphique", "DE.Controllers.Main.txtEditingMode": "Réglage mode d'édition...", "DE.Controllers.Main.txtEndOfFormula": "Fin de Formule Inattendue.", "DE.Controllers.Main.txtEnterDate": "Entrer une date", "DE.Controllers.Main.txtErrorLoadHistory": "Chargement de histoire a échoué", "DE.Controllers.Main.txtEvenPage": "Page paire", "DE.Controllers.Main.txtFiguredArrows": "Flèches figurées", "DE.Controllers.Main.txtFirstPage": "Premi<PERSON> Page", "DE.Controllers.Main.txtFooter": "Pied de page", "DE.Controllers.Main.txtFormulaNotInTable": "La formule n'est pas dans le tableau", "DE.Controllers.Main.txtHeader": "<PERSON>-tête", "DE.Controllers.Main.txtHyperlink": "Lien hypertexte", "DE.Controllers.Main.txtIndTooLarge": "Index trop long", "DE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtMainDocOnly": "Erreur ! Document principal seulement.", "DE.Controllers.Main.txtMath": "Maths", "DE.Controllers.Main.txtMissArg": "Argument Manquant", "DE.Controllers.Main.txtMissOperator": "Operateur <PERSON>", "DE.Controllers.Main.txtNeedSynchronize": "<PERSON><PERSON> avez des mises à jour", "DE.Controllers.Main.txtNone": "Rien", "DE.Controllers.Main.txtNoTableOfContents": "Aucune entrée de table des matières trouvée. L'application d'un style de titre sur une sélection de texte permettra l'affichage dans la table des matières. ", "DE.Controllers.Main.txtNoTableOfFigures": "Aucune entrée de table d'illustration n'a été trouvée.", "DE.Controllers.Main.txtNoText": "Erreur ! Il n'y a pas de texte répondant à ce style dans ce document.", "DE.Controllers.Main.txtNotInTable": "n'est pas dans le tableau", "DE.Controllers.Main.txtNotValidBookmark": "Erreur ! Référence non valide pour un signet.", "DE.Controllers.Main.txtOddPage": "Page impaire", "DE.Controllers.Main.txtOnPage": "sur la page", "DE.Controllers.Main.txtRectangles": "Rectangles", "DE.Controllers.Main.txtSameAsPrev": "Identique au précédent", "DE.Controllers.Main.txtSection": "-Section", "DE.Controllers.Main.txtSeries": "Série", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Légende encadrée avec une bordure 1", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Légende encadrée avec une bordure 2", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Légende encadrée avec une bordure 3", "DE.Controllers.Main.txtShape_accentCallout1": "Légende à une bordure 1", "DE.Controllers.Main.txtShape_accentCallout2": "Légende à une bordure 2", "DE.Controllers.Main.txtShape_accentCallout3": "Légende à une bordure 3", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "bouton \"Précédent\"", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Bouton \"Au Commencement\"", "DE.Controllers.Main.txtShape_actionButtonBlank": "Bouton vide", "DE.Controllers.Main.txtShape_actionButtonDocument": "Bouton Document", "DE.Controllers.Main.txtShape_actionButtonEnd": "Bouton à la fin", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Bouton Suivant", "DE.Controllers.Main.txtShape_actionButtonHelp": "Bouton Aide", "DE.Controllers.Main.txtShape_actionButtonHome": "Bouton \"Page d'accueil\"", "DE.Controllers.Main.txtShape_actionButtonInformation": "Bouton Informations", "DE.Controllers.Main.txtShape_actionButtonMovie": "Bouton Vidéo", "DE.Controllers.Main.txtShape_actionButtonReturn": "Bouton Retour", "DE.Controllers.Main.txtShape_actionButtonSound": "Bouton Son", "DE.Controllers.Main.txtShape_arc": "Arc", "DE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON> cour<PERSON>", "DE.Controllers.Main.txtShape_bentConnector5": "Connecteur en angle", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Connecteur en angle avec flèche\t", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Connecteur en angle avec deux flèches", "DE.Controllers.Main.txtShape_bentUpArrow": "Flèche à angle droit", "DE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_blockArc": "<PERSON>", "DE.Controllers.Main.txtShape_borderCallout1": "Légende encadrée 1", "DE.Controllers.Main.txtShape_borderCallout2": "Légende encadrée 2", "DE.Controllers.Main.txtShape_borderCallout3": "Légende encadrée 3", "DE.Controllers.Main.txtShape_bracePair": "Accolades", "DE.Controllers.Main.txtShape_callout1": "Légende encadrée sans bordure 1", "DE.Controllers.Main.txtShape_callout2": "Légende encadrée sans bordure 2", "DE.Controllers.Main.txtShape_callout3": "Légende encadrée sans bordure 3", "DE.Controllers.Main.txtShape_can": "Cylindre", "DE.Controllers.Main.txtShape_chevron": "Chevron", "DE.Controllers.Main.txtShape_chord": "Corde", "DE.Controllers.Main.txtShape_circularArrow": "Flèche en arc\t", "DE.Controllers.Main.txtShape_cloud": "Cloud", "DE.Controllers.Main.txtShape_cloudCallout": "Pensées", "DE.Controllers.Main.txtShape_corner": "Coin", "DE.Controllers.Main.txtShape_cube": "C<PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Connecteur en arc ", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Connecteur en arc avec flèche", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Connecteur en arc avec deux flèches", "DE.Controllers.Main.txtShape_curvedDownArrow": "Flèche courbée vers le bas", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Flèche courbée vers la gauche", "DE.Controllers.Main.txtShape_curvedRightArrow": "Flèche courbée vers la droite", "DE.Controllers.Main.txtShape_curvedUpArrow": "Flèche courbée vers le haut", "DE.Controllers.Main.txtShape_decagon": "Décagone", "DE.Controllers.Main.txtShape_diagStripe": "Bande diagonale", "DE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_dodecagon": "Dodécagone", "DE.Controllers.Main.txtShape_donut": "Bo<PERSON><PERSON>", "DE.Controllers.Main.txtShape_doubleWave": "Double vague", "DE.Controllers.Main.txtShape_downArrow": "Flèche vers le bas", "DE.Controllers.Main.txtShape_downArrowCallout": "Rectangle avec flèche vers le bas", "DE.Controllers.Main.txtShape_ellipse": "Ellipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "R<PERSON>n courbé vers le bas", "DE.Controllers.Main.txtShape_ellipseRibbon2": "R<PERSON>n courbé vers le haut", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Organigramme : Alternative", "DE.Controllers.Main.txtShape_flowChartCollate": "Organigramme: <PERSON><PERSON><PERSON>\t", "DE.Controllers.Main.txtShape_flowChartConnector": "Organigramme: Connecteur", "DE.Controllers.Main.txtShape_flowChartDecision": "Organigramme: Decision", "DE.Controllers.Main.txtShape_flowChartDelay": "Organigramme: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDisplay": "Organigramme : Affichage", "DE.Controllers.Main.txtShape_flowChartDocument": "Organigramme: Document", "DE.Controllers.Main.txtShape_flowChartExtract": "Organigramme: Extraire", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Organigramme: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Organigramme : Stockage interne", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Organigramme : Disque magnétique", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Organigramme : Stockage à accès direct", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Organigramme: Stockage à accès séquentiel\t", "DE.Controllers.Main.txtShape_flowChartManualInput": "Organigramme: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Organigramme: Opération manuelle", "DE.Controllers.Main.txtShape_flowChartMerge": "Organigramme: Fusion\t", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Organigramme: Multidocument", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Organigramme: Connecteur page suivante", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Organigramme: Donn<PERSON> stockées", "DE.Controllers.Main.txtShape_flowChartOr": "Organigramme: OU", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Organigramme: <PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartPreparation": "Organigramme: Préparation", "DE.Controllers.Main.txtShape_flowChartProcess": "Organigramme: <PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Organigramme: <PERSON><PERSON> perfo<PERSON>", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Organigramme: Bande perforée", "DE.Controllers.Main.txtShape_flowChartSort": "Organigramme : <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Organigramme: <PERSON><PERSON> de sommaire", "DE.Controllers.Main.txtShape_flowChartTerminator": "Organigramme: Terminaison", "DE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_frame": "Cadre", "DE.Controllers.Main.txtShape_halfFrame": "Demi-cadre", "DE.Controllers.Main.txtShape_heart": "Coeur", "DE.Controllers.Main.txtShape_heptagon": "Heptagone", "DE.Controllers.Main.txtShape_hexagon": "Hexagon", "DE.Controllers.Main.txtShape_homePlate": "Pentagone", "DE.Controllers.Main.txtShape_horizontalScroll": "Parchemin horizontal", "DE.Controllers.Main.txtShape_irregularSeal1": "Éclatement 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Éclatement 2", "DE.Controllers.Main.txtShape_leftArrow": "Flèche gauche", "DE.Controllers.Main.txtShape_leftArrowCallout": "Rectangle avec flèche vers la gauche\t", "DE.Controllers.Main.txtShape_leftBrace": "Accolade ouvrante", "DE.Controllers.Main.txtShape_leftBracket": "Parenthèse ouvrante", "DE.Controllers.Main.txtShape_leftRightArrow": "Flèche bilatérale", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Rectangle horizontal à deux flèches", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Flèche à trois pointes", "DE.Controllers.Main.txtShape_leftUpArrow": "Double flèche horizontale", "DE.Controllers.Main.txtShape_lightningBolt": "Éclair", "DE.Controllers.Main.txtShape_line": "Ligne", "DE.Controllers.Main.txtShape_lineWithArrow": "Flèche", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "Flèche à deux pointes", "DE.Controllers.Main.txtShape_mathDivide": "Division ", "DE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMinus": "<PERSON>ins", "DE.Controllers.Main.txtShape_mathMultiply": "Multiplication", "DE.Controllers.Main.txtShape_mathNotEqual": "<PERSON>ff<PERSON><PERSON> de", "DE.Controllers.Main.txtShape_mathPlus": "Plus", "DE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_noSmoking": "Signe \"Interdiction\"", "DE.Controllers.Main.txtShape_notchedRightArrow": "Flèche droite à entaille\t", "DE.Controllers.Main.txtShape_octagon": "Octogone", "DE.Controllers.Main.txtShape_parallelogram": "Parallélogramme", "DE.Controllers.Main.txtShape_pentagon": "Pentagone", "DE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_plaque": "<PERSON>e", "DE.Controllers.Main.txtShape_plus": "Plus", "DE.Controllers.Main.txtShape_polyline1": "Dessin à main levée", "DE.Controllers.Main.txtShape_polyline2": "Forme libre", "DE.Controllers.Main.txtShape_quadArrow": "Flèche à quatre pointes", "DE.Controllers.Main.txtShape_quadArrowCallout": "Rectangle à quatre flèches\t", "DE.Controllers.Main.txtShape_rect": "Rectangle", "DE.Controllers.Main.txtShape_ribbon": "R<PERSON>n vers le bas", "DE.Controllers.Main.txtShape_ribbon2": "R<PERSON><PERSON> vers le haut", "DE.Controllers.Main.txtShape_rightArrow": "Flèche droite", "DE.Controllers.Main.txtShape_rightArrowCallout": "Rectangle avec flèche vers le droit", "DE.Controllers.Main.txtShape_rightBrace": "Accolade fermante", "DE.Controllers.Main.txtShape_rightBracket": "Parenthèse fermante", "DE.Controllers.Main.txtShape_round1Rect": "Rectangle arrondi à un seul coin", "DE.Controllers.Main.txtShape_round2DiagRect": "Rectangle avec un coin diagonal rond", "DE.Controllers.Main.txtShape_round2SameRect": "Rectangle arrondi avec un coin du même côté", "DE.Controllers.Main.txtShape_roundRect": "Rectangle à coins arrondis", "DE.Controllers.Main.txtShape_rtTriangle": "Triangle rectangle", "DE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_snip1Rect": "Rogner un rectangle à un seul coin", "DE.Controllers.Main.txtShape_snip2DiagRect": "Couper un rectangle avec un coin diagonal", "DE.Controllers.Main.txtShape_snip2SameRect": "Couper un rectangle avec un coin du même côté", "DE.Controllers.Main.txtShape_snipRoundRect": "<PERSON><PERSON><PERSON> et arrondir un rectangle à un seul coin", "DE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "Étoile à 10 branches", "DE.Controllers.Main.txtShape_star12": "Étoile à 12 branches", "DE.Controllers.Main.txtShape_star16": "Étoile à 16 branches", "DE.Controllers.Main.txtShape_star24": "Étoile à 24 branches", "DE.Controllers.Main.txtShape_star32": "Étoile à 32 branches", "DE.Controllers.Main.txtShape_star4": "Étoile à 4 branches", "DE.Controllers.Main.txtShape_star5": "Étoile à 5 branches", "DE.Controllers.Main.txtShape_star6": "Étoile à 6 branches", "DE.Controllers.Main.txtShape_star7": "Étoile à 7 branches", "DE.Controllers.Main.txtShape_star8": "Étoile à 8 branches", "DE.Controllers.Main.txtShape_stripedRightArrow": "Flèche vers la droite  rayée", "DE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON> ", "DE.Controllers.Main.txtShape_textRect": "Zone de texte", "DE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_triangle": "Triangle", "DE.Controllers.Main.txtShape_upArrow": "Flèche vers le haut", "DE.Controllers.Main.txtShape_upArrowCallout": "Rectangle avec flèche vers le haut", "DE.Controllers.Main.txtShape_upDownArrow": "Double flèche verticale", "DE.Controllers.Main.txtShape_uturnArrow": "Demi-tour", "DE.Controllers.Main.txtShape_verticalScroll": "Parchemin vertical", "DE.Controllers.Main.txtShape_wave": "<PERSON>de", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Bulle ronde", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangle", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rectangle à coins arrondis", "DE.Controllers.Main.txtStarsRibbons": "Étoiles et rubans", "DE.Controllers.Main.txtStyle_Caption": "Légende", "DE.Controllers.Main.txtStyle_endnote_text": "Texte de note de fin", "DE.Controllers.Main.txtStyle_footnote_text": "Texte de pied de page", "DE.Controllers.Main.txtStyle_Heading_1": "Titre 1", "DE.Controllers.Main.txtStyle_Heading_2": "Titre 2", "DE.Controllers.Main.txtStyle_Heading_3": "Titre 3", "DE.Controllers.Main.txtStyle_Heading_4": "Titre 4", "DE.Controllers.Main.txtStyle_Heading_5": "Titre 5", "DE.Controllers.Main.txtStyle_Heading_6": "Titre 6", "DE.Controllers.Main.txtStyle_Heading_7": "Titre 7", "DE.Controllers.Main.txtStyle_Heading_8": "Titre 8", "DE.Controllers.Main.txtStyle_Heading_9": "Titre 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Citation intense", "DE.Controllers.Main.txtStyle_List_Paragraph": "Paragraphe de liste", "DE.Controllers.Main.txtStyle_No_Spacing": "Pas d'espacement", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "Citation", "DE.Controllers.Main.txtStyle_Subtitle": "Sous-titres", "DE.Controllers.Main.txtStyle_Title": "Titre", "DE.Controllers.Main.txtSyntaxError": "<PERSON><PERSON><PERSON> Syntaxe", "DE.Controllers.Main.txtTableInd": "Index d'un Tableau Ne Peut Pas Être Zero", "DE.Controllers.Main.txtTableOfContents": "Table des matières", "DE.Controllers.Main.txtTableOfFigures": "Table des figures", "DE.Controllers.Main.txtTOCHeading": "En-tête de table des matières", "DE.Controllers.Main.txtTooLarge": "Nom Trop Grand Pour Formater", "DE.Controllers.Main.txtTypeEquation": "Saisissez une équation ici.", "DE.Controllers.Main.txtUndefBookmark": "Signet indéterminé ", "DE.Controllers.Main.txtXAxis": "Axe X", "DE.Controllers.Main.txtYAxis": "Axe Y", "DE.Controllers.Main.txtZeroDivide": "Division par Zéro", "DE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> inconnue.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Votre navigateur n'est pas pris en charge.", "DE.Controllers.Main.uploadDocExtMessage": "Format de fichier inconnu.", "DE.Controllers.Main.uploadDocFileCountMessage": "Aucun fichier n'a été chargé.", "DE.Controllers.Main.uploadDocSizeMessage": "La taille du fichier dépasse la limite autorisée.", "DE.Controllers.Main.uploadImageExtMessage": "Format d'image inconnu.", "DE.Controllers.Main.uploadImageFileCountMessage": "Pas d'images chargées.", "DE.Controllers.Main.uploadImageSizeMessage": "L'image est trop grande. La taille limite est de 25 Mo.", "DE.Controllers.Main.uploadImageTextText": "Chargement d'une image...", "DE.Controllers.Main.uploadImageTitleText": "Chargement d'une image", "DE.Controllers.Main.waitText": "Veuillez patienter...", "DE.Controllers.Main.warnBrowserIE9": "L'application est peu compatible avec IE9. Utilisez IE10 ou version plus récente", "DE.Controllers.Main.warnBrowserZoom": "Le paramètre actuel de zoom de votre navigateur n'est pas accepté. Veuillez rétablir le niveau de zoom par défaut en appuyant sur Ctrl+0.", "DE.Controllers.Main.warnLicenseExceeded": "Vous avez dépassé le nombre maximal de connexions simultanées aux éditeurs %1. Ce document sera ouvert à la lecture seulement.<br>Contactez votre administrateur pour en savoir davantage.", "DE.Controllers.Main.warnLicenseExp": "Votre licence a expiré.<br>Veuillez mettre à jour votre licence et actualisez la page.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "La licence est expirée.<br>V<PERSON> n'avez plus d'accès aux outils d'édition.<br><PERSON><PERSON><PERSON>z contacter votre administrateur.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "Il est indispensable de renouveler la licence.<br>Vous avez un accès limité aux outils d'édition des documents.<br><PERSON><PERSON><PERSON>z contacter votre administrateur pour obtenir un accès complet", "DE.Controllers.Main.warnLicenseUsersExceeded": "Vous avez dépassé le nombre maximal d’utilisateurs des éditeurs %1. Contactez votre administrateur pour en savoir davantage.", "DE.Controllers.Main.warnNoLicense": "Vous avez dépassé le nombre maximal de connexions simultanées aux éditeurs %1. Ce document sera ouvert à la lecture seulement.<br>Contactez l’équipe des ventes %1 pour mettre à jour les termes de la licence.", "DE.Controllers.Main.warnNoLicenseUsers": "Vous avez dépassé le nombre maximal d’utilisateurs des éditeurs %1. Contactez l’équipe des ventes %1 pour mettre à jour les termes de la licence.", "DE.Controllers.Main.warnProcessRightsChange": "Le droit d'édition du fichier vous a été refusé.", "DE.Controllers.Navigation.txtBeginning": "Début du document", "DE.Controllers.Navigation.txtGotoBeginning": "Aller au début du document", "DE.Controllers.Print.textMarginsLast": "Derniers personnalisés", "DE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Print.txtPrintRangeInvalid": "Plage d'impression non valide", "DE.Controllers.Print.txtPrintRangeSingleRange": "Saisis<PERSON>z un numéro de page unique ou une plage de pages unique (par exemple, 5-12). Vous pouvez également imprimer en PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "Avertissement", "DE.Controllers.Search.textNoTextFound": "Les données que vous recherchez n'ont pas pu être trouvées. Veuillez modifier vos options de recherche.", "DE.Controllers.Search.textReplaceSkipped": "Le remplacement a été effectué. {0} occurrences ont été sautées.", "DE.Controllers.Search.textReplaceSuccess": "La recherche a été effectuée. {0} occurrences ont été remplacées", "DE.Controllers.Search.warnReplaceString": "{0} n'est pas un caractère spécial valide pour la case Remplacer par.", "DE.Controllers.Statusbar.textDisconnect": "<b>La connexion est perdue</b><br>Tentative de connexion. Veuillez vérifier les paramètres de connexion.", "DE.Controllers.Statusbar.textHasChanges": "Nouveaux changements ont été suivis", "DE.Controllers.Statusbar.textSetTrackChanges": "Vous êtes dans le mode de suivi des modifications.", "DE.Controllers.Statusbar.textTrackChanges": "Le document est ouvert avec le mode Suivi des modifications activé", "DE.Controllers.Statusbar.tipReview": "Suivi des modifications", "DE.Controllers.Statusbar.zoomText": "Zoom {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "La police que vous allez enregistrer n'est pas disponible sur l'appareil actuel.<br>Le style du texte sera affiché à l'aide de l'une des polices de système, la police sauvée sera utilisée lorsqu'il est disponible.<br><PERSON><PERSON><PERSON><PERSON>-vous continuer?", "DE.Controllers.Toolbar.dataUrl": "Collez une URL de données", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Avertissement", "DE.Controllers.Toolbar.textAccent": "Types d'accentuation", "DE.Controllers.Toolbar.textBracket": "Crochets", "DE.Controllers.Toolbar.textEmptyImgUrl": "Spécifiez l'URL de l'image", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "<PERSON><PERSON> devez indiquer l'URL.", "DE.Controllers.Toolbar.textFontSizeErr": "La valeur entrée est incorrecte.<br>Entrez une valeur numérique entre 1 et 300", "DE.Controllers.Toolbar.textFraction": "Fractions", "DE.Controllers.Toolbar.textFunction": "Fonctions", "DE.Controllers.Toolbar.textGroup": "Groupe", "DE.Controllers.Toolbar.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textIntegral": "Intégrales", "DE.Controllers.Toolbar.textLargeOperator": "Grands opérateurs", "DE.Controllers.Toolbar.textLimitAndLog": "Limites et logarithmes", "DE.Controllers.Toolbar.textMatrix": "Matrices", "DE.Controllers.Toolbar.textOperator": "Opérateurs", "DE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textRecentlyUsed": "Récemment utilisé", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "Symboles", "DE.Controllers.Toolbar.textTabForms": "Formulaires", "DE.Controllers.Toolbar.textWarning": "Avertissement", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Flèche gauche-droite au-dessus", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Flèche vers la gauche au-dessus", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Flèche vers la droite au-dessus", "DE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_BarBot": "Barre inférieure", "DE.Controllers.Toolbar.txtAccent_BarTop": "Barre supérieure", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Formule encadrée (avec espace réservé)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formule encadrée (exemple)", "DE.Controllers.Toolbar.txtAccent_Check": "Vérifier", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Accolade inférieure", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Accolade supérieure", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vecteur A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC avec barre supérieure", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y avec barre supérieure", "DE.Controllers.Toolbar.txtAccent_DDDot": "Point triple", "DE.Controllers.Toolbar.txtAccent_DDot": "Point double", "DE.Controllers.Toolbar.txtAccent_Dot": "Point", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Barre supérieure double", "DE.Controllers.Toolbar.txtAccent_Grave": "Grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Regroupement de caractère en dessus", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Regroupement de caractère au-dessus", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpon gauche au-dessus", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpon droite au-dessus", "DE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON> pointus", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Crochets pointus avec séparateur", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Crochets pointus avec deux séparateurs", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "C<PERSON>chet angulaire droite", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Crochet angulaire à gauche", "DE.Controllers.Toolbar.txtBracket_Curve": "Accolades", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Accolades avec séparateur", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Accolade droite", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Accolade gauche", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Cas (deux conditions)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Cas (trois conditions)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Objet empilé", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Objet de pile entre parenthèses", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Exemple de cas", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Coefficient binomial", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Coefficient binomial entre crochets pointus", "DE.Controllers.Toolbar.txtBracket_Line": "Barres verticales", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Barre verticale droite", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Barre verticale gauche", "DE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON> verticales doubles", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Double barre verticale droite", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Double barre verticale gauche", "DE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Plancher à droite", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Plancher à gauche", "DE.Controllers.Toolbar.txtBracket_Round": "Parenthèses", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parenthèses avec séparateur", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parenthèse droite", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Parenthèse gauche", "DE.Controllers.Toolbar.txtBracket_Square": "Crochets", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Placeholder entre deux crochets droits", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Crochets inversés", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Crochet droit", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Crochet gauche", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Placeholder entre deux crochets gauches", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "Double crochets", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Double crochet droit", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Double crochet gauche", "DE.Controllers.Toolbar.txtBracket_UppLim": "Plafond", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Plafond à droite", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Plafond à gauche", "DE.Controllers.Toolbar.txtFractionDiagonal": "Fraction oblique", "DE.Controllers.Toolbar.txtFractionDifferential_1": "dx sur dy", "DE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y sur cap delta x", "DE.Controllers.Toolbar.txtFractionDifferential_3": "partielle y sur partielle x", "DE.Controllers.Toolbar.txtFractionDifferential_4": "y delta sur delta x", "DE.Controllers.Toolbar.txtFractionHorizontal": "Fraction sur une ligne", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi divisé par 2", "DE.Controllers.Toolbar.txtFractionSmall": "Petite fraction", "DE.Controllers.Toolbar.txtFractionVertical": "Fraction sur deux lignes", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Fonction cosinus inverse", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Fonction cosinus inverse hyperbolique", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Fonction cotangente inverse", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Fonction cotangente inverse hyperbolique", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Fonction cosécante inverse", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Fonction cosécante inverse hyperbolique", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Fonction sécante inverse", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Fonction sécante inverse hyperbolique", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Fonction sinus inverse", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Fonction sinus inverse hyperbolique", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Fonction tangente inverse", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Fonction tangente inverse hyperbolique", "DE.Controllers.Toolbar.txtFunction_Cos": "Fonction cosinus", "DE.Controllers.Toolbar.txtFunction_Cosh": "Fonction cosinus hyperbolique", "DE.Controllers.Toolbar.txtFunction_Cot": "Fonction cotangente", "DE.Controllers.Toolbar.txtFunction_Coth": "Fonction cotangente hyperbolique", "DE.Controllers.Toolbar.txtFunction_Csc": "Fonction cosécante", "DE.Controllers.Toolbar.txtFunction_Csch": "Fonction cosécante hyperbolique", "DE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> thêta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cosinus 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Formule de la tangente", "DE.Controllers.Toolbar.txtFunction_Sec": "Fonction sécante", "DE.Controllers.Toolbar.txtFunction_Sech": "Fonction sécante hyperbolique", "DE.Controllers.Toolbar.txtFunction_Sin": "Fonction sinus", "DE.Controllers.Toolbar.txtFunction_Sinh": "Fonction sinus hyperbolique", "DE.Controllers.Toolbar.txtFunction_Tan": "Formule de la tangente", "DE.Controllers.Toolbar.txtFunction_Tanh": "Fonction tangente hyperbolique", "DE.Controllers.Toolbar.txtIntegral": "Intégrale", "DE.Controllers.Toolbar.txtIntegral_dtheta": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "DE.Controllers.Toolbar.txtIntegral_dx": "Différent<PERSON> x", "DE.Controllers.Toolbar.txtIntegral_dy": "<PERSON><PERSON><PERSON><PERSON><PERSON> y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Intégrale avec limites empilées", "DE.Controllers.Toolbar.txtIntegralDouble": "Double intégrale", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Intégrale double avec limites empilées", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Intégrale double avec limites", "DE.Controllers.Toolbar.txtIntegralOriented": "Intégrale de contour", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Intégrale de contour avec limites empilées", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Intégrale de surface", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Intégrale de surface avec limites empilées", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Intégrale de surface avec limites", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Intégrale de contour avec limites", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Intégrale de volume", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Intégrale de volume avec limites empilées", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Intégrale de volume avec limites", "DE.Controllers.Toolbar.txtIntegralSubSup": "Intégrale avec limites", "DE.Controllers.Toolbar.txtIntegralTriple": "Triple intégrale", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Intégrale triple avec limites empilées", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Intégrale triple avec limites", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Et logique", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Et logique avec limite inférieure", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Et logique avec limites", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Et logique avec limite inférieure en indice", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Et logique avec limites en indice/exposant", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-produit", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproduit avec limite inférieure", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproduit avec limites", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coproduit avec limite inférieure en indice", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproduit avec limites en indice/en exposant", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Somme sur k de n choix k", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Somme de i égal à zéro à n", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Exemple de somme utilisant deux indices", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Exemple de produit", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Exemple d’union", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Ou logique", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Ou logique avec limite inférieure", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Ou logique avec limites", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Ou logique avec limite inférieure en indice", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Ou logique avec limites en indice/exposant", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection avec limite inférieure", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection avec limites", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection avec limite inférieure en indice", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection avec limites en indice/exposant", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Produit", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produit avec limite inférieure", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produit avec limites", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produit avec limite inférieure en indice", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produit avec limites en indice/exposant", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Somme", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Somme avec limite inférieure", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Somme avec limites", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Somme avec limite inférieure en indice", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Somme avec limites en indice/exposant", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union avec limite inférieure", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union avec limites", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union avec limite inférieure en indice", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union avec limites en indice/exposant", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemple de limite", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemple de maximum", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Logarithme naturel", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logarithme", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithme", "DE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "DE.Controllers.Toolbar.txtMarginsH": "Les marges supérieure et inférieure sont trop élevés pour une hauteur de page donnée", "DE.Controllers.Toolbar.txtMarginsW": "Les marges gauche et droite sont trop larges pour une largeur de page donnée", "DE.Controllers.Toolbar.txtMatrix_1_2": "Matrice vide 1x2 ", "DE.Controllers.Toolbar.txtMatrix_1_3": "Matrice vide 1x3", "DE.Controllers.Toolbar.txtMatrix_2_1": "Matrice vide 2x1", "DE.Controllers.Toolbar.txtMatrix_2_2": "Matrice vide 2x2", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matrice 2x2 vide avec doubles barres verticales", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Déterminant 2x2 vide", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matrice d’identité 2x2 vide entre parenthèses", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matrice d’identité 2x2 vide entre crochets", "DE.Controllers.Toolbar.txtMatrix_2_3": "Matrice vide 2x3", "DE.Controllers.Toolbar.txtMatrix_3_1": "Matrice vide 3x1", "DE.Controllers.Toolbar.txtMatrix_3_2": "Matrice vide 3x2", "DE.Controllers.Toolbar.txtMatrix_3_3": "Matrice vide 3x3", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Points de ligne de base", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Points d'interligne", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Points diagonaux", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Points verticaux", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "Matrice avec pointillés entre parenthèses", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Matrice avec pointillés entre crochets", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "Matrice d’identité 2x2 avec zéros", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matrice d'identité 3x3", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "Matrice d’identité 3x3 avec zéros", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matrice d’identité 3x3 avec cellules hors diagonale vides", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Flèche gauche-droite en dessous", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Flèche gauche-droite au-dessus", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Flèche vers la gauche en dessous", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Flèche vers la gauche au-dessus", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Flèche vers la droite en dessous", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Flèche vers la droite au-dessus", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Deux-points É<PERSON>", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Produits", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Les rendements Delta", "DE.Controllers.Toolbar.txtOperator_Definition": "Égal par définition à", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta égal à", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Double flèche gauche-droite au-dessous", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Double flèche gauche-droite au-dessus", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Flèche vers la gauche en dessous", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Flèche vers la gauche au-dessus", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Flèche vers la droite en dessous", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Flèche vers la droite au-dessus", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Égal", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Mesuré(e) par", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Côté droit de la formule quadratique", "DE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON> car<PERSON> de a au carré plus b au carré", "DE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON> avec degré", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Racine cubique", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Radical avec degré", "DE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_1": "x indice y au carré", "DE.Controllers.Toolbar.txtScriptCustom_2": "e au i négatif oméga t", "DE.Controllers.Toolbar.txtScriptCustom_3": "x au carré", "DE.Controllers.Toolbar.txtScriptCustom_4": "Y exposant gauche n indice gauche un", "DE.Controllers.Toolbar.txtScriptSub": "Indice", "DE.Controllers.Toolbar.txtScriptSubSup": "Indice-Exposant", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Indice-Exposant gauche", "DE.Controllers.Toolbar.txtScriptSup": "Exposant", "DE.Controllers.Toolbar.txtSymbol_about": "Approximativement", "DE.Controllers.Toolbar.txtSymbol_additional": "Complément", "DE.Controllers.Toolbar.txtSymbol_aleph": "Aleph", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "DE.Controllers.Toolbar.txtSymbol_approx": "Presque égale à", "DE.Controllers.Toolbar.txtSymbol_ast": "Opérateur astérisque", "DE.Controllers.Toolbar.txtSymbol_beta": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_beth": "Beth", "DE.Controllers.Toolbar.txtSymbol_bullet": "Opérateur puce", "DE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Racine cubique", "DE.Controllers.Toolbar.txtSymbol_cdots": "Trois points médians", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Approximativement égal à", "DE.Controllers.Toolbar.txtSymbol_cup": "Union", "DE.Controllers.Toolbar.txtSymbol_ddots": "Trois points diagonaux vers le coin bas à droite", "DE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Signe de division", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Flèche vers le bas", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Ensemble vide", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_equiv": "Identique à", "DE.Controllers.Toolbar.txtSymbol_eta": "Êta", "DE.Controllers.Toolbar.txtSymbol_exists": "Existant", "DE.Controllers.Toolbar.txtSymbol_factorial": "Factorielle", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Degrés Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "Pour tous", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "Est supérieur ou égal à", "DE.Controllers.Toolbar.txtSymbol_gg": "Beaucoup plus grande que", "DE.Controllers.Toolbar.txtSymbol_greater": "Sup<PERSON>ur à", "DE.Controllers.Toolbar.txtSymbol_in": "Élément de", "DE.Controllers.Toolbar.txtSymbol_inc": "Incrément", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infini", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Flèche gauche", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Double flèche horizontale", "DE.Controllers.Toolbar.txtSymbol_leq": "Est inférieur ou égal à", "DE.Controllers.Toolbar.txtSymbol_less": "inférieur à", "DE.Controllers.Toolbar.txtSymbol_ll": "Beaucoup moins que", "DE.Controllers.Toolbar.txtSymbol_minus": "<PERSON>ins", "DE.Controllers.Toolbar.txtSymbol_mp": "Moins plus", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "N'est pas égale à", "DE.Controllers.Toolbar.txtSymbol_ni": "Contient comme élément", "DE.Controllers.Toolbar.txtSymbol_not": "Signe négation", "DE.Controllers.Toolbar.txtSymbol_notexists": "Inexistant", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Différentielle partielle", "DE.Controllers.Toolbar.txtSymbol_percent": "Pourcentage", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Plus", "DE.Controllers.Toolbar.txtSymbol_pm": "Plus moins", "DE.Controllers.Toolbar.txtSymbol_propto": "Proportionnel à", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON> quatri<PERSON>", "DE.Controllers.Toolbar.txtSymbol_qed": "Fin de la preuve", "DE.Controllers.Toolbar.txtSymbol_rddots": "Trois points diagonaux vers le coin haut à droite", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Flèche droite", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Symbole de radical", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Par conséquent", "DE.Controllers.Toolbar.txtSymbol_theta": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_times": "Signe de multiplication", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Flèche vers le haut", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Variante epsilon", "DE.Controllers.Toolbar.txtSymbol_varphi": "Variante phi", "DE.Controllers.Toolbar.txtSymbol_varpi": "Variante pi", "DE.Controllers.Toolbar.txtSymbol_varrho": "Variante rho", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON> th<PERSON>ta", "DE.Controllers.Toolbar.txtSymbol_vdots": "Trois points verticaux", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "DE.Controllers.Viewport.textFitPage": "Ajuster à la page", "DE.Controllers.Viewport.textFitWidth": "Ajuster à la largeur", "DE.Controllers.Viewport.txtDarkMode": "Mode sombre", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Étiquette :", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "Étiquette ne doit pas être vide", "DE.Views.BookmarksDialog.textAdd": "Ajouter", "DE.Views.BookmarksDialog.textBookmarkName": "Nom du signet", "DE.Views.BookmarksDialog.textClose": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textCopy": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textGetLink": "Obtenir le lien", "DE.Views.BookmarksDialog.textGoto": "<PERSON><PERSON>", "DE.Views.BookmarksDialog.textHidden": "Signets cachés", "DE.Views.BookmarksDialog.textLocation": "Emplacement", "DE.Views.BookmarksDialog.textName": "Nom", "DE.Views.BookmarksDialog.textSort": "Trier par", "DE.Views.BookmarksDialog.textTitle": "Signets", "DE.Views.BookmarksDialog.txtInvalidName": "Nom du signet ne peut pas contenir que des lettres, des  chiffres et des barres de soulignement et doit commencer avec une lettre", "DE.Views.CaptionDialog.textAdd": "Ajouter", "DE.Views.CaptionDialog.textAfter": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textBefore": "Avant", "DE.Views.CaptionDialog.textCaption": "Légende", "DE.Views.CaptionDialog.textChapter": "Style de début de chapitre", "DE.Views.CaptionDialog.textChapterInc": "<PERSON><PERSON><PERSON> le numéro de chapitre", "DE.Views.CaptionDialog.textColon": "Deux-points", "DE.Views.CaptionDialog.textDash": "tiret", "DE.Views.CaptionDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textEquation": "Équation", "DE.Views.CaptionDialog.textExamples": "Exemples: Table 2-A, Image 1.IV", "DE.Views.CaptionDialog.textExclude": "Exclure le texte de la légende", "DE.Views.CaptionDialog.textFigure": "Figure", "DE.Views.CaptionDialog.textHyphen": "trait d'union", "DE.Views.CaptionDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textLabel": "Étiquette", "DE.Views.CaptionDialog.textLongDash": "Tiret long", "DE.Views.CaptionDialog.textNumbering": "Numérotation", "DE.Views.CaptionDialog.textPeriod": "Période", "DE.Views.CaptionDialog.textSeparator": "Séparateur", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "Insérer une légende", "DE.Views.CellsAddDialog.textCol": "Colonnes", "DE.Views.CellsAddDialog.textDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textLeft": "Vers la gauche", "DE.Views.CellsAddDialog.textRight": "Vers la droite", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> plusieurs", "DE.Views.CellsAddDialog.textUp": "<PERSON><PERSON><PERSON><PERSON> du <PERSON>", "DE.Views.ChartSettings.text3dDepth": "Profondeur (% de la base)", "DE.Views.ChartSettings.text3dHeight": "Hauteur (% de la base)", "DE.Views.ChartSettings.text3dRotation": "Rotation 3D", "DE.Views.ChartSettings.textAdvanced": "Afficher les paramètres avancés", "DE.Views.ChartSettings.textAutoscale": "Mise à l'échelle automatique", "DE.Views.ChartSettings.textChartType": "Modifier le type de graphique", "DE.Views.ChartSettings.textDefault": "Rotation par défaut", "DE.Views.ChartSettings.textDown": "Bas", "DE.Views.ChartSettings.textEditData": "Modifier les données", "DE.Views.ChartSettings.textHeight": "<PERSON><PERSON>", "DE.Views.ChartSettings.textLeft": "G<PERSON><PERSON>", "DE.Views.ChartSettings.textNarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le champ de vision", "DE.Views.ChartSettings.textOriginalSize": "<PERSON><PERSON> act<PERSON>", "DE.Views.ChartSettings.textPerspective": "Perspective", "DE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textRightAngle": "Axes à angle droit", "DE.Views.ChartSettings.textSize": "<PERSON><PERSON>", "DE.Views.ChartSettings.textStyle": "Style", "DE.Views.ChartSettings.textUndock": "Détacher du panneau", "DE.Views.ChartSettings.textUp": "En haut", "DE.Views.ChartSettings.textWiden": "<PERSON><PERSON><PERSON><PERSON> le champ de vision", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Style d'habillage", "DE.Views.ChartSettings.textX": "Rotation X", "DE.Views.ChartSettings.textY": "Rotation Y", "DE.Views.ChartSettings.txtBehind": "<PERSON><PERSON><PERSON> le texte", "DE.Views.ChartSettings.txtInFront": "Devant le texte", "DE.Views.ChartSettings.txtInline": "Aligné sur le texte", "DE.Views.ChartSettings.txtSquare": "Carré", "DE.Views.ChartSettings.txtThrough": "Au travers", "DE.Views.ChartSettings.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtTitle": "Graphique", "DE.Views.ChartSettings.txtTopAndBottom": "Haut et bas", "DE.Views.ControlSettingsDialog.strGeneral": "Général", "DE.Views.ControlSettingsDialog.textAdd": "Ajouter", "DE.Views.ControlSettingsDialog.textAppearance": "Apparence", "DE.Views.ControlSettingsDialog.textApplyAll": "Appliquer à tout", "DE.Views.ControlSettingsDialog.textBox": "Boîte d'encombrement", "DE.Views.ControlSettingsDialog.textChange": "Modifier", "DE.Views.ControlSettingsDialog.textCheckbox": "Case à cocher", "DE.Views.ControlSettingsDialog.textChecked": "Symbole Activé", "DE.Views.ControlSettingsDialog.textColor": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCombobox": "Zone de liste déroulante", "DE.Views.ControlSettingsDialog.textDate": "Format de date", "DE.Views.ControlSettingsDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textDisplayName": "Nom d'affichage", "DE.Views.ControlSettingsDialog.textDown": "Bas", "DE.Views.ControlSettingsDialog.textDropDown": "Liste déroulante", "DE.Views.ControlSettingsDialog.textFormat": "Afficher la date comme suit", "DE.Views.ControlSettingsDialog.textLang": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textLock": "Verrouillage ", "DE.Views.ControlSettingsDialog.textName": "Titre", "DE.Views.ControlSettingsDialog.textNone": "Aucun", "DE.Views.ControlSettingsDialog.textPlaceholder": "Espace réservé", "DE.Views.ControlSettingsDialog.textShowAs": "Afficher comme ", "DE.Views.ControlSettingsDialog.textSystemColor": "Système", "DE.Views.ControlSettingsDialog.textTag": "Tag", "DE.Views.ControlSettingsDialog.textTitle": "Paramètres de contrôle du contenu", "DE.Views.ControlSettingsDialog.textUnchecked": "Symbole Désactivé", "DE.Views.ControlSettingsDialog.textUp": "En haut", "DE.Views.ControlSettingsDialog.textValue": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.tipChange": "Modifier le symbole", "DE.Views.ControlSettingsDialog.txtLockDelete": "Le contrôle du contenu ne peut pas être supprimé", "DE.Views.ControlSettingsDialog.txtLockEdit": "Le contenu ne peut pas être modifié", "DE.Views.CrossReferenceDialog.textAboveBelow": "Au-dessus/au-dessous", "DE.Views.CrossReferenceDialog.textBookmark": "Signet", "DE.Views.CrossReferenceDialog.textBookmarkText": "Texte du signet", "DE.Views.CrossReferenceDialog.textCaption": "A toute la légende", "DE.Views.CrossReferenceDialog.textEmpty": "La référence de la requête est vide.", "DE.Views.CrossReferenceDialog.textEndnote": "Note de fin", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Numéro de note de fin", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Numéro de note de fin (formaté)", "DE.Views.CrossReferenceDialog.textEquation": "Équation", "DE.Views.CrossReferenceDialog.textFigure": "Figure", "DE.Views.CrossReferenceDialog.textFootnote": "Note de bas de page", "DE.Views.CrossReferenceDialog.textHeading": "<PERSON>-tête", "DE.Views.CrossReferenceDialog.textHeadingNum": "Numéro de l'en-tête", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Numéro de l'en-tête (contexte globale)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Numéro de l'en-tête (pas de contexte)", "DE.Views.CrossReferenceDialog.textHeadingText": "Texte de l'en-tête", "DE.Views.CrossReferenceDialog.textIncludeAbove": "<PERSON><PERSON>re au-dessus/au-dessous", "DE.Views.CrossReferenceDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textInsertAs": "Insérer en tant que lien hypertexte", "DE.Views.CrossReferenceDialog.textLabelNum": "Nom et numéro seulement", "DE.Views.CrossReferenceDialog.textNoteNum": "Numéro de note de bas de page", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Numéro de note de bas de page (formaté)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Seulement le texte de la légende", "DE.Views.CrossReferenceDialog.textPageNum": "Numéro de <PERSON>", "DE.Views.CrossReferenceDialog.textParagraph": "Objet numéroté", "DE.Views.CrossReferenceDialog.textParaNum": "Numéro de paragraphe", "DE.Views.CrossReferenceDialog.textParaNumFull": "Numéro de paragraphe (contexte global)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Numéro de paragraphe (pas de contexte)", "DE.Views.CrossReferenceDialog.textSeparate": "<PERSON><PERSON><PERSON>er les numéros avec", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Texte du paragraphe", "DE.Views.CrossReferenceDialog.textWhich": "Pour quelle légende", "DE.Views.CrossReferenceDialog.textWhichBookmark": "Pour quel signet", "DE.Views.CrossReferenceDialog.textWhichEndnote": "Pour quelle note de fin", "DE.Views.CrossReferenceDialog.textWhichHeading": "Pour quel en-tête", "DE.Views.CrossReferenceDialog.textWhichNote": "A quelle note de bas de page", "DE.Views.CrossReferenceDialog.textWhichPara": "Pour quel objet numéroté", "DE.Views.CrossReferenceDialog.txtReference": "Insérer la référence à", "DE.Views.CrossReferenceDialog.txtTitle": "Renvoi", "DE.Views.CrossReferenceDialog.txtType": "Type de référence", "DE.Views.CustomColumnsDialog.textColumns": "Nombre de colonnes", "DE.Views.CustomColumnsDialog.textSeparator": "Diviseur de colonne", "DE.Views.CustomColumnsDialog.textSpacing": "Espacement entre les colonnes", "DE.Views.CustomColumnsDialog.textTitle": "Colonnes", "DE.Views.DateTimeDialog.confirmDefault": "Définir le format par défaut pour {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Définir par défaut", "DE.Views.DateTimeDialog.textFormat": "Formats", "DE.Views.DateTimeDialog.textLang": "<PERSON><PERSON>", "DE.Views.DateTimeDialog.textUpdate": "Mettre à jour automatiquement", "DE.Views.DateTimeDialog.txtTitle": "Date et heure", "DE.Views.DocProtection.hintProtectDoc": "Protéger le document", "DE.Views.DocProtection.txtDocProtectedComment": "Le document est protégé. <br>V<PERSON> ne pouvez que laisser des commentaires dans ce document.", "DE.Views.DocProtection.txtDocProtectedForms": "Le document est protégé. <br>Vous ne pouvez que remplir les champs de ce document.", "DE.Views.DocProtection.txtDocProtectedTrack": "Le document est protégé. <br><PERSON><PERSON> pouvez modifier ce document, mais toutes les modifications seront suivies.", "DE.Views.DocProtection.txtDocProtectedView": "Le document est protégé. <br>Vous ne pouvez que visualiser ce document.", "DE.Views.DocProtection.txtDocUnlockDescription": "Saisissez le mot de passe pour déprotéger le document", "DE.Views.DocProtection.txtProtectDoc": "Protéger le document", "DE.Views.DocProtection.txtUnlockTitle": "Déprotéger le document", "DE.Views.DocumentHolder.aboveText": "Au-dessus", "DE.Views.DocumentHolder.addCommentText": "Ajouter un commentaire", "DE.Views.DocumentHolder.advancedDropCapText": "Paramètres de la lettrine", "DE.Views.DocumentHolder.advancedEquationText": "Paramètres d'équations", "DE.Views.DocumentHolder.advancedFrameText": "Paramètres avancés du cadre", "DE.Views.DocumentHolder.advancedParagraphText": "Paramètres avancés du paragraphe", "DE.Views.DocumentHolder.advancedTableText": "Paramètres avancés du tableau", "DE.Views.DocumentHolder.advancedText": "Paramètres avancés", "DE.Views.DocumentHolder.alignmentText": "Alignement", "DE.Views.DocumentHolder.allLinearText": "Toutes - Linéaire", "DE.Views.DocumentHolder.allProfText": "Toutes - Professionnel", "DE.Views.DocumentHolder.belowText": "En dessous", "DE.Views.DocumentHolder.breakBeforeText": "Saut de page avant", "DE.Views.DocumentHolder.bulletsText": "Puces et Numéros", "DE.Views.DocumentHolder.cellAlignText": "Alignement vertical de la cellule", "DE.Views.DocumentHolder.cellText": "Cellule", "DE.Views.DocumentHolder.centerText": "Centre", "DE.Views.DocumentHolder.chartText": "Paramètres avancés du graphique ", "DE.Views.DocumentHolder.columnText": "Colonne", "DE.Views.DocumentHolder.currLinearText": "Actuelles - Linéaire", "DE.Views.DocumentHolder.currProfText": "Actuelles - Professionnel", "DE.Views.DocumentHolder.deleteColumnText": "Supprimer la colonne", "DE.Views.DocumentHolder.deleteRowText": "Supprimer la ligne", "DE.Views.DocumentHolder.deleteTableText": "Su<PERSON><PERSON><PERSON> le tableau", "DE.Views.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.direct270Text": "Rotation du texte vers le haut", "DE.Views.DocumentHolder.direct90Text": "Faire pivoter le texte vers le bas", "DE.Views.DocumentHolder.directHText": "Horizontal", "DE.Views.DocumentHolder.directionText": "Orientation du texte", "DE.Views.DocumentHolder.editChartText": "Modifier les données", "DE.Views.DocumentHolder.editFooterText": "Modifier le pied de page", "DE.Views.DocumentHolder.editHeaderText": "Modifier l'en-tête", "DE.Views.DocumentHolder.editHyperlinkText": "Modifier le lien hypertexte", "DE.Views.DocumentHolder.eqToInlineText": "Passer à Inline", "DE.Views.DocumentHolder.guestText": "Invi<PERSON>", "DE.Views.DocumentHolder.hyperlinkText": "Lien hypertexte", "DE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON>gno<PERSON> tout", "DE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.imageText": "Paramètres avancés de l'image", "DE.Views.DocumentHolder.insertColumnLeftText": "Colonne à gauche", "DE.Views.DocumentHolder.insertColumnRightText": "Colonne à droite", "DE.Views.DocumentHolder.insertColumnText": "Insérer une colonne", "DE.Views.DocumentHolder.insertRowAboveText": "Ligne au-dessus", "DE.Views.DocumentHolder.insertRowBelowText": "Ligne en dessous", "DE.Views.DocumentHolder.insertRowText": "Ins<PERSON>rer une ligne", "DE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.keepLinesText": "Lignes solidaires", "DE.Views.DocumentHolder.langText": "Sélectionner la langue", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "À gauche", "DE.Views.DocumentHolder.loadSpellText": "Chargement des variantes en cours...", "DE.Views.DocumentHolder.mergeCellsText": "<PERSON>ner les cellules", "DE.Views.DocumentHolder.moreText": "Plus de variantes...", "DE.Views.DocumentHolder.noSpellVariantsText": "Pas de variantes", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Attention", "DE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON> act<PERSON>", "DE.Views.DocumentHolder.paragraphText": "Paragraphe", "DE.Views.DocumentHolder.removeHyperlinkText": "Supprimer le lien hypertexte", "DE.Views.DocumentHolder.rightText": "A droite", "DE.Views.DocumentHolder.rowText": "Ligne", "DE.Views.DocumentHolder.saveStyleText": "Créer un nouveau style", "DE.Views.DocumentHolder.selectCellText": "Sélectionner la cellule", "DE.Views.DocumentHolder.selectColumnText": "Sélectionner la colonne", "DE.Views.DocumentHolder.selectRowText": "Sélectionner la ligne", "DE.Views.DocumentHolder.selectTableText": "Sélect<PERSON>ner le tableau", "DE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.shapeText": "Paramètres avancés de la forme", "DE.Views.DocumentHolder.spellcheckText": "Vérification orthographique", "DE.Views.DocumentHolder.splitCellsText": "Fractionner la cellule...", "DE.Views.DocumentHolder.splitCellTitleText": "Fractionner la cellule", "DE.Views.DocumentHolder.strDelete": "Supprimer la signature", "DE.Views.DocumentHolder.strDetails": "<PERSON><PERSON><PERSON> de la signature", "DE.Views.DocumentHolder.strSetup": "Mise en place de la signature", "DE.Views.DocumentHolder.strSign": "Signer", "DE.Views.DocumentHolder.styleText": "En tant que style", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "Accepter la <PERSON>", "DE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrange": "Organiser", "DE.Views.DocumentHolder.textArrangeBack": "Mettre en arrière-plan", "DE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeForward": "Avancer", "DE.Views.DocumentHolder.textArrangeFront": "Mettre au premier plan", "DE.Views.DocumentHolder.textCells": "Cellules", "DE.Views.DocumentHolder.textCol": "Supprimer la colonne entière", "DE.Views.DocumentHolder.textContentControls": "Contrôle du contenu", "DE.Views.DocumentHolder.textContinueNumbering": "Continuer la numérotation", "DE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCropFill": "Remplissage", "DE.Views.DocumentHolder.textCropFit": "Ajuster", "DE.Views.DocumentHolder.textCut": "Couper", "DE.Views.DocumentHolder.textDistributeCols": "Distribuer les colonnes", "DE.Views.DocumentHolder.textDistributeRows": "Distribuer les lignes", "DE.Views.DocumentHolder.textEditControls": "Paramètres de contrôle du contenu", "DE.Views.DocumentHolder.textEditPoints": "Modifier les points", "DE.Views.DocumentHolder.textEditWrapBoundary": "Modifier les limites du renvoi à la ligne", "DE.Views.DocumentHolder.textFlipH": "Retourner horizontalement", "DE.Views.DocumentHolder.textFlipV": "Retourner verticalement", "DE.Views.DocumentHolder.textFollow": "Suivre le mouvement", "DE.Views.DocumentHolder.textFromFile": "<PERSON>'un fichier", "DE.Views.DocumentHolder.textFromStorage": "À partir de l'espace de stockage", "DE.Views.DocumentHolder.textFromUrl": "D'une URL", "DE.Views.DocumentHolder.textJoinList": "Rejoindre la liste précédente. ", "DE.Views.DocumentHolder.textLeft": "<PERSON><PERSON><PERSON>r les cellules vers la gauche", "DE.Views.DocumentHolder.textNest": "Tableau imbriqué", "DE.Views.DocumentHolder.textNextPage": "<PERSON> suivante", "DE.Views.DocumentHolder.textNumberingValue": "Valeur initiale", "DE.Views.DocumentHolder.textPaste": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textPrevPage": "<PERSON> p<PERSON>", "DE.Views.DocumentHolder.textRefreshField": "Actualiser le champ", "DE.Views.DocumentHolder.textReject": "Rejeter la modification", "DE.Views.DocumentHolder.textRemCheckBox": "Supprimer une case à cocher", "DE.Views.DocumentHolder.textRemComboBox": "Supprimer une zone de liste déroulante", "DE.Views.DocumentHolder.textRemDropdown": "Supprimer une liste déroulante", "DE.Views.DocumentHolder.textRemField": "Supp<PERSON><PERSON> le champ texte", "DE.Views.DocumentHolder.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRemoveControl": "Supprimer le contrôle du contenu", "DE.Views.DocumentHolder.textRemPicture": "Supprimer l'image", "DE.Views.DocumentHolder.textRemRadioBox": "Supprimer le bouton radio", "DE.Views.DocumentHolder.textReplace": "Remplacer l’image", "DE.Views.DocumentHolder.textRotate": "Rotation", "DE.Views.DocumentHolder.textRotate270": "Faire pivoter à gauche de 90°", "DE.Views.DocumentHolder.textRotate90": "Faire pivoter à droite de 90°", "DE.Views.DocumentHolder.textRow": "Supprimer la ligne entière", "DE.Views.DocumentHolder.textSeparateList": "Liste séparée", "DE.Views.DocumentHolder.textSettings": "Paramètres", "DE.Views.DocumentHolder.textSeveral": "Plusieurs Lignes/Colonnes ", "DE.Views.DocumentHolder.textShapeAlignBottom": "Aligner en bas", "DE.Views.DocumentHolder.textShapeAlignCenter": "Aligner au centre", "DE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON> à gauche", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Aligner au milieu", "DE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "DE.Views.DocumentHolder.textShapeAlignTop": "Aligner en haut", "DE.Views.DocumentHolder.textStartNewList": "Commencer une autre liste", "DE.Views.DocumentHolder.textStartNumberingFrom": "Fixer la valeur initiale", "DE.Views.DocumentHolder.textTitleCellsRemove": "Supprimer les cellules", "DE.Views.DocumentHolder.textTOC": "Table des matières", "DE.Views.DocumentHolder.textTOCSettings": "Paramètres de la table des matières", "DE.Views.DocumentHolder.textUndo": "Annuler", "DE.Views.DocumentHolder.textUpdateAll": "Actualiser le tableau entier", "DE.Views.DocumentHolder.textUpdatePages": "Actualiser les numéros de page uniquement", "DE.Views.DocumentHolder.textUpdateTOC": "Actualiser la table des matières", "DE.Views.DocumentHolder.textWrap": "Style d'habillage", "DE.Views.DocumentHolder.tipIsLocked": "Cet élément est en cours d'édition par un autre utilisateur.", "DE.Views.DocumentHolder.toDictionaryText": "Ajouter au dictionnaire", "DE.Views.DocumentHolder.txtAddBottom": "Ajouter bordure inférieure", "DE.Views.DocumentHolder.txtAddFractionBar": "Ajouter barre de fraction", "DE.Views.DocumentHolder.txtAddHor": "Ajouter une ligne horizontale", "DE.Views.DocumentHolder.txtAddLB": "Ajouter une ligne en bas à gauche", "DE.Views.DocumentHolder.txtAddLeft": "Ajouter une bordure gauche", "DE.Views.DocumentHolder.txtAddLT": "Ajouter une ligne supérieure gauche", "DE.Views.DocumentHolder.txtAddRight": "Ajouter une bordure à droite", "DE.Views.DocumentHolder.txtAddTop": "Ajouter une bordure supérieure", "DE.Views.DocumentHolder.txtAddVer": "Ajouter une ligne verticale", "DE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> à caractère", "DE.Views.DocumentHolder.txtBehind": "<PERSON><PERSON><PERSON> le texte", "DE.Views.DocumentHolder.txtBorderProps": "Propriétés de bordure", "DE.Views.DocumentHolder.txtBottom": "En bas", "DE.Views.DocumentHolder.txtColumnAlign": "L'alignement de la colonne", "DE.Views.DocumentHolder.txtDecreaseArg": "Diminuer la taille de l'argument", "DE.Views.DocumentHolder.txtDeleteArg": "Supprimer l'argument", "DE.Views.DocumentHolder.txtDeleteBreak": "Supprimer un saut manuel", "DE.Views.DocumentHolder.txtDeleteChars": "Supprimer caractères enserrant", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Supprimer caractères et séparateurs qui entourent", "DE.Views.DocumentHolder.txtDeleteEq": "Supprimer l'équation", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Supprimer caractère d'imprimerie", "DE.Views.DocumentHolder.txtDeleteRadical": "Supprimer radical", "DE.Views.DocumentHolder.txtDistribHor": "Distribuer horizontalement", "DE.Views.DocumentHolder.txtDistribVert": "Distribuer verticalement", "DE.Views.DocumentHolder.txtEmpty": "(Vide)", "DE.Views.DocumentHolder.txtFractionLinear": "Modifier à la fraction linéaire", "DE.Views.DocumentHolder.txtFractionSkewed": "Modifier à la fraction oblique", "DE.Views.DocumentHolder.txtFractionStacked": "Modifier à la fraction empilée", "DE.Views.DocumentHolder.txtGroup": "Grouper", "DE.Views.DocumentHolder.txtGroupCharOver": "Char <PERSON><PERSON><PERSON><PERSON> le texte", "DE.Views.DocumentHolder.txtGroupCharUnder": "Char en-dessus le texte", "DE.Views.DocumentHolder.txtHideBottom": "Masquer bordure inférieure", "DE.Views.DocumentHolder.txtHideBottomLimit": "Cacher limite inférieure", "DE.Views.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON> le crochet de fermeture", "DE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON> de<PERSON>", "DE.Views.DocumentHolder.txtHideHor": "Cacher ligne horizontale", "DE.Views.DocumentHolder.txtHideLB": "Cacher la ligne en bas à gauche", "DE.Views.DocumentHolder.txtHideLeft": "Cacher la bordure gauche", "DE.Views.DocumentHolder.txtHideLT": "Cacher la ligne en haut à gauche", "DE.Views.DocumentHolder.txtHideOpenBracket": "<PERSON>acher crochet d'ouverture", "DE.Views.DocumentHolder.txtHidePlaceholder": "Cacher espace réservé", "DE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON> bordure droite", "DE.Views.DocumentHolder.txtHideTop": "Cacher bordure supérieure", "DE.Views.DocumentHolder.txtHideTopLimit": "Cacher limite supérieure", "DE.Views.DocumentHolder.txtHideVer": "Cacher ligne verticale", "DE.Views.DocumentHolder.txtIncreaseArg": "Augmenter la taille de l'argument", "DE.Views.DocumentHolder.txtInFront": "Devant le texte", "DE.Views.DocumentHolder.txtInline": "Aligné sur le texte", "DE.Views.DocumentHolder.txtInsertArgAfter": "Insérez l'argument après", "DE.Views.DocumentHolder.txtInsertArgBefore": "<PERSON><PERSON><PERSON><PERSON> argument devant", "DE.Views.DocumentHolder.txtInsertBreak": "In<PERSON><PERSON><PERSON> pause manuelle", "DE.Views.DocumentHolder.txtInsertCaption": "Insérer une légende", "DE.Views.DocumentHolder.txtInsertEqAfter": "Insérer équation après", "DE.Views.DocumentHolder.txtInsertEqBefore": "Ins<PERSON>rez l'équation avant", "DE.Views.DocumentHolder.txtKeepTextOnly": "G<PERSON>ez le texte seulement", "DE.Views.DocumentHolder.txtLimitChange": "Modifier d'emplacement des locations", "DE.Views.DocumentHolder.txtLimitOver": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> le texte", "DE.Views.DocumentHolder.txtLimitUnder": "Limite en dessous le texte", "DE.Views.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON>er crochets à la hauteur de l'argument", "DE.Views.DocumentHolder.txtMatrixAlign": "Alignement de la matrice", "DE.Views.DocumentHolder.txtOverbar": "<PERSON>e au-dessus d'un texte", "DE.Views.DocumentHolder.txtOverwriteCells": "<PERSON><PERSON>lace<PERSON> les cellules", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Garder la mise en forme source", "DE.Views.DocumentHolder.txtPressLink": "Appuyez sur {0} et cliquez sur le lien", "DE.Views.DocumentHolder.txtPrintSelection": "Imprimer la sélection", "DE.Views.DocumentHolder.txtRemFractionBar": "Supprimer la barre de fraction", "DE.Views.DocumentHolder.txtRemLimit": "Supprimer la limite", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Supp<PERSON>er le caractère d'accent", "DE.Views.DocumentHolder.txtRemoveBar": "Supprimer la barre", "DE.Views.DocumentHolder.txtRemoveWarning": "Voulez-vous supprimer cette signature?<br>Cette action ne peut pas être annulée.", "DE.Views.DocumentHolder.txtRemScripts": "Supprimer les scripts", "DE.Views.DocumentHolder.txtRemSubscript": "Supprimer la souscription", "DE.Views.DocumentHolder.txtRemSuperscript": "Supprimer la suscription", "DE.Views.DocumentHolder.txtScriptsAfter": "Scripts après le texte", "DE.Views.DocumentHolder.txtScriptsBefore": "Scripts avant le texte", "DE.Views.DocumentHolder.txtShowBottomLimit": "Montrer limite inférieure", "DE.Views.DocumentHolder.txtShowCloseBracket": "Afficher crochet de fermeture", "DE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "DE.Views.DocumentHolder.txtShowOpenBracket": "Afficher crochet d'ouverture", "DE.Views.DocumentHolder.txtShowPlaceholder": "Afficher espace réservé", "DE.Views.DocumentHolder.txtShowTopLimit": "Afficher limite supérieure", "DE.Views.DocumentHolder.txtSquare": "Carré", "DE.Views.DocumentHolder.txtStretchBrackets": "Allonger des crochets", "DE.Views.DocumentHolder.txtThrough": "Au travers", "DE.Views.DocumentHolder.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtTop": "En haut", "DE.Views.DocumentHolder.txtTopAndBottom": "Haut et bas", "DE.Views.DocumentHolder.txtUnderbar": "Barre en dessous d'un texte", "DE.Views.DocumentHolder.txtUngroup": "Dissocier", "DE.Views.DocumentHolder.txtWarnUrl": "Cliquer sur ce lien peut être dangereux pour votre appareil et vos données. <br>Êtes-vous sûr de vouloir continuer ?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Mettre à jour le style %1 ", "DE.Views.DocumentHolder.vertAlignText": "Alignement vertical", "DE.Views.DropcapSettingsAdvanced.strBorders": "Bordures et remplissage", "DE.Views.DropcapSettingsAdvanced.strDropcap": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.strMargins": "Marges", "DE.Views.DropcapSettingsAdvanced.textAlign": "Alignement", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "Au moins ", "DE.Views.DropcapSettingsAdvanced.textAuto": "Auto", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Couleur d'arrière-plan", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON> de bordure", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Cliquez sur le diagramme ou utilisez les boutons pour sélectionner les bordures", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBottom": "En bas", "DE.Views.DropcapSettingsAdvanced.textCenter": "Centre", "DE.Views.DropcapSettingsAdvanced.textColumn": "Colonne", "DE.Views.DropcapSettingsAdvanced.textDistance": "Distance du texte", "DE.Views.DropcapSettingsAdvanced.textExact": "Exactement", "DE.Views.DropcapSettingsAdvanced.textFlow": "Cadre flottant", "DE.Views.DropcapSettingsAdvanced.textFont": "Police", "DE.Views.DropcapSettingsAdvanced.textFrame": "Cadre", "DE.Views.DropcapSettingsAdvanced.textHeight": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.DropcapSettingsAdvanced.textInline": "Cadre en ligne", "DE.Views.DropcapSettingsAdvanced.textInMargin": "<PERSON><PERSON> la marge", "DE.Views.DropcapSettingsAdvanced.textInText": "<PERSON><PERSON> le texte", "DE.Views.DropcapSettingsAdvanced.textLeft": "À gauche", "DE.Views.DropcapSettingsAdvanced.textMargin": "Marge", "DE.Views.DropcapSettingsAdvanced.textMove": "Déplacer avec le texte", "DE.Views.DropcapSettingsAdvanced.textNone": "Aucune", "DE.Views.DropcapSettingsAdvanced.textPage": "Page", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Paragraphe", "DE.Views.DropcapSettingsAdvanced.textParameters": "Paramètres", "DE.Views.DropcapSettingsAdvanced.textPosition": "Position", "DE.Views.DropcapSettingsAdvanced.textRelative": "par rapport à", "DE.Views.DropcapSettingsAdvanced.textRight": "A droite", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "<PERSON>ur des lignes", "DE.Views.DropcapSettingsAdvanced.textTitle": "Lettrine - Paramètres avancés", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Cadre - Paramètres avancés", "DE.Views.DropcapSettingsAdvanced.textTop": "En haut", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertical", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "Police", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "Pas de bordures", "DE.Views.EditListItemDialog.textDisplayName": "Nom d'affichage", "DE.Views.EditListItemDialog.textNameError": "Veuillez renseigner le nom d'affichage.", "DE.Views.EditListItemDialog.textValue": "<PERSON><PERSON>", "DE.Views.EditListItemDialog.textValueError": "Un élément avec la même valeur existe déjà.", "DE.Views.FileMenu.btnBackCaption": "<PERSON>u<PERSON><PERSON>r l'emplacement du fichier", "DE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON> le menu", "DE.Views.FileMenu.btnCreateNewCaption": "Nouveau document", "DE.Views.FileMenu.btnDownloadCaption": "Télécharger comme", "DE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHelpCaption": "Aide", "DE.Views.FileMenu.btnHistoryCaption": "Historique des versions", "DE.Views.FileMenu.btnInfoCaption": "Descriptif du document", "DE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON>ger", "DE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnRenameCaption": "<PERSON>mmer", "DE.Views.FileMenu.btnReturnCaption": "Retour au Document", "DE.Views.FileMenu.btnRightsCaption": "Droits d'accès", "DE.Views.FileMenu.btnSaveAsCaption": "Enregistrer sous", "DE.Views.FileMenu.btnSaveCaption": "Enregistrer", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Enregistrer une copie comme", "DE.Views.FileMenu.btnSettingsCaption": "Paramètres avancés", "DE.Views.FileMenu.btnToEditCaption": "Modifier le document", "DE.Views.FileMenu.textDownload": "Télécharger", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Document vide", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Créer nouveau", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Appliquer", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Ajouter un auteur", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "A<PERSON>ter du texte", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Changer les droits d'accès", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Commentaire", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Affichage rapide sur le Web", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Chargement en cours...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Dernière modification par", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Dernière modification", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "Non", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "Pages", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Paragraphes", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "Producteur PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF marqué", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "Version PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Emplacement", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personnes qui ont des droits", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Caractères avec espaces", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Statistiques", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Sujet", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "Caractères", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titre du document", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "O<PERSON>", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Changer les droits d'accès", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Personnes qui ont des droits", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Avertissement", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Avec mot de passe", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protéger le document", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "Avec signature", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Modifier le document", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "L'édition supprimera les signatures du document.<br>Êtes-vous sûr de vouloir continuer?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Ce document a été protégé par mot de passe", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Ce document doit être signé.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Des signatures valables ont été ajoutées au document. Le document est protégé contre l'édition.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Certaines signatures numériques dans le document sont invalides ou n'ont pas pu être vérifiées. Le document est protégé contre l'édition.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Voir les signatures", "DE.Views.FileMenuPanels.Settings.okButtonText": "Appliquer", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Mode de co-édition ", "DE.Views.FileMenuPanels.Settings.strFast": "Rapide", "DE.Views.FileMenuPanels.Settings.strFontRender": "Hinting de la police", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorer les mots en MAJUSCULES", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignorer les mots contenant des chiffres", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Réglages macros", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Afficher le bouton Options de collage lorsque le contenu est collé ", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Visibilité des modifications en co-édition", "DE.Views.FileMenuPanels.Settings.strShowComments": "Afficher les commentaires dans le texte", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Afficher les modifications apportées par d'autres utilisateurs", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "Afficher les commentaires résolus", "DE.Views.FileMenuPanels.Settings.strStrict": "Strict", "DE.Views.FileMenuPanels.Settings.strTheme": "Thème d'interface", "DE.Views.FileMenuPanels.Settings.strUnit": "Unité de mesure", "DE.Views.FileMenuPanels.Settings.strZoom": "Valeur du zoom par défaut", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Toutes les 10 minutes", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Toutes les 30 minutes", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Toutes les 5 minutes", "DE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON> he<PERSON>", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Guides d'alignement", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Récupération automatique", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Enregistrement automatique", "DE.Views.FileMenuPanels.Settings.textDisabled": "Désactivé", "DE.Views.FileMenuPanels.Settings.textForceSave": "Enregistrer des versions intermédiaires", "DE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON> minute", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Rendre les fichiers compatibles avec les anciennes versions de MS Word lorsqu'ils sont enregistrés au format DOCX", "DE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON><PERSON><PERSON> toutes les modifications", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Options de correction automatique", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Mise en cache par défaut", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Afficher par clic dans les ballons", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Afficher lorsque le pointeur est maintenu sur l'infobulle", "DE.Views.FileMenuPanels.Settings.txtCm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaboration", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Activer le mode sombre pour les documents", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Édition et sauvegarde", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Coédition en temps réel. Toutes les modifications sont enregistrées automatiquement", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Ajuster à la page", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Ajuster à la largeur", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtLast": "Voir le dernier", "DE.Views.FileMenuPanels.Settings.txtMac": "comme OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtNone": "Surligner aucune modification", "DE.Views.FileMenuPanels.Settings.txtProofing": "Vérification", "DE.Views.FileMenuPanels.Settings.txtPt": "Point", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Afficher le bouton d'impression rapide dans l'en-tête de l'éditeur", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Le document sera imprimé sur la dernière imprimante sélectionnée ou par défaut", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Activer tout", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Activer toutes les macros sans notification", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Afficher le suivi des modifications", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Vérification de l'orthographe", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON> tout", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Désactiver toutes les macros sans notification", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilisez le bouton \"Enregistrer\" pour synchroniser les changements apportés par vous et d'autres personnes.", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utiliser la touche Alt pour naviguer dans l'interface utilisateur à l'aide du clavier.", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utiliser la touche Option pour naviguer dans l'interface utilisateur à l'aide du clavier.", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Montrer la notification", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Désactiver toutes les macros avec notification", "DE.Views.FileMenuPanels.Settings.txtWin": "comme Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Espace de travail", "DE.Views.FormSettings.textAlways": "Toujours", "DE.Views.FormSettings.textAnyone": "<PERSON>ut le monde", "DE.Views.FormSettings.textAspect": "Verrouiller les proportions", "DE.Views.FormSettings.textAtLeast": "Au moins ", "DE.Views.FormSettings.textAuto": "Automatique", "DE.Views.FormSettings.textAutofit": "Ajuster automatiquement", "DE.Views.FormSettings.textBackgroundColor": "Couleur d'arrière-plan", "DE.Views.FormSettings.textCheckbox": "Case à cocher", "DE.Views.FormSettings.textColor": "<PERSON><PERSON><PERSON> de bordure.", "DE.Views.FormSettings.textComb": "Peigne de caractères ", "DE.Views.FormSettings.textCombobox": "Zone de liste déroulante", "DE.Views.FormSettings.textComplex": "Champ complexe", "DE.Views.FormSettings.textConnected": "Champs connectés", "DE.Views.FormSettings.textCreditCard": "Numéro de carte de crédit (par exemple 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Champ Date et heure", "DE.Views.FormSettings.textDateFormat": "Afficher la date comme suit", "DE.Views.FormSettings.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDigits": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDisconnect": "Déconnexion ", "DE.Views.FormSettings.textDropDown": "Liste déroulante", "DE.Views.FormSettings.textExact": "Exactement", "DE.Views.FormSettings.textField": "<PERSON><PERSON> texte", "DE.Views.FormSettings.textFillRoles": "Qui doit remplir ce formulaire ?", "DE.Views.FormSettings.textFixed": "<PERSON><PERSON> de champ fixe", "DE.Views.FormSettings.textFormat": "Format", "DE.Views.FormSettings.textFormatSymbols": "Symboles autorisés", "DE.Views.FormSettings.textFromFile": "Depuis un fichier", "DE.Views.FormSettings.textFromStorage": "À partir de l'espace de stockage", "DE.Views.FormSettings.textFromUrl": "A partir d'une URL", "DE.Views.FormSettings.textGroupKey": "Clé de groupe", "DE.Views.FormSettings.textImage": "Image", "DE.Views.FormSettings.textKey": "Clé", "DE.Views.FormSettings.textLang": "<PERSON><PERSON>", "DE.Views.FormSettings.textLetters": "Lettres", "DE.Views.FormSettings.textLock": "Verrou ", "DE.Views.FormSettings.textMask": "Masque arbitraire", "DE.Views.FormSettings.textMaxChars": "Limite de caractères", "DE.Views.FormSettings.textMulti": "Champ de saisie à plusieurs lignes", "DE.Views.FormSettings.textNever": "<PERSON><PERSON>", "DE.Views.FormSettings.textNoBorder": "Sans bordures", "DE.Views.FormSettings.textNone": "Aucun", "DE.Views.FormSettings.textPhone1": "Numéro de téléphone (par exemple, (*************)", "DE.Views.FormSettings.textPhone2": "Numéro de téléphone (par exemple, +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Espace réservé", "DE.Views.FormSettings.textRadiobox": "Bouton radio", "DE.Views.FormSettings.textReg": "Expression régulière", "DE.Views.FormSettings.textRequired": "Obligatoire", "DE.Views.FormSettings.textScale": "Mise à l'échelle", "DE.Views.FormSettings.textSelectImage": "Sélectionner une image", "DE.Views.FormSettings.textTag": "Tag", "DE.Views.FormSettings.textTip": "Conseil", "DE.Views.FormSettings.textTipAdd": "Ajouter une nouvelle valeur", "DE.Views.FormSettings.textTipDelete": "Supprimer la valeur", "DE.Views.FormSettings.textTipDown": "Descendre", "DE.Views.FormSettings.textTipUp": "<PERSON><PERSON>", "DE.Views.FormSettings.textTooBig": "L'image semble trop grande", "DE.Views.FormSettings.textTooSmall": "L'image semble trop petite", "DE.Views.FormSettings.textUKPassport": "Numéro de passeport britannique (par exemple, *********)", "DE.Views.FormSettings.textUnlock": "Déverrouiller", "DE.Views.FormSettings.textUSSSN": "SSN des États-Unis (par exemple, ***********)", "DE.Views.FormSettings.textValue": "Options de valeur", "DE.Views.FormSettings.textWidth": "Largeur de cellule", "DE.Views.FormSettings.textZipCodeUS": "Code postal des États-Unis (par exemple, 92663 ou 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "Case à cocher", "DE.Views.FormsTab.capBtnComboBox": "Zone de liste déroulante", "DE.Views.FormsTab.capBtnComplex": "Champ complexe", "DE.Views.FormsTab.capBtnDownloadForm": "Télécharger comme oform", "DE.Views.FormsTab.capBtnDropDown": "Liste déroulante", "DE.Views.FormsTab.capBtnEmail": "<PERSON>resse E-mail", "DE.Views.FormsTab.capBtnImage": "Image", "DE.Views.FormsTab.capBtnManager": "<PERSON><PERSON><PERSON> rôles", "DE.Views.FormsTab.capBtnNext": "Champ suivant", "DE.Views.FormsTab.capBtnPhone": "Numéro de téléphone", "DE.Views.FormsTab.capBtnPrev": "<PERSON><PERSON>", "DE.Views.FormsTab.capBtnRadioBox": "Bouton radio", "DE.Views.FormsTab.capBtnSaveForm": "Enregistrer sous oform", "DE.Views.FormsTab.capBtnSubmit": "So<PERSON><PERSON><PERSON> ", "DE.Views.FormsTab.capBtnText": "<PERSON><PERSON> texte", "DE.Views.FormsTab.capBtnView": "Aperçu du formulaire", "DE.Views.FormsTab.capCreditCard": "Carte de <PERSON>", "DE.Views.FormsTab.capDateTime": "Date et heure", "DE.Views.FormsTab.capZipCode": "Code postal", "DE.Views.FormsTab.textAnyone": "<PERSON>ut le monde", "DE.Views.FormsTab.textClear": "Effacer les champs", "DE.Views.FormsTab.textClearFields": "Effacer tous les champs", "DE.Views.FormsTab.textCreateForm": "Ajoutez des champs et créer un document OFORM remplissable", "DE.Views.FormsTab.textGotIt": "OK", "DE.Views.FormsTab.textHighlight": "Paramètres de surbrillance", "DE.Views.FormsTab.textNoHighlight": "Pas de surbrillan<PERSON> ", "DE.Views.FormsTab.textRequired": "<PERSON><PERSON><PERSON><PERSON> remplir tous les champs obligatoires avant d'envoyer le formulaire.", "DE.Views.FormsTab.textSubmited": "Formulaire soumis avec succès", "DE.Views.FormsTab.tipCheckBox": "Insérer une case à cocher", "DE.Views.FormsTab.tipComboBox": "Insérer une zone de liste déroulante", "DE.Views.FormsTab.tipComplexField": "Insérer un champ complexe", "DE.Views.FormsTab.tipCreditCard": "Ins<PERSON>rer le numéro de la carte de crédit", "DE.Views.FormsTab.tipDateTime": "Insérer la date et l'heure", "DE.Views.FormsTab.tipDownloadForm": "Télécharger un fichier sous forme de document OFORM à remplir", "DE.Views.FormsTab.tipDropDown": "Insérer une liste déroulante", "DE.Views.FormsTab.tipEmailField": "Insérer l'adresse e-mail", "DE.Views.FormsTab.tipFixedText": "Insérer un champ de texte fixe", "DE.Views.FormsTab.tipImageField": "Insérer une image", "DE.Views.FormsTab.tipInlineText": "Insérer un champ de texte aligné", "DE.Views.FormsTab.tipManager": "<PERSON><PERSON><PERSON> rôles", "DE.Views.FormsTab.tipNextForm": "Allez au champ suivant", "DE.Views.FormsTab.tipPhoneField": "Insérer le numéro de téléphone", "DE.Views.FormsTab.tipPrevForm": "Allez au champs précédent", "DE.Views.FormsTab.tipRadioBox": "Insérer bouton radio", "DE.Views.FormsTab.tipSaveForm": "Enregistrer un fichier en tant que document OFORM remplissable", "DE.Views.FormsTab.tipSubmit": "Soumettre le formulaire ", "DE.Views.FormsTab.tipTextField": "Insérer un champ texte", "DE.Views.FormsTab.tipViewForm": "Aperçu du formulaire", "DE.Views.FormsTab.tipZipCode": "Insérer un code postal", "DE.Views.FormsTab.txtFixedDesc": "Insérer un champ de texte fixe", "DE.Views.FormsTab.txtFixedText": "Fixe", "DE.Views.FormsTab.txtInlineDesc": "Insérer un champ de texte aligné", "DE.Views.FormsTab.txtInlineText": "En ligne", "DE.Views.FormsTab.txtUntitled": "Sans titre", "DE.Views.HeaderFooterSettings.textBottomCenter": "En bas au centre", "DE.Views.HeaderFooterSettings.textBottomLeft": "En bas à gauche", "DE.Views.HeaderFooterSettings.textBottomPage": "Bas de page", "DE.Views.HeaderFooterSettings.textBottomRight": "En bas à droite", "DE.Views.HeaderFooterSettings.textDiffFirst": "Première page différente", "DE.Views.HeaderFooterSettings.textDiffOdd": "Pages paires et impaires différentes", "DE.Views.HeaderFooterSettings.textFrom": "Commencer par", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Pied de page à partir du bas", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "En-tête à partir du haut", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Insérer à la position actuelle", "DE.Views.HeaderFooterSettings.textOptions": "Options", "DE.Views.HeaderFooterSettings.textPageNum": "Insérer le numéro de page", "DE.Views.HeaderFooterSettings.textPageNumbering": "Numérotation des pages", "DE.Views.HeaderFooterSettings.textPosition": "Position", "DE.Views.HeaderFooterSettings.textPrev": "Continuer à partir de la section précédente", "DE.Views.HeaderFooterSettings.textSameAs": "<PERSON>r au précédent", "DE.Views.HeaderFooterSettings.textTopCenter": "Haut au centre", "DE.Views.HeaderFooterSettings.textTopLeft": "En haut à gauche", "DE.Views.HeaderFooterSettings.textTopPage": "<PERSON><PERSON> de page", "DE.Views.HeaderFooterSettings.textTopRight": "En haut à droite", "DE.Views.HyperlinkSettingsDialog.textDefault": "Fragment du texte sélectionné", "DE.Views.HyperlinkSettingsDialog.textDisplay": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.textExternal": "Lien externe", "DE.Views.HyperlinkSettingsDialog.textInternal": "Placer dans le document", "DE.Views.HyperlinkSettingsDialog.textTitle": "Paramètres du lien hypertexte", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Texte de l'info-bulle ", "DE.Views.HyperlinkSettingsDialog.textUrl": "Lien vers", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Début du document", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Signets", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "Ce champ est obligatoire", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ce champ doit être une URL au format \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Ce champ est limité à 2083 caractères.", "DE.Views.ImageSettings.textAdvanced": "Afficher les paramètres avancés", "DE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropFill": "Remplissage", "DE.Views.ImageSettings.textCropFit": "Ajuster", "DE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON><PERSON> à la forme", "DE.Views.ImageSettings.textEdit": "Modifier", "DE.Views.ImageSettings.textEditObject": "Modifier l'objet", "DE.Views.ImageSettings.textFitMargins": "Ajuster aux marges", "DE.Views.ImageSettings.textFlip": "Retournement", "DE.Views.ImageSettings.textFromFile": "Depuis un fichier", "DE.Views.ImageSettings.textFromStorage": "À partir de l'espace de stockage", "DE.Views.ImageSettings.textFromUrl": "D'une URL", "DE.Views.ImageSettings.textHeight": "<PERSON><PERSON>", "DE.Views.ImageSettings.textHint270": "Faire pivoter à gauche de 90°", "DE.Views.ImageSettings.textHint90": "Faire pivoter à droite de 90°", "DE.Views.ImageSettings.textHintFlipH": "Retourner horizontalement", "DE.Views.ImageSettings.textHintFlipV": "Retourner verticalement", "DE.Views.ImageSettings.textInsert": "Remplacer l’image", "DE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON> act<PERSON>", "DE.Views.ImageSettings.textRecentlyUsed": "Récemment utilisé", "DE.Views.ImageSettings.textRotate90": "Faire pivoter de 90°", "DE.Views.ImageSettings.textRotation": "Rotation", "DE.Views.ImageSettings.textSize": "<PERSON><PERSON>", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Style d'habillage", "DE.Views.ImageSettings.txtBehind": "<PERSON><PERSON><PERSON> le texte", "DE.Views.ImageSettings.txtInFront": "Devant le texte", "DE.Views.ImageSettings.txtInline": "Aligné sur le texte", "DE.Views.ImageSettings.txtSquare": "Carré", "DE.Views.ImageSettings.txtThrough": "Au travers", "DE.Views.ImageSettings.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtTopAndBottom": "Haut et bas", "DE.Views.ImageSettingsAdvanced.strMargins": "Marges intérieures", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absolue", "DE.Views.ImageSettingsAdvanced.textAlignment": "Alignement", "DE.Views.ImageSettingsAdvanced.textAlt": "Texte de remplacement", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Description", "DE.Views.ImageSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme automatique, du graphique ou du tableau.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "Titre", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "Flèches", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Verrouiller les proportions", "DE.Views.ImageSettingsAdvanced.textAutofit": "Ajuster automatiquement", "DE.Views.ImageSettingsAdvanced.textBeginSize": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "Style de début", "DE.Views.ImageSettingsAdvanced.textBelow": "en dessous", "DE.Views.ImageSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "En bas", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Marge inférieure", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Habillage du texte", "DE.Views.ImageSettingsAdvanced.textCapType": "Type de lettrine", "DE.Views.ImageSettingsAdvanced.textCenter": "Centre", "DE.Views.ImageSettingsAdvanced.textCharacter": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textColumn": "Colonne", "DE.Views.ImageSettingsAdvanced.textDistance": "Distance du texte", "DE.Views.ImageSettingsAdvanced.textEndSize": "<PERSON><PERSON> de <PERSON>", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Style final", "DE.Views.ImageSettingsAdvanced.textFlat": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalement", "DE.Views.ImageSettingsAdvanced.textJoinType": "Type de connexion", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Proportions constantes", "DE.Views.ImageSettingsAdvanced.textLeft": "À gauche", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "Marge gauche", "DE.Views.ImageSettingsAdvanced.textLine": "Ligne", "DE.Views.ImageSettingsAdvanced.textLineStyle": "Style de ligne", "DE.Views.ImageSettingsAdvanced.textMargin": "Marge", "DE.Views.ImageSettingsAdvanced.textMiter": "Onglet", "DE.Views.ImageSettingsAdvanced.textMove": "Déplacer avec le texte", "DE.Views.ImageSettingsAdvanced.textOptions": "Options", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON> r<PERSON>", "DE.Views.ImageSettingsAdvanced.textOverlap": "Autoriser le chevauchement", "DE.Views.ImageSettingsAdvanced.textPage": "Page", "DE.Views.ImageSettingsAdvanced.textParagraph": "Paragraphe", "DE.Views.ImageSettingsAdvanced.textPosition": "Position", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Position relative", "DE.Views.ImageSettingsAdvanced.textRelative": "par rapport à", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relatif", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Redimensionner la forme pour contenir le texte", "DE.Views.ImageSettingsAdvanced.textRight": "A droite", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Marge droite", "DE.Views.ImageSettingsAdvanced.textRightOf": "à droite de", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "DE.Views.ImageSettingsAdvanced.textRound": "Arrondi", "DE.Views.ImageSettingsAdvanced.textShape": "Paramètres de forme", "DE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textSquare": "Carré", "DE.Views.ImageSettingsAdvanced.textTextBox": "Zone de texte", "DE.Views.ImageSettingsAdvanced.textTitle": "Image - Paramètres avancés", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Graphique - Paramètres avancés", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Forme - Paramètres avancés", "DE.Views.ImageSettingsAdvanced.textTop": "En haut", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Marge supérieure", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "DE.Views.ImageSettingsAdvanced.textVertically": "Verticalement", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Poids et flèches", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "Style d'habillage", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "<PERSON><PERSON><PERSON> le texte", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "Devant le texte", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "Aligné sur le texte", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Carré", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Au travers", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Haut et bas", "DE.Views.LeftMenu.tipAbout": "À propos", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Commentaires", "DE.Views.LeftMenu.tipNavigation": "Navigation", "DE.Views.LeftMenu.tipOutline": "Titres", "DE.Views.LeftMenu.tipPageThumbnails": "Miniatures des pages", "DE.Views.LeftMenu.tipPlugins": "Plug-ins", "DE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipSupport": "Commentaires & assistance", "DE.Views.LeftMenu.tipTitles": "Titres", "DE.Views.LeftMenu.txtDeveloper": "MODE DEVELOPPEUR", "DE.Views.LeftMenu.txtEditor": "Éditeur de Documents", "DE.Views.LeftMenu.txtLimit": "Accès limité", "DE.Views.LeftMenu.txtTrial": "MODE DEMO", "DE.Views.LeftMenu.txtTrialDev": "Essai en mode Développeur", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Ajouter la numérotation de lignes", "DE.Views.LineNumbersDialog.textApplyTo": "Appliquer les modifications à", "DE.Views.LineNumbersDialog.textContinuous": "Continue", "DE.Views.LineNumbersDialog.textCountBy": "Compter par", "DE.Views.LineNumbersDialog.textDocument": "À tout le document", "DE.Views.LineNumbersDialog.textForward": "Ce point en avant", "DE.Views.LineNumbersDialog.textFromText": "A partir du texte", "DE.Views.LineNumbersDialog.textNumbering": "Numérotation", "DE.Views.LineNumbersDialog.textRestartEachPage": "Redémarrer à chaque page", "DE.Views.LineNumbersDialog.textRestartEachSection": "Redémarrer à chaque section", "DE.Views.LineNumbersDialog.textSection": "Section active", "DE.Views.LineNumbersDialog.textStartAt": "Commencer par", "DE.Views.LineNumbersDialog.textTitle": "Numérotation des lignes", "DE.Views.LineNumbersDialog.txtAutoText": "Auto", "DE.Views.Links.capBtnAddText": "A<PERSON>ter du texte", "DE.Views.Links.capBtnBookmarks": "Signet", "DE.Views.Links.capBtnCaption": "Légende", "DE.Views.Links.capBtnContentsUpdate": "Actualiser la table", "DE.Views.Links.capBtnCrossRef": "Renvoi", "DE.Views.Links.capBtnInsContents": "Table des matières", "DE.Views.Links.capBtnInsFootnote": "Note de bas de page", "DE.Views.Links.capBtnInsLink": "Lien hypertexte", "DE.Views.Links.capBtnTOF": "Table des figures", "DE.Views.Links.confirmDeleteFootnotes": "Souhaitez-vous supprimer toutes les notes de bas de page ?", "DE.Views.Links.confirmReplaceTOF": "V<PERSON><PERSON>z vous remplacer la table des figures sélectionnée ?", "DE.Views.Links.mniConvertNote": "Convertir toutes les notes", "DE.Views.Links.mniDelFootnote": "Supprimer toutes les notes", "DE.Views.Links.mniInsEndnote": "Insérer une note de fin", "DE.Views.Links.mniInsFootnote": "Insérer une note de bas de page", "DE.Views.Links.mniNoteSettings": "Paramètres des notes", "DE.Views.Links.textContentsRemove": "Supprimer la table des matières", "DE.Views.Links.textContentsSettings": "Paramètres", "DE.Views.Links.textConvertToEndnotes": "Convertir tous les pieds de page aux notes de fin", "DE.Views.Links.textConvertToFootnotes": "Convertir toutes les notes de fin aux pieds de page", "DE.Views.Links.textGotoEndnote": "Passer aux notes de fin", "DE.Views.Links.textGotoFootnote": "Passer aux notes de bas de page", "DE.Views.Links.textSwapNotes": "Changer les notes de pied de page et les notes de fin", "DE.Views.Links.textUpdateAll": "Actualiser le tableau entier", "DE.Views.Links.textUpdatePages": "Actualiser les numéros de page uniquement", "DE.Views.Links.tipAddText": "Inclure le titre dans la table des matières", "DE.Views.Links.tipBookmarks": "<PERSON><PERSON><PERSON> un signet", "DE.Views.Links.tipCaption": "Insérer une légende", "DE.Views.Links.tipContents": "Insérer la table des matières", "DE.Views.Links.tipContentsUpdate": "Actualiser la table des matières", "DE.Views.Links.tipCrossRef": "Insérer un renvoi", "DE.Views.Links.tipInsertHyperlink": "Ajouter un lien hypertexte", "DE.Views.Links.tipNotes": "Insérer ou modifier les notes de bas de page", "DE.Views.Links.tipTableFigures": "Insérer une table des figures", "DE.Views.Links.tipTableFiguresUpdate": "<PERSON><PERSON><PERSON><PERSON> la table des figures", "DE.Views.Links.titleUpdateTOF": "<PERSON><PERSON><PERSON><PERSON> la table des figures", "DE.Views.Links.txtDontShowTof": "Ne pas afficher dans la table des matières", "DE.Views.Links.txtLevel": "Niveau", "DE.Views.ListSettingsDialog.textAuto": "Automatique", "DE.Views.ListSettingsDialog.textCenter": "Au centre", "DE.Views.ListSettingsDialog.textLeft": "À gauche", "DE.Views.ListSettingsDialog.textLevel": "Niveau", "DE.Views.ListSettingsDialog.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.textRight": "A droite", "DE.Views.ListSettingsDialog.txtAlign": "Alignement", "DE.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtFont": "Police et symbole", "DE.Views.ListSettingsDialog.txtLikeText": "En tant que texte", "DE.Views.ListSettingsDialog.txtNewBullet": "Nouvelle puce", "DE.Views.ListSettingsDialog.txtNone": "Rien", "DE.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSymbol": "Symbole", "DE.Views.ListSettingsDialog.txtTitle": "Paramètres de la liste", "DE.Views.ListSettingsDialog.txtType": "Type", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Envoyer", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "Thème", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Joindre comme DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Jo<PERSON>re comme PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Nom de fi<PERSON>er", "DE.Views.MailMergeEmailDlg.textFormat": "Format du courrier", "DE.Views.MailMergeEmailDlg.textFrom": "De", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Message", "DE.Views.MailMergeEmailDlg.textSubject": "Ligne d'objet", "DE.Views.MailMergeEmailDlg.textTitle": "Envoyer par e-mail", "DE.Views.MailMergeEmailDlg.textTo": "à", "DE.Views.MailMergeEmailDlg.textWarning": "Attention !", "DE.Views.MailMergeEmailDlg.textWarningMsg": "S'il vous plaît noter que postale ne peut pas être arrêté une fois que vous cliquez sur le bouton 'Envoyer'.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Fusion", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Fusion a échoué.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Avertissement", "DE.Views.MailMergeSettings.textAddRecipients": "D'abord ajouter quelques destinataires à la liste", "DE.Views.MailMergeSettings.textAll": "Tous les enregistrements", "DE.Views.MailMergeSettings.textCurrent": "Enregistrement actuel", "DE.Views.MailMergeSettings.textDataSource": "La source de données", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "Télécharger", "DE.Views.MailMergeSettings.textEditData": "Modifier la liste de destinataires", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "De", "DE.Views.MailMergeSettings.textGoToMail": "<PERSON><PERSON> au Courrier", "DE.Views.MailMergeSettings.textHighlight": "Surligner les champs combiné", "DE.Views.MailMergeSettings.textInsertField": "Insérer un champ de fusion", "DE.Views.MailMergeSettings.textMaxRecepients": "Max 100 destinataires.", "DE.Views.MailMergeSettings.textMerge": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeFields": "Les champs de fusion", "DE.Views.MailMergeSettings.textMergeTo": "Fusionner pour", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Enregistrer", "DE.Views.MailMergeSettings.textPreview": "Aperçu des résultats", "DE.Views.MailMergeSettings.textReadMore": "Lire la suite", "DE.Views.MailMergeSettings.textSendMsg": "Tous les messages sont prêts et seront envoyés dans un certain temps.<br>La vitesse de diffusion dépendent de vos services de messagerie.<br>Vous pouvez continuer à travailler avec le document ou le fermer.Lorsque l'opération est terminée la notification sera envoyé à votre adresse email d'inscription.", "DE.Views.MailMergeSettings.textTo": "à", "DE.Views.MailMergeSettings.txtFirst": "Au premier enregistrement", "DE.Views.MailMergeSettings.txtFromToError": "La valeur de \"De\" doit être inférieure à la valeur de \"À\"", "DE.Views.MailMergeSettings.txtLast": "Au dernier enregistrement", "DE.Views.MailMergeSettings.txtNext": "A l'enregistrement suivant", "DE.Views.MailMergeSettings.txtPrev": "A l'enregistrement précédent", "DE.Views.MailMergeSettings.txtUntitled": "Sans titre", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Fusion a échoué", "DE.Views.Navigation.strNavigate": "Titres", "DE.Views.Navigation.txtClosePanel": "<PERSON><PERSON><PERSON> les titres", "DE.Views.Navigation.txtCollapse": "<PERSON><PERSON><PERSON><PERSON> tout", "DE.Views.Navigation.txtDemote": "Dégrader", "DE.Views.Navigation.txtEmpty": "Aucune entrée de table des matières trouvée.<br>L'application d'un style de titre sur une sélection de texte permettra l'affichage dans la table des matières. ", "DE.Views.Navigation.txtEmptyItem": "En-tête vide", "DE.Views.Navigation.txtEmptyViewer": "Aucune entrée de table des matières trouvée.", "DE.Views.Navigation.txtExpand": "Dévelop<PERSON> tout", "DE.Views.Navigation.txtExpandToLevel": "Développer jusqu'au niveau", "DE.Views.Navigation.txtFontSize": "Taille de police", "DE.Views.Navigation.txtHeadingAfter": "Nouvel en-tête après", "DE.Views.Navigation.txtHeadingBefore": "Nouvel en-tête avant", "DE.Views.Navigation.txtLarge": "Grand", "DE.Views.Navigation.txtMedium": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtNewHeading": "Nouveau sous-titre", "DE.Views.Navigation.txtPromote": "Promouvoir", "DE.Views.Navigation.txtSelect": "Sélectionner le contenu", "DE.Views.Navigation.txtSettings": "Paramètres des titres", "DE.Views.Navigation.txtSmall": "<PERSON>", "DE.Views.Navigation.txtWrapHeadings": "Renvoyer à la ligne les longs titres", "DE.Views.NoteSettingsDialog.textApply": "Appliquer", "DE.Views.NoteSettingsDialog.textApplyTo": "Appliquer les modifications à", "DE.Views.NoteSettingsDialog.textContinue": "Continue", "DE.Views.NoteSettingsDialog.textCustom": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textDocEnd": "Fin du document", "DE.Views.NoteSettingsDialog.textDocument": "À tout le document", "DE.Views.NoteSettingsDialog.textEachPage": "À chaque page", "DE.Views.NoteSettingsDialog.textEachSection": "À chaque section", "DE.Views.NoteSettingsDialog.textEndnote": "Note de fin", "DE.Views.NoteSettingsDialog.textFootnote": "Note de bas de page", "DE.Views.NoteSettingsDialog.textFormat": "Format", "DE.Views.NoteSettingsDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textLocation": "Emplacement", "DE.Views.NoteSettingsDialog.textNumbering": "Numérotation", "DE.Views.NoteSettingsDialog.textNumFormat": "Format de nombre", "DE.Views.NoteSettingsDialog.textPageBottom": "Bas de page", "DE.Views.NoteSettingsDialog.textSectEnd": "Fin de section", "DE.Views.NoteSettingsDialog.textSection": "Section active", "DE.Views.NoteSettingsDialog.textStart": "Commencer par", "DE.Views.NoteSettingsDialog.textTextBottom": "Sous le texte", "DE.Views.NoteSettingsDialog.textTitle": "Paramètres des notes", "DE.Views.NotesRemoveDialog.textEnd": "Supprimer toutes les notes de fin de page", "DE.Views.NotesRemoveDialog.textFoot": "Supprimer toutes les notes de bas de page", "DE.Views.NotesRemoveDialog.textTitle": "Supprimer les notes", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Avertissement", "DE.Views.PageMarginsDialog.textBottom": "Bas", "DE.Views.PageMarginsDialog.textGutter": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textGutterPosition": "Position de la reliure", "DE.Views.PageMarginsDialog.textInside": "À l’intérieur", "DE.Views.PageMarginsDialog.textLandscape": "Paysage", "DE.Views.PageMarginsDialog.textLeft": "G<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textMirrorMargins": "Pages en vis-à-vis", "DE.Views.PageMarginsDialog.textMultiplePages": "Plusieurs pages", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientation", "DE.Views.PageMarginsDialog.textOutside": "À l’extérieur", "DE.Views.PageMarginsDialog.textPortrait": "Portrait", "DE.Views.PageMarginsDialog.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "Marges", "DE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.txtMarginsH": "Les marges supérieure et inférieure sont trop élevés pour une hauteur de page donnée", "DE.Views.PageMarginsDialog.txtMarginsW": "Les marges gauche et droite sont trop larges pour une largeur de page donnée", "DE.Views.PageSizeDialog.textHeight": "<PERSON><PERSON>", "DE.Views.PageSizeDialog.textPreset": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.textTitle": "<PERSON><PERSON>", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageThumbnails.textClosePanel": "Fe<PERSON><PERSON> les miniatures des pages", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Mettre en surbrillance la partie visible de la page", "DE.Views.PageThumbnails.textPageThumbnails": "Miniatures des pages", "DE.Views.PageThumbnails.textThumbnailsSettings": "Paramètres des miniatures", "DE.Views.PageThumbnails.textThumbnailsSize": "Taille des miniatures", "DE.Views.ParagraphSettings.strIndent": "Retraits", "DE.Views.ParagraphSettings.strIndentsLeftText": "À gauche", "DE.Views.ParagraphSettings.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsSpecial": "Spécial", "DE.Views.ParagraphSettings.strLineHeight": "Interligne", "DE.Views.ParagraphSettings.strParagraphSpacing": "Espacement de paragraphe", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Ne pas ajouter d'intervalle entre paragraphes du même style", "DE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.strSpacingBefore": "Avant", "DE.Views.ParagraphSettings.textAdvanced": "Afficher les paramètres avancés", "DE.Views.ParagraphSettings.textAt": "A", "DE.Views.ParagraphSettings.textAtLeast": "Au moins ", "DE.Views.ParagraphSettings.textAuto": "Multiple ", "DE.Views.ParagraphSettings.textBackColor": "Couleur d'arrière-plan", "DE.Views.ParagraphSettings.textExact": "Exactement", "DE.Views.ParagraphSettings.textFirstLine": "Première ligne", "DE.Views.ParagraphSettings.textHanging": "Suspendu", "DE.Views.ParagraphSettings.textNoneSpecial": "(aucun)", "DE.Views.ParagraphSettings.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.noTabs": "Les onglets spécifiés s'affichent dans ce champ", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "Toutes en majuscules", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Bordures et remplissage", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Saut de page avant", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Barré double", "DE.Views.ParagraphSettingsAdvanced.strIndent": "Retraits", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "À gauche", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interligne", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Niveau hiérarchique", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "A droite", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Avant", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Spécial", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "Lignes solidaires", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Paragraphes solidaires", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Marges intérieures", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Police", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Retraits et espacement", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Sauts de ligne et de page", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Emplacement", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Petites majuscules", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Ne pas ajouter d'intervalle entre paragraphes du même style", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Espacement", "DE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Indice", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Exposant", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Supp<PERSON>er les numéros des lignes", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulation", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Alignement", "DE.Views.ParagraphSettingsAdvanced.textAll": "<PERSON>ut", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "Au moins ", "DE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple ", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Couleur d'arrière-plan", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Texte simple", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON> de bordure", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Cliquez sur le diagramme ou utilisez les boutons pour sélectionner les bordures et appliquez le style choisi", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBottom": "En bas", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Centré", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espacement des caractères", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contextuels", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contextuelles et discrétionnaires", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Contextuelles, historiques et discrétionnaires", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contextuelles et historiques", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Onglet par défaut", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Discrétionnaires", "DE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textExact": "Exactement", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "Première ligne", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendu", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Historiques", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Historiques et discrétionnaires", "DE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Points de suite", "DE.Views.ParagraphSettingsAdvanced.textLeft": "A gauche", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Niveau", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligatures", "DE.Views.ParagraphSettingsAdvanced.textNone": "Aucune", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(aucun)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "Fonctionnalités OpenType", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Position", "DE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Supp<PERSON>er tout", "DE.Views.ParagraphSettingsAdvanced.textRight": "A droite", "DE.Views.ParagraphSettingsAdvanced.textSet": "Spécifier", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Espacement", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Standards uniquement", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Standards et contextuelles", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "Standards, contextuelles et discrétionnaires", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "Standards, contextuelles et historiques", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Standards et discrétionnaires", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Standards, historiques et discrétionnaires", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Standards et historiques", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centre", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "À gauche", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Position de l'onglet", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "A droite", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraphe - Paramètres avancés", "DE.Views.ParagraphSettingsAdvanced.textTop": "En haut", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Bordure extérieure et la totalité des lignes intérieures", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Seulement bordure inférieure", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Seulement lignes intérieures horizontales", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Seulement bordure gauche", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Pas de bordures", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Seulement bordure extérieure", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Seulement bordure droite", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Seulement bordure supérieure", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "Pas de bordures", "DE.Views.PrintWithPreview.textMarginsLast": "Derniers personnalisés", "DE.Views.PrintWithPreview.textMarginsModerate": "Mo<PERSON>nnes", "DE.Views.PrintWithPreview.textMarginsNarrow": "Étroites", "DE.Views.PrintWithPreview.textMarginsNormal": "Normales", "DE.Views.PrintWithPreview.textMarginsUsNormal": "US normale", "DE.Views.PrintWithPreview.textMarginsWide": "Larges", "DE.Views.PrintWithPreview.txtAllPages": "Toutes les pages", "DE.Views.PrintWithPreview.txtBottom": "Bas", "DE.Views.PrintWithPreview.txtCurrentPage": "Page active", "DE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtCustomPages": "Impression personnalisée", "DE.Views.PrintWithPreview.txtLandscape": "Paysage", "DE.Views.PrintWithPreview.txtLeft": "G<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtMargins": "Marges", "DE.Views.PrintWithPreview.txtOf": "de {0}", "DE.Views.PrintWithPreview.txtPage": "Page", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Numéro de page non valide", "DE.Views.PrintWithPreview.txtPageOrientation": "Orientation de page", "DE.Views.PrintWithPreview.txtPages": "Pages", "DE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPortrait": "Portrait", "DE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPrintPdf": "Imprimer au format PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Zone d'impression", "DE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtSelection": "Sélection", "DE.Views.PrintWithPreview.txtTop": "<PERSON><PERSON>", "DE.Views.ProtectDialog.textComments": "Commentaires", "DE.Views.ProtectDialog.textForms": "Remplissage des formulaires", "DE.Views.ProtectDialog.textReview": "Modifications", "DE.Views.ProtectDialog.textView": "Aucune modification (Lecture seule)", "DE.Views.ProtectDialog.txtAllow": "Autoriser uniquement ce type de modification dans le document", "DE.Views.ProtectDialog.txtIncorrectPwd": "Le mot de passe de confirmation n'est pas identique", "DE.Views.ProtectDialog.txtLimit": "Le mot de passe est limité à 15 caractères", "DE.Views.ProtectDialog.txtOptional": "optionnel", "DE.Views.ProtectDialog.txtPassword": "Mot de passe", "DE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON>ger", "DE.Views.ProtectDialog.txtRepeat": "Confirmer le mot de passe", "DE.Views.ProtectDialog.txtTitle": "<PERSON><PERSON>ger", "DE.Views.ProtectDialog.txtWarning": "Attention : si vous oubliez ou perdez votre mot de passe, il sera impossible de le récupérer. Conservez-le en lieu sûr.", "DE.Views.RightMenu.txtChartSettings": "Paramètres du graphique", "DE.Views.RightMenu.txtFormSettings": "Paramètres du formulaire", "DE.Views.RightMenu.txtHeaderFooterSettings": "Paramètres d'en-têtes et de pieds de page", "DE.Views.RightMenu.txtImageSettings": "Paramètres de l'image", "DE.Views.RightMenu.txtMailMergeSettings": "Paramètres de fusion et publipostage", "DE.Views.RightMenu.txtParagraphSettings": "Paramètres du paragraphe", "DE.Views.RightMenu.txtShapeSettings": "Paramètres de la forme", "DE.Views.RightMenu.txtSignatureSettings": "Paramètres de signature", "DE.Views.RightMenu.txtTableSettings": "Paramètres du tableau", "DE.Views.RightMenu.txtTextArtSettings": "Paramètres de texte d'art", "DE.Views.RoleDeleteDlg.textLabel": "Pour supprimer ce rôle, il faut déplacer les champs qui lui sont associés vers un autre rôle.", "DE.Views.RoleDeleteDlg.textSelect": "Sélectionner pour le rôle de fusion de champs", "DE.Views.RoleDeleteDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> le rôle", "DE.Views.RoleEditDlg.errNameExists": "Le rôle portant ce nom existe déjà.", "DE.Views.RoleEditDlg.textEmptyError": "Le nom du rôle ne doit pas être vide.", "DE.Views.RoleEditDlg.textName": "Nom du rôle", "DE.Views.RoleEditDlg.textNoHighlight": "Pas de surbrillan<PERSON> ", "DE.Views.RoleEditDlg.txtTitleEdit": "Modifier le rôle", "DE.Views.RoleEditDlg.txtTitleNew": "Créer un nouveau rôle", "DE.Views.RolesManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "DE.Views.RolesManagerDlg.textAnyone": "<PERSON>ut le monde", "DE.Views.RolesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.RolesManagerDlg.textDeleteLast": "Êtes-vous sûr de vouloir supprimer le rôle {0}?<br>Une fois supprimé, le rôle par défaut sera créé.", "DE.Views.RolesManagerDlg.textDescription": "Ajoutez des rôles et définissez l'ordre dans lequel les remplisseurs reçoivent et signent le document", "DE.Views.RolesManagerDlg.textDown": "<PERSON><PERSON><PERSON><PERSON> le rôle vers le bas", "DE.Views.RolesManagerDlg.textEdit": "Modifier", "DE.Views.RolesManagerDlg.textEmpty": "Aucun rôle n'a encore été créé.<br><PERSON><PERSON><PERSON> au moins un rôle et il apparaîtra dans ce champ.", "DE.Views.RolesManagerDlg.textNew": "Nouveau", "DE.Views.RolesManagerDlg.textUp": "<PERSON><PERSON><PERSON><PERSON> le rôle vers le haut", "DE.Views.RolesManagerDlg.txtTitle": "<PERSON><PERSON><PERSON> rôles", "DE.Views.RolesManagerDlg.warnCantDelete": "Vous ne pouvez pas supprimer ce rôle, car il a des champs associés.", "DE.Views.RolesManagerDlg.warnDelete": "Êtes-vous sûr de vouloir supprimer le rôle {0} ?", "DE.Views.SaveFormDlg.saveButtonText": "Enregistrer", "DE.Views.SaveFormDlg.textAnyone": "<PERSON>ut le monde", "DE.Views.SaveFormDlg.textDescription": "Lors de la sauvegarde en oform, seuls les rôles avec des champs sont ajoutés à la liste de remplissage", "DE.Views.SaveFormDlg.textEmpty": "Il n'y a pas de rôles associés aux champs.", "DE.Views.SaveFormDlg.textFill": "Liste de remplissage", "DE.Views.SaveFormDlg.txtTitle": "Enregistrer comme formulaire", "DE.Views.ShapeSettings.strBackground": "Couleur d'arrière-plan", "DE.Views.ShapeSettings.strChange": "Modifier la forme automatique", "DE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strFill": "Remplissage", "DE.Views.ShapeSettings.strForeground": "Couleur de premier plan", "DE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strShadow": "Ajouter une ombre", "DE.Views.ShapeSettings.strSize": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strStroke": "Ligne", "DE.Views.ShapeSettings.strTransparency": "Opacité", "DE.Views.ShapeSettings.strType": "Type", "DE.Views.ShapeSettings.textAdvanced": "Afficher les paramètres avancés", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "La valeur saisie est incorrecte. <br>En<PERSON>z une valeur de 0 à 1584 points.", "DE.Views.ShapeSettings.textColor": "Couleur de remplissage", "DE.Views.ShapeSettings.textDirection": "Direction", "DE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON> <PERSON> mod<PERSON>", "DE.Views.ShapeSettings.textFlip": "Retournement", "DE.Views.ShapeSettings.textFromFile": "Depuis un fichier", "DE.Views.ShapeSettings.textFromStorage": "À partir de l'espace de stockage", "DE.Views.ShapeSettings.textFromUrl": "D'une URL", "DE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textGradientFill": "Remplissage en dégradé", "DE.Views.ShapeSettings.textHint270": "Faire pivoter à gauche de 90°", "DE.Views.ShapeSettings.textHint90": "Faire pivoter à droite de 90°", "DE.Views.ShapeSettings.textHintFlipH": "Retourner horizontalement", "DE.Views.ShapeSettings.textHintFlipV": "Retourner verticalement", "DE.Views.ShapeSettings.textImageTexture": "Image ou texture", "DE.Views.ShapeSettings.textLinear": "Linéaire", "DE.Views.ShapeSettings.textNoFill": "Pas de remplissage", "DE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textPosition": "Position", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "Récemment utilisés", "DE.Views.ShapeSettings.textRotate90": "Faire pivoter de 90°", "DE.Views.ShapeSettings.textRotation": "Rotation", "DE.Views.ShapeSettings.textSelectImage": "Sélectionner l'image", "DE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textStretch": "Étirement", "DE.Views.ShapeSettings.textStyle": "Style", "DE.Views.ShapeSettings.textTexture": "D'une texture", "DE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textWrap": "Style d'habillage", "DE.Views.ShapeSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "DE.Views.ShapeSettings.txtBehind": "<PERSON><PERSON><PERSON> le texte", "DE.Views.ShapeSettings.txtBrownPaper": "Papier brun", "DE.Views.ShapeSettings.txtCanvas": "Toile", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "Tissu foncé", "DE.Views.ShapeSettings.txtGrain": "Grain", "DE.Views.ShapeSettings.txtGranite": "Granit", "DE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON>r gris", "DE.Views.ShapeSettings.txtInFront": "Devant le texte", "DE.Views.ShapeSettings.txtInline": "Aligné sur le texte", "DE.Views.ShapeSettings.txtKnit": "Tricot", "DE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> de ligne", "DE.Views.ShapeSettings.txtPapyrus": "Papyrus", "DE.Views.ShapeSettings.txtSquare": "Carré", "DE.Views.ShapeSettings.txtThrough": "Au travers", "DE.Views.ShapeSettings.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtTopAndBottom": "Haut et bas", "DE.Views.ShapeSettings.txtWood": "<PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Avertissement", "DE.Views.SignatureSettings.strDelete": "Supprimer la signature", "DE.Views.SignatureSettings.strDetails": "<PERSON><PERSON><PERSON> de la signature", "DE.Views.SignatureSettings.strInvalid": "Signatures non valides", "DE.Views.SignatureSettings.strRequested": "Signatures requises", "DE.Views.SignatureSettings.strSetup": "Mise en place de la signature", "DE.Views.SignatureSettings.strSign": "Signer", "DE.Views.SignatureSettings.strSignature": "Signature", "DE.Views.SignatureSettings.strSigner": "Signataire", "DE.Views.SignatureSettings.strValid": "Signatures valables", "DE.Views.SignatureSettings.txtContinueEditing": "Modifier quand même", "DE.Views.SignatureSettings.txtEditWarning": "L'édition supprimera les signatures du document.<br>Êtes-vous sûr de vouloir continuer?", "DE.Views.SignatureSettings.txtRemoveWarning": "V<PERSON><PERSON>z vous supprimer cette signature ? <br> <PERSON><PERSON> ne peut pas être r<PERSON>.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Ce document doit être signé.", "DE.Views.SignatureSettings.txtSigned": "Des signatures valables ont été ajoutées au document. Le document est protégé contre l'édition.", "DE.Views.SignatureSettings.txtSignedInvalid": "Certaines signatures numériques dans le document sont invalides ou n'ont pas pu être vérifiées. Le document est protégé contre l'édition.", "DE.Views.Statusbar.goToPageText": "<PERSON>er à la page", "DE.Views.Statusbar.pageIndexText": "Page {0} de {1}", "DE.Views.Statusbar.tipFitPage": "Ajuster à la page", "DE.Views.Statusbar.tipFitWidth": "Ajuster à la largeur", "DE.Views.Statusbar.tipHandTool": "Outil <PERSON>", "DE.Views.Statusbar.tipSelectTool": "<PERSON><PERSON> de <PERSON>", "DE.Views.Statusbar.tipSetLang": "Définir la langue du texte", "DE.Views.Statusbar.tipZoomFactor": "Grossissement", "DE.Views.Statusbar.tipZoomIn": "Zoom avant", "DE.Views.Statusbar.tipZoomOut": "Zoom arri<PERSON>", "DE.Views.Statusbar.txtPageNumInvalid": "Numéro de page non valide", "DE.Views.Statusbar.txtPages": "Pages", "DE.Views.Statusbar.txtParagraphs": "Paragraphes", "DE.Views.Statusbar.txtSpaces": "Symboles avec espaces", "DE.Views.Statusbar.txtSymbols": "Symboles", "DE.Views.Statusbar.txtWordCount": "Statistiques", "DE.Views.Statusbar.txtWords": "<PERSON><PERSON>", "DE.Views.StyleTitleDialog.textHeader": "Créer un nouveau style", "DE.Views.StyleTitleDialog.textNextStyle": "Style du nouveau paragraphe", "DE.Views.StyleTitleDialog.textTitle": "Titre", "DE.Views.StyleTitleDialog.txtEmpty": "Ce champ est obligatoire", "DE.Views.StyleTitleDialog.txtNotEmpty": "Le champ ne doit pas être vide", "DE.Views.StyleTitleDialog.txtSameAs": "Identique au nouveau style créé", "DE.Views.TableFormulaDialog.textBookmark": "<PERSON><PERSON><PERSON><PERSON> le signet", "DE.Views.TableFormulaDialog.textFormat": "Format de nombre", "DE.Views.TableFormulaDialog.textFormula": "Formule", "DE.Views.TableFormulaDialog.textInsertFunction": "Coller une fonction", "DE.Views.TableFormulaDialog.textTitle": "Paramètres de formule", "DE.Views.TableOfContentsSettings.strAlign": "<PERSON><PERSON><PERSON> les numéros de page à droite", "DE.Views.TableOfContentsSettings.strFullCaption": "Inclure l'étiquette et le numéro", "DE.Views.TableOfContentsSettings.strLinks": "Mettre la table des matières sous forme de liens", "DE.Views.TableOfContentsSettings.strLinksOF": "Mettre la table des figures sous forme de liens", "DE.Views.TableOfContentsSettings.strShowPages": "Afficher les numéros de page", "DE.Views.TableOfContentsSettings.textBuildTable": "Construire la table des matières à partir de", "DE.Views.TableOfContentsSettings.textBuildTableOF": "Construire un table des figures à partir de", "DE.Views.TableOfContentsSettings.textEquation": "Équation", "DE.Views.TableOfContentsSettings.textFigure": "Figure", "DE.Views.TableOfContentsSettings.textLeader": "Points de suite", "DE.Views.TableOfContentsSettings.textLevel": "Niveau", "DE.Views.TableOfContentsSettings.textLevels": "Niveaux", "DE.Views.TableOfContentsSettings.textNone": "Aucune", "DE.Views.TableOfContentsSettings.textRadioCaption": "Légende", "DE.Views.TableOfContentsSettings.textRadioLevels": "Niveaux hiérarchiques", "DE.Views.TableOfContentsSettings.textRadioStyle": "Style", "DE.Views.TableOfContentsSettings.textRadioStyles": "Styles sélectionnés", "DE.Views.TableOfContentsSettings.textStyle": "Style", "DE.Views.TableOfContentsSettings.textStyles": "Styles", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "Table des matières", "DE.Views.TableOfContentsSettings.textTitleTOF": "Table des illustrations", "DE.Views.TableOfContentsSettings.txtCentered": "Centré", "DE.Views.TableOfContentsSettings.txtClassic": "Classique", "DE.Views.TableOfContentsSettings.txtCurrent": "Actuel", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distinctif", "DE.Views.TableOfContentsSettings.txtFormal": "Formel", "DE.Views.TableOfContentsSettings.txtModern": "Moderne", "DE.Views.TableOfContentsSettings.txtOnline": "En ligne", "DE.Views.TableOfContentsSettings.txtSimple": "Simple", "DE.Views.TableOfContentsSettings.txtStandard": "Standard", "DE.Views.TableSettings.deleteColumnText": "Supprimer la colonne", "DE.Views.TableSettings.deleteRowText": "Supprimer la ligne", "DE.Views.TableSettings.deleteTableText": "Su<PERSON><PERSON><PERSON> le tableau", "DE.Views.TableSettings.insertColumnLeftText": "Insérer une colonne à gauche", "DE.Views.TableSettings.insertColumnRightText": "Insérer une colonne à droite", "DE.Views.TableSettings.insertRowAboveText": "Insérer une ligne au-dessus", "DE.Views.TableSettings.insertRowBelowText": "Insérer une ligne en dessous", "DE.Views.TableSettings.mergeCellsText": "<PERSON>ner les cellules", "DE.Views.TableSettings.selectCellText": "Sélectionner la cellule", "DE.Views.TableSettings.selectColumnText": "Sélectionner la colonne", "DE.Views.TableSettings.selectRowText": "Sélectionner la ligne", "DE.Views.TableSettings.selectTableText": "Sélect<PERSON>ner le tableau", "DE.Views.TableSettings.splitCellsText": "Fractionner la cellule...", "DE.Views.TableSettings.splitCellTitleText": "Fractionner la cellule", "DE.Views.TableSettings.strRepeatRow": "<PERSON><PERSON><PERSON><PERSON><PERSON> en haut de chaque page en tant que ligne d'en-tête", "DE.Views.TableSettings.textAddFormula": "Ajouter une formule", "DE.Views.TableSettings.textAdvanced": "Afficher les paramètres avancés", "DE.Views.TableSettings.textBackColor": "Couleur d'arrière-plan", "DE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textBorders": "Style des bordures", "DE.Views.TableSettings.textCellSize": "Taille des lignes et des colonnes.", "DE.Views.TableSettings.textColumns": "Colonnes", "DE.Views.TableSettings.textConvert": "Convertir un tableau en texte", "DE.Views.TableSettings.textDistributeCols": "Distribuer les colonnes", "DE.Views.TableSettings.textDistributeRows": "Distribuer les lignes", "DE.Views.TableSettings.textEdit": "Lignes et colonnes", "DE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON> mod<PERSON>", "DE.Views.TableSettings.textFirst": "Premier", "DE.Views.TableSettings.textHeader": "<PERSON>-tête", "DE.Views.TableSettings.textHeight": "<PERSON><PERSON>", "DE.Views.TableSettings.textLast": "<PERSON><PERSON>", "DE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "Sélectionnez les bordures à modifier en appliquant le style choisi ci-dessus", "DE.Views.TableSettings.textTemplate": "Sélectionner à partir d'un modèle", "DE.Views.TableSettings.textTotal": "Total", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "Bordure extérieure et la totalité des lignes intérieures", "DE.Views.TableSettings.tipBottom": "Seulement bordure extérieure inférieure", "DE.Views.TableSettings.tipInner": "Seulement lignes intérieures", "DE.Views.TableSettings.tipInnerHor": "Seulement lignes intérieures horizontales", "DE.Views.TableSettings.tipInnerVert": "Seulement lignes verticales intérieures", "DE.Views.TableSettings.tipLeft": "Seulement bordure extérieure gauche", "DE.Views.TableSettings.tipNone": "Pas de bordures", "DE.Views.TableSettings.tipOuter": "Seulement bordure extérieure", "DE.Views.TableSettings.tipRight": "Seulement bordure extérieure droite", "DE.Views.TableSettings.tipTop": "Seulement bordure extérieure supérieure", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Tableaux à bordures et à lignes", "DE.Views.TableSettings.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtGroupTable_Grid": "Tableaux Grille", "DE.Views.TableSettings.txtGroupTable_List": "Tableaux Liste", "DE.Views.TableSettings.txtGroupTable_Plain": "Tableaux simples", "DE.Views.TableSettings.txtNoBorders": "Pas de bordures", "DE.Views.TableSettings.txtTable_Accent": "Accentuation", "DE.Views.TableSettings.txtTable_Bordered": "À bordure", "DE.Views.TableSettings.txtTable_BorderedAndLined": "À bordures et à lignes", "DE.Views.TableSettings.txtTable_Colorful": "En couleurs", "DE.Views.TableSettings.txtTable_Dark": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_GridTable": "Table Grille", "DE.Views.TableSettings.txtTable_Light": "<PERSON>", "DE.Views.TableSettings.txtTable_Lined": "À lignes", "DE.Views.TableSettings.txtTable_ListTable": "Tableau de listes", "DE.Views.TableSettings.txtTable_PlainTable": "Tableau simple", "DE.Views.TableSettings.txtTable_TableGrid": "Grille du tableau", "DE.Views.TableSettingsAdvanced.textAlign": "Alignement", "DE.Views.TableSettingsAdvanced.textAlignment": "Alignement", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Espacement entre les cellules", "DE.Views.TableSettingsAdvanced.textAlt": "Texte de remplacement", "DE.Views.TableSettingsAdvanced.textAltDescription": "Description", "DE.Views.TableSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme automatique, du graphique ou du tableau.", "DE.Views.TableSettingsAdvanced.textAltTitle": "Titre", "DE.Views.TableSettingsAdvanced.textAnchorText": "Texte", "DE.Views.TableSettingsAdvanced.textAutofit": "Redimensionner automatiquement pour ajuster au contenu", "DE.Views.TableSettingsAdvanced.textBackColor": "Arrière-plan de cellule ", "DE.Views.TableSettingsAdvanced.textBelow": "en dessous", "DE.Views.TableSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON> de bordure", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Cliquez sur le diagramme ou utilisez les boutons pour sélectionner bordures et appliquez le style choisi", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Bordures et arrière-plan", "DE.Views.TableSettingsAdvanced.textBorderWidth": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBottom": "En bas", "DE.Views.TableSettingsAdvanced.textCellOptions": "Options de la cellule", "DE.Views.TableSettingsAdvanced.textCellProps": "Сellule", "DE.Views.TableSettingsAdvanced.textCellSize": "Taille de la cellule", "DE.Views.TableSettingsAdvanced.textCenter": "Centre", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Centre", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Utiliser marges par défaut", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Marges des cellules par défaut", "DE.Views.TableSettingsAdvanced.textDistance": "Distance du texte", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.TableSettingsAdvanced.textIndLeft": "Retrait à gauche", "DE.Views.TableSettingsAdvanced.textLeft": "À gauche", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "À gauche", "DE.Views.TableSettingsAdvanced.textMargin": "Marge", "DE.Views.TableSettingsAdvanced.textMargins": "Marges de la cellule", "DE.Views.TableSettingsAdvanced.textMeasure": "Mesure en", "DE.Views.TableSettingsAdvanced.textMove": "Déplacer avec le texte", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Seulement pour des cellules sélectionnées", "DE.Views.TableSettingsAdvanced.textOptions": "Options", "DE.Views.TableSettingsAdvanced.textOverlap": "Autoriser le chevauchement", "DE.Views.TableSettingsAdvanced.textPage": "Page", "DE.Views.TableSettingsAdvanced.textPosition": "Position", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON>ur p<PERSON>", "DE.Views.TableSettingsAdvanced.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRelative": "par rapport à", "DE.Views.TableSettingsAdvanced.textRight": "A droite", "DE.Views.TableSettingsAdvanced.textRightOf": "à droite de", "DE.Views.TableSettingsAdvanced.textRightTooltip": "A droite", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "Arrière-plan de tableau", "DE.Views.TableSettingsAdvanced.textTablePosition": "Position du tableau", "DE.Views.TableSettingsAdvanced.textTableSize": "<PERSON><PERSON>au", "DE.Views.TableSettingsAdvanced.textTitle": "Tableau - Paramètres avancés", "DE.Views.TableSettingsAdvanced.textTop": "En haut", "DE.Views.TableSettingsAdvanced.textVertical": "Vertical", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Largeur et espaces", "DE.Views.TableSettingsAdvanced.textWrap": "Habillage du texte", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "Tableau aligné", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Tableau flottant", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Style d'habillage", "DE.Views.TableSettingsAdvanced.textWrapText": "Envelop<PERSON> le texte ", "DE.Views.TableSettingsAdvanced.tipAll": "Bordure extérieure et la totalité des lignes intérieures", "DE.Views.TableSettingsAdvanced.tipCellAll": "Seulement bordures pour les cellules intérieures", "DE.Views.TableSettingsAdvanced.tipCellInner": "Seulement lignes verticales et horizontales pour les cellules intérieures", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Seulement bordures extérieures pour les cellules intérieures", "DE.Views.TableSettingsAdvanced.tipInner": "Seulement lignes intérieures", "DE.Views.TableSettingsAdvanced.tipNone": "Pas de bordures", "DE.Views.TableSettingsAdvanced.tipOuter": "Seulement bordure extérieure", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Bordure intérieure et bordures pour les cellules intérieures", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Bordure extérieure et lignes verticales et horizontales pour les cellules intérieures", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Bordures extérieures de la table et bordures extérieures pour les cellules intérieures", "DE.Views.TableSettingsAdvanced.txtCm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtInch": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtNoBorders": "Pas de bordures", "DE.Views.TableSettingsAdvanced.txtPercent": "Pour-cent", "DE.Views.TableSettingsAdvanced.txtPt": "Point", "DE.Views.TableToTextDialog.textEmpty": "Saisissez un caractère pour un séparateur personnalisé", "DE.Views.TableToTextDialog.textNested": "Convertir les tableaux imbriqués", "DE.Views.TableToTextDialog.textOther": "<PERSON><PERSON>", "DE.Views.TableToTextDialog.textPara": "<PERSON><PERSON> de <PERSON>e", "DE.Views.TableToTextDialog.textSemicolon": "Points-virgules", "DE.Views.TableToTextDialog.textSeparator": "<PERSON><PERSON><PERSON><PERSON> le texte par des", "DE.Views.TableToTextDialog.textTab": "Tabulation", "DE.Views.TableToTextDialog.textTitle": "Convertir un tableau en texte", "DE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strFill": "Remplissage", "DE.Views.TextArtSettings.strSize": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strStroke": "Ligne", "DE.Views.TextArtSettings.strTransparency": "Opacité", "DE.Views.TextArtSettings.strType": "Type", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "La valeur saisie est incorrecte. <br>En<PERSON>z une valeur de 0 à 1584 points.", "DE.Views.TextArtSettings.textColor": "Couleur de remplissage", "DE.Views.TextArtSettings.textDirection": "Direction", "DE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textGradientFill": "Remplissage en dégradé", "DE.Views.TextArtSettings.textLinear": "Linéaire", "DE.Views.TextArtSettings.textNoFill": "Pas de remplissage", "DE.Views.TextArtSettings.textPosition": "Position", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textStyle": "Style", "DE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textTransform": "Transformer", "DE.Views.TextArtSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "DE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> de ligne", "DE.Views.TextToTableDialog.textAutofit": "Comportement de l’ajustement automatique", "DE.Views.TextToTableDialog.textColumns": "Colonnes", "DE.Views.TextToTableDialog.textContents": "Ajuster au contenu", "DE.Views.TextToTableDialog.textEmpty": "Saisissez un caractère pour un séparateur personnalisé", "DE.Views.TextToTableDialog.textFixed": "Largeur de colonne fixe", "DE.Views.TextToTableDialog.textOther": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textPara": "Paragraphes", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Points-virgules", "DE.Views.TextToTableDialog.textSeparator": "<PERSON><PERSON><PERSON><PERSON> le texte au niveau des", "DE.Views.TextToTableDialog.textTab": "Tabulation", "DE.Views.TextToTableDialog.textTableSize": "<PERSON><PERSON>au", "DE.Views.TextToTableDialog.textTitle": "Convertir un texte en tableau", "DE.Views.TextToTableDialog.textWindow": "Ajuster à la fenêtre", "DE.Views.TextToTableDialog.txtAutoText": "Auto", "DE.Views.Toolbar.capBtnAddComment": "Ajouter un commentaire", "DE.Views.Toolbar.capBtnBlankPage": "Page vide", "DE.Views.Toolbar.capBtnColumns": "Colonnes", "DE.Views.Toolbar.capBtnComment": "Commentaire", "DE.Views.Toolbar.capBtnDateTime": "Date et heure", "DE.Views.Toolbar.capBtnInsChart": "Graphique", "DE.Views.Toolbar.capBtnInsControls": "Contrôles de contenu", "DE.Views.Toolbar.capBtnInsDropcap": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsEquation": "Équation", "DE.Views.Toolbar.capBtnInsHeader": "En-tête/Pied de page", "DE.Views.Toolbar.capBtnInsImage": "Image", "DE.Views.Toolbar.capBtnInsPagebreak": "Sauts", "DE.Views.Toolbar.capBtnInsShape": "Forme", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Symbole", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "Zone de texte", "DE.Views.Toolbar.capBtnLineNumbers": "Numéros des lignes", "DE.Views.Toolbar.capBtnMargins": "Marges", "DE.Views.Toolbar.capBtnPageOrient": "Orientation", "DE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnWatermark": "Filigrane", "DE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capImgBackward": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capImgForward": "Déplacer vers l'avant", "DE.Views.Toolbar.capImgGroup": "Grouper", "DE.Views.Toolbar.capImgWrapping": "Retour à la ligne", "DE.Views.Toolbar.mniCapitalizeWords": "Mettre en majuscule chaque mot", "DE.Views.Toolbar.mniCustomTable": "Insérer un tableau personnalisé", "DE.Views.Toolbar.mniDrawTable": "<PERSON><PERSON>er un tableau", "DE.Views.Toolbar.mniEditControls": "Paramètres de contrôle", "DE.Views.Toolbar.mniEditDropCap": "Paramètres de la lettrine", "DE.Views.Toolbar.mniEditFooter": "Modifier le pied de page", "DE.Views.Toolbar.mniEditHeader": "Modifier l'en-tête", "DE.Views.Toolbar.mniEraseTable": "Supprimer un tableau", "DE.Views.Toolbar.mniFromFile": "Depuis un fichier", "DE.Views.Toolbar.mniFromStorage": "À partir de l'espace de stockage", "DE.Views.Toolbar.mniFromUrl": "À partir de l'URL", "DE.Views.Toolbar.mniHiddenBorders": "Bordures de tableau cachées", "DE.Views.Toolbar.mniHiddenChars": "Caractères non imprimables", "DE.Views.Toolbar.mniHighlightControls": "Paramètres de surbrillance", "DE.Views.Toolbar.mniImageFromFile": "Image à partir d'un fichier", "DE.Views.Toolbar.mniImageFromStorage": "Image de stockage", "DE.Views.Toolbar.mniImageFromUrl": "Image à partir d'une URL", "DE.Views.Toolbar.mniInsertSSE": "Insérer la feuille de calcul", "DE.Views.Toolbar.mniLowerCase": "minuscule", "DE.Views.Toolbar.mniRemoveFooter": "Supprimer le pied de page", "DE.Views.Toolbar.mniRemoveHeader": "Supp<PERSON>er l'en-tête", "DE.Views.Toolbar.mniSentenceCase": "Majuscule en début de phrase.", "DE.Views.Toolbar.mniTextToTable": "Convertir un texte en tableau", "DE.Views.Toolbar.mniToggleCase": "Inverser la casse", "DE.Views.Toolbar.mniUpperCase": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.strMenuNoFill": "Aucun remplissage", "DE.Views.Toolbar.textAutoColor": "Automatique", "DE.Views.Toolbar.textBold": "Gras", "DE.Views.Toolbar.textBottom": "En bas: ", "DE.Views.Toolbar.textChangeLevel": "Changer le niveau de liste", "DE.Views.Toolbar.textCheckboxControl": "Case à cocher", "DE.Views.Toolbar.textColumnsCustom": "Colonnes personnalisées", "DE.Views.Toolbar.textColumnsLeft": "À gauche", "DE.Views.Toolbar.textColumnsOne": "Un", "DE.Views.Toolbar.textColumnsRight": "A droite", "DE.Views.Toolbar.textColumnsThree": "Trois", "DE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "DE.Views.Toolbar.textComboboxControl": "Zone de liste déroulante", "DE.Views.Toolbar.textContinuous": "Continue", "DE.Views.Toolbar.textContPage": "Page continue", "DE.Views.Toolbar.textCustomLineNumbers": "Options de numérotation de ligne", "DE.Views.Toolbar.textDateControl": "Date", "DE.Views.Toolbar.textDropdownControl": "Liste déroulante", "DE.Views.Toolbar.textEditWatermark": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textEvenPage": "Page paire", "DE.Views.Toolbar.textInMargin": "<PERSON><PERSON> la Marge", "DE.Views.Toolbar.textInsColumnBreak": "Insérer un saut de colonne", "DE.Views.Toolbar.textInsertPageCount": "Insérer le nombre de pages", "DE.Views.Toolbar.textInsertPageNumber": "Insérer le numéro de page", "DE.Views.Toolbar.textInsPageBreak": "Insérer un saut de page", "DE.Views.Toolbar.textInsSectionBreak": "Insérer saut de section", "DE.Views.Toolbar.textInText": "<PERSON><PERSON> le Texte", "DE.Views.Toolbar.textItalic": "Italique", "DE.Views.Toolbar.textLandscape": "Paysage", "DE.Views.Toolbar.textLeft": "À gauche:", "DE.Views.Toolbar.textListSettings": "Paramètres de la liste", "DE.Views.Toolbar.textMarginsLast": "Dernières personnalisées", "DE.Views.Toolbar.textMarginsModerate": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textMarginsNarrow": "Étroit", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsUsNormal": "US normale", "DE.Views.Toolbar.textMarginsWide": "Large", "DE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON> person<PERSON>", "DE.Views.Toolbar.textNextPage": "<PERSON> suivante", "DE.Views.Toolbar.textNoHighlight": "Pas de surbrillan<PERSON> ", "DE.Views.Toolbar.textNone": "Aucune", "DE.Views.Toolbar.textOddPage": "Page impaire", "DE.Views.Toolbar.textPageMarginsCustom": "Marges personnalisées", "DE.Views.Toolbar.textPageSizeCustom": "<PERSON><PERSON> de page personnalis<PERSON>", "DE.Views.Toolbar.textPictureControl": "Image", "DE.Views.Toolbar.textPlainControl": "Insérer un contrôle de contenu en texte brut", "DE.Views.Toolbar.textPortrait": "Portrait", "DE.Views.Toolbar.textRemoveControl": "Supprimer le contrôle du contenu", "DE.Views.Toolbar.textRemWatermark": "Supp<PERSON>er le filigrane", "DE.Views.Toolbar.textRestartEachPage": "Redémarrer à chaque page", "DE.Views.Toolbar.textRestartEachSection": "Redémarrer à chaque section", "DE.Views.Toolbar.textRichControl": "Insérer un contrôle de contenu en texte enrichi", "DE.Views.Toolbar.textRight": "A droite: ", "DE.Views.Toolbar.textStrikeout": "<PERSON><PERSON>", "DE.Views.Toolbar.textStyleMenuDelete": "Supprimer le style", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Supprimer tous les styles personnalisés", "DE.Views.Toolbar.textStyleMenuNew": "Nouveau style à partir du fragment sélectionné", "DE.Views.Toolbar.textStyleMenuRestore": "Restaurer les paramètres par défaut", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Restaurer tous les styles par défaut", "DE.Views.Toolbar.textStyleMenuUpdate": "Mettre à jour selon la sélection", "DE.Views.Toolbar.textSubscript": "Indice", "DE.Views.Toolbar.textSuperscript": "Exposant", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Supprimer pour le paragraphe actif", "DE.Views.Toolbar.textTabCollaboration": "Collaboration", "DE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabHome": "Accueil", "DE.Views.Toolbar.textTabInsert": "Insertion", "DE.Views.Toolbar.textTabLayout": "Mise en page", "DE.Views.Toolbar.textTabLinks": "Références", "DE.Views.Toolbar.textTabProtect": "Protection", "DE.Views.Toolbar.textTabReview": "Révision", "DE.Views.Toolbar.textTabView": "Affichage", "DE.Views.Toolbar.textTitleError": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textToCurrent": "À la position actuelle", "DE.Views.Toolbar.textTop": "En haut: ", "DE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignCenter": "Aligner au centre", "DE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON> à gauche", "DE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "DE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipBlankPage": "Insérer page vide", "DE.Views.Toolbar.tipChangeCase": "Modifier la casse", "DE.Views.Toolbar.tipChangeChart": "Modifier le type de graphique", "DE.Views.Toolbar.tipClearStyle": "Effacer le style", "DE.Views.Toolbar.tipColorSchemas": "Modifier le jeu de couleurs", "DE.Views.Toolbar.tipColumns": "Insérer des colonnes ", "DE.Views.Toolbar.tipControls": "Insérer des contrôles de contenu", "DE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipCopyStyle": "Co<PERSON><PERSON> le style", "DE.Views.Toolbar.tipCut": "Couper", "DE.Views.Toolbar.tipDateTime": "Inserer la date et l'heure actuelle", "DE.Views.Toolbar.tipDecFont": "Ré<PERSON><PERSON> la taille de la police", "DE.Views.Toolbar.tipDecPrLeft": "Réduire le retrait", "DE.Views.Toolbar.tipDropCap": "Ins<PERSON>rer une lettrine", "DE.Views.Toolbar.tipEditHeader": "Modifier l'en-tête ou le pied de page", "DE.Views.Toolbar.tipFontColor": "Couleur de police", "DE.Views.Toolbar.tipFontName": "Police", "DE.Views.Toolbar.tipFontSize": "Taille de la police", "DE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON><PERSON> de surlignage", "DE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> les objets", "DE.Views.Toolbar.tipImgGroup": "Grouper les objets", "DE.Views.Toolbar.tipImgWrapping": "Renvoyer à la ligne automatiquement", "DE.Views.Toolbar.tipIncFont": "Augmenter la taille de la police", "DE.Views.Toolbar.tipIncPrLeft": "Augmenter le retrait", "DE.Views.Toolbar.tipInsertChart": "Insérer un graphique", "DE.Views.Toolbar.tipInsertEquation": "Insérer une équation", "DE.Views.Toolbar.tipInsertHorizontalText": "Insérer une zone de texte horizontale", "DE.Views.Toolbar.tipInsertImage": "Insérer une image", "DE.Views.Toolbar.tipInsertNum": "Insérer le numéro de page", "DE.Views.Toolbar.tipInsertShape": "Insérer une forme automatique", "DE.Views.Toolbar.tipInsertSmartArt": "Insérer un graphique SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Insérer un symbole", "DE.Views.Toolbar.tipInsertTable": "Insérer un tableau", "DE.Views.Toolbar.tipInsertText": "Insérez zone de texte", "DE.Views.Toolbar.tipInsertTextArt": "Insérer Text Art", "DE.Views.Toolbar.tipInsertVerticalText": "Insérer une zone de texte verticale", "DE.Views.Toolbar.tipLineNumbers": "Afficher les numéros des lignes", "DE.Views.Toolbar.tipLineSpace": "Interligne du paragraphe", "DE.Views.Toolbar.tipMailRecepients": "Fusion et publipostage", "DE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipMarkersArrow": "<PERSON><PERSON><PERSON> fl<PERSON>", "DE.Views.Toolbar.tipMarkersCheckmark": "<PERSON><PERSON>s coches", "DE.Views.Toolbar.tipMarkersDash": "<PERSON>ire<PERSON>", "DE.Views.Toolbar.tipMarkersFRhombus": "<PERSON><PERSON><PERSON> remplis", "DE.Views.Toolbar.tipMarkersFRound": "<PERSON><PERSON>s arrondies remplies", "DE.Views.Toolbar.tipMarkersFSquare": "<PERSON><PERSON>s car<PERSON> remplies", "DE.Views.Toolbar.tipMarkersHRound": "Puces rondes vides", "DE.Views.Toolbar.tipMarkersStar": "Puces en étoile", "DE.Views.Toolbar.tipMultiLevelArticl": "Articles numérotés à plusieurs niveaux", "DE.Views.Toolbar.tipMultiLevelChapter": "Chapitres numérotés à plusieurs niveaux", "DE.Views.Toolbar.tipMultiLevelHeadings": "Titres numérotés à plusieurs niveaux", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Diverses en-têtes numérotées à plusieurs niveaux", "DE.Views.Toolbar.tipMultiLevelNumbered": "Puces numérotées à plusieurs niveaux", "DE.Views.Toolbar.tipMultilevels": "Liste multiniveau", "DE.Views.Toolbar.tipMultiLevelSymbols": "Puces de symboles à plusieurs niveaux", "DE.Views.Toolbar.tipMultiLevelVarious": "Puces numérotées à plusieurs niveaux", "DE.Views.Toolbar.tipNumbers": "Numérotation", "DE.Views.Toolbar.tipPageBreak": "Insérer un saut de page ou de section", "DE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON> de la page", "DE.Views.Toolbar.tipPageOrient": "Orientation de page", "DE.Views.Toolbar.tipPageSize": "<PERSON><PERSON>", "DE.Views.Toolbar.tipParagraphStyle": "Style de paragraphe", "DE.Views.Toolbar.tipPaste": "<PERSON><PERSON>", "DE.Views.Toolbar.tipPrColor": "Ombrage", "DE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPrintQuick": "Impression rapide", "DE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSave": "Enregistrer", "DE.Views.Toolbar.tipSaveCoauth": "Enregistrez vos modifications pour que les autres utilisateurs puissent les voir.", "DE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "DE.Views.Toolbar.tipSendBackward": "Envoyer vers l'arrière.", "DE.Views.Toolbar.tipSendForward": "Déplacer vers l'avant", "DE.Views.Toolbar.tipShowHiddenChars": "Caractères non imprimables", "DE.Views.Toolbar.tipSynchronize": "Le document a été modifié par un autre utilisateur. Cliquez pour enregistrer vos modifications et recharger des mises à jour.", "DE.Views.Toolbar.tipUndo": "Annuler", "DE.Views.Toolbar.tipWatermark": "Modifier le filigrane", "DE.Views.Toolbar.txtDistribHor": "Distribuer horizontalement", "DE.Views.Toolbar.txtDistribVert": "Distribuer verticalement", "DE.Views.Toolbar.txtMarginAlign": "<PERSON><PERSON><PERSON> sur la marge", "DE.Views.Toolbar.txtObjectsAlign": "Aligner les objets sélectionnés", "DE.Views.Toolbar.txtPageAlign": "<PERSON><PERSON><PERSON> sur la page", "DE.Views.Toolbar.txtScheme1": "Office", "DE.Views.Toolbar.txtScheme10": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme11": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "Opulent", "DE.Views.Toolbar.txtScheme14": "Oriel", "DE.Views.Toolbar.txtScheme15": "Origine", "DE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme17": "Solstice", "DE.Views.Toolbar.txtScheme18": "Technique", "DE.Views.Toolbar.txtScheme19": "Promenade", "DE.Views.Toolbar.txtScheme2": "Niveaux de gris", "DE.Views.Toolbar.txtScheme20": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme21": "Verve", "DE.Views.Toolbar.txtScheme22": "New Office", "DE.Views.Toolbar.txtScheme3": "Apex", "DE.Views.Toolbar.txtScheme4": "Proportions", "DE.Views.Toolbar.txtScheme5": "Civil", "DE.Views.Toolbar.txtScheme6": "Rotonde", "DE.Views.Toolbar.txtScheme7": "Capitaux", "DE.Views.Toolbar.txtScheme8": "Flux", "DE.Views.Toolbar.txtScheme9": "Fonderie", "DE.Views.ViewTab.textAlwaysShowToolbar": "Toujours afficher la barre d'outils", "DE.Views.ViewTab.textDarkDocument": "Document sombre", "DE.Views.ViewTab.textFitToPage": "Ajuster à la page", "DE.Views.ViewTab.textFitToWidth": "Ajuster à la largeur", "DE.Views.ViewTab.textInterfaceTheme": "Thème d’interface", "DE.Views.ViewTab.textLeftMenu": "Panneau gauche", "DE.Views.ViewTab.textNavigation": "Navigation", "DE.Views.ViewTab.textOutline": "Titres", "DE.Views.ViewTab.textRightMenu": "Panneau droit", "DE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textStatusBar": "Barre d'état", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Document sombre", "DE.Views.ViewTab.tipFitToPage": "Ajuster à la page", "DE.Views.ViewTab.tipFitToWidth": "Ajuster à la largeur", "DE.Views.ViewTab.tipHeadings": "Titres", "DE.Views.ViewTab.tipInterfaceTheme": "Thème d'interface", "DE.Views.WatermarkSettingsDialog.textAuto": "Auto", "DE.Views.WatermarkSettingsDialog.textBold": "Gras", "DE.Views.WatermarkSettingsDialog.textColor": "Couleur du texte", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonale", "DE.Views.WatermarkSettingsDialog.textFont": "Police", "DE.Views.WatermarkSettingsDialog.textFromFile": "<PERSON>'un fichier", "DE.Views.WatermarkSettingsDialog.textFromStorage": "À partir de l'espace de stockage", "DE.Views.WatermarkSettingsDialog.textFromUrl": "D'une URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horizontal", "DE.Views.WatermarkSettingsDialog.textImageW": "Image en filigrane", "DE.Views.WatermarkSettingsDialog.textItalic": "Italique", "DE.Views.WatermarkSettingsDialog.textLanguage": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textLayout": "Mise en page", "DE.Views.WatermarkSettingsDialog.textNone": "Aucun", "DE.Views.WatermarkSettingsDialog.textScale": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textSelect": "Sélectionner une image", "DE.Views.WatermarkSettingsDialog.textStrikeout": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textText": "Texte", "DE.Views.WatermarkSettingsDialog.textTextW": "Filigrane de texte", "DE.Views.WatermarkSettingsDialog.textTitle": "Paramètres du filigrane", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semi-transparent", "DE.Views.WatermarkSettingsDialog.textUnderline": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.tipFontName": "Nom de la police", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Taille de police"}