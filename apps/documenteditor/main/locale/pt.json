{"Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Insira sua mensagem aqui", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "O objeto está desabilitado por que está sendo editado por outro usuário.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Aviso", "Common.Controllers.ExternalMergeEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.warningText": "O objeto está desabilitado por que está sendo editado por outro usuário.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Aviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "O objeto está desabilitado por que está sendo editado por outro usuário.", "Common.Controllers.ExternalOleEditor.warningTitle": "Aviso", "Common.Controllers.History.notcriticalErrorTitle": "Aviso", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "Para comparar os documentos, todas as alterações neles serão consideradas aceitas. Deseja continuar?", "Common.Controllers.ReviewChanges.textAtLeast": "pelo menos", "Common.Controllers.ReviewChanges.textAuto": "auto", "Common.Controllers.ReviewChanges.textBaseline": "Linha de <PERSON>", "Common.Controllers.ReviewChanges.textBold": "Negrito", "Common.Controllers.ReviewChanges.textBreakBefore": "Quebra de página antes", "Common.Controllers.ReviewChanges.textCaps": "<PERSON><PERSON> ma<PERSON>", "Common.Controllers.ReviewChanges.textCenter": "Alinhar ao centro", "Common.Controllers.ReviewChanges.textChar": "<PERSON><PERSON> de caracter", "Common.Controllers.ReviewChanges.textChart": "Gráfico", "Common.Controllers.ReviewChanges.textColor": "<PERSON><PERSON> da fonte", "Common.Controllers.ReviewChanges.textContextual": "Não adicionar intervalo entre parágrafos do mesmo estilo", "Common.Controllers.ReviewChanges.textDeleted": "<b>Exclu<PERSON>do:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textEquation": "Equação", "Common.Controllers.ReviewChanges.textExact": "exatamente", "Common.Controllers.ReviewChanges.textFirstLine": "Primeira linha", "Common.Controllers.ReviewChanges.textFontSize": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textFormatted": "Formatado", "Common.Controllers.ReviewChanges.textHighlight": "Cor de realce", "Common.Controllers.ReviewChanges.textImage": "Imagem", "Common.Controllers.ReviewChanges.textIndentLeft": "Recuo à esquerda", "Common.Controllers.ReviewChanges.textIndentRight": "Recuo à direita", "Common.Controllers.ReviewChanges.textInserted": "<b>Inserido:</b>", "Common.Controllers.ReviewChanges.textItalic": "Itálico", "Common.Controllers.ReviewChanges.textJustify": "Alinhamento justificado", "Common.Controllers.ReviewChanges.textKeepLines": "<PERSON><PERSON> as linhas juntas", "Common.Controllers.ReviewChanges.textKeepNext": "Manter com o próximo", "Common.Controllers.ReviewChanges.textLeft": "Alinhar à esquerda", "Common.Controllers.ReviewChanges.textLineSpacing": "Espaçamento de linha:", "Common.Controllers.ReviewChanges.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Sem quebra de página antes", "Common.Controllers.ReviewChanges.textNoContextual": "Adicionar intervalo entre parágrafos do mesmo estilo", "Common.Controllers.ReviewChanges.textNoKeepLines": "Não mantenha linhas juntas", "Common.Controllers.ReviewChanges.textNoKeepNext": "Não mantenha com o próximo", "Common.Controllers.ReviewChanges.textNot": "Não", "Common.Controllers.ReviewChanges.textNoWidow": "Sem controle de linhas órfãs/viúvas", "Common.Controllers.ReviewChanges.textNum": "Alterar numeração", "Common.Controllers.ReviewChanges.textOff": "{0} não está mais usando o controle de alterações.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} Rastreamento de alterações desabilitado para todos. ", "Common.Controllers.ReviewChanges.textOn": "{0} usando controle de alterações. ", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} Rastreamento de alterações habilitado para todos. ", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON><PERSON><PERSON></b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Parágrafo formatado", "Common.Controllers.ReviewChanges.textParaInserted": "<b>Parágrafo Inserido</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Movido Para Baixo:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Movido Para Cima:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b><PERSON><PERSON><PERSON>:</b>", "Common.Controllers.ReviewChanges.textPosition": "Posição", "Common.Controllers.ReviewChanges.textRight": "Alinhar à direita", "Common.Controllers.ReviewChanges.textShape": "Forma", "Common.Controllers.ReviewChanges.textShd": "Cor do plano de fundo", "Common.Controllers.ReviewChanges.textShow": "Mostrar mudanças em", "Common.Controllers.ReviewChanges.textSmallCaps": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSpacing": "Espaçamento", "Common.Controllers.ReviewChanges.textSpacingAfter": "Espaçamento depois", "Common.Controllers.ReviewChanges.textSpacingBefore": "Espaçamento antes", "Common.Controllers.ReviewChanges.textStrikeout": "Taxado", "Common.Controllers.ReviewChanges.textSubScript": "Subscrito", "Common.Controllers.ReviewChanges.textSuperScript": "Sobrescrito", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Configurações da Tabela Alteradas</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b><PERSON><PERSON>a <PERSON></b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b><PERSON><PERSON>a Excluídas</b>", "Common.Controllers.ReviewChanges.textTabs": "<PERSON><PERSON><PERSON> g<PERSON>", "Common.Controllers.ReviewChanges.textTitleComparison": "Configurações de Comparação", "Common.Controllers.ReviewChanges.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textUrl": "Colar um arquivo URL", "Common.Controllers.ReviewChanges.textWidow": "Controle de linhas órfãs/viúvas", "Common.Controllers.ReviewChanges.textWord": "Nível de palavra", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> empi<PERSON>", "Common.define.chartData.textAreaStackedPer": "100% <PERSON><PERSON> al<PERSON>a", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "Colunas agrupadas", "Common.define.chartData.textBarNormal3d": "3-D Coluna agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Coluna 3-D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON> al<PERSON>hada", "Common.define.chartData.textBarStacked3d": "Coluna empilhada 3-D", "Common.define.chartData.textBarStackedPer": "100% <PERSON>una alinhada", "Common.define.chartData.textBarStackedPer3d": "3-D 100% <PERSON><PERSON> alinhada", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Coluna", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> empilhada - coluna agrupada", "Common.define.chartData.textComboBarLine": "Coluna agrupada - linha", "Common.define.chartData.textComboBarLineSecondary": "Coluna agrupada - linha no eixo secundário", "Common.define.chartData.textComboCustom": "Combinação personalizada", "Common.define.chartData.textDoughnut": "Rosquin<PERSON>", "Common.define.chartData.textHBarNormal": "Barras agrupadas", "Common.define.chartData.textHBarNormal3d": "3-D Barra agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON>", "Common.define.chartData.textHBarStacked3d": "Barra empilhada 3-D", "Common.define.chartData.textHBarStackedPer": "100% Barra alinhada", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% <PERSON><PERSON> al<PERSON>a", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Linha 3-D", "Common.define.chartData.textLineMarker": "Linha com marcadores", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStackedMarker": "Linha empilhada com marcadores", "Common.define.chartData.textLineStackedPer": "100% Ali<PERSON><PERSON>", "Common.define.chartData.textLineStackedPerMarker": "100% Alinhado com marcadores", "Common.define.chartData.textPie": "Gráfico de pizza", "Common.define.chartData.textPie3d": "Pizza 3-D", "Common.define.chartData.textPoint": "Gráfico de dispersão", "Common.define.chartData.textScatter": "Di<PERSON>são", "Common.define.chartData.textScatterLine": "Dispersão com linhas retas", "Common.define.chartData.textScatterLineMarker": "Dispersão com linhas retas e marcadores", "Common.define.chartData.textScatterSmooth": "Dispersão com linhas suaves", "Common.define.chartData.textScatterSmoothMarker": "Dispersão com linhas suaves e marcadores", "Common.define.chartData.textStock": "Gráfico de ações", "Common.define.chartData.textSurface": "Superfície", "Common.define.smartArt.textAccentedPicture": "Imagem com Ênfase", "Common.define.smartArt.textAccentProcess": "Processo em Destaque", "Common.define.smartArt.textAlternatingFlow": "Fluxo alternado", "Common.define.smartArt.textAlternatingHexagons": "Hexágonos alternados", "Common.define.smartArt.textAlternatingPictureBlocks": "Blocos de imagem alternados", "Common.define.smartArt.textAlternatingPictureCircles": "Círculos de imagens alternadas", "Common.define.smartArt.textArchitectureLayout": "Layout de arquitetura", "Common.define.smartArt.textArrowRibbon": "Seta em Forma de Fita", "Common.define.smartArt.textAscendingPictureAccentProcess": "Processo de acentuação da imagem ascendente", "Common.define.smartArt.textBalance": "<PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Processo Básico de Dobragem", "Common.define.smartArt.textBasicBlockList": "Lista básica de blocos", "Common.define.smartArt.textBasicChevronProcess": "Processo Básico em Divisas", "Common.define.smartArt.textBasicCycle": "Ciclo Básico", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Torta Básica", "Common.define.smartArt.textBasicProcess": "Processo Básico", "Common.define.smartArt.textBasicPyramid": "Pirâmide Básica", "Common.define.smartArt.textBasicRadial": "Radial Básico", "Common.define.smartArt.textBasicTarget": "Alvo Básico", "Common.define.smartArt.textBasicTimeline": "Linha do tempo básica", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n b<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Lista de Acentos de Imagem Dobrada", "Common.define.smartArt.textBendingPictureBlocks": "<PERSON><PERSON><PERSON> Imagem", "Common.define.smartArt.textBendingPictureCaption": "<PERSON><PERSON><PERSON> a legenda da imagem", "Common.define.smartArt.textBendingPictureCaptionList": "Lista de legendas de imagens dobradas", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Dobrando o Texto Semitransparente da Imagem", "Common.define.smartArt.textBlockCycle": "Ciclo de b<PERSON>o", "Common.define.smartArt.textBubblePictureList": "Lista de imagens de bolhas", "Common.define.smartArt.textCaptionedPictures": "Imagens legendadas", "Common.define.smartArt.textChevronAccentProcess": "Processo de Ênfase em Divisas", "Common.define.smartArt.textChevronList": "Lista de Divisas", "Common.define.smartArt.textCircleAccentTimeline": "Linha do tempo de destaque do círculo", "Common.define.smartArt.textCircleArrowProcess": "Processo de seta circular", "Common.define.smartArt.textCirclePictureHierarchy": "Hierarquia de imagem do círculo", "Common.define.smartArt.textCircleProcess": "<PERSON><PERSON>", "Common.define.smartArt.textCircleRelationship": "Relacionamento do Círculo", "Common.define.smartArt.textCircularBendingProcess": "Processo de dobra circular", "Common.define.smartArt.textCircularPictureCallout": "Texto explicativo de imagem circular", "Common.define.smartArt.textClosedChevronProcess": "Processo Fechado em Divisas", "Common.define.smartArt.textContinuousArrowProcess": "Processo de Seta Contínua", "Common.define.smartArt.textContinuousBlockProcess": "Processo de Bloco Contínuo", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textContinuousPictureList": "Lista de Imagens Contínua", "Common.define.smartArt.textConvergingArrows": "Setas convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergente", "Common.define.smartArt.textConvergingText": "Texto convergente", "Common.define.smartArt.textCounterbalanceArrows": "Setas de contrapeso", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Lista de Bloqueios Descendentes", "Common.define.smartArt.textDescendingProcess": "Processo descendente", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDivergingArrows": "Flechas divergentes", "Common.define.smartArt.textDivergingRadial": "Radial divergente", "Common.define.smartArt.textEquation": "Equação", "Common.define.smartArt.textFramedTextPicture": "Imagem de texto emoldurada", "Common.define.smartArt.textFunnel": "Funil", "Common.define.smartArt.textGear": "Engrenagem", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON> de <PERSON>", "Common.define.smartArt.textGroupedList": "Lista Agrupada", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organograma de meio círculo", "Common.define.smartArt.textHexagonCluster": "Conjunto Hexagonal", "Common.define.smartArt.textHexagonRadial": "Radial <PERSON>", "Common.define.smartArt.textHierarchy": "Hierarquia", "Common.define.smartArt.textHierarchyList": "Lista de hierarquia", "Common.define.smartArt.textHorizontalBulletList": "Lista de marcadores horizontais", "Common.define.smartArt.textHorizontalHierarchy": "Hierarquia Horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hierarquia Horizontal Rotulada", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hierarquia horizontal multinível", "Common.define.smartArt.textHorizontalOrganizationChart": "Organograma Horizontal", "Common.define.smartArt.textHorizontalPictureList": "Lista de imagens horizontais", "Common.define.smartArt.textIncreasingArrowProcess": "Processo de seta crescente", "Common.define.smartArt.textIncreasingCircleProcess": "Aumentando o Processo do Círculo", "Common.define.smartArt.textInterconnectedBlockProcess": "Processo de bloco interconectado", "Common.define.smartArt.textInterconnectedRings": "<PERSON><PERSON><PERSON>ec<PERSON>", "Common.define.smartArt.textInvertedPyramid": "Pirâmide invertida", "Common.define.smartArt.textLabeledHierarchy": "Hierarquia rotulada", "Common.define.smartArt.textLinearVenn": "Venn Linear", "Common.define.smartArt.textLinedList": "<PERSON>a alinhada", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclo multidirecional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organograma de Nome e Título", "Common.define.smartArt.textNestedTarget": "<PERSON><PERSON>", "Common.define.smartArt.textNondirectionalCycle": "Ciclo Não Direcional", "Common.define.smartArt.textOpposingArrows": "Setas Opostas", "Common.define.smartArt.textOpposingIdeas": "Ideias opost<PERSON>", "Common.define.smartArt.textOrganizationChart": "Organograma", "Common.define.smartArt.textOther": "Outro", "Common.define.smartArt.textPhasedProcess": "Processo em fases", "Common.define.smartArt.textPicture": "Imagem", "Common.define.smartArt.textPictureAccentBlocks": "Blocos de destaque de imagem", "Common.define.smartArt.textPictureAccentList": "Lista de destaques da imagem", "Common.define.smartArt.textPictureAccentProcess": "Processo de destaque da imagem", "Common.define.smartArt.textPictureCaptionList": "Lista de legendas de imagens", "Common.define.smartArt.textPictureFrame": "Porta-retrato", "Common.define.smartArt.textPictureGrid": "Grade de imagens", "Common.define.smartArt.textPictureLineup": "Alinhamento de imagens", "Common.define.smartArt.textPictureOrganizationChart": "Organograma de imagens", "Common.define.smartArt.textPictureStrips": "Tiras de imagem", "Common.define.smartArt.textPieProcess": "Processo em Pizza", "Common.define.smartArt.textPlusAndMinus": "Mais e menos", "Common.define.smartArt.textProcess": "Processo", "Common.define.smartArt.textProcessArrows": "Setas de processo", "Common.define.smartArt.textProcessList": "Lista de processos", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Lista de pirâmides", "Common.define.smartArt.textRadialCluster": "Aglomerado Radial", "Common.define.smartArt.textRadialCycle": "Ciclo radial", "Common.define.smartArt.textRadialList": "Lista radial", "Common.define.smartArt.textRadialPictureList": "Lista de imagens radiais", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Processo aleatório para resultado", "Common.define.smartArt.textRelationship": "Relação", "Common.define.smartArt.textRepeatingBendingProcess": "Repetindo o processo de dobra", "Common.define.smartArt.textReverseList": "Lista reversa", "Common.define.smartArt.textSegmentedCycle": "Ciclo Segmentado", "Common.define.smartArt.textSegmentedProcess": "Processo segmentado", "Common.define.smartArt.textSegmentedPyramid": "Pirâmide segmentada", "Common.define.smartArt.textSnapshotPictureList": "Lista de fotos instantâneas", "Common.define.smartArt.textSpiralPicture": "Imagem em espiral", "Common.define.smartArt.textSquareAccentList": "Lista de Acentos Quadrados", "Common.define.smartArt.textStackedList": "Lista empilhada", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "Processo escalonado", "Common.define.smartArt.textStepDownProcess": "Processo de redução", "Common.define.smartArt.textStepUpProcess": "Processo de intensificação", "Common.define.smartArt.textSubStepProcess": "Processo de subetapas", "Common.define.smartArt.textTabbedArc": "Arco com abas", "Common.define.smartArt.textTableHierarchy": "Hierar<PERSON><PERSON> da Tabela", "Common.define.smartArt.textTableList": "Lista de Tabelas", "Common.define.smartArt.textTabList": "Lista de guias", "Common.define.smartArt.textTargetList": "Lista de alvos", "Common.define.smartArt.textTextCycle": "Ciclo de texto", "Common.define.smartArt.textThemePictureAccent": "Destaque da Imagem do Tema", "Common.define.smartArt.textThemePictureAlternatingAccent": "Acento Alternado da Imagem do Tema", "Common.define.smartArt.textThemePictureGrid": "Grade de imagens do tema", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Lista de Acentos de Imagem Intitulada", "Common.define.smartArt.textTitledPictureBlocks": "Blocos de imagens intitulados", "Common.define.smartArt.textTitlePictureLineup": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textTrapezoidList": "Lista de trapézios", "Common.define.smartArt.textUpwardArrow": "Seta para cima", "Common.define.smartArt.textVaryingWidthList": "Lista de largura variável", "Common.define.smartArt.textVerticalAccentList": "Lista de acentos verticais", "Common.define.smartArt.textVerticalArrowList": "Lista de setas verticais", "Common.define.smartArt.textVerticalBendingProcess": "Processo de dobra vertical", "Common.define.smartArt.textVerticalBlockList": "Lista de Bloqueios Verticais", "Common.define.smartArt.textVerticalBoxList": "Lista de caixas verticais", "Common.define.smartArt.textVerticalBracketList": "Lista de colchetes verticais", "Common.define.smartArt.textVerticalBulletList": "Lista de marcadores verticais", "Common.define.smartArt.textVerticalChevronList": "Lista Vertical em Divisas", "Common.define.smartArt.textVerticalCircleList": "Lista de círculos verticais", "Common.define.smartArt.textVerticalCurvedList": "Lista Curva Vertical", "Common.define.smartArt.textVerticalEquation": "Equação Vertical", "Common.define.smartArt.textVerticalPictureAccentList": "Lista Vertical de Acentos de Imagem", "Common.define.smartArt.textVerticalPictureList": "Lista de imagens verticais", "Common.define.smartArt.textVerticalProcess": "Processo Vertical", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "O documento está bloqueado para edição. Você pode fazer alterações e salvá-lo como cópia local mais tarde.", "Common.Translation.tipFileReadOnly": "O arquivo é somente leitura. Para manter suas alterações, salve o arquivo com um novo nome ou em um local diferente.", "Common.Translation.warnFileLocked": "Documento está em uso por outra aplicação. Você pode continuar editando e salvá-lo como uma cópia.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON> uma c<PERSON>pia", "Common.Translation.warnFileLockedBtnView": "Aberto para visualização", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Nova cor personalizada", "Common.UI.Calendar.textApril": "Abril", "Common.UI.Calendar.textAugust": "Agosto", "Common.UI.Calendar.textDecember": "Dezembro", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "Janeiro", "Common.UI.Calendar.textJuly": "<PERSON><PERSON>", "Common.UI.Calendar.textJune": "<PERSON><PERSON>", "Common.UI.Calendar.textMarch": "Março", "Common.UI.Calendar.textMay": "<PERSON>", "Common.UI.Calendar.textMonths": "Meses", "Common.UI.Calendar.textNovember": "Novembro", "Common.UI.Calendar.textOctober": "Out<PERSON>ro", "Common.UI.Calendar.textSeptember": "Setembro", "Common.UI.Calendar.textShortApril": "Abr", "Common.UI.Calendar.textShortAugust": "Ago", "Common.UI.Calendar.textShortDecember": "<PERSON>z", "Common.UI.Calendar.textShortFebruary": "<PERSON>v", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "Jan", "Common.UI.Calendar.textShortJuly": "Jul", "Common.UI.Calendar.textShortJune": "Jun", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "<PERSON><PERSON>", "Common.UI.Calendar.textShortMonday": "Me", "Common.UI.Calendar.textShortNovember": "Nov", "Common.UI.Calendar.textShortOctober": "Out", "Common.UI.Calendar.textShortSaturday": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortSeptember": "Set", "Common.UI.Calendar.textShortSunday": "Dom", "Common.UI.Calendar.textShortThursday": "º", "Common.UI.Calendar.textShortTuesday": "<PERSON><PERSON>", "Common.UI.Calendar.textShortWednesday": "<PERSON>ua", "Common.UI.Calendar.textYears": "<PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboDataView.emptyComboText": "Sem estilos", "Common.UI.ExtendedColorDialog.addButtonText": "Incluir", "Common.UI.ExtendedColorDialog.textCurrent": "Atual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor inserido está incorreto.<br>Insira um valor entre 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor inserido está incorreto.<br>Insira um valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sem cor", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar palavra-chave", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON> senha", "Common.UI.SearchBar.textFind": "Localizar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.UI.SearchBar.tipNextResult": "Próximo resultado", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON> as configura<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Destacar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto de substituição", "Common.UI.SearchDialog.textSearchStart": "Insira seu texto aqui", "Common.UI.SearchDialog.textTitle": "Localizar e substituir", "Common.UI.SearchDialog.textTitle2": "Localizar", "Common.UI.SearchDialog.textWholeWords": "Palavras inteiras apenas", "Common.UI.SearchDialog.txtBtnHideReplace": "Ocultar Substituição", "Common.UI.SearchDialog.txtBtnReplace": "Substituir", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituir tudo", "Common.UI.SynchronizeTip.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.SynchronizeTip.textSynchronize": "O documento foi alterado por outro usuário.<br>Clique para salvar suas alterações e recarregar as atualizações.", "Common.UI.ThemeColorPalette.textRecentColors": "Cores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON> padron<PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores de tema", "Common.UI.Themes.txtThemeClassicLight": "Clássico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste escuro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "O mesmo que sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Não", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmação", "Common.UI.Window.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Informações", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "<PERSON>m", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "Pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "endereço:", "Common.Views.About.txtLicensee": "LICENÇA", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Aplicar enquanto você digita", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorreção", "Common.Views.AutoCorrectDialog.textAutoFormat": "Auto Formatação conforme você digita", "Common.Views.AutoCorrectDialog.textBulleted": "Listas com marcadores automáticas", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Excluir", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Adicionar ponto com espaço duplo", "Common.Views.AutoCorrectDialog.textFLCells": "Capitalizar a primeira letra das células da tabela", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalizar a primeira carta de sentenças", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e caminhos de rede com hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON>s (--) com traço (-)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorreção matemática", "Common.Views.AutoCorrectDialog.textNumbered": "Listas com numeradores automáticos", "Common.Views.AutoCorrectDialog.textQuotes": "\"Aspas retas\" com \"aspas inteligentes\"", "Common.Views.AutoCorrectDialog.textRecognized": "Funções Reconhecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expressões são expressões matemáticas reconhecidas. Eles não ficarão em itálico automaticamente.", "Common.Views.AutoCorrectDialog.textReplace": "Substituir", "Common.Views.AutoCorrectDialog.textReplaceText": "Substituir ao Digitar", "Common.Views.AutoCorrectDialog.textReplaceType": "Substitua o texto enquanto você digita", "Common.Views.AutoCorrectDialog.textReset": "Redefinir", "Common.Views.AutoCorrectDialog.textResetAll": "Voltar para predefinições", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorreção", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funções reconhecidas devem conter apenas as letras de A a Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Qualquer expressão que tenha acrescentado será removida e as expressões removidas serão restauradas. Quer continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A correção automática para %1 já existe. Quer substituir?", "Common.Views.AutoCorrectDialog.warnReset": "Qualquer autocorrecção que tenha adicionado será removida e as alterações serão restauradas aos seus valores originais. Quer continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A entrada de autocorreção para %1 será redefinida para seu valor original. Você quer continuar?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> anti<PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "De cima", "Common.Views.Comments.mniPositionDesc": "Do fundo", "Common.Views.Comments.textAdd": "Incluir", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Adicionar comentário ao documento", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.Comments.textAll": "Todos", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Comentários próximos", "Common.Views.Comments.textComments": "Comentários", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Insira seu coment<PERSON>rio aqui", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolvido", "Common.Views.Comments.textSort": "Ordenar comentá<PERSON>s", "Common.Views.Comments.textViewResolved": "Você não tem permissão para reabrir comentários", "Common.Views.Comments.txtEmpty": "Não há comentários no documento.", "Common.Views.CopyWarningDialog.textDontShow": "Não exibir esta mensagem novamente", "Common.Views.CopyWarningDialog.textMsg": "As ações copiar, cortar e colar usando os botões da barra de ferramentas do editor e as ações de menu de contexto serão realizadas apenas nesta aba do editor.<br><br>Para copiar ou colar para ou de aplicativos externos a aba do editor, use as seguintes combinações do teclado:", "Common.Views.CopyWarningDialog.textTitle": "Copiar, Cortar e Colar", "Common.Views.CopyWarningDialog.textToCopy": "para Copiar", "Common.Views.CopyWarningDialog.textToCut": "para Cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Colar", "Common.Views.DocumentAccessDialog.textLoading": "Carregando...", "Common.Views.DocumentAccessDialog.textTitle": "Configurações de compartilhamento", "Common.Views.ExternalDiagramEditor.textTitle": "Editor de gráfico", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "<PERSON><PERSON> e <PERSON>", "Common.Views.ExternalMergeEditor.textTitle": "Mail Merge Recepients", "Common.Views.ExternalOleEditor.textTitle": "Editor <PERSON> <PERSON><PERSON>", "Common.Views.Header.labelCoUsersDescr": "Usuários que estão editando o arquivo:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Configurações avançadas", "Common.Views.Header.textBack": "Local do arquivo aberto", "Common.Views.Header.textCompactView": "Ocultar Barra de Ferramentas", "Common.Views.Header.textHideLines": "Ocultar Réguas", "Common.Views.Header.textHideStatusBar": "Ocultar Barra de Status", "Common.Views.Header.textReadOnly": "<PERSON>nte leitura", "Common.Views.Header.textRemoveFavorite": "Remover dos Favoritos", "Common.Views.Header.textShare": "Compartilhar", "Common.Views.Header.textZoom": "Ampliação", "Common.Views.Header.tipAccessRights": "Gerenciar direitos de acesso ao documento", "Common.Views.Header.tipDownload": "Baixar arquivo", "Common.Views.Header.tipGoEdit": "Editar arquivo atual", "Common.Views.Header.tipPrint": "Imprimir arquivo", "Common.Views.Header.tipPrintQuick": "Impressão rápida", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUsers": "Ver usuários", "Common.Views.Header.tipViewSettings": "Visualizar configurações", "Common.Views.Header.tipViewUsers": "Ver usuários e gerenciar direitos de acesso ao documento", "Common.Views.Header.txtAccessRights": "Alterar direitos de acesso", "Common.Views.Header.txtRename": "Renomear", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar alterações detalhadas ", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Mostrar alterações detalhadas", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Colar uma URL de imagem:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Você precisa especificar a contagem de linhas e colunas válida.", "Common.Views.InsertTableDialog.txtColumns": "Número de <PERSON>nas", "Common.Views.InsertTableDialog.txtMaxText": "O valor máximo para este campo é {0}.", "Common.Views.InsertTableDialog.txtMinText": "O valor mínimo para este campo é {0}.", "Common.Views.InsertTableDialog.txtRows": "Número de l<PERSON>has", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Selecionar idioma do documento", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Encoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON> incorreta.", "Common.Views.OpenDialog.txtOpenFile": "Inserir a Senha para Abrir o Arquivo", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Visualizar", "Common.Views.OpenDialog.txtProtected": "Ao abrir o arquivo com sua senha, a senha atual será redefinida.", "Common.Views.OpenDialog.txtTitle": "Choose %1 options", "Common.Views.OpenDialog.txtTitleProtected": "Arquivo protegido", "Common.Views.PasswordDialog.txtDescription": "Defina uma senha para proteger o documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "A confirmação da senha não é idêntica", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON>r a senha", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Cuidado: se você perder ou esquecer a senha, não será possível recuperá-la. Guarde-o em local seguro.", "Common.Views.PluginDlg.textLoading": "Carregamento", "Common.Views.Plugins.groupCaption": "Plug-ins", "Common.Views.Plugins.strPlugins": "Plug-ins", "Common.Views.Plugins.textClosePanel": "Fechar plug-in", "Common.Views.Plugins.textLoading": "Carregamento", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Criptografar com senha", "Common.Views.Protection.hintDelPwd": "Excluir senha", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> ou excluir senha", "Common.Views.Protection.hintSignature": "Inserir assinatura digital ou linha de assinatura", "Common.Views.Protection.txtAddPwd": "Inserir a senha", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "Excluir senha", "Common.Views.Protection.txtEncrypt": "Criptografar", "Common.Views.Protection.txtInvisibleSignature": "Inserir assinatura digital", "Common.Views.Protection.txtSignature": "Assinatura", "Common.Views.Protection.txtSignatureLine": "Adicionar linha de assinatura", "Common.Views.RenameDialog.textName": "Nome de arquivo", "Common.Views.RenameDialog.txtInvalidName": "Nome de arquivo não pode conter os seguintes caracteres:", "Common.Views.ReviewChanges.hintNext": "Para a próxima alteração", "Common.Views.ReviewChanges.hintPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.mniFromFile": "Documento de Arquivo", "Common.Views.ReviewChanges.mniFromStorage": "Documento de Armazenamento", "Common.Views.ReviewChanges.mniFromUrl": "Documento de URL", "Common.Views.ReviewChanges.mniSettings": "Configurações de Comparação", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Coedição em tempo real. Todas as alterações são salvas automaticamente.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Use o botão 'Salvar' para sincronizar as alterações que você e outros realizaram.", "Common.Views.ReviewChanges.textEnable": "Habilitar", "Common.Views.ReviewChanges.textWarnTrackChanges": "As mudanças de faixa serão ativadas para todos os usuários com acesso total. Na próxima vez que alguém abrir o documento, as Mudanças de Trilha permanecerão ativadas.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Habilitar rastreamento de alterações para todos?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceitar a alteração atual", "Common.Views.ReviewChanges.tipCoAuthMode": "Definir modo de coedição", "Common.Views.ReviewChanges.tipCommentRem": "Excluir coment<PERSON>", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Excluir comentários atuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentários", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.tipCompare": "Comparar o documento atual com outro", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Selecione o modo que você quiser que as alterações sejam exibidas", "Common.Views.ReviewChanges.tipSetDocLang": "Definir idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.tipSharing": "Gerenciar os direitos de acesso ao documento", "Common.Views.ReviewChanges.txtAccept": "Aceitar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceitar todas as alteraç<PERSON><PERSON>.", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceitar as alterações", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceitar alterações atuais", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de coedição", "Common.Views.ReviewChanges.txtCommentRemAll": "Excluir todos os comentários", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Excluir comentários atuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Excluir meus comentários", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Excluir meus comentários atuais", "Common.Views.ReviewChanges.txtCommentRemove": "Excluir", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentários", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver meus comentários", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver meus comentários atuais", "Common.Views.ReviewChanges.txtCompare": "Comparar", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtEditing": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> as alterações aceitas {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Histórico <PERSON>ão", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> as alterações {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Marcação e balões", "Common.Views.ReviewChanges.txtMarkupSimple": "<PERSON><PERSON> as mudan<PERSON><PERSON> {0}<br><PERSON><PERSON> <PERSON> bal<PERSON>es", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Somente marcação", "Common.Views.ReviewChanges.txtNext": "Para a próxima alteração", "Common.Views.ReviewChanges.txtOff": "Desligado pra mim", "Common.Views.ReviewChanges.txtOffGlobal": "Desligado pra mim e para todos", "Common.Views.ReviewChanges.txtOn": "Ligado pra mim", "Common.Views.ReviewChanges.txtOnGlobal": "Ligado para mim e para todos", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> as alterações rejeitadas {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.txtPreview": "Pré-visualizar", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeitar alterações", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewChanges.txtSharing": "Compartilhar", "Common.Views.ReviewChanges.txtSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Modo de exibição", "Common.Views.ReviewChangesDialog.textTitle": "Rever alter<PERSON>", "Common.Views.ReviewChangesDialog.txtAccept": "Aceitar", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Aceitar todas as alteraç<PERSON><PERSON>.", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Aceitar a alteração atual", "Common.Views.ReviewChangesDialog.txtNext": "Para a próxima alteração", "Common.Views.ReviewChangesDialog.txtPrev": "Para a alteração anterior", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewPopover.textAdd": "Incluir", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Insira seu coment<PERSON>rio aqui", "Common.Views.ReviewPopover.textFollowMove": "<PERSON><PERSON><PERSON> movimento", "Common.Views.ReviewPopover.textMention": "+menção fornecerá acesso ao documento e enviará um e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+menção notificará o usuário por e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Abrir Novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Não tem permissão para reabrir comentários", "Common.Views.ReviewPopover.txtAccept": "Aceitar", "Common.Views.ReviewPopover.txtDeleteTip": "Excluir", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Carregando", "Common.Views.SaveAsDlg.textTitle": "Pasta para salvar", "Common.Views.SearchPanel.textCaseSensitive": "Maiúsculas e Minúsculas", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.Views.SearchPanel.textContentChanged": "Documento alterado.", "Common.Views.SearchPanel.textFind": "Localizar", "Common.Views.SearchPanel.textFindAndReplace": "Localizar e substituir", "Common.Views.SearchPanel.textMatchUsingRegExp": "Corresponder usando expressões regulares", "Common.Views.SearchPanel.textNoMatches": "Nenhuma correspondê<PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "Nenhum resultado de pesquisa", "Common.Views.SearchPanel.textReplace": "Substituir", "Common.Views.SearchPanel.textReplaceAll": "Substituir tudo", "Common.Views.SearchPanel.textReplaceWith": "Substituir com", "Common.Views.SearchPanel.textSearchAgain": "{0}Realize uma nova pesquisa{1} para obter resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "A pesquisa parou", "Common.Views.SearchPanel.textSearchResults": "Resultados da pesquisa: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Há muitos resultados para mostrar aqui", "Common.Views.SearchPanel.textWholeWords": "Palavras inteiras apenas", "Common.Views.SearchPanel.tipNextResult": "Próximo resultado", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "Carregando", "Common.Views.SelectFileDlg.textTitle": "Selecionar fonte de dados", "Common.Views.SignDialog.textBold": "Negrito", "Common.Views.SignDialog.textCertificate": "Certificado", "Common.Views.SignDialog.textChange": "Alterar", "Common.Views.SignDialog.textInputName": "Nome do signatário de entrada", "Common.Views.SignDialog.textItalic": "Itálico", "Common.Views.SignDialog.textNameError": "Nome de assinante não deve estar vazio.", "Common.Views.SignDialog.textPurpose": "Objetivo para assinar o documento", "Common.Views.SignDialog.textSelect": "Selecionar", "Common.Views.SignDialog.textSelectImage": "Selecionar Imagem", "Common.Views.SignDialog.textSignature": "Ver assinatura como", "Common.Views.SignDialog.textTitle": "Assinar o Documento", "Common.Views.SignDialog.textUseImage": "ou clique 'Selecionar Imagem' para usar uma figura como assinatura", "Common.Views.SignDialog.textValid": "Válido de %1 até %2", "Common.Views.SignDialog.tipFontName": "Nome da Fonte", "Common.Views.SignDialog.tipFontSize": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir ao signatário inserir comentários no diálogo de assinatura", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de assinar este documento, verifique se o conteúdo que está a assinar está correto.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail do assinante sugerido", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "Título do assinante", "Common.Views.SignSettingsDialog.textInstructions": "Instruções para o Assinante", "Common.Views.SignSettingsDialog.textShowDate": "Exibir a data da assinatura na linha da assinatura", "Common.Views.SignSettingsDialog.textTitle": "Configurações da Assinatura", "Common.Views.SignSettingsDialog.txtEmpty": "O campo é obrigatório", "Common.Views.SymbolTableDialog.textCharacter": "Caractere", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Assinatura de copyright", "Common.Views.SymbolTableDialog.textDCQuote": "Fechamento Duplo Orçamento", "Common.Views.SymbolTableDialog.textDOQuote": "Abertura de aspas duplas", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Travessão", "Common.Views.SymbolTableDialog.textEmSpace": "Em Espaço", "Common.Views.SymbolTableDialog.textEnDash": "Travessão", "Common.Views.SymbolTableDialog.textEnSpace": "Espaço", "Common.Views.SymbolTableDialog.textFont": "Fonte", "Common.Views.SymbolTableDialog.textNBHyphen": "Hífen sem quebra", "Common.Views.SymbolTableDialog.textNBSpace": "Espaço sem interrupção", "Common.Views.SymbolTableDialog.textPilcrow": "Sinal de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em Espaço", "Common.Views.SymbolTableDialog.textRange": "Intervalo", "Common.Views.SymbolTableDialog.textRecent": "Símbolos usados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Símbolo de marca registrada", "Common.Views.SymbolTableDialog.textSCQuote": "Cotação Única de Fechamento", "Common.Views.SymbolTableDialog.textSection": "Sinal de seção", "Common.Views.SymbolTableDialog.textShortcut": "Teclas de atalho", "Common.Views.SymbolTableDialog.textSHyphen": "Hífen suave", "Common.Views.SymbolTableDialog.textSOQuote": "Abertura de aspas simples", "Common.Views.SymbolTableDialog.textSpecial": "caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca registrada", "Common.Views.UserNameDialog.textDontShow": "Não perguntar novamente", "Common.Views.UserNameDialog.textLabel": "Rótulo:", "Common.Views.UserNameDialog.textLabelError": "O rótulo não pode estar vazio.", "DE.Controllers.DocProtection.txtIsProtectedComment": "O documento está protegido. Você só pode inserir comentários neste documento.", "DE.Controllers.DocProtection.txtIsProtectedForms": "O documento está protegido. Você só pode preencher os formulários deste documento.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "O documento está protegido. Você pode editar este documento, mas todas as alterações serão rastreadas.", "DE.Controllers.DocProtection.txtIsProtectedView": "O documento está protegido. Você só pode visualizar este documento.", "DE.Controllers.DocProtection.txtWasProtectedComment": "O documento foi protegido por outro usuário.\nVocê só pode inserir comentários neste documento.", "DE.Controllers.DocProtection.txtWasProtectedForms": "O documento foi protegido por outro usuário.\nVocê só pode preencher os formulários deste documento.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "O documento foi protegido por outro usuário.\nVocê pode editar este documento, mas todas as alterações serão rastreadas.", "DE.Controllers.DocProtection.txtWasProtectedView": "O documento foi protegido por outro usuário.\nVocê só pode visualizar este documento.", "DE.Controllers.DocProtection.txtWasUnprotected": "O documento foi desprotegido.", "DE.Controllers.LeftMenu.leavePageText": "<PERSON><PERSON> as alterações não salvas neste documento serão perdidas.<br> Clique em \"Cancelar\" e depois em \"Salvar\" para salvá-las. Clique em \"OK\" para descartar todas as alterações não salvas.", "DE.Controllers.LeftMenu.newDocumentTitle": "Documento sem nome", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Aviso", "DE.Controllers.LeftMenu.requestEditRightsText": "Solicitando direitos de edição...", "DE.Controllers.LeftMenu.textLoadHistory": "Carregando o histórico de versões...", "DE.Controllers.LeftMenu.textNoTextFound": "Os dados que você tem estado procurando não podem ser encontrados. Ajuste suas opções de pesquisa.", "DE.Controllers.LeftMenu.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "DE.Controllers.LeftMenu.textReplaceSuccess": "A pesquisa foi realizada. Ocorrências substituídas: {0}", "DE.Controllers.LeftMenu.txtCompatible": "O documento será salvo em novo formato. Isto permitirá usar todos os recursos de editor, mas pode afetar o layout do documento. <br> Use a opção de 'Compatibilidade' para configurações avançadas se deseja tornar o arquivo compatível com versões antigas do MS Word.", "DE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "DE.Controllers.LeftMenu.warnDownloadAs": "Se você continuar salvando neste formato algumas formatações podem ser perdidas.<br>Você tem certeza que deseja continuar?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "O documento resultante será otimizado para permitir que você edite o texto, portanto, não gráficos exatamente iguais ao original, se o arquivo original contiver muitos gráficos.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Se você continuar salvando neste formato algumas formatações podem ser perdidas.<br>Você tem certeza que deseja continuar?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} não é um caractere especial válido para o campo de substituição.", "DE.Controllers.Main.applyChangesTextText": "Carregando as alterações...", "DE.Controllers.Main.applyChangesTitleText": "Carregando as alterações", "DE.Controllers.Main.confirmMaxChangesSize": "O tamanho das ações excede a limitação definida para seu servidor.<br>Pressione \"Desfazer\" para cancelar sua última ação ou pressione \"Continue\" para manter a ação localmente (você precisa baixar o arquivo ou copiar seu conteúdo para garantir que nada seja perdido).", "DE.Controllers.Main.convertationTimeoutText": "Tempo limite de conversão excedido.", "DE.Controllers.Main.criticalErrorExtText": "Pressione \"OK\" para voltar para a lista de documentos.", "DE.Controllers.Main.criticalErrorTitle": "Erro", "DE.Controllers.Main.downloadErrorText": "Erro ao baixar arquivo.", "DE.Controllers.Main.downloadMergeText": "Baixando...", "DE.Controllers.Main.downloadMergeTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.downloadTextText": "Baixando documento...", "DE.Controllers.Main.downloadTitleText": "<PERSON><PERSON>ndo documento", "DE.Controllers.Main.errorAccessDeny": "Você está tentando executar uma ação que você não tem direitos. <br> Contate o administrador do Servidor de Documentos.", "DE.Controllers.Main.errorBadImageUrl": "URL de imagem está incorreta", "DE.Controllers.Main.errorCannotPasteImg": "Não podemos colar esta imagem da área de transferência, mas você pode salvá-la em seu dispositivo e\ninsira-o a partir daí ou copie a imagem sem texto e cole-a no documento.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Conexão com servidor perdida. O documento não pode ser editado neste momento.", "DE.Controllers.Main.errorComboSeries": "Para criar uma tabela de combinação, selecione pelo menos duas séries de dados.", "DE.Controllers.Main.errorCompare": "O recurso Comparar documentos não está disponível durante a coedição.", "DE.Controllers.Main.errorConnectToServer": "O documento não pode ser gravado. Verifique as configurações de conexão ou entre em contato com o administrador.<br>Quando você clicar no botão 'OK', você será solicitado ao baixar o documento.", "DE.Controllers.Main.errorDatabaseConnection": "Erro externo.<br><PERSON><PERSON> de conexão ao banco de dados. Entre em contato com o suporte caso o erro persista.", "DE.Controllers.Main.errorDataEncrypted": "Alterações criptografadas foram recebidas, e não podem ser decifradas.", "DE.Controllers.Main.errorDataRange": "Intervalo de dados incorreto.", "DE.Controllers.Main.errorDefaultMessage": "Código do erro: %1", "DE.Controllers.Main.errorDirectUrl": "Por favor, verifique o link para o documento.<br>Este link deve ser o link direto para baixar o arquivo.", "DE.Controllers.Main.errorEditingDownloadas": "Ocorreu um erro. <br> Use a opção 'Baixar como' para gravar a cópia de backup em seu computador.", "DE.Controllers.Main.errorEditingSaveas": "Ocorreu um erro durante o trabalho com o documento.<br>Use a opção 'Salvar como ...' para salvar a cópia de backup do arquivo no disco rígido do computador.", "DE.Controllers.Main.errorEmailClient": "Nenhum cliente de e-mail foi encontrado.", "DE.Controllers.Main.errorEmptyTOC": "Comece a criar um sumário aplicando um estilo de título da galeria Estilos ao texto selecionado.", "DE.Controllers.Main.errorFilePassProtect": "O documento é protegido por senha e não pode ser aberto.", "DE.Controllers.Main.errorFileSizeExceed": "O tamanho do arquivo excede o limite de seu servidor. <br> Por favor, contate seu administrador de Servidor de Documentos para detalhes.", "DE.Controllers.Main.errorForceSave": "Ocorreu um erro na gravação. Favor utilizar a opção 'Baixar como' para gravar o arquivo em seu computador ou tente novamente mais tarde.", "DE.Controllers.Main.errorInconsistentExt": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo não corresponde à extensão do arquivo.", "DE.Controllers.Main.errorInconsistentExtDocx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a documentos de texto (por exemplo, docx), mas o arquivo tem a extensão inconsistente: %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a um dos seguintes formatos: pdf/djvu/xps/oxps, mas o arquivo tem a extensão inconsistente: %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a apresentações (por exemplo, pptx), mas o arquivo tem a extensão inconsistente: %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "Ocorreu um erro ao abrir o arquivo.<br>O conteúdo do arquivo corresponde a planilhas (por exemplo, xlsx), mas o arquivo tem a extensão inconsistente: %1.", "DE.Controllers.Main.errorKeyEncrypt": "Descritor de chave desconhecido", "DE.Controllers.Main.errorKeyExpire": "Descritor de chave expirado", "DE.Controllers.Main.errorLoadingFont": "As fontes não foram carregadas. <br> Entre em contato com o administrador do Document Server.", "DE.Controllers.Main.errorMailMergeLoadFile": "Carregamento falhou. Por favor, selecione um arquivo diferente.", "DE.Controllers.Main.errorMailMergeSaveFile": "Me<PERSON> failed.", "DE.Controllers.Main.errorNoTOC": "Não há índice para atualizar. Você pode inserir um na guia Referências.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "A senha fornecida não está correta. <br> Verifique se a tecla CAPS LOCK está desligada e use a capitalização correta.", "DE.Controllers.Main.errorProcessSaveResult": "Salvamento falhou.", "DE.Controllers.Main.errorServerVersion": "A versão do editor foi atualizada. A página será recarregada para aplicar as alterações.", "DE.Controllers.Main.errorSessionAbsolute": "A sessão de edição de documentos expirou. Por Favor atualize a página.", "DE.Controllers.Main.errorSessionIdle": "O documento ficou sem edição por muito tempo. Por favor atualize a página.", "DE.Controllers.Main.errorSessionToken": "A conexão com o servidor foi interrompida. Por favor atualize a página.", "DE.Controllers.Main.errorSetPassword": "Não foi possível definir a senha.", "DE.Controllers.Main.errorStockChart": "Ordem da linha incorreta. Para criar um gráfico de ações coloque os dados na planilha na seguinte ordem:<br>preço de abertura, preço máx., preço mín., preço de fechamento.", "DE.Controllers.Main.errorSubmit": "<PERSON><PERSON>ha no envio.", "DE.Controllers.Main.errorTextFormWrongFormat": "O valor inserido não corresponde ao formato do campo.", "DE.Controllers.Main.errorToken": "O token de segurança do documento não foi formado corretamente. <br> Entre em contato com o administrador do Document Server.", "DE.Controllers.Main.errorTokenExpire": "O token de segurança do documento expirou. <br> Entre em contato com o administrador do Document Server.", "DE.Controllers.Main.errorUpdateVersion": "A versão do arquivo foi alterada. A página será recarregada.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "A conexão a internet foi restaurada, e a versão do arquivo foi alterada.<br>Antes de continuar seu trabalho, baixe o arquivo ou copie seu conteúdo para assegurar que nada seja perdido, e então, recarregue esta página.", "DE.Controllers.Main.errorUserDrop": "O arquivo não pode ser acessado agora.", "DE.Controllers.Main.errorUsersExceed": "O número de usuários permitidos pelo plano de preços foi excedido", "DE.Controllers.Main.errorViewerDisconnect": "Perda de conexão. Você ainda pode exibir o documento,<br>mas não pode fazer o download ou imprimir até que a conexão seja restaurada.", "DE.Controllers.Main.leavePageText": "Você não salvou as alterações neste documento. Clique em \"Permanecer nesta página\", em seguida, clique em \"Salvar\" para salvá-las. Clique em \"Sair desta página\" para descartar todas as alterações não salvas.", "DE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON> as alterações não salvas neste documento serão perdidas.<br> Clique em \"Cancelar\" e depois em \"Salvar\" para salvá-las. Clique em \"OK\" para descartar todas as alterações não salvas.", "DE.Controllers.Main.loadFontsTextText": "Carregando dados...", "DE.Controllers.Main.loadFontsTitleText": "Carregando dados", "DE.Controllers.Main.loadFontTextText": "Carregando dados...", "DE.Controllers.Main.loadFontTitleText": "Carregando dados", "DE.Controllers.Main.loadImagesTextText": "Carregando imagens...", "DE.Controllers.Main.loadImagesTitleText": "Carregando imagens", "DE.Controllers.Main.loadImageTextText": "Carregando imagem...", "DE.Controllers.Main.loadImageTitleText": "Carregando imagem", "DE.Controllers.Main.loadingDocumentTextText": "Carregando documento...", "DE.Controllers.Main.loadingDocumentTitleText": "Carregando documento", "DE.Controllers.Main.mailMergeLoadFileText": "Loading Data Source...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Loading Data Source", "DE.Controllers.Main.notcriticalErrorTitle": "Aviso", "DE.Controllers.Main.openErrorText": "Ocorreu um erro ao abrir o arquivo", "DE.Controllers.Main.openTextText": "Abrindo documento...", "DE.Controllers.Main.openTitleText": "Abrindo documento", "DE.Controllers.Main.printTextText": "Imprimindo documento...", "DE.Controllers.Main.printTitleText": "Imprimindo documento", "DE.Controllers.Main.reloadButtonText": "Re<PERSON><PERSON><PERSON> p<PERSON>gina", "DE.Controllers.Main.requestEditFailedMessageText": "Alguém está editando este documento neste momento. Tente novamente mais tarde.", "DE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>", "DE.Controllers.Main.saveErrorText": "Ocorreu um erro ao gravar o arquivo", "DE.Controllers.Main.saveErrorTextDesktop": "Este arquivo não pode ser salvo ou criado.<br>Possíveis razões são: <br>1. O arquivo é somente leitura. <br>2. O arquivo está sendo editado por outros usuários. <br>3. O disco está cheio ou corrompido.", "DE.Controllers.Main.saveTextText": "<PERSON><PERSON><PERSON> documento...", "DE.Controllers.Main.saveTitleText": "<PERSON><PERSON><PERSON>o", "DE.Controllers.Main.scriptLoadError": "A conexão está muito lenta, e alguns dos componentes não puderam ser carregados. Por favor, recarregue a página.", "DE.Controllers.Main.sendMergeText": "Sending Merge...", "DE.Controllers.Main.sendMergeTitle": "Sending Merge", "DE.Controllers.Main.splitDividerErrorText": "O número de linhas deve ser um divisor de %1.", "DE.Controllers.Main.splitMaxColsErrorText": "O número de colunas deve ser inferior a %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "O número de linhas deve ser inferior a %1.", "DE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.textAnyone": "Alguém", "DE.Controllers.Main.textApplyAll": "Aplicar a todas as equações", "DE.Controllers.Main.textBuyNow": "Visitar website", "DE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> as alteraç<PERSON>es foram salvas", "DE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textCloseTip": "Clique para fechar a dica", "DE.Controllers.Main.textContactUs": "Contate as vendas", "DE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.textConvertEquation": "Esta equação foi criada com uma versão antiga do editor de equação que não é mais compatível. Para editá-lo, converta a equação para o formato Office Math ML. <br> Converter agora?", "DE.Controllers.Main.textCustomLoader": "Por favor, observe que de acordo com os termos de licença, você não tem autorização para alterar o carregador. <br> Por favor, contate o Departamento de Vendas para fazer cotação.", "DE.Controllers.Main.textDisconnect": "A conexão está perdida", "DE.Controllers.Main.textGuest": "Convidado (a)", "DE.Controllers.Main.textHasMacros": "O arquivo contém macros automáticas.<br> Você quer executar macros?", "DE.Controllers.Main.textLearnMore": "<PERSON><PERSON> mais", "DE.Controllers.Main.textLoadingDocument": "Carregando documento", "DE.Controllers.Main.textLongName": "Insira um nome com menos de 128 caracteres.", "DE.Controllers.Main.textNoLicenseTitle": "Limite de licença atingido", "DE.Controllers.Main.textPaidFeature": "Recurso pago", "DE.Controllers.Main.textReconnect": "A conexão é restaurada", "DE.Controllers.Main.textRemember": "Le<PERSON><PERSON> da minha escolha para todos os arquivos. ", "DE.Controllers.Main.textRememberMacros": "<PERSON><PERSON><PERSON> minha escolha para todas as macros", "DE.Controllers.Main.textRenameError": "O nome de usuário não pode estar vazio.", "DE.Controllers.Main.textRenameLabel": "Insira um nome a ser usado para colaboração", "DE.Controllers.Main.textRequestMacros": "Uma macro faz uma solicitação para URL. Deseja permitir a solicitação para %1?", "DE.Controllers.Main.textShape": "Forma", "DE.Controllers.Main.textStrict": "Modo estrito", "DE.Controllers.Main.textText": "Тexto", "DE.Controllers.Main.textTryQuickPrint": "Você selecionou Impressão rápida: todo o documento será impresso na última impressora selecionada ou padrão.<br>Deseja continuar?", "DE.Controllers.Main.textTryUndoRedo": "As funções Desfazer/Refazer ficam desabilitadas no modo de Coedição Rápida.<br>Selecione o modo 'Estrito' para editar o aquivo sem que outros usuários interfiram e envie suas mudanças somente ao salvar o documento. Você pode alternar entre os modos de coedição usando as Configurações Avançadas.\",", "DE.Controllers.Main.textTryUndoRedoWarn": "As funções Desfazer/Refazer estão desabilitadas para o modo de coedição rápido", "DE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.titleLicenseExp": "A licença expirou", "DE.Controllers.Main.titleServerVersion": "Editor atual<PERSON><PERSON>", "DE.Controllers.Main.titleUpdateVersion": "Versão alterada", "DE.Controllers.Main.txtAbove": "Acima", "DE.Controllers.Main.txtArt": "Your text here", "DE.Controllers.Main.txtBasicShapes": "Formas básicas", "DE.Controllers.Main.txtBelow": "abaixo", "DE.Controllers.Main.txtBookmarkError": "Erro! Bookmark não definido", "DE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtCallouts": "Textos explicativos", "DE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "DE.Controllers.Main.txtChoose": "Escolha um item", "DE.Controllers.Main.txtClickToLoad": "Clique para carregar imagem", "DE.Controllers.Main.txtCurrentDocument": "Documento atual", "DE.Controllers.Main.txtDiagramTitle": "Título do <PERSON>a", "DE.Controllers.Main.txtEditingMode": "Definir modo de edição...", "DE.Controllers.Main.txtEndOfFormula": "Fim inesperado da fórmula", "DE.Controllers.Main.txtEnterDate": "Insira uma data", "DE.Controllers.Main.txtErrorLoadHistory": "O carregamento de histórico falhou", "DE.Controllers.Main.txtEvenPage": "Página Par", "DE.Controllers.Main.txtFiguredArrows": "Setas figuradas", "DE.Controllers.Main.txtFirstPage": "Primeira Página", "DE.Controllers.Main.txtFooter": "Rodapé", "DE.Controllers.Main.txtFormulaNotInTable": "A fórmula não está na tabela", "DE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtHyperlink": "Hiperlink", "DE.Controllers.Main.txtIndTooLarge": "Índice muito grande", "DE.Controllers.Main.txtLines": "<PERSON><PERSON>", "DE.Controllers.Main.txtMainDocOnly": "Erro! Documento principal apenas.", "DE.Controllers.Main.txtMath": "Matemática", "DE.Controllers.Main.txtMissArg": "Argumento ausente", "DE.Controllers.Main.txtMissOperator": "Operador ausente", "DE.Controllers.Main.txtNeedSynchronize": "Você tem atualizações", "DE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtNoTableOfContents": "Não há cabeçalhos no documento. Aplique um estilo de cabeçalho ao texto para que ele apareça no índice.", "DE.Controllers.Main.txtNoTableOfFigures": "Nenhuma entrada de tabela de figuras encontrada.", "DE.Controllers.Main.txtNoText": "Erro! Nenhum texto do estilo especificado no documento.", "DE.Controllers.Main.txtNotInTable": "Não está na Tabela", "DE.Controllers.Main.txtNotValidBookmark": "Erro! Não é uma auto-referência de marcador válida.", "DE.Controllers.Main.txtOddPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtOnPage": "na página", "DE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSameAsPrev": "<PERSON><PERSON> Anterior", "DE.Controllers.Main.txtSection": "-Seção", "DE.Controllers.Main.txtSeries": "Série", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Texto explicativo da linha 1 (borda e barra de destaque)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Texto explicativo da linha 2 (borda e barra de destaque)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Texto explicativo da linha 3 (borda e barra de destaque)", "DE.Controllers.Main.txtShape_accentCallout1": "Texto explicativo da linha 1 (barra de destaque)", "DE.Controllers.Main.txtShape_accentCallout2": "Texto explicativo da linha 2 (barra de destaque)", "DE.Controllers.Main.txtShape_accentCallout3": "Texto explicativo da linha 3 (barra de destaque)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Botão Voltar ou Anterior", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Botão Inicial", "DE.Controllers.Main.txtShape_actionButtonBlank": "Botão em branco", "DE.Controllers.Main.txtShape_actionButtonDocument": "Botão Documento", "DE.Controllers.Main.txtShape_actionButtonEnd": "Botão Terminar", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON> ou Avançar", "DE.Controllers.Main.txtShape_actionButtonHelp": "Botão de Ajuda", "DE.Controllers.Main.txtShape_actionButtonHome": "Botão Home", "DE.Controllers.Main.txtShape_actionButtonInformation": "Botão de Informação", "DE.Controllers.Main.txtShape_actionButtonMovie": "Botão de Filme", "DE.Controllers.Main.txtShape_actionButtonReturn": "Botão de Voltar", "DE.Controllers.Main.txtShape_actionButtonSound": "Botão de Som", "DE.Controllers.Main.txtShape_arc": "Arco", "DE.Controllers.Main.txtShape_bentArrow": "Seta curvada", "DE.Controllers.Main.txtShape_bentConnector5": "Con<PERSON>", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "<PERSON><PERSON> <PERSON>", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "<PERSON><PERSON> <PERSON>", "DE.Controllers.Main.txtShape_bentUpArrow": "Seta para cima dobrada", "DE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_blockArc": "Arco de bloco", "DE.Controllers.Main.txtShape_borderCallout1": "Texto explicativo da linha 1", "DE.Controllers.Main.txtShape_borderCallout2": "Texto explicativo da linha 2", "DE.Controllers.Main.txtShape_borderCallout3": "Texto explicativo da linha 3", "DE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_callout1": "Texto explicativo da linha 1 (sem borda)", "DE.Controllers.Main.txtShape_callout2": "Texto explicativo da linha 2 (sem borda)", "DE.Controllers.Main.txtShape_callout3": "Texto explicativo da linha 3 (sem borda)", "DE.Controllers.Main.txtShape_can": "Pode", "DE.Controllers.Main.txtShape_chevron": "Divisa", "DE.Controllers.Main.txtShape_chord": "Acorde", "DE.Controllers.Main.txtShape_circularArrow": "Seta circular", "DE.Controllers.Main.txtShape_cloud": "Nuvem", "DE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_corner": "Canto", "DE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector de seta curvada", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON> <PERSON> Seta Dupla Curvado", "DE.Controllers.Main.txtShape_curvedDownArrow": "Seta Curva para Baixo", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Seta Curva para a Esquerda", "DE.Controllers.Main.txtShape_curvedRightArrow": "Seta Curva para a Direita", "DE.Controllers.Main.txtShape_curvedUpArrow": "Seta Curva para Cima", "DE.Controllers.Main.txtShape_decagon": "Decágono", "DE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "DE.Controllers.Main.txtShape_diamond": "Diamante", "DE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "DE.Controllers.Main.txtShape_donut": "Rosquin<PERSON>", "DE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_downArrow": "Seta para baixo", "DE.Controllers.Main.txtShape_downArrowCallout": "Texto explicativo em seta para baixo", "DE.Controllers.Main.txtShape_ellipse": "Elipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "Fita curvada para baixo", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Fita curvada para cima", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Fluxograma: Processo Alternativo", "DE.Controllers.Main.txtShape_flowChartCollate": "Fluxograma: Agrupar", "DE.Controllers.Main.txtShape_flowChartConnector": "Fluxograma: Conector", "DE.Controllers.Main.txtShape_flowChartDecision": "Fluxograma: Decisão", "DE.Controllers.Main.txtShape_flowChartDelay": "Fluxograma: Atraso", "DE.Controllers.Main.txtShape_flowChartDisplay": "Fluxograma: Exibir", "DE.Controllers.Main.txtShape_flowChartDocument": "Fluxograma: Documento", "DE.Controllers.Main.txtShape_flowChartExtract": "Fluxograma: Extrair", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Fluxograma: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Fluxograma: Armazenamento Interno", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Fluxograma: Disco Magnético", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Fluxograma: Armazenamento de Acesso Direto", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Fluxograma: Armazenamento de Acesso Sequencial", "DE.Controllers.Main.txtShape_flowChartManualInput": "Fluxograma: Entrada Manual", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Fluxograma: Operação Manual", "DE.Controllers.Main.txtShape_flowChartMerge": "Fluxograma: Mesclar", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Fluxograma: V<PERSON><PERSON>s Documentos", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Fluxograma: Conector fora da página", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Fluxograma: Dados <PERSON>", "DE.Controllers.Main.txtShape_flowChartOr": "Fluxograma: Ou", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Fluxograma: Processo Predefinido", "DE.Controllers.Main.txtShape_flowChartPreparation": "Fluxograma: Preparação", "DE.Controllers.Main.txtShape_flowChartProcess": "Fluxograma: Processo", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Fluxograma: Cartão", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Fluxograma: <PERSON><PERSON> perfurada", "DE.Controllers.Main.txtShape_flowChartSort": "Fluxograma: Classificar", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Fluxograma: Junção de Soma", "DE.Controllers.Main.txtShape_flowChartTerminator": "Fluxograma: Terminação", "DE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON> do<PERSON>", "DE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_heart": "Coração", "DE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_hexagon": "Hexágono", "DE.Controllers.Main.txtShape_homePlate": "Pentágono", "DE.Controllers.Main.txtShape_horizontalScroll": "Rolagem Horizontal", "DE.Controllers.Main.txtShape_irregularSeal1": "Explosão 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Explosão 2", "DE.Controllers.Main.txtShape_leftArrow": "Seta para esquerda", "DE.Controllers.Main.txtShape_leftArrowCallout": "Texto explicativo à esquerda", "DE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftBracket": "Colchete Esquerdo", "DE.Controllers.Main.txtShape_leftRightArrow": "Seta da direita para esquerda", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Seta para a esquerda e para a direita", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Seta para Cima Direita Esquerda", "DE.Controllers.Main.txtShape_leftUpArrow": "Seta esquerda e para cima", "DE.Controllers.Main.txtShape_lightningBolt": "Raio", "DE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathDivide": "Divisão", "DE.Controllers.Main.txtShape_mathEqual": "Igual", "DE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "DE.Controllers.Main.txtShape_mathNotEqual": "Não é Igual", "DE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_noSmoking": "Símbolo de \"Não\"", "DE.Controllers.Main.txtShape_notchedRightArrow": "Seta entalhada para a direita", "DE.Controllers.Main.txtShape_octagon": "Octágono", "DE.Controllers.Main.txtShape_parallelogram": "Paralelograma", "DE.Controllers.Main.txtShape_pentagon": "Pentágono", "DE.Controllers.Main.txtShape_pie": "Gráfico de pizza", "DE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_polyline1": "Rabisco", "DE.Controllers.Main.txtShape_polyline2": "Forma livre", "DE.Controllers.Main.txtShape_quadArrow": "Setas Cruzadas", "DE.Controllers.Main.txtShape_quadArrowCallout": "Texto explicativo em seta cruzadas", "DE.Controllers.Main.txtShape_rect": "Re<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_ribbon": "Fita para baixo", "DE.Controllers.Main.txtShape_ribbon2": "Fita para cima", "DE.Controllers.Main.txtShape_rightArrow": "Seta para direita", "DE.Controllers.Main.txtShape_rightArrowCallout": "Texto explicativo em seta à direita", "DE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON> a direita", "DE.Controllers.Main.txtShape_rightBracket": "Colchete direito", "DE.Controllers.Main.txtShape_round1Rect": "Retângulo com Único Canto Arredondado", "DE.Controllers.Main.txtShape_round2DiagRect": "Retângulo Arredondado em um Canto Diagonal", "DE.Controllers.Main.txtShape_round2SameRect": "Retângulo com Canto Arredondado do Mesmo Lado", "DE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_rtTriangle": "Triângulo Retângulo", "DE.Controllers.Main.txtShape_smileyFace": "Rosto sorridente", "DE.Controllers.Main.txtShape_snip1Rect": "Retângulo de canto único recortado", "DE.Controllers.Main.txtShape_snip2DiagRect": "Retângulo de canto diagonal recortado", "DE.Controllers.Main.txtShape_snip2SameRect": "Retângulo com canto recortado do mesmo lado", "DE.Controllers.Main.txtShape_snipRoundRect": "Retângulo com canto recortado e arredondado", "DE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "Estrela de 10 pontas", "DE.Controllers.Main.txtShape_star12": "Estrela de 12 pontas", "DE.Controllers.Main.txtShape_star16": "Estrela de 16 pontas", "DE.Controllers.Main.txtShape_star24": "Estrela de 24 pontas", "DE.Controllers.Main.txtShape_star32": "Estrela de 32 pontos", "DE.Controllers.Main.txtShape_star4": "Estrela de 4 pontas", "DE.Controllers.Main.txtShape_star5": "Estrela de 5 pontas", "DE.Controllers.Main.txtShape_star6": "Estrela de 6 pontas", "DE.Controllers.Main.txtShape_star7": "Estrela de 7 pontas", "DE.Controllers.Main.txtShape_star8": "Estrela de 8 pontas", "DE.Controllers.Main.txtShape_stripedRightArrow": "Seta para a direita listrada", "DE.Controllers.Main.txtShape_sun": "Sol", "DE.Controllers.Main.txtShape_teardrop": "Lágrima", "DE.Controllers.Main.txtShape_textRect": "Caixa de texto", "DE.Controllers.Main.txtShape_trapezoid": "Trapé<PERSON>", "DE.Controllers.Main.txtShape_triangle": "Triângulo", "DE.Controllers.Main.txtShape_upArrow": "Seta para cima", "DE.Controllers.Main.txtShape_upArrowCallout": "Texto explicativo em seta para cima", "DE.Controllers.Main.txtShape_upDownArrow": "Seta para baixo", "DE.Controllers.Main.txtShape_uturnArrow": "Seta em forma de U", "DE.Controllers.Main.txtShape_verticalScroll": "Rolagem Vertical", "DE.Controllers.Main.txtShape_wave": "On<PERSON>", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Texto explicativo oval", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Texto explicativo retangular", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Texto explicativo retangular arredondado", "DE.Controllers.Main.txtStarsRibbons": "Estrelas e Faixas", "DE.Controllers.Main.txtStyle_Caption": "<PERSON>a", "DE.Controllers.Main.txtStyle_endnote_text": "Texto de fim de nota", "DE.Controllers.Main.txtStyle_footnote_text": "Texto de notas de rodapé", "DE.Controllers.Main.txtStyle_Heading_1": "Cabeçalho 1", "DE.Controllers.Main.txtStyle_Heading_2": "Cabeçalho 2", "DE.Controllers.Main.txtStyle_Heading_3": "Cabeçalho 3", "DE.Controllers.Main.txtStyle_Heading_4": "Cabeçalho 4", "DE.Controllers.Main.txtStyle_Heading_5": "Cabeçalho 5", "DE.Controllers.Main.txtStyle_Heading_6": "Cabeçalho 6", "DE.Controllers.Main.txtStyle_Heading_7": "Cabeçalho 7", "DE.Controllers.Main.txtStyle_Heading_8": "Cabeçalho 8", "DE.Controllers.Main.txtStyle_Heading_9": "Cabeçalho 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Citação intensa", "DE.Controllers.Main.txtStyle_List_Paragraph": "Listar <PERSON>", "DE.Controllers.Main.txtStyle_No_Spacing": "Sem espaçamento", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "Citar", "DE.Controllers.Main.txtStyle_Subtitle": "<PERSON>a", "DE.Controllers.Main.txtStyle_Title": "Titulo", "DE.Controllers.Main.txtSyntaxError": "Erro de Sintaxe", "DE.Controllers.Main.txtTableInd": "O índice da tabela não pode ser zero", "DE.Controllers.Main.txtTableOfContents": "Tabela de Conteúdo", "DE.Controllers.Main.txtTableOfFigures": "Tabela de figuras", "DE.Controllers.Main.txtTOCHeading": "Rúbrica TOC", "DE.Controllers.Main.txtTooLarge": "Número Muito Extenso para Formatar", "DE.Controllers.Main.txtTypeEquation": "Digite uma equação aqui.", "DE.Controllers.Main.txtUndefBookmark": "Indicador indefinido", "DE.Controllers.Main.txtXAxis": "Eixo X", "DE.Controllers.Main.txtYAxis": "Eixo Y", "DE.Controllers.Main.txtZeroDivide": "Divisão por zero", "DE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> desconhecido.", "DE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>u navegador não é suportado.", "DE.Controllers.Main.uploadDocExtMessage": "Formato de documento desconhecido.", "DE.Controllers.Main.uploadDocFileCountMessage": "Nenhum documento car<PERSON>.", "DE.Controllers.Main.uploadDocSizeMessage": "Tamanho máximo do documento excedido.", "DE.Controllers.Main.uploadImageExtMessage": "Formato de imagem desconhecido.", "DE.Controllers.Main.uploadImageFileCountMessage": "Sem imagens carregadas.", "DE.Controllers.Main.uploadImageSizeMessage": "Tamanho limite máximo da imagem excedido. O tamanho máximo é de 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Carregando imagem...", "DE.Controllers.Main.uploadImageTitleText": "Carregando imagem", "DE.Controllers.Main.waitText": "Aguarde...", "DE.Controllers.Main.warnBrowserIE9": "O aplicativo tem baixa capacidade no IE9. Usar IE10 ou superior", "DE.Controllers.Main.warnBrowserZoom": "A configuração de zoom atual de seu navegador não é completamente suportada. Redefina para o zoom padrão pressionando Ctrl+0.", "DE.Controllers.Main.warnLicenseExceeded": "Você atingiu o limite de conexões simultâneas para editores %1. Este documento será aberto apenas para visualização.<br>Entre em contato com seu administrador para saber mais.", "DE.Controllers.Main.warnLicenseExp": "Sua licença expirou.<br>Atualize sua licença e refresque a página.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "A licença expirou.<br>Você não tem acesso à funcionalidade de edição de documentos.<br>Por favor, contate seu administrador.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "A licença precisa ser renovada. <br> Você tem acesso limitado à funcionalidade de edição de documentos. <br> Entre em contato com o administrador para obter acesso total.", "DE.Controllers.Main.warnLicenseUsersExceeded": "Você atingiu o limite de usuários para editores %1. Entre em contato com seu administrador para saber mais.", "DE.Controllers.Main.warnNoLicense": "Você atingiu o limite de conexões simultâneas para editores %1. Este documento será aberto apenas para visualização.<br>Entre em contato com a equipe de vendas da %1 para obter os termos de atualização pessoais.", "DE.Controllers.Main.warnNoLicenseUsers": "Você atingiu o limite de usuários para editores %1.<br>Entre em contato com a equipe de vendas da %1 para obter os termos de atualização pessoais.", "DE.Controllers.Main.warnProcessRightsChange": "Foi negado a você o direito de editar o arquivo.", "DE.Controllers.Navigation.txtBeginning": "Início do documento", "DE.Controllers.Navigation.txtGotoBeginning": "Ir para o início do documento", "DE.Controllers.Print.textMarginsLast": "Últimos personalizados", "DE.Controllers.Print.txtCustom": "Personalizado", "DE.Controllers.Print.txtPrintRangeInvalid": "Intervalo de impressão inválido", "DE.Controllers.Print.txtPrintRangeSingleRange": "Digite um único número de página ou um único intervalo de páginas (por exemplo, 5-12). Ou você pode imprimir em PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "Aviso", "DE.Controllers.Search.textNoTextFound": "Os dados que você tem estado procurando não podem ser encontrados. Ajuste suas opções de pesquisa.", "DE.Controllers.Search.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "DE.Controllers.Search.textReplaceSuccess": "A pesquisa foi feita. {0} ocorrências foram substituídas", "DE.Controllers.Search.warnReplaceString": "{0} não é um caractere especial válido para a caixa Substituir por.", "DE.Controllers.Statusbar.textDisconnect": "<b>A conexão foi perdida</b><br>Tentando conectar. Verifique as configurações de conexão.", "DE.Controllers.Statusbar.textHasChanges": "New changes have been tracked", "DE.Controllers.Statusbar.textSetTrackChanges": "Você está em modo de rastreamento de alterações", "DE.Controllers.Statusbar.textTrackChanges": "The document is opened with the Track Changes mode enabled", "DE.Controllers.Statusbar.tipReview": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Statusbar.zoomText": "Ampliação {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "A fonte que você vai salvar não está disponível no dispositivo atual.<br>O estilo de texto será exibido usando uma das fontes do sistema, a fonte salva será usada quando ela estiver disponível.<br>Você deseja continuar?", "DE.Controllers.Toolbar.dataUrl": "Colar uma URL de dados", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Aviso", "DE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textEmptyImgUrl": "Você precisa especificar uma URL de imagem.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "Você precisa especificar o URL.", "DE.Controllers.Toolbar.textFontSizeErr": "O valor inserido está incorreto.<br>Insira um valor numérico entre 1 e 300", "DE.Controllers.Toolbar.textFraction": "Frações", "DE.Controllers.Toolbar.textFunction": "Funções", "DE.Controllers.Toolbar.textGroup": "Grupo", "DE.Controllers.Toolbar.textInsert": "Inserir", "DE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "DE.Controllers.Toolbar.textLargeOperator": "Grandes operadores", "DE.Controllers.Toolbar.textLimitAndLog": "Limites e logaritmos", "DE.Controllers.Toolbar.textMatrix": "Matrizes", "DE.Controllers.Toolbar.textOperator": "Operadores", "DE.Controllers.Toolbar.textRadical": "Radicais", "DE.Controllers.Toolbar.textRecentlyUsed": "Usado recentemente", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textTabForms": "Formulários", "DE.Controllers.Toolbar.textWarning": "Aviso", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Seta para direita-esquerda acima", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Seta adiante para cima", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Seta para direita acima", "DE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "DE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "DE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "DE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON> (com Espaço Reservado)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "F<PERSON>rmula embalada(Exemplo)", "DE.Controllers.Toolbar.txtAccent_Check": "Verificar", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave <PERSON>", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Chave Superior", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vetor A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "Barra superior com ABC", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y com barra superior", "DE.Controllers.Toolbar.txtAccent_DDDot": "Ponto <PERSON>lo", "DE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Dot": "Ponto", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra superior dupla", "DE.Controllers.Toolbar.txtAccent_Grave": "Grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Agrupamento de caracteres abaixo", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Agrupamento de caracteres acima", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpão adiante para cima", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpão para direita acima", "DE.Controllers.Toolbar.txtAccent_Hat": "Acento circunflexo", "DE.Controllers.Toolbar.txtAccent_Smile": "Breve", "DE.Controllers.Toolbar.txtAccent_Tilde": "Til", "DE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parênteses com separadores", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parênteses com separadores", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Colchete de ângulo reto", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Curve": "Colchetes", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Colchetes com separador", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Colchete direito", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "colchete esquerdo", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (Duas Condições)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (Três Condições)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Objeto empil<PERSON>o entre parênteses", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente binominal", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binominal", "DE.Controllers.Toolbar.txtBracket_Line": "Barras verticais", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Barra vertical direita", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Barra vertical esquerda", "DE.Controllers.Toolbar.txtBracket_LineDouble": "Barras verticais duplas", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Barra vertical dupla direita", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Barra vertical dupla esquerda", "DE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON> dire<PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parênteses com separadores", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parê<PERSON><PERSON> direito", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square": "Colchetes", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Espaço reservado entre dois colchetes direitos", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Colchetes invertidos", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Colchete direito", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Colchete esquerdo", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Espaço reservado entre dois colchetes esquerdos", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "Colchetes duplos", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Colchete duplo direito", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Col<PERSON>e duplo es<PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim": "Teto", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON> direito", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtFractionDiagonal": "Fração inclinada", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Der<PERSON>da", "DE.Controllers.Toolbar.txtFractionDifferential_2": "limite delta y sobre limite delta x", "DE.Controllers.Toolbar.txtFractionDifferential_3": "y parcial sobre x parcial", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Delta y sobre delta x", "DE.Controllers.Toolbar.txtFractionHorizontal": "Fração linear", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi sobre 2", "DE.Controllers.Toolbar.txtFractionSmall": "Fração pequena", "DE.Controllers.Toolbar.txtFractionVertical": "Fração Empilhada", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Função cosseno inverso", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Função cosseno inverso hiperbólico", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Função cotangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Função cotangente inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Função cossecante inversa", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Função cossecante inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Função secante inversa", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Função secante inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Função seno inverso", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Função seno inverso hiperbólico", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Função tangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Função tangente inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_Cos": "Função cosseno", "DE.Controllers.Toolbar.txtFunction_Cosh": "Função cosseno hiperbólico", "DE.Controllers.Toolbar.txtFunction_Cot": "Função cotangente", "DE.Controllers.Toolbar.txtFunction_Coth": "Função cotangente hiperbólica", "DE.Controllers.Toolbar.txtFunction_Csc": "Função cossecante", "DE.Controllers.Toolbar.txtFunction_Csch": "Função co-secante hiperbólica", "DE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON>ta seno", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON> da tangente", "DE.Controllers.Toolbar.txtFunction_Sec": "Função secante", "DE.Controllers.Toolbar.txtFunction_Sech": "Função secante hiperbólica", "DE.Controllers.Toolbar.txtFunction_Sin": "Função de seno", "DE.Controllers.Toolbar.txtFunction_Sinh": "Função seno hiperbólico", "DE.Controllers.Toolbar.txtFunction_Tan": "Função da tangente", "DE.Controllers.Toolbar.txtFunction_Tanh": "Função tangente hiperbólica", "DE.Controllers.Toolbar.txtIntegral": "Integral", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Teta diferencial", "DE.Controllers.Toolbar.txtIntegral_dx": "Derivada x", "DE.Controllers.Toolbar.txtIntegral_dy": "Derivada y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral com limites empilhados", "DE.Controllers.Toolbar.txtIntegralDouble": "Integra<PERSON> dupla", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral dupla com limites empilhados", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral dupla com limites", "DE.Controllers.Toolbar.txtIntegralOriented": "Integral de linha", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno com limites empilhados", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de Superfície", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superfície com limites empilhados", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superfície com limites", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de linha", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume Integral", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volume com limites empilhados", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume Integral", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "DE.Controllers.Toolbar.txtIntegralTriple": "Integral Tripla", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral Tripla", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral Tripla", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Lógico e", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Lógico E com limite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Lógico E com limites", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Lógico E com limite inferior subscrito", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Lógico E com limites subscritos/sobrescritos", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproduto com limite inferior", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproduto com limites", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-produto com limite inferior subscrito", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproduto com limites subscritos/sobrescritos", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Soma sobre k de n escolha k", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Soma de i igual a zero a n", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Exemplo de soma usando dois índices", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Exemplo de produto", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "União", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Lógico ou", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Lógico Ou com limite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Lógico Ou com limites", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Lógico Ou com limite inferior subscrito", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Ou Lógico com limites subscritos/sobrescritos", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Interseção", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Interseção com limite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Interseção", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Interseção com limite inferior subscrito", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Interseção com limites subscritos/sobrescritos", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produto com limite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produto com limites", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produto com limite inferior subscrito", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produto com limites subscritos/sobrescritos", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Soma com limite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Soma com limites", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Soma com limite inferior subscrito", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Soma com limites subscritos/sobrescritos", "DE.Controllers.Toolbar.txtLargeOperator_Union": "União", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "União com limite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "União com limites", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "União com limite inferior subscrito", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "União com limites subscritos/sobrescritos", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo limite", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo máximo", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "DE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtMarginsH": "Margens superior e inferior são muito altas para uma determinada altura da página", "DE.Controllers.Toolbar.txtMarginsW": "Margens são muito grandes para uma determinada largura da página", "DE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON><PERSON> 1x2", "DE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON><PERSON> 1x3", "DE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON><PERSON> 2x1", "DE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON><PERSON> 2x2", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON><PERSON> 2x3", "DE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON><PERSON> Vazi<PERSON> 3x1", "DE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON><PERSON> 3x2", "DE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON><PERSON> 3x3", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pontos de linha de base", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Pontos de linha média", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Pontos diagonais", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Pontos verticais", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> entre parêntes<PERSON>", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Matriz esparsa em parênteses", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON>riz da identidade 2x2", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "<PERSON>riz da identidade 2x2", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz da identidade 3x3", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz da identidade 3x3", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Seta para direita esquerda abaixo", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Seta para direita-esquerda acima", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Seta adiante para baixo", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Seta adiante para cima", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Seta para direita abaixo", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Seta para direita acima", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Dois-pontos-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Resultados de Delta", "DE.Controllers.Toolbar.txtOperator_Definition": "Igual a por definição", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Seta para direita esquerda abaixo", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Seta para direita-esquerda acima", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Seta adiante para baixo", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Seta adiante para cima", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Seta para direita abaixo", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Rightwards Arrow Above", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Sinal de Igual-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "Sinal de Menos-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Sinal de Mais-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Lado direito da fórmula quadrática", "DE.Controllers.Toolbar.txtRadicalCustom_2": "Raiz quadrada de a ao quadrado mais b ao quadrado", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Raiz quadrada com grau", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Raiz cúbica", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Radical com grau", "DE.Controllers.Toolbar.txtRadicalSqrt": "Raiz quadrada", "DE.Controllers.Toolbar.txtScriptCustom_1": "x subscrito y ao quadrado", "DE.Controllers.Toolbar.txtScriptCustom_2": "e elevado a menos i ômega t", "DE.Controllers.Toolbar.txtScriptCustom_3": "x ao quadrado", "DE.Controllers.Toolbar.txtScriptCustom_4": "Y sobrescrito à esquerda n subscrito à esquerda um", "DE.Controllers.Toolbar.txtScriptSub": "Subscrito", "DE.Controllers.Toolbar.txtScriptSubSup": "Subscrito-Sobrescrito", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "LeftSubscript-Superscript", "DE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "DE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "DE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "DE.Controllers.Toolbar.txtSymbol_approx": "Quase igual a", "DE.Controllers.Toolbar.txtSymbol_ast": "Operador de asterisco", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "DE.Controllers.Toolbar.txtSymbol_bullet": "Operador de marcador", "DE.Controllers.Toolbar.txtSymbol_cap": "Interseção", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Raiz cúbica", "DE.Controllers.Toolbar.txtSymbol_cdots": "Reticências horizontais de linha média", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "DE.Controllers.Toolbar.txtSymbol_cup": "União", "DE.Controllers.Toolbar.txtSymbol_ddots": "Reticências diagonal para baixo à direita", "DE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Sinal de divisão", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Seta para baixo", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vazio", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsílon", "DE.Controllers.Toolbar.txtSymbol_equals": "Igual", "DE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON><PERSON> a", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "Existe", "DE.Controllers.Toolbar.txtSymbol_factorial": "Fatorial", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON>us Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "DE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "DE.Controllers.Toolbar.txtSymbol_gg": "Muito superior a", "DE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "DE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "DE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infinidade", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Capa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Seta para esquerda", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Seta esquerda-direita", "DE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "DE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "DE.Controllers.Toolbar.txtSymbol_ll": "Muito inferior a", "DE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_mp": "Sinal de Menos-Sinal de <PERSON>", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "Não igual a", "DE.Controllers.Toolbar.txtSymbol_ni": "Contém como membro", "DE.Controllers.Toolbar.txtSymbol_not": "Não entrar", "DE.Controllers.Toolbar.txtSymbol_notexists": "Não existe", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Ômega", "DE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "DE.Controllers.Toolbar.txtSymbol_percent": "Porcentagem", "DE.Controllers.Toolbar.txtSymbol_phi": "Fi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_pm": "Sinal de Menos-Sinal de Igual", "DE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "Quarta raiz", "DE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> da prova", "DE.Controllers.Toolbar.txtSymbol_rddots": "Reticências diagonal direitas para cima", "DE.Controllers.Toolbar.txtSymbol_rho": "Rô", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Seta para direita", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de Radical", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Portanto", "DE.Controllers.Toolbar.txtSymbol_theta": "Teta", "DE.Controllers.Toolbar.txtSymbol_times": "Sinal de multiplicação", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Seta para cima", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON> de Epsílon", "DE.Controllers.Toolbar.txtSymbol_varphi": "Variante de fi", "DE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vdots": "Reticências verticais", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "Ajustar a página", "DE.Controllers.Viewport.textFitWidth": "<PERSON><PERSON><PERSON> largura", "DE.Controllers.Viewport.txtDarkMode": "<PERSON><PERSON> es<PERSON>ro", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Etiqueta:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "Etiqueta não deve estar vazia.", "DE.Views.BookmarksDialog.textAdd": "Incluir", "DE.Views.BookmarksDialog.textBookmarkName": "Nome do favorito", "DE.Views.BookmarksDialog.textClose": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textCopy": "Copiar", "DE.Views.BookmarksDialog.textDelete": "Excluir", "DE.Views.BookmarksDialog.textGetLink": "Obter link", "DE.Views.BookmarksDialog.textGoto": "<PERSON>r <PERSON>", "DE.Views.BookmarksDialog.textHidden": "Favoritos ocultos", "DE.Views.BookmarksDialog.textLocation": "Localização", "DE.Views.BookmarksDialog.textName": "Nome", "DE.Views.BookmarksDialog.textSort": "Ordenar por", "DE.Views.BookmarksDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.txtInvalidName": "O nome do marcador só pode conter letras, dígitos e sublinhados, e deve começar com a letra", "DE.Views.CaptionDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textAfter": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textBefore": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textCaption": "<PERSON>a", "DE.Views.CaptionDialog.textChapter": "Capítulo começa com estilo", "DE.Views.CaptionDialog.textChapterInc": "Inclui o número do capítulo", "DE.Views.CaptionDialog.textColon": "<PERSON><PERSON> pontos", "DE.Views.CaptionDialog.textDash": "traço", "DE.Views.CaptionDialog.textDelete": "Excluir", "DE.Views.CaptionDialog.textEquation": "Equação", "DE.Views.CaptionDialog.textExamples": "Exemplos: Tabela 2-<PERSON>, Imagem 1.IV", "DE.Views.CaptionDialog.textExclude": "Excluir ró<PERSON><PERSON> da legenda", "DE.Views.CaptionDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textHyphen": "Hífen", "DE.Views.CaptionDialog.textInsert": "Inserir", "DE.Views.CaptionDialog.textLabel": "Etiqueta", "DE.Views.CaptionDialog.textLongDash": "traço longo", "DE.Views.CaptionDialog.textNumbering": "Numeração", "DE.Views.CaptionDialog.textPeriod": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textSeparator": "Use separador", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "Inserir <PERSON>", "DE.Views.CellsAddDialog.textCol": "Colunas", "DE.Views.CellsAddDialog.textDown": "Abaixo do cursor", "DE.Views.CellsAddDialog.textLeft": "Para esquerda", "DE.Views.CellsAddDialog.textRight": "Para direita", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "Insira vários", "DE.Views.CellsAddDialog.textUp": "Acima do cursor", "DE.Views.ChartSettings.text3dDepth": "Profundidade (% da base)", "DE.Views.ChartSettings.text3dHeight": "Altura (% da base)", "DE.Views.ChartSettings.text3dRotation": "Rotação 3D", "DE.Views.ChartSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "DE.Views.ChartSettings.textAutoscale": "Autoescala", "DE.Views.ChartSettings.textChartType": "Alterar tipo de gráfico", "DE.Views.ChartSettings.textDefault": "Rotação padrão", "DE.Views.ChartSettings.textDown": "Abaixo", "DE.Views.ChartSettings.textEditData": "<PERSON><PERSON> dad<PERSON>", "DE.Views.ChartSettings.textHeight": "Altura", "DE.Views.ChartSettings.textLeft": "E<PERSON>rda", "DE.Views.ChartSettings.textNarrow": "Campo de visão estreito", "DE.Views.ChartSettings.textOriginalSize": "<PERSON><PERSON><PERSON> at<PERSON>", "DE.Views.ChartSettings.textPerspective": "Perspectiva", "DE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textRightAngle": "Eixos de ângulo reto", "DE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textUndock": "Desencaixar do painel", "DE.Views.ChartSettings.textUp": "Para cima", "DE.Views.ChartSettings.textWiden": "Ampliar o campo de visão", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Estilo da quebra automática", "DE.Views.ChartSettings.textX": "Rotação X", "DE.Views.ChartSettings.textY": "Rotação Y", "DE.Views.ChartSettings.txtBehind": "Atrás", "DE.Views.ChartSettings.txtInFront": "<PERSON> frente", "DE.Views.ChartSettings.txtInline": "<PERSON> linha", "DE.Views.ChartSettings.txtSquare": "Quadrado", "DE.Views.ChartSettings.txtThrough": "Através", "DE.Views.ChartSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ChartSettings.txtTitle": "Gráfico", "DE.Views.ChartSettings.txtTopAndBottom": "Parte superior e inferior", "DE.Views.ControlSettingsDialog.strGeneral": "G<PERSON>", "DE.Views.ControlSettingsDialog.textAdd": "Incluir", "DE.Views.ControlSettingsDialog.textAppearance": "Aparência", "DE.Views.ControlSettingsDialog.textApplyAll": "Aplicar a Todos", "DE.Views.ControlSettingsDialog.textBox": "Caixa delimitadora", "DE.Views.ControlSettingsDialog.textChange": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCheckbox": "Caixa de seleção", "DE.Views.ControlSettingsDialog.textChecked": "<PERSON><PERSON><PERSON><PERSON> marcado", "DE.Views.ControlSettingsDialog.textColor": "Cor", "DE.Views.ControlSettingsDialog.textCombobox": "Caixa de combinação", "DE.Views.ControlSettingsDialog.textDate": "Formato de data", "DE.Views.ControlSettingsDialog.textDelete": "Excluir", "DE.Views.ControlSettingsDialog.textDisplayName": "Nome de exibição", "DE.Views.ControlSettingsDialog.textDown": "Abaixo", "DE.Views.ControlSettingsDialog.textDropDown": "Lista suspensa", "DE.Views.ControlSettingsDialog.textFormat": "Mostra a data assim", "DE.Views.ControlSettingsDialog.textLang": "Idioma", "DE.Views.ControlSettingsDialog.textLock": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textName": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textPlaceholder": "Marcador de posição", "DE.Views.ControlSettingsDialog.textShowAs": "<PERSON>ibir como", "DE.Views.ControlSettingsDialog.textSystemColor": "Sistema", "DE.Views.ControlSettingsDialog.textTag": "Etiqueta", "DE.Views.ControlSettingsDialog.textTitle": "Propriedades do controle de conteúdo", "DE.Views.ControlSettingsDialog.textUnchecked": "Símbolo não verificado", "DE.Views.ControlSettingsDialog.textUp": "Para cima", "DE.Views.ControlSettingsDialog.textValue": "Valor", "DE.Views.ControlSettingsDialog.tipChange": "Símbolo de Alterar", "DE.Views.ControlSettingsDialog.txtLockDelete": "Controle de conteúdo não pode ser excluído", "DE.Views.ControlSettingsDialog.txtLockEdit": "Conteúdo não pode ser editado", "DE.Views.CrossReferenceDialog.textAboveBelow": "Acima/Abaixo", "DE.Views.CrossReferenceDialog.textBookmark": "Marcador", "DE.Views.CrossReferenceDialog.textBookmarkText": "<PERSON>ar texto", "DE.Views.CrossReferenceDialog.textCaption": "<PERSON><PERSON><PERSON><PERSON> completo", "DE.Views.CrossReferenceDialog.textEmpty": "A referência do pedido está vazia.", "DE.Views.CrossReferenceDialog.textEndnote": "Nota final", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Número de Nota Final", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Número da nota final (formatado)", "DE.Views.CrossReferenceDialog.textEquation": "Equação", "DE.Views.CrossReferenceDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textFootnote": "<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textHeading": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textHeadingNum": "Número do cabeçalho", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Número do cabeçalho (contexto completo)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Número do cabeçalho (sem contexto)", "DE.Views.CrossReferenceDialog.textHeadingText": "Texto do título", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Incluir acima/abaixo", "DE.Views.CrossReferenceDialog.textInsert": "Inserir", "DE.Views.CrossReferenceDialog.textInsertAs": "Inserir como hiperlink", "DE.Views.CrossReferenceDialog.textLabelNum": "Apenas etiqueta e número", "DE.Views.CrossReferenceDialog.textNoteNum": "Númer<PERSON> da nota de rodapé", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Número da nota de rodapé (formatado)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Apenas texto de legenda", "DE.Views.CrossReferenceDialog.textPageNum": "Número da página", "DE.Views.CrossReferenceDialog.textParagraph": "Item numerado", "DE.Views.CrossReferenceDialog.textParaNum": "Número do parágrafo", "DE.Views.CrossReferenceDialog.textParaNumFull": "Número do parágrafo (contexto completo)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Número do parágrafo (sem contexto)", "DE.Views.CrossReferenceDialog.textSeparate": "Números separados com", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Texto do parágrafo", "DE.Views.CrossReferenceDialog.textWhich": "Para qual legenda", "DE.Views.CrossReferenceDialog.textWhichBookmark": "Para qual favorito", "DE.Views.CrossReferenceDialog.textWhichEndnote": "Para qual nota final", "DE.Views.CrossReferenceDialog.textWhichHeading": "Para qual título", "DE.Views.CrossReferenceDialog.textWhichNote": "Para qual nota de rodapé", "DE.Views.CrossReferenceDialog.textWhichPara": "Para qual item numerado", "DE.Views.CrossReferenceDialog.txtReference": "Inserir referência a", "DE.Views.CrossReferenceDialog.txtTitle": "Referência cruzada", "DE.Views.CrossReferenceDialog.txtType": "Tipo de referência", "DE.Views.CustomColumnsDialog.textColumns": "Número de <PERSON>nas", "DE.Views.CustomColumnsDialog.textSeparator": "Divisor de coluna", "DE.Views.CustomColumnsDialog.textSpacing": "Espaçamento entre colunas", "DE.Views.CustomColumnsDialog.textTitle": "Colunas", "DE.Views.DateTimeDialog.confirmDefault": "Definir formato padrão para {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Definir como padrão", "DE.Views.DateTimeDialog.textFormat": "Formatos", "DE.Views.DateTimeDialog.textLang": "Idioma", "DE.Views.DateTimeDialog.textUpdate": "Atualizar automaticamente", "DE.Views.DateTimeDialog.txtTitle": "Data e Hora", "DE.Views.DocProtection.hintProtectDoc": "Proteger o Documento", "DE.Views.DocProtection.txtDocProtectedComment": "O documento está protegido.<br>Voc<PERSON> só pode inserir comentários neste documento.", "DE.Views.DocProtection.txtDocProtectedForms": "O documento está protegido.<br>Voc<PERSON> só pode preencher formulários neste documento.", "DE.Views.DocProtection.txtDocProtectedTrack": "O documento está protegido.<br><PERSON><PERSON><PERSON> pode editar este documento, mas todas as alterações serão rastreadas.", "DE.Views.DocProtection.txtDocProtectedView": "O documento está protegido.<br>Você só pode visualizar este documento.", "DE.Views.DocProtection.txtDocUnlockDescription": "Digite uma senha para desproteger o documento", "DE.Views.DocProtection.txtProtectDoc": "Proteger o documento", "DE.Views.DocProtection.txtUnlockTitle": "Desproteger documento", "DE.Views.DocumentHolder.aboveText": "Acima", "DE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.advancedDropCapText": "Configurações de capitulação", "DE.Views.DocumentHolder.advancedEquationText": "Definições de equações", "DE.Views.DocumentHolder.advancedFrameText": "Configurações avançadas de moldura", "DE.Views.DocumentHolder.advancedParagraphText": "Configurações avançadas de parágrafo", "DE.Views.DocumentHolder.advancedTableText": "Configurações avançadas de tabela", "DE.Views.DocumentHolder.advancedText": "Configurações avançadas", "DE.Views.DocumentHolder.alignmentText": "Alinhamento", "DE.Views.DocumentHolder.allLinearText": "Tudo - Linear", "DE.Views.DocumentHolder.allProfText": "Tudo - Profissional", "DE.Views.DocumentHolder.belowText": "Abaixo", "DE.Views.DocumentHolder.breakBeforeText": "Quebra de página antes", "DE.Views.DocumentHolder.bulletsText": "Marcadores e numeração", "DE.Views.DocumentHolder.cellAlignText": "Alinhamento vertical da célula", "DE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.centerText": "Centro", "DE.Views.DocumentHolder.chartText": "Configurações avançadas de gráfico", "DE.Views.DocumentHolder.columnText": "Coluna", "DE.Views.DocumentHolder.currLinearText": "Atual - Linear", "DE.Views.DocumentHolder.currProfText": "Atual - Profissional", "DE.Views.DocumentHolder.deleteColumnText": "Excluir coluna", "DE.Views.DocumentHolder.deleteRowText": "Excluir linha", "DE.Views.DocumentHolder.deleteTableText": "Excluir tabela", "DE.Views.DocumentHolder.deleteText": "Excluir", "DE.Views.DocumentHolder.direct270Text": "Girar o texto para cima", "DE.Views.DocumentHolder.direct90Text": "G<PERSON>r o texto para baixo", "DE.Views.DocumentHolder.directHText": "Horizontal", "DE.Views.DocumentHolder.directionText": "Text Direction", "DE.Views.DocumentHolder.editChartText": "<PERSON><PERSON> dad<PERSON>", "DE.Views.DocumentHolder.editFooterText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.editHeaderText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.eqToInlineText": "Alterar para em linha", "DE.Views.DocumentHolder.guestText": "Visitante", "DE.Views.DocumentHolder.hyperlinkText": "Hiperlink", "DE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> tudo", "DE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.imageText": "Configurações avançadas de imagem", "DE.Views.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "DE.Views.DocumentHolder.insertColumnRightText": "Coluna direita", "DE.Views.DocumentHolder.insertColumnText": "Inserir coluna", "DE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON> linha", "DE.Views.DocumentHolder.insertText": "Inserir", "DE.Views.DocumentHolder.keepLinesText": "<PERSON><PERSON> as linhas juntas", "DE.Views.DocumentHolder.langText": "Selecionar idioma", "DE.Views.DocumentHolder.latexText": "LaTex", "DE.Views.DocumentHolder.leftText": "E<PERSON>rda", "DE.Views.DocumentHolder.loadSpellText": "Carregando variantes...", "DE.Views.DocumentHolder.mergeCellsText": "Mesclar célu<PERSON>", "DE.Views.DocumentHolder.moreText": "Mais variantes...", "DE.Views.DocumentHolder.noSpellVariantsText": "Sem varientes", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Aviso", "DE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.paragraphText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.removeHyperlinkText": "Remover hiperlink", "DE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.saveStyleText": "Criar novo estilo", "DE.Views.DocumentHolder.selectCellText": "Selecionar célula", "DE.Views.DocumentHolder.selectColumnText": "Selecionar coluna", "DE.Views.DocumentHolder.selectRowText": "Selecionar linha", "DE.Views.DocumentHolder.selectTableText": "Selecionar tabela", "DE.Views.DocumentHolder.selectText": "Selecionar", "DE.Views.DocumentHolder.shapeText": "Configurações avançadas de forma", "DE.Views.DocumentHolder.spellcheckText": "Verificação ortográfica", "DE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON>...", "DE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.strDelete": "Remover assinatura", "DE.Views.DocumentHolder.strDetails": "<PERSON><PERSON><PERSON> da Assinatura", "DE.Views.DocumentHolder.strSetup": "Configuração da Assinatura", "DE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.styleText": "Formatar como Estilo", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "Aceitar alteração", "DE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrange": "Organizar", "DE.Views.DocumentHolder.textArrangeBack": "Enviar para plano de fundo", "DE.Views.DocumentHolder.textArrangeBackward": "Enviar para trás", "DE.Views.DocumentHolder.textArrangeForward": "Trazer para frente", "DE.Views.DocumentHolder.textArrangeFront": "Trazer para primeiro plano", "DE.Views.DocumentHolder.textCells": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCol": "Excluir coluna", "DE.Views.DocumentHolder.textContentControls": "Controle de conteúdo", "DE.Views.DocumentHolder.textContinueNumbering": "Continuar numerando", "DE.Views.DocumentHolder.textCopy": "Copiar", "DE.Views.DocumentHolder.textCrop": "Cortar", "DE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCropFit": "Ajustar", "DE.Views.DocumentHolder.textCut": "Cortar", "DE.Views.DocumentHolder.textDistributeCols": "Distribuir colunas", "DE.Views.DocumentHolder.textDistributeRows": "Distribuir linhas", "DE.Views.DocumentHolder.textEditControls": "Propriedades do controle de conteúdo", "DE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textEditWrapBoundary": "Editar limite de disposição", "DE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "DE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "DE.Views.DocumentHolder.textFollow": "<PERSON><PERSON><PERSON> movimento", "DE.Views.DocumentHolder.textFromFile": "Do Arquivo", "DE.Views.DocumentHolder.textFromStorage": "De armazenamento", "DE.Views.DocumentHolder.textFromUrl": "Da URL", "DE.Views.DocumentHolder.textJoinList": "Junta-se <PERSON> lista anterior", "DE.Views.DocumentHolder.textLeft": "Deslocar células para a esquerda", "DE.Views.DocumentHolder.textNest": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textNextPage": "Próxima página", "DE.Views.DocumentHolder.textNumberingValue": "Valor de numeração", "DE.Views.DocumentHolder.textPaste": "Colar", "DE.Views.DocumentHolder.textPrevPage": "Página anterior", "DE.Views.DocumentHolder.textRefreshField": "Atualizar o campo", "DE.Views.DocumentHolder.textReject": "Rejeitar alteração", "DE.Views.DocumentHolder.textRemCheckBox": "Remover caixa de seleção", "DE.Views.DocumentHolder.textRemComboBox": "Remover caixa de combinação", "DE.Views.DocumentHolder.textRemDropdown": "Remover lista suspensa", "DE.Views.DocumentHolder.textRemField": "Remover Campo de Texto", "DE.Views.DocumentHolder.textRemove": "Excluir", "DE.Views.DocumentHolder.textRemoveControl": "Remover controle de conteúdo", "DE.Views.DocumentHolder.textRemPicture": "Remover imagem", "DE.Views.DocumentHolder.textRemRadioBox": "Remover botão de rádio", "DE.Views.DocumentHolder.textReplace": "Substituir imagem", "DE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate270": "Girar 90º no sentido anti-horário.", "DE.Views.DocumentHolder.textRotate90": "Girar 90º no sentido horário", "DE.Views.DocumentHolder.textRow": "Excluir linha", "DE.Views.DocumentHolder.textSeparateList": "Lista separada", "DE.Views.DocumentHolder.textSettings": "Configurações", "DE.Views.DocumentHolder.textSeveral": "<PERSON><PERSON><PERSON><PERSON>/Colunas", "DE.Views.DocumentHolder.textShapeAlignBottom": "Alinhar à parte inferior", "DE.Views.DocumentHolder.textShapeAlignCenter": "Alinhar ao centro", "DE.Views.DocumentHolder.textShapeAlignLeft": "Alinhar à esquerda", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Alinhar ao Centro", "DE.Views.DocumentHolder.textShapeAlignRight": "Alinhar à direita", "DE.Views.DocumentHolder.textShapeAlignTop": "Alinhar à parte superior", "DE.Views.DocumentHolder.textStartNewList": "Começar nova lista", "DE.Views.DocumentHolder.textStartNumberingFrom": "Definir valor de numeração", "DE.Views.DocumentHolder.textTitleCellsRemove": "Excluir <PERSON>", "DE.Views.DocumentHolder.textTOC": "Tabela de Conteúdo", "DE.Views.DocumentHolder.textTOCSettings": "Definições da tabela de conteúdo", "DE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textUpdateAll": "<PERSON><PERSON><PERSON><PERSON> toda a tabela", "DE.Views.DocumentHolder.textUpdatePages": "Atualizar somente os números de páginas", "DE.Views.DocumentHolder.textUpdateTOC": "Atualizar a tabela de conteúdo", "DE.Views.DocumentHolder.textWrap": "Estilo da quebra automática", "DE.Views.DocumentHolder.tipIsLocked": "Este elemento está sendo atualmente editado por outro usuário.", "DE.Views.DocumentHolder.toDictionaryText": "Incluir no Dicionário", "DE.Views.DocumentHolder.txtAddBottom": "Adicionar borda inferior", "DE.Views.DocumentHolder.txtAddFractionBar": "Adicionar barra de fração", "DE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.DocumentHolder.txtAddLB": "Adicionar linha inferior esquerda", "DE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON> borda es<PERSON>", "DE.Views.DocumentHolder.txtAddLT": "Adicionar linha superior esquerda", "DE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>r borda direita", "DE.Views.DocumentHolder.txtAddTop": "Adicionar borda superior", "DE.Views.DocumentHolder.txtAddVer": "Adicionar lin<PERSON>", "DE.Views.DocumentHolder.txtAlignToChar": "Alinhar à símbolo", "DE.Views.DocumentHolder.txtBehind": "Atrás do texto", "DE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON>a", "DE.Views.DocumentHolder.txtBottom": "Inferior", "DE.Views.DocumentHolder.txtColumnAlign": "Alinhamento de colunas", "DE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON> de <PERSON>o", "DE.Views.DocumentHolder.txtDeleteArg": "Excluir argumento", "DE.Views.DocumentHolder.txtDeleteBreak": "Eliminar quebra manual", "DE.Views.DocumentHolder.txtDeleteChars": "Excluir caracteres anexos ", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Excluir separadores e caracteres anexos", "DE.Views.DocumentHolder.txtDeleteEq": "Remover equação", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Excluir caractere", "DE.Views.DocumentHolder.txtDeleteRadical": "Eliminar radical", "DE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "DE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "DE.Views.DocumentHolder.txtEmpty": "(Vazio)", "DE.Views.DocumentHolder.txtFractionLinear": "Alterar para fração linear", "DE.Views.DocumentHolder.txtFractionSkewed": "Alterar para fração inclinada", "DE.Views.DocumentHolder.txtFractionStacked": "Alterar para fração empilhada", "DE.Views.DocumentHolder.txtGroup": "Agrupar", "DE.Views.DocumentHolder.txtGroupCharOver": "Caractere sobre texto", "DE.Views.DocumentHolder.txtGroupCharUnder": "Caractere sob texto", "DE.Views.DocumentHolder.txtHideBottom": "Ocultar borda inferior", "DE.Views.DocumentHolder.txtHideBottomLimit": "Ocultar limite inferior", "DE.Views.DocumentHolder.txtHideCloseBracket": "Ocultar colchete de fechamento", "DE.Views.DocumentHolder.txtHideDegree": "Ocultar grau", "DE.Views.DocumentHolder.txtHideHor": "Ocultar linha horizontal", "DE.Views.DocumentHolder.txtHideLB": "Ocultar linha inferior esquerda", "DE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON>r borda esquer<PERSON>", "DE.Views.DocumentHolder.txtHideLT": "Ocultar linha superior esquerda", "DE.Views.DocumentHolder.txtHideOpenBracket": "Ocultar colchete de abertura", "DE.Views.DocumentHolder.txtHidePlaceholder": "Ocultar espaço reservado", "DE.Views.DocumentHolder.txtHideRight": "O<PERSON>ltar borda direita", "DE.Views.DocumentHolder.txtHideTop": "Ocultar borda superior", "DE.Views.DocumentHolder.txtHideTopLimit": "Ocultar limite superior", "DE.Views.DocumentHolder.txtHideVer": "Ocultar linha vertical", "DE.Views.DocumentHolder.txtIncreaseArg": "Aumentar o tamanho do argumento", "DE.Views.DocumentHolder.txtInFront": "<PERSON> frente", "DE.Views.DocumentHolder.txtInline": "Alinhado com o Texto", "DE.Views.DocumentHolder.txtInsertArgAfter": "Inserir argumento após", "DE.Views.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "DE.Views.DocumentHolder.txtInsertBreak": "Inserir quebra manual", "DE.Views.DocumentHolder.txtInsertCaption": "Inserir <PERSON>", "DE.Views.DocumentHolder.txtInsertEqAfter": "Inserir equação a seguir", "DE.Views.DocumentHolder.txtInsertEqBefore": "Inserir equação à frente", "DE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> apenas texto", "DE.Views.DocumentHolder.txtLimitChange": "Alterar localização de limites", "DE.Views.DocumentHolder.txtLimitOver": "Limite sobre o texto", "DE.Views.DocumentHolder.txtLimitUnder": "Limite sob o texto", "DE.Views.DocumentHolder.txtMatchBrackets": "Combinar parênteses com a altura do argumento", "DE.Views.DocumentHolder.txtMatrixAlign": "Alinhamento de matriz", "DE.Views.DocumentHolder.txtOverbar": "Barra sobre texto", "DE.Views.DocumentHolder.txtOverwriteCells": "Sobrescrever c<PERSON>", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Manter formatação da origem", "DE.Views.DocumentHolder.txtPressLink": "Pressione {0} e clique no link", "DE.Views.DocumentHolder.txtPrintSelection": "Imprimir <PERSON>", "DE.Views.DocumentHolder.txtRemFractionBar": "Remover barra de fração", "DE.Views.DocumentHolder.txtRemLimit": "Remover limite", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Remover caractere de acento", "DE.Views.DocumentHolder.txtRemoveBar": "Excluir barra", "DE.Views.DocumentHolder.txtRemoveWarning": "Você quer remover esta assinatura? <br><PERSON><PERSON> não pode ser desfeito.", "DE.Views.DocumentHolder.txtRemScripts": "Remover scripts", "DE.Views.DocumentHolder.txtRemSubscript": "Remover subscrito", "DE.Views.DocumentHolder.txtRemSuperscript": "Remover sobrescrito", "DE.Views.DocumentHolder.txtScriptsAfter": "Scripts após o texto", "DE.Views.DocumentHolder.txtScriptsBefore": "Scripts antes do texto", "DE.Views.DocumentHolder.txtShowBottomLimit": "Mostrar limite inferior", "DE.Views.DocumentHolder.txtShowCloseBracket": "Mostrar encerramento dos colchetes", "DE.Views.DocumentHolder.txtShowDegree": "Mostrar grau", "DE.Views.DocumentHolder.txtShowOpenBracket": "Mostrar abertura dos colchetes", "DE.Views.DocumentHolder.txtShowPlaceholder": "Mostrar espaço reservado", "DE.Views.DocumentHolder.txtShowTopLimit": "Mostrar limite superior", "DE.Views.DocumentHolder.txtSquare": "Quadrado", "DE.Views.DocumentHolder.txtStretchBrackets": "Esticar colchetes", "DE.Views.DocumentHolder.txtThrough": "Através", "DE.Views.DocumentHolder.txtTight": "<PERSON><PERSON>", "DE.Views.DocumentHolder.txtTop": "Parte superior", "DE.Views.DocumentHolder.txtTopAndBottom": "Parte superior e inferior", "DE.Views.DocumentHolder.txtUnderbar": "Barra abaixo de texto", "DE.Views.DocumentHolder.txtUngroup": "Desagrupar", "DE.Views.DocumentHolder.txtWarnUrl": "Clicar neste link pode ser prejudicial ao seu dispositivo e dados.<br>Você tem certeza de que quer continuar?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Update %1 style", "DE.Views.DocumentHolder.vertAlignText": "Alinhamento vertical", "DE.Views.DropcapSettingsAdvanced.strBorders": "Bordas e preenchimento", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Letra capitular", "DE.Views.DropcapSettingsAdvanced.strMargins": "Margens", "DE.Views.DropcapSettingsAdvanced.textAlign": "Alinhamento", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAuto": "Automático", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Cor de fundo", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "<PERSON><PERSON> <PERSON> b<PERSON>a", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Clique no diagrama ou use os botões para selecionar bordas", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBottom": "Inferior", "DE.Views.DropcapSettingsAdvanced.textCenter": "Centro", "DE.Views.DropcapSettingsAdvanced.textColumn": "Coluna", "DE.Views.DropcapSettingsAdvanced.textDistance": "Distância do texto", "DE.Views.DropcapSettingsAdvanced.textExact": "Exatamente", "DE.Views.DropcapSettingsAdvanced.textFlow": "Estrutura de fluxo", "DE.Views.DropcapSettingsAdvanced.textFont": "Fonte", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHeight": "Altura", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.DropcapSettingsAdvanced.textInline": "Moldura embutida", "DE.Views.DropcapSettingsAdvanced.textInMargin": "<PERSON> margem", "DE.Views.DropcapSettingsAdvanced.textInText": "No texto", "DE.Views.DropcapSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.DropcapSettingsAdvanced.textMargin": "Margem", "DE.Views.DropcapSettingsAdvanced.textMove": "Mover com texto", "DE.Views.DropcapSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parâmetros", "DE.Views.DropcapSettingsAdvanced.textPosition": "Posição", "DE.Views.DropcapSettingsAdvanced.textRelative": "Relativo para", "DE.Views.DropcapSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Altura em linhas", "DE.Views.DropcapSettingsAdvanced.textTitle": "Letra capitular - Configurações avançadas", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Moldura - Configurações Avançadas", "DE.Views.DropcapSettingsAdvanced.textTop": "Parte superior", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertical", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "Fonte", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "<PERSON><PERSON> bordas", "DE.Views.EditListItemDialog.textDisplayName": "Nome de exibição", "DE.Views.EditListItemDialog.textNameError": "O nome de exibição não deve estar vazio.", "DE.Views.EditListItemDialog.textValue": "Valor", "DE.Views.EditListItemDialog.textValueError": "Um item com o mesmo valor já existe.", "DE.Views.FileMenu.btnBackCaption": "Local do arquivo aberto", "DE.Views.FileMenu.btnCloseMenuCaption": "Fechar menu", "DE.Views.FileMenu.btnCreateNewCaption": "Criar novo", "DE.Views.FileMenu.btnDownloadCaption": "Baixar como", "DE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnFileOpenCaption": "Abrir", "DE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHistoryCaption": "Histórico <PERSON>ão", "DE.Views.FileMenu.btnInfoCaption": "Informações do documento", "DE.Views.FileMenu.btnPrintCaption": "Imprimir", "DE.Views.FileMenu.btnProtectCaption": "Proteger", "DE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "DE.Views.FileMenu.btnRenameCaption": "Renomear", "DE.Views.FileMenu.btnReturnCaption": "Voltar para documento", "DE.Views.FileMenu.btnRightsCaption": "Direitos de Acesso", "DE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON> como", "DE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Salvar cópia como", "DE.Views.FileMenu.btnSettingsCaption": "Configurações avançadas", "DE.Views.FileMenu.btnToEditCaption": "Editar documento", "DE.Views.FileMenu.textDownload": "Baixar", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Documento em branco", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Criar novo", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Adicionar Autor", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicativo", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Alterar direitos de acesso", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Visualização rápida da Web", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Carregando...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última Modificação Por", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificação", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "Não", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "Produtor de <PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF marcado", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "Versão PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Localização", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Pessoas que têm direitos", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Caracteres com espaços", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Estatísticas", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "Caracteres", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etiquetas", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Título do documento", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Carregado", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Palavras", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "<PERSON>m", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Alterar direitos de acesso", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Pessoas que têm direitos", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON> senha", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger o documento", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "Com assinatura", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar documento", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editar excluirá as assinaturas do documento. <br> <PERSON><PERSON><PERSON> continuar?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Este documento foi protegido com senha.", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "O documento deve ser assinado.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Assinaturas válidas foram adicionadas ao documento. O documento está protegido contra edição.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algumas das assinaturas digitais no documento estão inválidas ou não puderam ser verificadas. O documento está protegido para edição.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Visualizar assinaturas", "DE.Views.FileMenuPanels.Settings.okButtonText": "Aplicar", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modo de coedição", "DE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strFontRender": "Dicas de fonte", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorar palavras em MAIÚSCULAS", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignorar palavras com números", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Configurações de macros", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Mostrar o botão Opções de colagem quando o conteúdo for colado", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Alterações de colaboração em tempo real", "DE.Views.FileMenuPanels.Settings.strShowComments": "Mostrar comentários em texto", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Mostrar alterações de outros usuários", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "<PERSON>rar coment<PERSON>rios resolvidos", "DE.Views.FileMenuPanels.Settings.strStrict": "Estrito", "DE.Views.FileMenuPanels.Settings.strTheme": "Tema de interface", "DE.Views.FileMenuPanels.Settings.strUnit": "Unidade de medida", "DE.Views.FileMenuPanels.Settings.strZoom": "Valor de zoom padrão", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Cada 10 minutos", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Cada 30 minutos", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Cada 5 minutos", "DE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON> hora", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON> <PERSON>", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Recuperação automática", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Salvamento automático", "DE.Views.FileMenuPanels.Settings.textDisabled": "Desabilitado", "DE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON><PERSON> para servidor", "DE.Views.FileMenuPanels.Settings.textMinute": "Cada minuto", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Tornar compatível com versão antiga do MS Word quando gravar como DOCX.", "DE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON><PERSON> todos", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opções de autocorreção...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Modo de cache padrão", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Mostrar por clique em balões", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Mostrar com a ponta de ferramentas", "DE.Views.FileMenuPanels.Settings.txtCm": "Centímetro", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Colaboração", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Ativar modo escuro de documento", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editando e salvando", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Co-edição em tempo real. Todas as alterações são salvas automaticamente", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Ajustar a página", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "<PERSON><PERSON><PERSON> largura", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtInch": "Polegada", "DE.Views.FileMenuPanels.Settings.txtLast": "Visualizar <PERSON>", "DE.Views.FileMenuPanels.Settings.txtMac": "como SO X", "DE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "DE.Views.FileMenuPanels.Settings.txtNone": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtProofing": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtPt": "Ponto", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Mostrar o botão Impressão rápida no cabeçalho do editor", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "O documento será impresso na última impressora selecionada ou padrão", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Habilitar todos", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Mostrar alterações de faixa", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Verificação ortográfica", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Desabilitar tudo", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "<PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Use o bot<PERSON> \"Salvar\" para sincronizar as alterações que você e outras pessoas fazem", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use a tecla Alt para navegar na interface do usuário usando o teclado", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use a tecla Option para navegar na interface do usuário usando o teclado", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostrar notificação", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros com uma notificação", "DE.Views.FileMenuPanels.Settings.txtWin": "como Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON>", "DE.Views.FormSettings.textAlways": "Sempre", "DE.Views.FormSettings.textAnyone": "Alguém", "DE.Views.FormSettings.textAspect": "Bloquear proporção", "DE.Views.FormSettings.textAtLeast": "<PERSON><PERSON>", "DE.Views.FormSettings.textAuto": "Automático", "DE.Views.FormSettings.textAutofit": "Ajuste automático", "DE.Views.FormSettings.textBackgroundColor": "Cor do plano de fundo", "DE.Views.FormSettings.textCheckbox": "Caixa de seleção", "DE.Views.FormSettings.textColor": "<PERSON><PERSON> <PERSON> b<PERSON>a", "DE.Views.FormSettings.textComb": "Conjunto de caracteres", "DE.Views.FormSettings.textCombobox": "Caixa de combinação", "DE.Views.FormSettings.textComplex": "Campo complexo", "DE.Views.FormSettings.textConnected": "Campos conectados", "DE.Views.FormSettings.textCreditCard": "Número do cartão de crédito (por exemplo, 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Campo de data e hora", "DE.Views.FormSettings.textDateFormat": "Mostra a data assim", "DE.Views.FormSettings.textDelete": "Excluir", "DE.Views.FormSettings.textDigits": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDisconnect": "Desconectar", "DE.Views.FormSettings.textDropDown": "Suspenso", "DE.Views.FormSettings.textExact": "Exatamente", "DE.Views.FormSettings.textField": "Campo de texto", "DE.Views.FormSettings.textFillRoles": "Quem precisa preencher isso?", "DE.Views.FormSettings.textFixed": "Campo de tamanho fixo", "DE.Views.FormSettings.textFormat": "Formato", "DE.Views.FormSettings.textFormatSymbols": "Símbolos permitidos", "DE.Views.FormSettings.textFromFile": "Do Arquivo", "DE.Views.FormSettings.textFromStorage": "De armazenamento", "DE.Views.FormSettings.textFromUrl": "Da URL", "DE.Views.FormSettings.textGroupKey": "Chave de grupo", "DE.Views.FormSettings.textImage": "Imagem", "DE.Views.FormSettings.textKey": "Chave", "DE.Views.FormSettings.textLang": "Idioma", "DE.Views.FormSettings.textLetters": "<PERSON><PERSON>", "DE.Views.FormSettings.textLock": "Bloquear", "DE.Views.FormSettings.textMask": "Máscara arbitrária", "DE.Views.FormSettings.textMaxChars": "Limite de caracteres", "DE.Views.FormSettings.textMulti": "Campo multilinha", "DE.Views.FormSettings.textNever": "Nunca", "DE.Views.FormSettings.textNoBorder": "Sem limite", "DE.Views.FormSettings.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textPhone1": "Número de telefone (por exemplo, (*************)", "DE.Views.FormSettings.textPhone2": "Número de telefone (por exemplo, +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Marcador de posição", "DE.Views.FormSettings.textRadiobox": "Botao de radio", "DE.Views.FormSettings.textReg": "Expressão regular", "DE.Views.FormSettings.textRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textScale": "Quando escalar", "DE.Views.FormSettings.textSelectImage": "Selecionar Imagem", "DE.Views.FormSettings.textTag": "Etiqueta", "DE.Views.FormSettings.textTip": "Dica", "DE.Views.FormSettings.textTipAdd": "Adicionar novo valor", "DE.Views.FormSettings.textTipDelete": "Excluir valor", "DE.Views.FormSettings.textTipDown": "Mover para baixo", "DE.Views.FormSettings.textTipUp": "Mover para cima", "DE.Views.FormSettings.textTooBig": "A imagem é muito grande", "DE.Views.FormSettings.textTooSmall": "A imagem é muito pequena", "DE.Views.FormSettings.textUKPassport": "Número do passaporte do Reino Unido (por exemplo, *********)", "DE.Views.FormSettings.textUnlock": "Desb<PERSON>que<PERSON>", "DE.Views.FormSettings.textUSSSN": "SSN dos EUA (por exemplo, ***********)", "DE.Views.FormSettings.textValue": "Opções de valor", "DE.Views.FormSettings.textWidth": "<PERSON><PERSON><PERSON> da célula", "DE.Views.FormSettings.textZipCodeUS": "Código postal dos EUA (por exemplo, 92663 ou 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "Caixa de seleção", "DE.Views.FormsTab.capBtnComboBox": "Caixa de combinação", "DE.Views.FormsTab.capBtnComplex": "Campo complexo", "DE.Views.FormsTab.capBtnDownloadForm": "Baixe como oform", "DE.Views.FormsTab.capBtnDropDown": "Suspenso", "DE.Views.FormsTab.capBtnEmail": "Endereço de e-mail", "DE.Views.FormsTab.capBtnImage": "Imagem", "DE.Views.FormsTab.capBtnManager": "Gerenciar funções", "DE.Views.FormsTab.capBtnNext": "Próximo campo", "DE.Views.FormsTab.capBtnPhone": "Número de telefone", "DE.Views.FormsTab.capBtnPrev": "Campo anterior", "DE.Views.FormsTab.capBtnRadioBox": "Botao de radio", "DE.Views.FormsTab.capBtnSaveForm": "Salvar como um formulário", "DE.Views.FormsTab.capBtnSubmit": "Enviar", "DE.Views.FormsTab.capBtnText": "Campo de texto", "DE.Views.FormsTab.capBtnView": "<PERSON>er formul<PERSON>", "DE.Views.FormsTab.capCreditCard": "Cartão de crédito", "DE.Views.FormsTab.capDateTime": "Data e Hora", "DE.Views.FormsTab.capZipCode": "CEP", "DE.Views.FormsTab.textAnyone": "Alguém", "DE.Views.FormsTab.textClear": "Limpar campos.", "DE.Views.FormsTab.textClearFields": "Limpar todos os campos", "DE.Views.FormsTab.textCreateForm": "Adicione campos e crie um documento FORM preenchível", "DE.Views.FormsTab.textGotIt": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.textHighlight": "Configurações de destaque", "DE.Views.FormsTab.textNoHighlight": "Sem destaque", "DE.Views.FormsTab.textRequired": "Preencha todos os campos obrigatórios para enviar o formulário.", "DE.Views.FormsTab.textSubmited": "Formulário enviado com sucesso", "DE.Views.FormsTab.tipCheckBox": "Inserir caixa de seleção", "DE.Views.FormsTab.tipComboBox": "Inserir caixa de combinação", "DE.Views.FormsTab.tipComplexField": "Inserir campo complexo", "DE.Views.FormsTab.tipCreditCard": "Inserir número de cartão de crédito", "DE.Views.FormsTab.tipDateTime": "Inserir data e hora", "DE.Views.FormsTab.tipDownloadForm": "Baixar um arquivo como um documento FORM preenchível", "DE.Views.FormsTab.tipDropDown": "Inserir lista suspensa", "DE.Views.FormsTab.tipEmailField": "Inserir endereço de e-mail", "DE.Views.FormsTab.tipFixedText": "Inserir campo de texto fixo", "DE.Views.FormsTab.tipImageField": "Inserir imagem", "DE.Views.FormsTab.tipInlineText": "Inserir campo de texto embutido", "DE.Views.FormsTab.tipManager": "Gerenciar funções", "DE.Views.FormsTab.tipNextForm": "Ir para o próximo campo", "DE.Views.FormsTab.tipPhoneField": "Inserir número de telefone", "DE.Views.FormsTab.tipPrevForm": "Ir para o campo anterior", "DE.Views.FormsTab.tipRadioBox": "Inserir botão de rádio", "DE.Views.FormsTab.tipSaveForm": "Salvar um arquivo como um documento OFORM preenchível", "DE.Views.FormsTab.tipSubmit": "Enviar para", "DE.Views.FormsTab.tipTextField": "Inserir campo de texto", "DE.Views.FormsTab.tipViewForm": "<PERSON>er formul<PERSON>", "DE.Views.FormsTab.tipZipCode": "Inserir código postal", "DE.Views.FormsTab.txtFixedDesc": "Inserir campo de texto fixo", "DE.Views.FormsTab.txtFixedText": "Fixo", "DE.Views.FormsTab.txtInlineDesc": "Inserir campo de texto embutido", "DE.Views.FormsTab.txtInlineText": "<PERSON> linha", "DE.Views.FormsTab.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "DE.Views.HeaderFooterSettings.textBottomCenter": "Centro inferior", "DE.Views.HeaderFooterSettings.textBottomLeft": "Esquerda inferior", "DE.Views.HeaderFooterSettings.textBottomPage": "<PERSON>m da página", "DE.Views.HeaderFooterSettings.textBottomRight": "Direita inferior", "DE.Views.HeaderFooterSettings.textDiffFirst": "Primeira página diferente", "DE.Views.HeaderFooterSettings.textDiffOdd": "Páginas pares e ímpares diferentes", "DE.Views.HeaderFooterSettings.textFrom": "<PERSON><PERSON><PERSON> em", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Cabeçalho no início", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Inserir na Posição Atual ", "DE.Views.HeaderFooterSettings.textOptions": "Opções", "DE.Views.HeaderFooterSettings.textPageNum": "Inserir número da página", "DE.Views.HeaderFooterSettings.textPageNumbering": "Numeração da página", "DE.Views.HeaderFooterSettings.textPosition": "Posição", "DE.Views.HeaderFooterSettings.textPrev": "Continuar da seção anterior", "DE.Views.HeaderFooterSettings.textSameAs": "Vincular a Anterior", "DE.Views.HeaderFooterSettings.textTopCenter": "Superior central", "DE.Views.HeaderFooterSettings.textTopLeft": "Superior esquerdo", "DE.Views.HeaderFooterSettings.textTopPage": "Topo da Página", "DE.Views.HeaderFooterSettings.textTopRight": "Superior direito", "DE.Views.HyperlinkSettingsDialog.textDefault": "Fragmento de texto selecionado", "DE.Views.HyperlinkSettingsDialog.textDisplay": "<PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.textExternal": "Link externo", "DE.Views.HyperlinkSettingsDialog.textInternal": "Colocar no documento", "DE.Views.HyperlinkSettingsDialog.textTitle": "Configurações do hiperlink", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Texto da dica de tela", "DE.Views.HyperlinkSettingsDialog.textUrl": "Vincular a", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Início do documento", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatório", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo é limitado a 2083 caracteres. ", "DE.Views.ImageSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "DE.Views.ImageSettings.textCrop": "Cortar", "DE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropFit": "Ajustar", "DE.Views.ImageSettings.textCropToShape": "Cortar para dar forma", "DE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "DE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "DE.Views.ImageSettings.textFitMargins": "Ajustar à margem", "DE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textFromFile": "Do Arquivo", "DE.Views.ImageSettings.textFromStorage": "De armazenamento", "DE.Views.ImageSettings.textFromUrl": "Da URL", "DE.Views.ImageSettings.textHeight": "Altura", "DE.Views.ImageSettings.textHint270": "Girar 90º no sentido anti-horário.", "DE.Views.ImageSettings.textHint90": "Girar 90º no sentido horário", "DE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "DE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "DE.Views.ImageSettings.textInsert": "Substituir imagem", "DE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textRecentlyUsed": "Usado recentemente", "DE.Views.ImageSettings.textRotate90": "Girar 90º", "DE.Views.ImageSettings.textRotation": "Rotação", "DE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Estilo da quebra automática", "DE.Views.ImageSettings.txtBehind": "Atrás", "DE.Views.ImageSettings.txtInFront": "<PERSON> frente", "DE.Views.ImageSettings.txtInline": "<PERSON> linha", "DE.Views.ImageSettings.txtSquare": "Quadrado", "DE.Views.ImageSettings.txtThrough": "Através", "DE.Views.ImageSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ImageSettings.txtTopAndBottom": "Parte superior e inferior", "DE.Views.ImageSettingsAdvanced.strMargins": "Preenchimento de texto", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absoluto", "DE.Views.ImageSettingsAdvanced.textAlignment": "Alinhamento", "DE.Views.ImageSettingsAdvanced.textAlt": "Texto Alternativo", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Descrição", "DE.Views.ImageSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto das informações de objetos visuais, que serão lidas para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor quais informações há na imagem, autoshape, gráfico ou tabela.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "Set<PERSON>", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Bloquear proporção", "DE.Views.ImageSettingsAdvanced.textAutofit": "Ajuste automático", "DE.Views.ImageSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> inicial", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "DE.Views.ImageSettingsAdvanced.textBelow": "abaixo", "DE.Views.ImageSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "Inferior", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Margem inferior", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Disposição do texto", "DE.Views.ImageSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "DE.Views.ImageSettingsAdvanced.textCenter": "Centro", "DE.Views.ImageSettingsAdvanced.textCharacter": "Caractere", "DE.Views.ImageSettingsAdvanced.textColumn": "Coluna", "DE.Views.ImageSettingsAdvanced.textDistance": "Distância do texto", "DE.Views.ImageSettingsAdvanced.textEndSize": "Tamanho final", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Estilo final", "DE.Views.ImageSettingsAdvanced.textFlat": "Plano", "DE.Views.ImageSettingsAdvanced.textFlipped": "Invertido", "DE.Views.ImageSettingsAdvanced.textHeight": "Altura", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "DE.Views.ImageSettingsAdvanced.textJoinType": "Tipo de junção", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporções constantes", "DE.Views.ImageSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "Mar<PERSON>m es<PERSON>", "DE.Views.ImageSettingsAdvanced.textLine": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMargin": "Margem", "DE.Views.ImageSettingsAdvanced.textMiter": "Malhete", "DE.Views.ImageSettingsAdvanced.textMove": "Mover objeto com texto", "DE.Views.ImageSettingsAdvanced.textOptions": "Opções", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON> sobrepo<PERSON>", "DE.Views.ImageSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textPosition": "Posição", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Posição relativa", "DE.Views.ImageSettingsAdvanced.textRelative": "relativo para", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relativo", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Redimensionar forma para caber no texto", "DE.Views.ImageSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Margem direita", "DE.Views.ImageSettingsAdvanced.textRightOf": "para a direita de", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotação", "DE.Views.ImageSettingsAdvanced.textRound": "Rodada", "DE.Views.ImageSettingsAdvanced.textShape": "Configurações da forma", "DE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textSquare": "Quadrado", "DE.Views.ImageSettingsAdvanced.textTextBox": "Caixa de texto", "DE.Views.ImageSettingsAdvanced.textTitle": "Imagem - Configurações avançadas", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Gráfico - Configurações avançadas", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Forma - Configurações avançadas", "DE.Views.ImageSettingsAdvanced.textTop": "Parte superior", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Margem superior", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "DE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Pesos e Setas", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "Estilo da quebra automática", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "Atrás do texto", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "<PERSON> frente", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "Alinhado com o Texto", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Quadrado", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Através", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Parte superior e inferior", "DE.Views.LeftMenu.tipAbout": "Sobre", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Comentários", "DE.Views.LeftMenu.tipNavigation": "Navegação", "DE.Views.LeftMenu.tipOutline": "Cabeçalhos", "DE.Views.LeftMenu.tipPageThumbnails": "Miniaturas de página", "DE.Views.LeftMenu.tipPlugins": "Plug-ins", "DE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipSupport": "Feedback e Suporte", "DE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.txtDeveloper": "MODO DE DESENVOLVEDOR", "DE.Views.LeftMenu.txtEditor": "Editor de documentos", "DE.Views.LeftMenu.txtLimit": "Limitar o acesso", "DE.Views.LeftMenu.txtTrial": "MODO DE TESTE", "DE.Views.LeftMenu.txtTrialDev": "<PERSON><PERSON> desen<PERSON>or de teste", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Adicionar numeração de linha", "DE.Views.LineNumbersDialog.textApplyTo": "Aplicar alterações a", "DE.Views.LineNumbersDialog.textContinuous": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LineNumbersDialog.textCountBy": "Contar por", "DE.Views.LineNumbersDialog.textDocument": "Documento inteiro", "DE.Views.LineNumbersDialog.textForward": "Este ponto à frente", "DE.Views.LineNumbersDialog.textFromText": "Do texto", "DE.Views.LineNumbersDialog.textNumbering": "Numeração", "DE.Views.LineNumbersDialog.textRestartEachPage": "Reiniciar cada uma das página", "DE.Views.LineNumbersDialog.textRestartEachSection": "Reiniciar cada uma das seções", "DE.Views.LineNumbersDialog.textSection": "Seção atual", "DE.Views.LineNumbersDialog.textStartAt": "<PERSON><PERSON><PERSON> em", "DE.Views.LineNumbersDialog.textTitle": "Números de linhas", "DE.Views.LineNumbersDialog.txtAutoText": "Automático", "DE.Views.Links.capBtnAddText": "Adicionar texto", "DE.Views.Links.capBtnBookmarks": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnCaption": "<PERSON>a", "DE.Views.Links.capBtnContentsUpdate": "<PERSON><PERSON><PERSON><PERSON> tabela", "DE.Views.Links.capBtnCrossRef": "Referência cruzada", "DE.Views.Links.capBtnInsContents": "Tabela de Conteúdo", "DE.Views.Links.capBtnInsFootnote": "<PERSON><PERSON>", "DE.Views.Links.capBtnInsLink": "Hiperlink", "DE.Views.Links.capBtnTOF": "Tabela de Figuras", "DE.Views.Links.confirmDeleteFootnotes": "Deseja excluir todas as notas de rodapé?", "DE.Views.Links.confirmReplaceTOF": "Quer substituir a tabela de figuras selecionada?", "DE.Views.Links.mniConvertNote": "Converter todas as notas", "DE.Views.Links.mniDelFootnote": "<PERSON>c<PERSON><PERSON> todas as notas de rodapé", "DE.Views.Links.mniInsEndnote": "Inserir nota final", "DE.Views.Links.mniInsFootnote": "Inserir Nota de Rodapé", "DE.Views.Links.mniNoteSettings": "Configurações de Notas", "DE.Views.Links.textContentsRemove": "Excluir tabela de conteúdo", "DE.Views.Links.textContentsSettings": "Configurações", "DE.Views.Links.textConvertToEndnotes": "Converter todas as notas de rodapé em notas finais", "DE.Views.Links.textConvertToFootnotes": "Converter todas as notas finais em notas de rodapé", "DE.Views.Links.textGotoEndnote": "Vá para notas finais", "DE.Views.Links.textGotoFootnote": "Ir para notas de rodapé", "DE.Views.Links.textSwapNotes": "Trocar notas de rodapé e notas finais", "DE.Views.Links.textUpdateAll": "<PERSON><PERSON><PERSON><PERSON> toda a tabela", "DE.Views.Links.textUpdatePages": "Atualizar somente os números de páginas", "DE.Views.Links.tipAddText": "Incluir título no Índice", "DE.Views.Links.tipBookmarks": "<PERSON><PERSON><PERSON>", "DE.Views.Links.tipCaption": "<PERSON>ser<PERSON> legenda", "DE.Views.Links.tipContents": "Inserir tabela de conteúdo", "DE.Views.Links.tipContentsUpdate": "Atualizar a tabela de conteúdo", "DE.Views.Links.tipCrossRef": "Inserir <PERSON><PERSON> cru<PERSON>a", "DE.Views.Links.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.tipNotes": "Inserir ou editar notas de rodapé", "DE.Views.Links.tipTableFigures": "Inserir tabela de figuras", "DE.Views.Links.tipTableFiguresUpdate": "Atualizar tabela de figuras", "DE.Views.Links.titleUpdateTOF": "Atualizar tabela de figuras", "DE.Views.Links.txtDontShowTof": "Não Mostrar no Índice", "DE.Views.Links.txtLevel": "Nível", "DE.Views.ListSettingsDialog.textAuto": "Automático", "DE.Views.ListSettingsDialog.textCenter": "Centro", "DE.Views.ListSettingsDialog.textLeft": "E<PERSON>rda", "DE.Views.ListSettingsDialog.textLevel": "Nível", "DE.Views.ListSettingsDialog.textPreview": "Visualizar", "DE.Views.ListSettingsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtAlign": "Alinhamento", "DE.Views.ListSettingsDialog.txtBullet": "Marcador", "DE.Views.ListSettingsDialog.txtColor": "Cor", "DE.Views.ListSettingsDialog.txtFont": "Fonte e Símbolo", "DE.Views.ListSettingsDialog.txtLikeText": "Como un texto", "DE.Views.ListSettingsDialog.txtNewBullet": "Novo marcador", "DE.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSymbol": "Símbolo", "DE.Views.ListSettingsDialog.txtTitle": "Configurações da lista", "DE.Views.ListSettingsDialog.txtType": "Tipo", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Send", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "Theme", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Anexar como DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Anexar como PDF", "DE.Views.MailMergeEmailDlg.textFileName": "File name", "DE.Views.MailMergeEmailDlg.textFormat": "Mail format", "DE.Views.MailMergeEmailDlg.textFrom": "From", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Message", "DE.Views.MailMergeEmailDlg.textSubject": "Subject Line", "DE.Views.MailMergeEmailDlg.textTitle": "Send to Email", "DE.Views.MailMergeEmailDlg.textTo": "To", "DE.Views.MailMergeEmailDlg.textWarning": "Aviso!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "Por favor, observe que o envio não poderá ser parado após clicar o botão 'Enviar'.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Merging", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Me<PERSON> failed.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Aviso", "DE.Views.MailMergeSettings.textAddRecipients": "Add some recipients to the list first", "DE.Views.MailMergeSettings.textAll": "Todos os registros", "DE.Views.MailMergeSettings.textCurrent": "Registro atual", "DE.Views.MailMergeSettings.textDataSource": "Data Source", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "Baixar", "DE.Views.MailMergeSettings.textEditData": "Editar lista de destinatário", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "From", "DE.Views.MailMergeSettings.textGoToMail": "Go to Mail", "DE.Views.MailMergeSettings.textHighlight": "Highlight merge fields", "DE.Views.MailMergeSettings.textInsertField": "Insert Merge Field", "DE.Views.MailMergeSettings.textMaxRecepients": "Max 100 recepients.", "DE.Views.MailMergeSettings.textMerge": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeFields": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeTo": "Merge to", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Save", "DE.Views.MailMergeSettings.textPreview": "Preview results", "DE.Views.MailMergeSettings.textReadMore": "Read more", "DE.Views.MailMergeSettings.textSendMsg": "All mail messages are ready and will be sent out within some time.<br>The speed of mailing depends on your mail service.<br>You can continue working with document or close it. After the operation is over the notification will be sent to your registration email address.", "DE.Views.MailMergeSettings.textTo": "To", "DE.Views.MailMergeSettings.txtFirst": "Para o primeiro registro", "DE.Views.MailMergeSettings.txtFromToError": "O valor \"De\" deve ser menor que o valor \"Para\"", "DE.Views.MailMergeSettings.txtLast": "Para o registro anterior", "DE.Views.MailMergeSettings.txtNext": "Para o próximo registro", "DE.Views.MailMergeSettings.txtPrev": "Para o registro anterior", "DE.Views.MailMergeSettings.txtUntitled": "Untitled", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Starting merge failed", "DE.Views.Navigation.strNavigate": "Cabeçalhos", "DE.Views.Navigation.txtClosePanel": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtCollapse": "<PERSON><PERSON><PERSON><PERSON> tudo", "DE.Views.Navigation.txtDemote": "Re<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtEmpty": "Não há títulos no documento.<br>Aplique um estilo de título ao texto para que ele apareça no índice.", "DE.Views.Navigation.txtEmptyItem": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtEmptyViewer": "Não há títulos no documento.", "DE.Views.Navigation.txtExpand": "Expandir tudo", "DE.Views.Navigation.txtExpandToLevel": "Expandir ao nível", "DE.Views.Navigation.txtFontSize": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtHeadingAfter": "Novo título após", "DE.Views.Navigation.txtHeadingBefore": "Novo título antes de", "DE.Views.Navigation.txtLarge": "Grande", "DE.Views.Navigation.txtMedium": "Médio", "DE.Views.Navigation.txtNewHeading": "Novo subtítulo", "DE.Views.Navigation.txtPromote": "Promover", "DE.Views.Navigation.txtSelect": "Selecionar conteúdo", "DE.Views.Navigation.txtSettings": "Configurações de títulos", "DE.Views.Navigation.txtSmall": "Pequeno", "DE.Views.Navigation.txtWrapHeadings": "Envolver tí<PERSON><PERSON> long<PERSON>", "DE.Views.NoteSettingsDialog.textApply": "Aplicar", "DE.Views.NoteSettingsDialog.textApplyTo": "Aplicar alterações a", "DE.Views.NoteSettingsDialog.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textCustom": "Marca personal<PERSON>", "DE.Views.NoteSettingsDialog.textDocEnd": "Fim do documento", "DE.Views.NoteSettingsDialog.textDocument": "Documento inteiro", "DE.Views.NoteSettingsDialog.textEachPage": "Reiniciar cada uma das página", "DE.Views.NoteSettingsDialog.textEachSection": "Reiniciar cada uma das seções", "DE.Views.NoteSettingsDialog.textEndnote": "Nota final", "DE.Views.NoteSettingsDialog.textFootnote": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textFormat": "Formato", "DE.Views.NoteSettingsDialog.textInsert": "Inserir", "DE.Views.NoteSettingsDialog.textLocation": "Localização", "DE.Views.NoteSettingsDialog.textNumbering": "Numeração", "DE.Views.NoteSettingsDialog.textNumFormat": "Formato Numérico", "DE.Views.NoteSettingsDialog.textPageBottom": "Inferior da página", "DE.Views.NoteSettingsDialog.textSectEnd": "Fim da seção", "DE.Views.NoteSettingsDialog.textSection": "Seção atual", "DE.Views.NoteSettingsDialog.textStart": "<PERSON><PERSON><PERSON> em", "DE.Views.NoteSettingsDialog.textTextBottom": "Abaixo do texto", "DE.Views.NoteSettingsDialog.textTitle": "Definições de Notas", "DE.Views.NotesRemoveDialog.textEnd": "<PERSON><PERSON><PERSON> todas as notas finais", "DE.Views.NotesRemoveDialog.textFoot": "<PERSON><PERSON><PERSON> to<PERSON> as notas de rodapé", "DE.Views.NotesRemoveDialog.textTitle": "<PERSON><PERSON><PERSON> notas", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Aviso", "DE.Views.PageMarginsDialog.textBottom": "Inferior", "DE.Views.PageMarginsDialog.textGutter": "Calha", "DE.Views.PageMarginsDialog.textGutterPosition": "Posição da calha", "DE.Views.PageMarginsDialog.textInside": "<PERSON><PERSON> de", "DE.Views.PageMarginsDialog.textLandscape": "Paisagem", "DE.Views.PageMarginsDialog.textLeft": "E<PERSON>rda", "DE.Views.PageMarginsDialog.textMirrorMargins": "Margens espelho", "DE.Views.PageMarginsDialog.textMultiplePages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientação", "DE.Views.PageMarginsDialog.textOutside": "Exterior", "DE.Views.PageMarginsDialog.textPortrait": "Retrato ", "DE.Views.PageMarginsDialog.textPreview": "Visualizar", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "Margens", "DE.Views.PageMarginsDialog.textTop": "Parte superior", "DE.Views.PageMarginsDialog.txtMarginsH": "Margens superior e inferior são muito altas para uma determinada altura da página", "DE.Views.PageMarginsDialog.txtMarginsW": "Margens são muito grandes para uma determinada largura da página", "DE.Views.PageSizeDialog.textHeight": "Altura", "DE.Views.PageSizeDialog.textPreset": "<PERSON><PERSON> a<PERSON>", "DE.Views.PageSizeDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "Personalizar", "DE.Views.PageThumbnails.textClosePanel": "<PERSON><PERSON>r miniaturas de página", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Realçar parte visível da página", "DE.Views.PageThumbnails.textPageThumbnails": "Miniaturas de página", "DE.Views.PageThumbnails.textThumbnailsSettings": "Configurações de miniaturas", "DE.Views.PageThumbnails.textThumbnailsSize": "<PERSON><PERSON><PERSON> das miniaturas", "DE.Views.ParagraphSettings.strIndent": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsLeftText": "E<PERSON>rda", "DE.Views.ParagraphSettings.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsSpecial": "Especial", "DE.Views.ParagraphSettings.strLineHeight": "Espaçamento de linha", "DE.Views.ParagraphSettings.strParagraphSpacing": "Espaçamento", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Não adicionar intervalo entre parágrafos do mesmo estilo", "DE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "DE.Views.ParagraphSettings.textAt": "Em", "DE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textBackColor": "Cor do plano de fundo", "DE.Views.ParagraphSettings.textExact": "Exatamente", "DE.Views.ParagraphSettings.textFirstLine": "Primeira linha", "DE.Views.ParagraphSettings.textHanging": "Suspensão", "DE.Views.ParagraphSettings.textNoneSpecial": "(nenhum)", "DE.Views.ParagraphSettings.txtAutoText": "Automático", "DE.Views.ParagraphSettingsAdvanced.noTabs": "As abas especificadas aparecerão neste campo", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON> ma<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Bordas e Preenchimento", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Quebra de página antes", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaçamento entre linhas", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Nível de contorno", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "<PERSON><PERSON> as linhas juntas", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Manter com o próximo", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Preenchimentos", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Controle de órfão", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fonte", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Recuos e Espaciamento", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Quebras de linha e página", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Posicionamento", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Não adicionar intervalo entre parágrafos do mesmo estilo", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Espaçamento", "DE.Views.ParagraphSettingsAdvanced.strStrike": "Taxado", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "<PERSON><PERSON><PERSON><PERSON> nú<PERSON> linha", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Aba", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Alinhamento", "DE.Views.ParagraphSettingsAdvanced.textAll": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Cor de fundo", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Texto Básico", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "<PERSON><PERSON> <PERSON> b<PERSON>a", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Clique no diagrama ou use os botões para selecionar bordas e aplicar o estilo escolhido a elas", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBottom": "Inferior", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Centralizado", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espaçamento entre caracteres", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contextual", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contextuais e Discricionários", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "<PERSON><PERSON><PERSON><PERSON>, Históricos e Discricionários", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contextuais e Históricos", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Aba padrão", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Efeitos", "DE.Views.ParagraphSettingsAdvanced.textExact": "Exatamente", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira linha", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Suspensão", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Hist<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Histórico e discricionário", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "DE.Views.ParagraphSettingsAdvanced.textLeader": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Nível", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligaduras", "DE.Views.ParagraphSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nenhum)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "Recursos OpenType", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Posição", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Excluir", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Excluir todos", "DE.Views.ParagraphSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Espaçamento", "DE.Views.ParagraphSettingsAdvanced.textStandard": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Padrão e contextual", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "<PERSON><PERSON><PERSON>, contextual e discricionário", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "<PERSON><PERSON><PERSON>, contextual e histórico", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Padrão e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Padrão, Histórico e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Padrão e Histórico", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centro", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posição da aba", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Configurações avançadas", "DE.Views.ParagraphSettingsAdvanced.textTop": "Parte superior", "DE.Views.ParagraphSettingsAdvanced.tipAll": "De<PERSON>ir borda externa e todas as linhas internas", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Definir borda inferior apenas", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Definir apenas linhas internas horizontais", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Definir apenas borda esquerda", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Definir sem bordas", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Definir apenas borda externa", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Definir apenas borda direita", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Definir apenas borda superior", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "<PERSON><PERSON> bordas", "DE.Views.PrintWithPreview.textMarginsLast": "Últimos personalizados", "DE.Views.PrintWithPreview.textMarginsModerate": "Moderado", "DE.Views.PrintWithPreview.textMarginsNarrow": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.textMarginsNormal": "Normal", "DE.Views.PrintWithPreview.textMarginsUsNormal": "US Normal", "DE.Views.PrintWithPreview.textMarginsWide": "Amplo", "DE.Views.PrintWithPreview.txtAllPages": "<PERSON><PERSON> as p<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtBottom": "Inferior", "DE.Views.PrintWithPreview.txtCurrentPage": "<PERSON><PERSON><PERSON> atual", "DE.Views.PrintWithPreview.txtCustom": "Personalizado", "DE.Views.PrintWithPreview.txtCustomPages": "Impressão personalizada", "DE.Views.PrintWithPreview.txtLandscape": "Paisagem", "DE.Views.PrintWithPreview.txtLeft": "E<PERSON>rda", "DE.Views.PrintWithPreview.txtMargins": "Margens", "DE.Views.PrintWithPreview.txtOf": "de {0}", "DE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Número da página inválido", "DE.Views.PrintWithPreview.txtPageOrientation": "Orientação da página", "DE.Views.PrintWithPreview.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPortrait": "Retrato ", "DE.Views.PrintWithPreview.txtPrint": "Imprimir", "DE.Views.PrintWithPreview.txtPrintPdf": "Imprimir em PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Imprimir intervalo", "DE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtSelection": "Se<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtTop": "Parte superior", "DE.Views.ProtectDialog.textComments": "Comentários", "DE.Views.ProtectDialog.textForms": "Preenchimento de formulários", "DE.Views.ProtectDialog.textReview": "Mudanças rastread<PERSON>", "DE.Views.ProtectDialog.textView": "Sem alterações (somente leitura)", "DE.Views.ProtectDialog.txtAllow": "Permitir apenas este tipo de edição no documento", "DE.Views.ProtectDialog.txtIncorrectPwd": "A confirmação da senha não é idêntica", "DE.Views.ProtectDialog.txtLimit": "A senha é limitada a 15 caracteres", "DE.Views.ProtectDialog.txtOptional": "Opcional", "DE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON>", "DE.Views.ProtectDialog.txtProtect": "Proteger", "DE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON>r a senha", "DE.Views.ProtectDialog.txtTitle": "Proteger", "DE.Views.ProtectDialog.txtWarning": "Cuidado: se você perder ou esquecer a senha, não será possível recuperá-la. Guarde-o em local seguro.", "DE.Views.RightMenu.txtChartSettings": "Configurações de gráfico", "DE.Views.RightMenu.txtFormSettings": "Configurações do formulário", "DE.Views.RightMenu.txtHeaderFooterSettings": "Configurações de cabeçalho e rodapé", "DE.Views.RightMenu.txtImageSettings": "Configurações de imagem", "DE.Views.RightMenu.txtMailMergeSettings": "Mail Merge Settings", "DE.Views.RightMenu.txtParagraphSettings": "Configurações do parágrafo", "DE.Views.RightMenu.txtShapeSettings": "Configurações da forma", "DE.Views.RightMenu.txtSignatureSettings": "Configurações de Assinatura", "DE.Views.RightMenu.txtTableSettings": "Configurações da tabela", "DE.Views.RightMenu.txtTextArtSettings": "Configurações de Arte de Texto", "DE.Views.RoleDeleteDlg.textLabel": "Para excluir esta função, você precisa mover os campos associados a ela para outra função.", "DE.Views.RoleDeleteDlg.textSelect": "Selecione para função de fusão de campo", "DE.Views.RoleDeleteDlg.textTitle": "Excluir função", "DE.Views.RoleEditDlg.errNameExists": "A função com esse nome já existe.", "DE.Views.RoleEditDlg.textEmptyError": "O nome da função não deve estar vazio.", "DE.Views.RoleEditDlg.textName": "Nome do papel", "DE.Views.RoleEditDlg.textNoHighlight": "Sem destaque", "DE.Views.RoleEditDlg.txtTitleEdit": "<PERSON><PERSON> p<PERSON>", "DE.Views.RoleEditDlg.txtTitleNew": "Criar nova função", "DE.Views.RolesManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "DE.Views.RolesManagerDlg.textAnyone": "Alguém", "DE.Views.RolesManagerDlg.textDelete": "Excluir", "DE.Views.RolesManagerDlg.textDeleteLast": "Tem certeza de que deseja excluir a função {0}?<br><PERSON><PERSON><PERSON> de excluída, a função padrão será criada.", "DE.Views.RolesManagerDlg.textDescription": "Adicione funções e defina a ordem em que os responsáveis recebem e assinam o documento", "DE.Views.RolesManagerDlg.textDown": "Mover função para baixo", "DE.Views.RolesManagerDlg.textEdit": "<PERSON><PERSON>", "DE.Views.RolesManagerDlg.textEmpty": "Nenhuma função foi criada ainda.<br>Crie pelo menos uma função e ela aparecerá neste campo.", "DE.Views.RolesManagerDlg.textNew": "Novo", "DE.Views.RolesManagerDlg.textUp": "Mover função para cima", "DE.Views.RolesManagerDlg.txtTitle": "Gerenciar funções", "DE.Views.RolesManagerDlg.warnCantDelete": "Você não pode excluir esta função porque ela tem campos associados.", "DE.Views.RolesManagerDlg.warnDelete": "Tem certeza de que deseja excluir a função {0}?", "DE.Views.SaveFormDlg.saveButtonText": "<PERSON><PERSON>", "DE.Views.SaveFormDlg.textAnyone": "Alguém", "DE.Views.SaveFormDlg.textDescription": "Ao salvar no oform, apenas as funções com campos são adicionadas à lista de preenchimento", "DE.Views.SaveFormDlg.textEmpty": "Não há funções associadas aos campos.", "DE.Views.SaveFormDlg.textFill": "Lista de preenchimento", "DE.Views.SaveFormDlg.txtTitle": "Salvar como formulário", "DE.Views.ShapeSettings.strBackground": "Cor do plano de fundo", "DE.Views.ShapeSettings.strChange": "Alterar forma automática", "DE.Views.ShapeSettings.strColor": "Cor", "DE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strForeground": "Cor do primeiro plano", "DE.Views.ShapeSettings.strPattern": "Padrão", "DE.Views.ShapeSettings.strShadow": "Mostrar sombra", "DE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strTransparency": "Opacidade", "DE.Views.ShapeSettings.strType": "Tipo", "DE.Views.ShapeSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "O valor inserido está incorreto.<br>Insira um valor entre 0 pt e 1.584 pt.", "DE.Views.ShapeSettings.textColor": "Preenchimento de cor", "DE.Views.ShapeSettings.textDirection": "Direção", "DE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textFromFile": "Do Arquivo", "DE.Views.ShapeSettings.textFromStorage": "De armazenamento", "DE.Views.ShapeSettings.textFromUrl": "Da URL", "DE.Views.ShapeSettings.textGradient": "Pontos de gradiente", "DE.Views.ShapeSettings.textGradientFill": "Preenchimento gradiente", "DE.Views.ShapeSettings.textHint270": "Girar 90º no sentido anti-horário.", "DE.Views.ShapeSettings.textHint90": "Girar 90º no sentido horário", "DE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "DE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "DE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "DE.Views.ShapeSettings.textLinear": "Linear", "DE.Views.ShapeSettings.textNoFill": "Sem preenchimento", "DE.Views.ShapeSettings.textPatternFill": "Padrão", "DE.Views.ShapeSettings.textPosition": "Posição", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "Usado recentemente", "DE.Views.ShapeSettings.textRotate90": "Girar 90º", "DE.Views.ShapeSettings.textRotation": "Rotação", "DE.Views.ShapeSettings.textSelectImage": "Selecionar imagem", "DE.Views.ShapeSettings.textSelectTexture": "Selecionar", "DE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textTexture": "Da Textura", "DE.Views.ShapeSettings.textTile": "Lado a lado", "DE.Views.ShapeSettings.textWrap": "Estilo da quebra automática", "DE.Views.ShapeSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "DE.Views.ShapeSettings.txtBehind": "Atrás", "DE.Views.ShapeSettings.txtBrownPaper": "<PERSON>pel pardo", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "Papelão", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>o es<PERSON>ro", "DE.Views.ShapeSettings.txtGrain": "Granulação", "DE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtGreyPaper": "Papel cinza", "DE.Views.ShapeSettings.txtInFront": "<PERSON> frente", "DE.Views.ShapeSettings.txtInline": "<PERSON> linha", "DE.Views.ShapeSettings.txtKnit": "Encontro", "DE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> linha", "DE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtSquare": "Quadrado", "DE.Views.ShapeSettings.txtThrough": "Através", "DE.Views.ShapeSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtTopAndBottom": "Parte superior e inferior", "DE.Views.ShapeSettings.txtWood": "Madeira", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "DE.Views.SignatureSettings.strDelete": "Remover assinatura", "DE.Views.SignatureSettings.strDetails": "<PERSON><PERSON><PERSON> da Assinatura", "DE.Views.SignatureSettings.strInvalid": "Assinaturas inválidas", "DE.Views.SignatureSettings.strRequested": "Assinaturas solicitadas", "DE.Views.SignatureSettings.strSetup": "Configurações da Assinatura", "DE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strSignature": "Assinatura", "DE.Views.SignatureSettings.strSigner": "Signatário", "DE.Views.SignatureSettings.strValid": "Assinaturas válidas", "DE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON> <PERSON> qualquer <PERSON>", "DE.Views.SignatureSettings.txtEditWarning": "Editar excluirá as assinaturas do documento. <br> <PERSON><PERSON><PERSON> continuar?", "DE.Views.SignatureSettings.txtRemoveWarning": "Você quer remover esta assinatura? <br><PERSON><PERSON> não pode ser desfeito.", "DE.Views.SignatureSettings.txtRequestedSignatures": "O documento deve ser assinado.", "DE.Views.SignatureSettings.txtSigned": "Assinaturas válidas foram adicionadas ao documento. O documento está protegido contra edição.", "DE.Views.SignatureSettings.txtSignedInvalid": "Algumas das assinaturas digitais no documento estão inválidas ou não puderam ser verificadas. O documento está protegido para edição.", "DE.Views.Statusbar.goToPageText": "Ir para a Página", "DE.Views.Statusbar.pageIndexText": "Página {0} de {1}", "DE.Views.Statusbar.tipFitPage": "Ajustar a página", "DE.Views.Statusbar.tipFitWidth": "Ajustar à largura", "DE.Views.Statusbar.tipHandTool": "Ferramenta de mão", "DE.Views.Statusbar.tipSelectTool": "Selecionar ferramenta", "DE.Views.Statusbar.tipSetLang": "Definir idioma do texto", "DE.Views.Statusbar.tipZoomFactor": "Ampliação", "DE.Views.Statusbar.tipZoomIn": "Ampliar", "DE.Views.Statusbar.tipZoomOut": "Reduzir", "DE.Views.Statusbar.txtPageNumInvalid": "Número da página inválido", "DE.Views.Statusbar.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtParagraphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtSpaces": "Símbolos com espaços", "DE.Views.Statusbar.txtSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtWordCount": "Contagem de palavras", "DE.Views.Statusbar.txtWords": "Palavras", "DE.Views.StyleTitleDialog.textHeader": "Criar Novo Estilo", "DE.Views.StyleTitleDialog.textNextStyle": "Estilo do próximo parágrafo", "DE.Views.StyleTitleDialog.textTitle": "Title", "DE.Views.StyleTitleDialog.txtEmpty": "This field is required", "DE.Views.StyleTitleDialog.txtNotEmpty": "Field must not be empty", "DE.Views.StyleTitleDialog.txtSameAs": "Igual ao novo estilo criado", "DE.Views.TableFormulaDialog.textBookmark": "Colar marcador", "DE.Views.TableFormulaDialog.textFormat": "Formato de número", "DE.Views.TableFormulaDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableFormulaDialog.textInsertFunction": "Colar função", "DE.Views.TableFormulaDialog.textTitle": "Configurações de fórmula", "DE.Views.TableOfContentsSettings.strAlign": "Números de página alinhados à direita", "DE.Views.TableOfContentsSettings.strFullCaption": "Incluir etiqueta e número", "DE.Views.TableOfContentsSettings.strLinks": "Formatar tabela de conteúdo como links", "DE.Views.TableOfContentsSettings.strLinksOF": "Formatar tabela de figuras como links", "DE.Views.TableOfContentsSettings.strShowPages": "Mostrar números de páginas", "DE.Views.TableOfContentsSettings.textBuildTable": "Construir tabela de conteúdo de", "DE.Views.TableOfContentsSettings.textBuildTableOF": "construir tabela de figuras de", "DE.Views.TableOfContentsSettings.textEquation": "Equação", "DE.Views.TableOfContentsSettings.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLeader": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLevel": "Nível", "DE.Views.TableOfContentsSettings.textLevels": "Níveis", "DE.Views.TableOfContentsSettings.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioCaption": "<PERSON>a", "DE.Views.TableOfContentsSettings.textRadioLevels": "Níveis do marcador", "DE.Views.TableOfContentsSettings.textRadioStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioStyles": "Estilos selecionados", "DE.Views.TableOfContentsSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textStyles": "Estilos", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "Tabela de conteúdo", "DE.Views.TableOfContentsSettings.textTitleTOF": "Tabela de figuras", "DE.Views.TableOfContentsSettings.txtCentered": "Centralizado", "DE.Views.TableOfContentsSettings.txtClassic": "Clássico", "DE.Views.TableOfContentsSettings.txtCurrent": "Atual", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distintivo", "DE.Views.TableOfContentsSettings.txtFormal": "Regular", "DE.Views.TableOfContentsSettings.txtModern": "Moderno", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "Simples", "DE.Views.TableOfContentsSettings.txtStandard": "Padrão", "DE.Views.TableSettings.deleteColumnText": "Excluir coluna", "DE.Views.TableSettings.deleteRowText": "Excluir linha", "DE.Views.TableSettings.deleteTableText": "Excluir tabela", "DE.Views.TableSettings.insertColumnLeftText": "Inserir coluna à esquerda", "DE.Views.TableSettings.insertColumnRightText": "Inserir coluna à direita", "DE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> linha acima", "DE.Views.TableSettings.insertRowBelowText": "<PERSON>ser<PERSON> linha a<PERSON>o", "DE.Views.TableSettings.mergeCellsText": "Mesclar célu<PERSON>", "DE.Views.TableSettings.selectCellText": "Selecionar célula", "DE.Views.TableSettings.selectColumnText": "Selecionar coluna", "DE.Views.TableSettings.selectRowText": "Selecionar linha", "DE.Views.TableSettings.selectTableText": "Selecionar tabela", "DE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON>...", "DE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.strRepeatRow": "Repetir como linha de cabeçalho na parte superior de todas as páginas", "DE.Views.TableSettings.textAddFormula": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textAdvanced": "Exibir configura<PERSON><PERSON><PERSON> avan<PERSON>", "DE.Views.TableSettings.textBackColor": "Cor do plano de fundo", "DE.Views.TableSettings.textBanded": "Em tiras", "DE.Views.TableSettings.textBorderColor": "Cor", "DE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON> & colunas", "DE.Views.TableSettings.textColumns": "Colunas", "DE.Views.TableSettings.textConvert": "Converter tabela em texto", "DE.Views.TableSettings.textDistributeCols": "Colunas distribuídas", "DE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON> distribu<PERSON>", "DE.Views.TableSettings.textEdit": "Linhas e Colunas", "DE.Views.TableSettings.textEmptyTemplate": "Sem modelos", "DE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "DE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textHeight": "Altura", "DE.Views.TableSettings.textLast": "Último", "DE.Views.TableSettings.textRows": "<PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> as bordas que você deseja alterar aplicando o estilo escolhido acima", "DE.Views.TableSettings.textTemplate": "Selecionar a partir do modelo", "DE.Views.TableSettings.textTotal": "Total", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "De<PERSON>ir borda externa e todas as linhas internas", "DE.Views.TableSettings.tipBottom": "Definir apenas borda inferior externa", "DE.Views.TableSettings.tipInner": "Definir apenas linhas internas", "DE.Views.TableSettings.tipInnerHor": "Definir apenas linhas internas horizontais", "DE.Views.TableSettings.tipInnerVert": "Definir apenas linhas internas verticais", "DE.Views.TableSettings.tipLeft": "Definir apenas borda esquerda externa", "DE.Views.TableSettings.tipNone": "Definir sem bordas", "DE.Views.TableSettings.tipOuter": "Definir apenas borda externa", "DE.Views.TableSettings.tipRight": "Definir apenas borda direita externa", "DE.Views.TableSettings.tipTop": "Definir apenas borda superior externa", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Tabelas Contornadas e Alinhadas", "DE.Views.TableSettings.txtGroupTable_Custom": "Personalizado", "DE.Views.TableSettings.txtGroupTable_Grid": "Tabelas de grade", "DE.Views.TableSettings.txtGroupTable_List": "Listar tabelas", "DE.Views.TableSettings.txtGroupTable_Plain": "Tabelas simples", "DE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON> bordas", "DE.Views.TableSettings.txtTable_Accent": "Ace<PERSON>", "DE.Views.TableSettings.txtTable_Bordered": "Delimitado", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Contornado e Alinhado", "DE.Views.TableSettings.txtTable_Colorful": "Colorido", "DE.Views.TableSettings.txtTable_Dark": "Escuro", "DE.Views.TableSettings.txtTable_GridTable": "Tabela de grade", "DE.Views.TableSettings.txtTable_Light": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Lined": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_ListTable": "Tabela de Lista", "DE.Views.TableSettings.txtTable_PlainTable": "Tabela Normal", "DE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON> <PERSON>", "DE.Views.TableSettingsAdvanced.textAlign": "Alinhamento", "DE.Views.TableSettingsAdvanced.textAlignment": "Alinhamento", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Permitir <PERSON> entre células", "DE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "DE.Views.TableSettingsAdvanced.textAltDescription": "Descrição", "DE.Views.TableSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação do objeto visual, que será lida para as pessoas com deficiência visual ou cognitiva para ajudá-las a entender melhor quais informações existem na imagem, forma automática, gráfico ou mesa.", "DE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAnchorText": "Тexto", "DE.Views.TableSettingsAdvanced.textAutofit": "Automaticamente redimensionado para ajustar conteúdo", "DE.Views.TableSettingsAdvanced.textBackColor": "Plano de fundo da célula", "DE.Views.TableSettingsAdvanced.textBelow": "abaixo", "DE.Views.TableSettingsAdvanced.textBorderColor": "<PERSON><PERSON> <PERSON> b<PERSON>a", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Clique no diagrama ou use os botões para selecionar bordas e aplicar o estilo escolhido a elas", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Bordas e Plano de fundo", "DE.Views.TableSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBottom": "Inferior", "DE.Views.TableSettingsAdvanced.textCellOptions": "Opções de célula", "DE.Views.TableSettingsAdvanced.textCellProps": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCellSize": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCenter": "Centro", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Centro", "DE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON>ar margens pad<PERSON>", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Margens de célula padrão", "DE.Views.TableSettingsAdvanced.textDistance": "Distância do texto", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.TableSettingsAdvanced.textIndLeft": "<PERSON><PERSON><PERSON> da es<PERSON>da", "DE.Views.TableSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "E<PERSON>rda", "DE.Views.TableSettingsAdvanced.textMargin": "Margem", "DE.Views.TableSettingsAdvanced.textMargins": "Margens da célula", "DE.Views.TableSettingsAdvanced.textMeasure": "Medir em", "DE.Views.TableSettingsAdvanced.textMove": "Mover objeto com texto", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Apenas para as células selecionadas", "DE.Views.TableSettingsAdvanced.textOptions": "Opções", "DE.Views.TableSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON> sobrepo<PERSON>", "DE.Views.TableSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPosition": "Posição", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON><PERSON><PERSON> preferida", "DE.Views.TableSettingsAdvanced.textPreview": "Pré-visualizar", "DE.Views.TableSettingsAdvanced.textRelative": "relativo para", "DE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRightOf": "para a direita de", "DE.Views.TableSettingsAdvanced.textRightTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "Plano de fundo da tabela", "DE.Views.TableSettingsAdvanced.textTablePosition": "Posição de tabela", "DE.Views.TableSettingsAdvanced.textTableSize": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTitle": "Tabela - Configurações avançadas", "DE.Views.TableSettingsAdvanced.textTop": "Parte superior", "DE.Views.TableSettingsAdvanced.textVertical": "Vertical", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Largura e Espaços", "DE.Views.TableSettingsAdvanced.textWrap": "Disposição do texto", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "<PERSON><PERSON><PERSON> embutida", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Tabela de fluxo", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Estilo da quebra automática", "DE.Views.TableSettingsAdvanced.textWrapText": "Quebrar texto ", "DE.Views.TableSettingsAdvanced.tipAll": "De<PERSON>ir borda externa e todas as linhas internas", "DE.Views.TableSettingsAdvanced.tipCellAll": "Definir bordas para células internas apenas", "DE.Views.TableSettingsAdvanced.tipCellInner": "Definir linhas verticais e horizontais apenas para células internas", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Definir bordas externas apenas para células internas", "DE.Views.TableSettingsAdvanced.tipInner": "Definir apenas linhas internas", "DE.Views.TableSettingsAdvanced.tipNone": "Definir sem bordas", "DE.Views.TableSettingsAdvanced.tipOuter": "Definir apenas borda externa", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Definir borda externa e bordas para todas as células internas", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Definir borda externa e linhas verticais e horizontais para células internas", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Definir borda externa da tabela e bordas externas para células internas", "DE.Views.TableSettingsAdvanced.txtCm": "Centímetro", "DE.Views.TableSettingsAdvanced.txtInch": "Polegada", "DE.Views.TableSettingsAdvanced.txtNoBorders": "<PERSON><PERSON> bordas", "DE.Views.TableSettingsAdvanced.txtPercent": "Por cento", "DE.Views.TableSettingsAdvanced.txtPt": "Ponto", "DE.Views.TableToTextDialog.textEmpty": "Você deve digitar um caractere para o separador personalizado.", "DE.Views.TableToTextDialog.textNested": "Converter tabelas anin<PERSON>as", "DE.Views.TableToTextDialog.textOther": "Outro", "DE.Views.TableToTextDialog.textPara": "Marcas de parágrafo", "DE.Views.TableToTextDialog.textSemicolon": "Ponto e vírgula", "DE.Views.TableToTextDialog.textSeparator": "Separe o texto com", "DE.Views.TableToTextDialog.textTab": "Aba", "DE.Views.TableToTextDialog.textTitle": "Converter tabela em texto", "DE.Views.TextArtSettings.strColor": "Color", "DE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strSize": "Size", "DE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strTransparency": "Opacity", "DE.Views.TextArtSettings.strType": "Tipo", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "O valor inserido está incorreto.<br>Insira um valor entre 0 pt e 1.584 pt.", "DE.Views.TextArtSettings.textColor": "Preenchimento de cor", "DE.Views.TextArtSettings.textDirection": "Direção", "DE.Views.TextArtSettings.textGradient": "Pontos de gradiente", "DE.Views.TextArtSettings.textGradientFill": "Preenchimento gradiente", "DE.Views.TextArtSettings.textLinear": "Linear", "DE.Views.TextArtSettings.textNoFill": "No Fill", "DE.Views.TextArtSettings.textPosition": "Posição", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "Selecionar", "DE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textTransform": "Transform", "DE.Views.TextArtSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Remover ponto de gradiente", "DE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> linha", "DE.Views.TextToTableDialog.textAutofit": "Comportamento de Auto-ajuste", "DE.Views.TextToTableDialog.textColumns": "Colunas", "DE.Views.TextToTableDialog.textContents": "Adaptação automática ao conteúdo", "DE.Views.TextToTableDialog.textEmpty": "Você deve digitar um caractere para o separador personalizado.", "DE.Views.TextToTableDialog.textFixed": "Largura fixa da coluna", "DE.Views.TextToTableDialog.textOther": "Outro", "DE.Views.TextToTableDialog.textPara": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Ponto e vírgula", "DE.Views.TextToTableDialog.textSeparator": "Separar texto em", "DE.Views.TextToTableDialog.textTab": "Aba", "DE.Views.TextToTableDialog.textTableSize": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textTitle": "Converter texto em tabela", "DE.Views.TextToTableDialog.textWindow": "Ajustar automaticamente para janela", "DE.Views.TextToTableDialog.txtAutoText": "Automático", "DE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnBlankPage": "Página em branco", "DE.Views.Toolbar.capBtnColumns": "Colunas", "DE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnDateTime": "Data e Hora", "DE.Views.Toolbar.capBtnInsChart": "Gráfico", "DE.Views.Toolbar.capBtnInsControls": "Controles de conteúdo", "DE.Views.Toolbar.capBtnInsDropcap": "Letra capitular", "DE.Views.Toolbar.capBtnInsEquation": "Equação", "DE.Views.Toolbar.capBtnInsHeader": "Cabeçalho/rodapé", "DE.Views.Toolbar.capBtnInsImage": "Imagem", "DE.Views.Toolbar.capBtnInsPagebreak": "Quebras", "DE.Views.Toolbar.capBtnInsShape": "Forma", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Arte de texto", "DE.Views.Toolbar.capBtnInsTextbox": "Caixa de texto", "DE.Views.Toolbar.capBtnLineNumbers": "Números de Linhas", "DE.Views.Toolbar.capBtnMargins": "Margens", "DE.Views.Toolbar.capBtnPageOrient": "Orientação", "DE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnWatermark": "Marca d'á<PERSON>", "DE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capImgBackward": "Enviar para trás", "DE.Views.Toolbar.capImgForward": "Mover para frente", "DE.Views.Toolbar.capImgGroup": "Grupo", "DE.Views.Toolbar.capImgWrapping": "Quebra Automática", "DE.Views.Toolbar.mniCapitalizeWords": "Utilize cada palavra", "DE.Views.Toolbar.mniCustomTable": "Inserir tabela personalizada", "DE.Views.Toolbar.mniDrawTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniEditControls": "Propriedades de Controle", "DE.Views.Toolbar.mniEditDropCap": "Configurações avançadas de Letra capitular", "DE.Views.Toolbar.mniEditFooter": "<PERSON><PERSON>", "DE.Views.Toolbar.mniEditHeader": "<PERSON><PERSON>", "DE.Views.Toolbar.mniEraseTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniFromFile": "Do Arquivo", "DE.Views.Toolbar.mniFromStorage": "De armazenamento", "DE.Views.Toolbar.mniFromUrl": "Da URL", "DE.Views.Toolbar.mniHiddenBorders": "<PERSON><PERSON><PERSON>r bordas da tabela", "DE.Views.Toolbar.mniHiddenChars": "Caracteres não imprimíveis", "DE.Views.Toolbar.mniHighlightControls": "Configurações de destaque", "DE.Views.Toolbar.mniImageFromFile": "Imagem do arquivo", "DE.Views.Toolbar.mniImageFromStorage": "Imagem de armazenamento", "DE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON> da URL", "DE.Views.Toolbar.mniInsertSSE": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniLowerCase": "minúscula", "DE.Views.Toolbar.mniRemoveFooter": "Remover rodapé", "DE.Views.Toolbar.mniRemoveHeader": "Remover cabeçalho", "DE.Views.Toolbar.mniSentenceCase": "Capitular o início de uma frase.", "DE.Views.Toolbar.mniTextToTable": "Converter texto em tabela", "DE.Views.Toolbar.mniToggleCase": "aLTERNAR", "DE.Views.Toolbar.mniUpperCase": "MAIÚSCULO", "DE.Views.Toolbar.strMenuNoFill": "Sem preenchimento", "DE.Views.Toolbar.textAutoColor": "Automático", "DE.Views.Toolbar.textBold": "Negrito", "DE.Views.Toolbar.textBottom": "Inferior:", "DE.Views.Toolbar.textChangeLevel": "Alterar nível de lista", "DE.Views.Toolbar.textCheckboxControl": "Caixa de seleção", "DE.Views.Toolbar.textColumnsCustom": "Colunas personalizadas", "DE.Views.Toolbar.textColumnsLeft": "E<PERSON>rda", "DE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON>", "DE.Views.Toolbar.textColumnsRight": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "DE.Views.Toolbar.textComboboxControl": "Caixa de combinação", "DE.Views.Toolbar.textContinuous": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textContPage": "<PERSON><PERSON>gin<PERSON> contínua", "DE.Views.Toolbar.textCustomLineNumbers": "Opções de numeração de linha", "DE.Views.Toolbar.textDateControl": "Data", "DE.Views.Toolbar.textDropdownControl": "Lista suspensa", "DE.Views.Toolbar.textEditWatermark": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textEvenPage": "Página par", "DE.Views.Toolbar.textInMargin": "<PERSON> margem", "DE.Views.Toolbar.textInsColumnBreak": "Inserir quebra de coluna", "DE.Views.Toolbar.textInsertPageCount": "Inserir número de páginas", "DE.Views.Toolbar.textInsertPageNumber": "Inserir número de página", "DE.Views.Toolbar.textInsPageBreak": "Inserir quebra de página", "DE.Views.Toolbar.textInsSectionBreak": "Inserir quebra de seção", "DE.Views.Toolbar.textInText": "No texto", "DE.Views.Toolbar.textItalic": "Itálico", "DE.Views.Toolbar.textLandscape": "Paisagem", "DE.Views.Toolbar.textLeft": "Esquerda:", "DE.Views.Toolbar.textListSettings": "Configurações da lista", "DE.Views.Toolbar.textMarginsLast": "Últimos personalizados", "DE.Views.Toolbar.textMarginsModerate": "Moderado", "DE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsUsNormal": "US Normal", "DE.Views.Toolbar.textMarginsWide": "Amplo", "DE.Views.Toolbar.textNewColor": "Nova cor personalizada", "DE.Views.Toolbar.textNextPage": "Próxima página", "DE.Views.Toolbar.textNoHighlight": "Sem destaque", "DE.Views.Toolbar.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textOddPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textPageMarginsCustom": "Margens personalizadas", "DE.Views.Toolbar.textPageSizeCustom": "<PERSON><PERSON><PERSON> de página personalizado", "DE.Views.Toolbar.textPictureControl": "Imagem", "DE.Views.Toolbar.textPlainControl": "Texto simples", "DE.Views.Toolbar.textPortrait": "Retrato ", "DE.Views.Toolbar.textRemoveControl": "Remover controle de conteúdo", "DE.Views.Toolbar.textRemWatermark": "Excluir marca d'água", "DE.Views.Toolbar.textRestartEachPage": "Reinicie cada página", "DE.Views.Toolbar.textRestartEachSection": "Reiniciar cada uma das seções", "DE.Views.Toolbar.textRichControl": "Texto rico", "DE.Views.Toolbar.textRight": "Direita:", "DE.Views.Toolbar.textStrikeout": "Taxado", "DE.Views.Toolbar.textStyleMenuDelete": "Excluir estilo", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Excluir todos os estilos personalizados", "DE.Views.Toolbar.textStyleMenuNew": "Novo estilo a partir da seleção", "DE.Views.Toolbar.textStyleMenuRestore": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Restaurar todos os estilos padrão", "DE.Views.Toolbar.textStyleMenuUpdate": "Atualizar da seleção", "DE.Views.Toolbar.textSubscript": "Subscrito", "DE.Views.Toolbar.textSuperscript": "Sobrescrito", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Suprimir para o parágrafo atual", "DE.Views.Toolbar.textTabCollaboration": "Colaboração", "DE.Views.Toolbar.textTabFile": "Arquivo", "DE.Views.Toolbar.textTabHome": "Página Inicial", "DE.Views.Toolbar.textTabInsert": "Inserir", "DE.Views.Toolbar.textTabLayout": "Layout", "DE.Views.Toolbar.textTabLinks": "Referências", "DE.Views.Toolbar.textTabProtect": "Proteção", "DE.Views.Toolbar.textTabReview": "Rev<PERSON><PERSON>", "DE.Views.Toolbar.textTabView": "<PERSON>er", "DE.Views.Toolbar.textTitleError": "Erro", "DE.Views.Toolbar.textToCurrent": "Para posição atual", "DE.Views.Toolbar.textTop": "Parte superior: ", "DE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignCenter": "Alinhar ao centro", "DE.Views.Toolbar.tipAlignJust": "Justificado", "DE.Views.Toolbar.tipAlignLeft": "Alinhar à esquerda", "DE.Views.Toolbar.tipAlignRight": "Alinhar à direita", "DE.Views.Toolbar.tipBack": "Voltar", "DE.Views.Toolbar.tipBlankPage": "Inserir página em branco", "DE.Views.Toolbar.tipChangeCase": "Mudar maiúsculas e minúsculas", "DE.Views.Toolbar.tipChangeChart": "Alterar Tipo de Gráfico", "DE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON> estilo", "DE.Views.Toolbar.tipColorSchemas": "Alterar esquema de cor", "DE.Views.Toolbar.tipColumns": "Inserir colunas", "DE.Views.Toolbar.tipControls": "Adicionar controles de conteúdo", "DE.Views.Toolbar.tipCopy": "Copiar", "DE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "DE.Views.Toolbar.tipCut": "Cortar", "DE.Views.Toolbar.tipDateTime": "Insira a data e hora atuais", "DE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON><PERSON> da <PERSON>e", "DE.Views.Toolbar.tipDecPrLeft": "Diminuir o Recuo", "DE.Views.Toolbar.tipDropCap": "Inserir letra capitular", "DE.Views.Toolbar.tipEditHeader": "Editar cabeçalho e rodapé", "DE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> da fonte", "DE.Views.Toolbar.tipFontName": "Fonte", "DE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipHighlightColor": "Cor de realce", "DE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> ob<PERSON>", "DE.Views.Toolbar.tipImgGroup": "Agrupar objetos", "DE.Views.Toolbar.tipImgWrapping": "Quebrar texto ", "DE.Views.Toolbar.tipIncFont": "Aumentar tamanho da fonte", "DE.Views.Toolbar.tipIncPrLeft": "Aumentar recuo", "DE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "DE.Views.Toolbar.tipInsertEquation": "Inserir equação", "DE.Views.Toolbar.tipInsertHorizontalText": "Inserir caixa de texto horizontal", "DE.Views.Toolbar.tipInsertImage": "Inserir imagem", "DE.Views.Toolbar.tipInsertNum": "Inserir número da página", "DE.Views.Toolbar.tipInsertShape": "Inserir forma automática", "DE.Views.Toolbar.tipInsertSmartArt": "Inserir SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "DE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> tabela", "DE.Views.Toolbar.tipInsertText": "Inserir caixa de texto", "DE.Views.Toolbar.tipInsertTextArt": "Inserir arte de texto", "DE.Views.Toolbar.tipInsertVerticalText": "Inserir caixa de texto vertical", "DE.Views.Toolbar.tipLineNumbers": "Mostrar números de linha", "DE.Views.Toolbar.tipLineSpace": "Espaçamento entre linhas do parágrafo", "DE.Views.Toolbar.tipMailRecepients": "Mescla de e-mail", "DE.Views.Toolbar.tipMarkers": "Marcadores", "DE.Views.Toolbar.tipMarkersArrow": "<PERSON>las de flecha", "DE.Views.Toolbar.tipMarkersCheckmark": "Marcas de verificação", "DE.Views.Toolbar.tipMarkersDash": "Marcadores de roteiro", "DE.Views.Toolbar.tipMarkersFRhombus": "Vinhetas rômbicas cheias", "DE.Views.Toolbar.tipMarkersFRound": "Balas redondas cheias", "DE.Views.Toolbar.tipMarkersFSquare": "Balas quadradas cheias", "DE.Views.Toolbar.tipMarkersHRound": "Balas redondas ocas", "DE.Views.Toolbar.tipMarkersStar": "Balas de estrelas", "DE.Views.Toolbar.tipMultiLevelArticl": "Artigos numerados em vários níveis", "DE.Views.Toolbar.tipMultiLevelChapter": "Capítulos numerados em vários níveis", "DE.Views.Toolbar.tipMultiLevelHeadings": "Títulos numerados em vários níveis", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Vários títulos numerados de vários níveis", "DE.Views.Toolbar.tipMultiLevelNumbered": "Marcadores numerados de vários níveis", "DE.Views.Toolbar.tipMultilevels": "Contorno", "DE.Views.Toolbar.tipMultiLevelSymbols": "Marcadores de símbolos de vários níveis", "DE.Views.Toolbar.tipMultiLevelVarious": "Várias balas numeradas de vários níveis", "DE.Views.Toolbar.tipNumbers": "Numeração", "DE.Views.Toolbar.tipPageBreak": "Inserir página ou quebra de seção", "DE.Views.Toolbar.tipPageMargins": "Margens da página", "DE.Views.Toolbar.tipPageOrient": "Orientação da página", "DE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipParagraphStyle": "Estilo do parágrafo", "DE.Views.Toolbar.tipPaste": "Colar", "DE.Views.Toolbar.tipPrColor": "Cor do plano de fundo do parágrafo", "DE.Views.Toolbar.tipPrint": "Imprimir", "DE.Views.Toolbar.tipPrintQuick": "Impressão rápida", "DE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "DE.Views.Toolbar.tipSaveCoauth": "<PERSON>var suas alterações para que os outros usuários as vejam.", "DE.Views.Toolbar.tipSelectAll": "Selecionar todos", "DE.Views.Toolbar.tipSendBackward": "Enviar para trás", "DE.Views.Toolbar.tipSendForward": "Mover para frente", "DE.Views.Toolbar.tipShowHiddenChars": "Caracteres não imprimíveis", "DE.Views.Toolbar.tipSynchronize": "O documento foi alterado por outro usuário. Clique para salvar suas alterações e recarregar as atualizações.", "DE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipWatermark": "Editar marca d'água", "DE.Views.Toolbar.txtDistribHor": "Distribuir horizontalmente", "DE.Views.Toolbar.txtDistribVert": "Distribuir verticalmente", "DE.Views.Toolbar.txtMarginAlign": "<PERSON><PERSON><PERSON>rge<PERSON>", "DE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON> objetos selecionados", "DE.Views.Toolbar.txtPageAlign": "<PERSON><PERSON><PERSON>ágin<PERSON>", "DE.Views.Toolbar.txtScheme1": "Office", "DE.Views.Toolbar.txtScheme10": "Mediana", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "Opulento", "DE.Views.Toolbar.txtScheme14": "Oriel", "DE.Views.Toolbar.txtScheme15": "Origem", "DE.Views.Toolbar.txtScheme16": "Papel", "DE.Views.Toolbar.txtScheme17": "<PERSON>st<PERSON><PERSON>", "DE.Views.Toolbar.txtScheme18": "Técnica", "DE.Views.Toolbar.txtScheme19": "Viagem", "DE.Views.Toolbar.txtScheme2": "Escala de cinza", "DE.Views.Toolbar.txtScheme20": "Urbano", "DE.Views.Toolbar.txtScheme21": "Verve", "DE.Views.Toolbar.txtScheme22": "Novo Office", "DE.Views.Toolbar.txtScheme3": "Ápice", "DE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "DE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme6": "Concurso", "DE.Views.Toolbar.txtScheme7": "Equidade", "DE.Views.Toolbar.txtScheme8": "Fluxo", "DE.Views.Toolbar.txtScheme9": "Fundição", "DE.Views.ViewTab.textAlwaysShowToolbar": "Sempre mostrar a barra de ferramentas", "DE.Views.ViewTab.textDarkDocument": "Documento escuro", "DE.Views.ViewTab.textFitToPage": "Ajustar a página", "DE.Views.ViewTab.textFitToWidth": "<PERSON><PERSON><PERSON> largura", "DE.Views.ViewTab.textInterfaceTheme": "Tema de interface", "DE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "DE.Views.ViewTab.textNavigation": "Navegação", "DE.Views.ViewTab.textOutline": "Cabeçalhos", "DE.Views.ViewTab.textRightMenu": "<PERSON><PERSON> dire<PERSON>", "DE.Views.ViewTab.textRulers": "Regras", "DE.Views.ViewTab.textStatusBar": "Barra de status", "DE.Views.ViewTab.textZoom": "Ampliação", "DE.Views.ViewTab.tipDarkDocument": "Documento escuro", "DE.Views.ViewTab.tipFitToPage": "Ajustar a página", "DE.Views.ViewTab.tipFitToWidth": "<PERSON><PERSON><PERSON> largura", "DE.Views.ViewTab.tipHeadings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.tipInterfaceTheme": "Tema de interface", "DE.Views.WatermarkSettingsDialog.textAuto": "Automático", "DE.Views.WatermarkSettingsDialog.textBold": "Negrito", "DE.Views.WatermarkSettingsDialog.textColor": "Cor do texto", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "Fonte", "DE.Views.WatermarkSettingsDialog.textFromFile": "Do Arquivo", "DE.Views.WatermarkSettingsDialog.textFromStorage": "De armazenamento", "DE.Views.WatermarkSettingsDialog.textFromUrl": "Da URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horizontal", "DE.Views.WatermarkSettingsDialog.textImageW": "Marca d'água de imagem", "DE.Views.WatermarkSettingsDialog.textItalic": "Itálico", "DE.Views.WatermarkSettingsDialog.textLanguage": "Idioma", "DE.Views.WatermarkSettingsDialog.textLayout": "Layout", "DE.Views.WatermarkSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textScale": "Redimensionar", "DE.Views.WatermarkSettingsDialog.textSelect": "Selecionar Imagem", "DE.Views.WatermarkSettingsDialog.textStrikeout": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textText": "Тexto", "DE.Views.WatermarkSettingsDialog.textTextW": "Marca d'água de texto", "DE.Views.WatermarkSettingsDialog.textTitle": "Configurações de marca d'água", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semitransparente", "DE.Views.WatermarkSettingsDialog.textUnderline": "Sublinhar", "DE.Views.WatermarkSettingsDialog.tipFontName": "Nome da Fonte", "DE.Views.WatermarkSettingsDialog.tipFontSize": "<PERSON><PERSON><PERSON>"}