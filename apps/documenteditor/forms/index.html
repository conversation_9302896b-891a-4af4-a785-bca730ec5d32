<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Documents</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <!-- debug begin -->
    <link rel="stylesheet/less" type="text/css" href="resources/less/application.less" />
    <!-- debug end -->

    <!-- splash -->

    <style type="text/css">
        .theme-dark {
            --background-toolbar: #404040;
            --border-toolbar: #2a2a2a;
            --highlight-button-hover: #555;
            --canvas-background: #555;
        }

        .content-theme-dark {
            --skeleton-canvas-content-background: #3a3a3a;
            --skeleton-canvas-page-border: #2a2a2a;
            --skeleton-canvas-line: rgba(255,255,255,.05);
        }

        .theme-classic-light {
        }

        .theme-light {
            --background-toolbar: #f7f7f7;
            --border-toolbar: #cbcbcb;
            --highlight-button-hover: #e0e0e0;
            --canvas-background: #eee;
        }

        .loadmask {
            left: 0;
            top: 0;
            position: absolute;
            height: 100%;
            width: 100%;
            overflow: hidden;
            border: none;
            background-color: #e2e2e2;
            background-color: var(--canvas-background, #e2e2e2);
            z-index: 1001;
        }

        .loadmask > .brendpanel {
            width: 100%;
            position: absolute;
            height: 40px;
            background-color: #F7F7F7;
            background-color: var(--background-toolbar, #F7F7F7);
            -webkit-box-shadow: inset 0 -1px 0 #cbcbcb;
            box-shadow: inset 0 -1px 0 #cbcbcb;
            -webkit-box-shadow: inset 0 -1px 0 var(--border-toolbar, #cbcbcb);
            box-shadow: inset 0 -1px 0 var(--border-toolbar, #cbcbcb);
        }

        .loadmask > .brendpanel > div {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .loadmask > .brendpanel .loading-logo {
            max-width: 200px;
            height: 20px;
            margin-left: 10px;
            text-align: center;
        }

        .loadmask > .brendpanel .loading-logo > img {
            display: inline-block;
            max-width: 100px;
            max-height: 20px;
            opacity: 0;
        }
        .loadmask > .brendpanel .doc-title {
            flex-grow: 1;
        }

        .loadmask > .brendpanel .circle {
            vertical-align: middle;
            width: 20px;
            height: 20px;
            border-radius: 12px;
            margin: 4px 10px;
            background: rgba(255, 255, 255, 0.2);
        }

        .loadmask .placeholder-outer {
            width: 100%;
            padding-right: 14px;
        }

        .loadmask .placeholder {
            background: #fff;
            background: var(--skeleton-canvas-content-background, #fff);
            width: 796px;
            margin: 59px auto;
            height: 100%;
            border: 1px solid #bbbec2;
            border: var(--scaled-one-px-value, 1px) solid var(--skeleton-canvas-page-border, #bbbec2);
            padding-top: 50px;
        }

        .loadmask .placeholder > .line {
            height: 15px;
            margin: 30px 80px;
            background: var(--skeleton-canvas-line, rgba(0,0,0,.05));
            overflow: hidden;
            position: relative;

            -webkit-animation: flickerAnimation 2s infinite ease-in-out;
            -moz-animation: flickerAnimation 2s infinite ease-in-out;
            -o-animation: flickerAnimation 2s infinite ease-in-out;
            animation: flickerAnimation 2s infinite ease-in-out;
        }

        @keyframes flickerAnimation {
            0%   { opacity:0.1; }
            50%  { opacity:1; }
            100% { opacity:0.1; }
        }
        @-o-keyframes flickerAnimation{
            0%   { opacity:0.1; }
            50%  { opacity:1; }
            100% { opacity:0.1; }
        }
        @-moz-keyframes flickerAnimation{
            0%   { opacity:0.1; }
            50%  { opacity:1; }
            100% { opacity:0.1; }
        }
        @-webkit-keyframes flickerAnimation{
            0%   { opacity:0.1; }
            50%  { opacity:1; }
            100% { opacity:0.1; }
        }
    </style>

    <!--[if lt IE 9]>
      <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.6.1/html5shiv.js"></script>
    <![endif]-->
  </head>

  <body class="embed-body">
      <script src="../../common/main/lib/util/htmlutils.js"></script>
      <script>
          if ( !!window.desktop ) {
              var sheet = document.createElement('style')
              sheet.innerHTML = ".device-desktop .loading-logo {display:none;}";
              document.body.appendChild(sheet);

              document.body.classList.add('device-desktop');
          }
      </script>

      <div id="loading-mask" class="loadmask">
          <div class="brendpanel">
            <div><div class="brand-logo loading-logo"><img src=""></div><div class="doc-title"></div><div class="circle"></div></div>
          </div>
          <div class="placeholder-outer">
          <div class="placeholder">
              <div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div>
          </div>
          </div>
      </div>

       <script>
           var  userAgent = navigator.userAgent.toLowerCase(),
                check = function(regex){ return regex.test(userAgent); },
                stopLoading = false;
           if (!check(/opera/) && (check(/msie/) || check(/trident/))) {
                var m = /msie (\d+\.\d+)/.exec(userAgent);
                if (m && parseFloat(m[1]) < 10.0) {
                    document.write(
                        '<div id="id-error-mask" class="errormask">',
                            '<div class="error-body" align="center">',
                                '<div id="id-error-mask-title" class="title">Your browser is not supported.</div>',
                                '<div id="id-error-mask-text">Sorry, Document Editor is currently only supported in the latest versions of the Chrome, Firefox, Safari or Internet Explorer web browsers.</div>',
                            '</div>',
                        '</div>'
                    );
                    stopLoading = true;
                }
           } else
           if (check(/windows\snt/i)) {
               var re = /chrome\/(\d+)/i.exec(userAgent);
               if (!!re && !!re[1] && !(re[1] > 49)) {
                   setTimeout(function () {
                       document.getElementsByTagName('body')[0].className += "winxp";
                   },0);
               }
           }
           
            function getUrlParams() {
                var e,
                    a = /\+/g,  // Regex for replacing addition symbol with a space
                    r = /([^&=]+)=?([^&]*)/g,
                    d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
                    q = window.location.search.substring(1),
                    urlParams = {};

                while (e = r.exec(q))
                    urlParams[d(e[1])] = d(e[2]);

                return urlParams;
            }

            function encodeUrlParam(str) {
                return str.replace(/"/g, '&quot;')
                        .replace(/'/g, '&#39;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;');
            }

           var params = getUrlParams(),
               lang = (params["lang"] || 'en').split(/[\-\_]/)[0],
               logo = params["headerlogo"] ? encodeUrlParam(params["headerlogo"]) : null,
               logoDark = params["headerlogodark"] ? encodeUrlParam(params["headerlogodark"]) : null;

           window.frameEditorId = params["frameEditorId"];
           window.parentOrigin = params["parentOrigin"];

           if(/MSIE \d|Trident.*rv:/.test(navigator.userAgent))
               document.write('<script src="../../common/main/lib/util/fix-ie-compat.js"><\/script>');

           if (stopLoading) {
               document.body.removeChild(document.getElementById('loading-mask'));
           } else {
               var elem = document.querySelector('.loading-logo');
               if (elem && (logo || logoDark)) {
                   elem.style.backgroundImage= 'none';
                   elem.style.width = 'auto';
                   elem.style.height = 'auto';
                   var img = document.querySelector('.loading-logo img');
                   img && img.setAttribute('src', /theme-dark/.test(document.body.className) ? logoDark || logo : logo || logoDark);
                   img.style.opacity = 1;
               }
           }
       </script>

      <div id="editor_sdk" class="viewer" style="overflow: hidden;" tabindex="-1"></div>

      <div class="overlay-controls" style="margin-left: -32px">
          <ul class="left">
              <li id="id-btn-zoom-in"><button class="overlay svg-icon zoom-up"></button></li>
              <li id="id-btn-zoom-out"><button class="overlay svg-icon zoom-down"></button></li>
          </ul>
      </div>

      <div class="toolbar" id="toolbar">
          <div class="group left">
              <div class="margin-right-large"><a id="header-logo" class="brand-logo" href="http://www.onlyoffice.com/" target="_blank"></a></div>
              <span id="id-btn-undo" class="margin-right-small"></span>
              <span id="id-btn-redo" class="margin-right-small"></span>
              <div class="separator"></div>
              <span id="id-btn-prev-field" class="margin-left-small margin-right-small"></span>
              <span id="id-btn-next-field" class="margin-right-large"></span>
              <span id="id-btn-clear-fields"></span>
          </div>
          <div class="group center">
              <span id="title-doc-name"></span>
          </div>
          <div class="group right">
              <div id="id-pages" class="item margin-right-small" style="vertical-align: middle;">
                  <div id="page-number" style="display: inline-block; vertical-align: middle;"></div><span class="text" id="pages" tabindex="-1">of 1</span>
              </div>
              <div id="id-submit-group" style="display: inline-block;"></div>
              <div id="id-download-group" style="display: inline-block;"></div>
              <span id="box-tools"></span>
          </div>
      </div>

      <!-- debug begin -->
      <script type="text/javascript">var less=less||{};less.env='development';</script>
      <script src="../../../vendor/less/dist/less.js" type="text/javascript"></script>
      <!-- debug end -->

      <!--vendor-->
      <script type="text/javascript" src="../../../vendor/jquery/jquery.min.js"></script>
      <script type="text/javascript" src="../../../vendor/xregexp/xregexp-all-min.js"></script>

      <script type="text/javascript" src="../../../../sdkjs/develop/sdkjs/word/scripts.js"></script>
      <script>
          window.sdk_scripts.forEach(function(item){
              document.write('<script type="text/javascript" src="' + item + '"><\/script>');
          });
          window.requireTimeourError = function(){
              var reqerr;

              if ( lang == 'de')      reqerr = 'Die Verbindung ist zu langsam, einige Komponenten konnten nicht geladen werden. Aktualisieren Sie bitte die Seite.';
              else if ( lang == 'es') reqerr = 'La conexión es muy lenta, algunos de los componentes no han podido cargar. Por favor recargue la página.';
              else if ( lang == 'fr') reqerr = 'La connexion est trop lente, certains des composants n\'ons pas pu être chargé. Veuillez recharger la page.';
              else if ( lang == 'ru') reqerr = 'Слишком медленное соединение, не удается загрузить некоторые компоненты. Пожалуйста, обновите страницу.';
              else if ( lang == 'tr') reqerr = 'Bağlantı çok yavaş, bileşenlerin bazıları yüklenemedi. Lütfen sayfayı yenileyin.';
              else reqerr = 'The connection is too slow, some of the components could not be loaded. Please reload the page.';

              return reqerr;
          };
      </script>
      <script data-main="app_dev" src="../../../vendor/requirejs/require.js"></script>
  </body>
</html>
