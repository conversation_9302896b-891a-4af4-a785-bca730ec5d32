<svg width="40" height="40" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <style>
        svg>g {display: none}
        :target {display: inline}
    </style>
    <g id="attention" >
        <path d="M3.25245 31.1904L18.366 4.94276C19.0898 3.68575 20.9102 3.68575 21.634 4.94275L36.7475 31.1904C37.4678 32.4412 36.5612 34 35.1136 34H4.88642C3.43881 34 2.53225 32.4412 3.25245 31.1904Z" fill="#FFD112"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M21 25H19L18 18V12H22V18L21 25ZM22 31H18V27H22V31Z" fill="white"/>
    </g>


    <g id="done" >
        <circle cx="20" cy="20" r="16" fill="#009900"/>
        <path d="M12 20L18 26L29 15" stroke="white" stroke-width="4"/>
    </g>

    <g id="info">
        <circle cx="20" cy="20" r="16" fill="#007DF1"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M22 10H18V14H22V10ZM22 17H18V30H22V17Z" fill="white"/>
    </g>

    <g id="warning">
        <circle cx="20" cy="20" r="16" fill="#F62211"/>
        <rect x="10" y="17" width="20" height="6" fill="white"/>
    </g>
</svg>
