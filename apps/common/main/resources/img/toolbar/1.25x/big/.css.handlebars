{{#spritesheet}}
@media only screen {
    @media (-webkit-min-device-pixel-ratio: 1.25) and (-webkit-max-device-pixel-ratio: 1.4),
            (min-resolution: 1.25dppx) and (max-resolution: 1.4dppx),
                (min-resolution: 120dpi) and (max-resolution: 143dpi)
    {
        .x-huge .toolbar__icon, .toolbar__icon.toolbar__icon-big {
            background-image: url(resources/{{{escaped_image}}});
            background-size: {{scaled width 1.25}}px auto;
        }
    }
}

.toolbar__icon.toolbar__icon-big {
    background-size: 56px auto;
    background-size: var(--big-icon-background-image-width) auto;
}
{{/spritesheet}}
