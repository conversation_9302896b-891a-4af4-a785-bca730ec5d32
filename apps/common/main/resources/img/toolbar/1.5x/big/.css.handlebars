{{#spritesheet}}
@media only screen {
    @media (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.7),
            (min-resolution: 1.5dppx) and (max-resolution: 1.7dppx),
                (min-resolution: 144dpi) and (max-resolution: 167dpi)
    {
        .x-huge .toolbar__icon, .toolbar__icon.toolbar__icon-big {
            background-image: url(resources/{{{escaped_image}}});
            background-size: {{scaled width 1.5}}px auto;
        }
    }
}

.toolbar__icon.toolbar__icon-big {
    background-size: 56px auto;
    background-size: var(--big-icon-background-image-width) auto;
}
{{/spritesheet}}
