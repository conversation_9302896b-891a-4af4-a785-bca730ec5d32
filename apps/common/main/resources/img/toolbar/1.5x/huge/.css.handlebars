{{#spritesheet}}
@media only screen {
    @media (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.9),
            (min-resolution: 1.5dppx) and (max-resolution: 1.9dppx),
            (min-resolution: 144dpi) and (max-resolution: 191dpi)
    {
        .options__icon.options__icon-huge {
            background-image: url(resources/{{{escaped_image}}});
            background-size: 80px auto;
            background-size: var(--huge-icon-background-image-width) auto;
        }
    }
}
{{/spritesheet}}
