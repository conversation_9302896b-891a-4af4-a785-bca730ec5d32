{{#spritesheet}}
.btn {
    .options__icon.options__icon-huge {
        background-position-x: 0;
        background-position-x: var(--button-huge-normal-icon-offset-x,0);
    }

    &.active, &:active {
        &:not(:disabled):not(.disabled) {
            .options__icon.options__icon-huge {
                background-position-x: 0;
                background-position-x: var(--button-huge-active-icon-offset-x,0);
            }
        }
    }
}

.options__icon.options__icon-huge {
    background-image: url(resources/{{{escaped_image}}});
}
{{/spritesheet}}
{{#sprites}}
.options__icon-huge.{{name}}
{
    background-position: {{px.offset_x}} {{px.offset_y}};
}
{{/sprites}}
