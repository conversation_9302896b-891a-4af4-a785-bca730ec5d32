{{#spritesheet}}
.btn {
    &.x-huge {
        &.active, &:active {
            &:not(:disabled):not(.disabled) {
                .toolbar__icon {
                    background-position-x: -28px;
                    background-position-x: var(--button-xhuge-active-icon-offset-x,0);
                }
            }
        }
    }

    &.active, &:active {
        &:not(:disabled):not(.disabled) {
            .toolbar__icon.toolbar__icon-big {
                background-position-x: -28px;
                background-position-x: var(--button-xhuge-active-icon-offset-x,0);
            }
        }
    }
}
.x-huge .toolbar__icon, .toolbar__icon.toolbar__icon-big {
    background-image: url(resources/{{{escaped_image}}});
}
{{/spritesheet}}
{{#sprites}}
{{#parselang name}}.x-huge .{{name}}{{/parselang}}:not(.menu__icon),
    .toolbar__icon-big.{{name}}
{
    background-position: 0 {{px.offset_y}};
    background-position: var(--button-xhuge-normal-icon-offset-x,0) {{px.offset_y}};
}
{{/sprites}}
