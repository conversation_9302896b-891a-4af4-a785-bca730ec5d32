{{#spritesheet}}
.btn {
    &.active, &:active {
        &:not(:disabled):not(.disabled) {
            .toolbar__icon {
                @btn-active-icon-offset: -20px;
                background-position-x: @btn-active-icon-offset;

                background-position-x: var(--button-small-active-icon-offset-x, 0);
            }
        }
    }
}
.toolbar__icon, .menu__icon {
    background-image: url(resources/{{{escaped_image}}});
}
{{/spritesheet}}
{{#sprites}}
{{#parselang name}}.{{name}}{{/parselang}} {
    @btn-icon-offset: 0;
    background-position: @btn-icon-offset {{px.offset_y}};

    background-position: var(--button-small-normal-icon-offset-x, 0) {{px.offset_y}};
}
{{/sprites}}
