{{#spritesheet}}
@media only screen {
    @media (-webkit-min-device-pixel-ratio: 1.75) and (-webkit-max-device-pixel-ratio: 1.9),
            (min-resolution: 1.75dppx) and (max-resolution: 1.9dppx),
                (min-resolution: 168dpi) and (max-resolution: 191dpi)
    {
        .x-huge .toolbar__icon, .toolbar__icon.toolbar__icon-big {
            background-image: url(resources/{{{escaped_image}}});
            background-size: {{scaled width 1.75}}px auto;
        }
    }
}

.toolbar__icon.toolbar__icon-big {
    background-size: 56px auto;
    background-size: var(--big-icon-background-image-width) auto;
}
{{/spritesheet}}
