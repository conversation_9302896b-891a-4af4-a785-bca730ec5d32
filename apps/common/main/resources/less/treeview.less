.treeview {
  border: 1px solid @input-border;
  .border-radius(@border-radius-small);

  &.inner {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;

    .empty-text {
      text-align: center;
      height: 100%;
      width: 100%;
      color: @text-tertiary-ie;
      color: @text-tertiary;
      td {
        padding: 5px;
      }
    }
  }

  &:focus {
    border-color: @border-preview-select-ie;
    border-color: @border-preview-select;
  }

  > .item {
    display: block;
    width: 100%;
    .box-shadow(none);
    margin: 0;

    &:hover,
    &.over {
      background-color: @highlight-button-hover-ie;
      background-color: @highlight-button-hover;
    }

    &.selected {
      background-color: @highlight-button-pressed-ie;
      background-color: @highlight-button-pressed;
      color: @text-normal-pressed-ie;
      color: @text-normal-pressed;
    }

    &.selected .empty {
      display: none;
    }

  }

  .tree-item {
    width: 100%;
    min-height: 28px;
    padding: 0px 12px 0 24px;
  }

  .name {
    width: 100%;
    padding: 5px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.empty {
      color: @text-tertiary-ie;
      color: @text-tertiary;
      font-style: italic;
    }
  }

  .tree-caret {
    width: 24px;
    height: 24px;
    background-position: 3px -270px;
    display: inline-block;
    position: absolute;
    left: 0;
    cursor: pointer;

    &.up {
      transform: rotate(270deg);
    }
  }
}

.safari {
  .treeview .name::before {
      content: '';
      display: block;
    }
}