
.tooltip {
    &.in {
        opacity: 1;
    }

    &.arrow-free {
        &.top    { margin-top: 0; }
    //    &.right  { margin-left:  3px; padding: 0 @tooltip-arrow-width; }
        &.bottom { margin-top: 0; }
    //    &.left   { margin-left: -3px; padding: 0 @tooltip-arrow-width; }
    }

    &.top-left .tooltip-arrow,
    &.top-right .tooltip-arrow {
        bottom: 1px;
        border-width: 5px 5px 0;
        border-top-color: @background-normal-ie;
        border-top-color: @background-normal;
    }

    &.top-left .tooltip-arrow,
    &.bottom-left .tooltip-arrow {
        left: 5px;
    }

    &.top-right .tooltip-arrow,
    &.bottom-right .tooltip-arrow {
        right: 5px;
    }

    &.bottom-left .tooltip-arrow,
    &.bottom-right .tooltip-arrow {
        top: 1px;
        border-width: 0 5px 5px;
        border-bottom-color: @background-normal-ie;
        border-bottom-color: @background-normal;
    }
}

.tooltip-inner {
    font-size: 11px;
    background-color: @background-normal-ie;
    background-color: @background-normal;
    color: @text-normal-ie;
    color: @text-normal;
    padding: 5px 12px;
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;
    .box-shadow(0 6px 12px rgba(0, 0, 0, 0.175));
    background-clip: padding-box;

    white-space: pre-wrap;
    text-align: left;
    word-wrap: break-word;
    max-width: 300px;
}

.link-tooltip .tooltip-inner {
    max-width: 500px;
}

.auto-tooltip .tooltip-inner {
    max-width: none;
}