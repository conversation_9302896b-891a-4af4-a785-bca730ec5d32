.form-control {
    .border-radius(@border-radius-small);
    .box-shadow(none);
    color: @text-normal-ie;
    color: @text-normal;
    .user-select(text);

    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;
    background-color: @background-normal-ie;
    background-color: @background-normal;
    &:-ms-input-placeholder {
        color: @text-tertiary-ie;
    }
    &:focus {
        border-color: @border-control-focus-ie;
        border-color: @border-control-focus;
        outline: 0;
        .box-shadow(none);
    }
}

.form-control[readonly] {
    cursor: pointer;
    background-color: @background-normal-ie;
    background-color: @background-normal;
}

.form-control[disabled] {
    cursor: default;
    background-color: @background-normal-ie;
    background-color: @background-normal;
   .user-select(none);
}

.input-row {
    position: relative;
}

.input-error {
    display: none;

    &:before {
        content: '';
        position: absolute;
        right: 3px;
        top: 3px;
        width: 16px;
        height: 16px;
        background-position: @input-error-offset-x @input-error-offset-y;
        filter: none;
    }
}

.input-field {
    input,
    .input-error {
        float: left;
        position: relative;
    }

    &.form-control:focus,
    .form-control:focus {
        //border-color: @border-control-focus;
    }

    &.error {
        input:not([disabled]) {
            border-color: @brand-danger;
        }

        input:not([disabled]) + .input-error {
            display: block;
        }
    }

    &.warning {
        input:not([disabled]) + .input-error {
            display: block;
        }

        .input-error {
            &:before {
                background-position: @input-warning-offset-x @input-warning-offset-y;
            }
        }
    }
}

input:required:focus:invalid,
input:focus:invalid,
input.error {
    border-color: @brand-danger;

    & + span.input-error {
        display: block;
    }
}

.disabled .form-control {
    opacity: @component-disabled-opacity-ie;
    opacity: @component-disabled-opacity;
    cursor: default !important;
}

::-ms-clear {
    display: none;
}

input[type="password"] {
    font-size: 16px;
}

input[type="text"]::selection, textarea::selection {
    background: @highlight-text-select-ie;
    background: @highlight-text-select;
    color: @text-inverse-ie;
    color: @text-inverse;
}

textarea.form-control:focus {
    //border-color: @border-control-focus;
}

.input-field-btn {
    position: relative;

    .select-button {
        position: absolute;
        right: @scaled-one-px-value-ie;
        right: @scaled-one-px-value;
        top: @scaled-one-px-value-ie;
        top: @scaled-one-px-value;

        .btn-group > .btn-toolbar,
        & > .btn-toolbar {
            height: 20px;
            height: calc(22px - @scaled-one-px-value * 2);
        }
    }

    .input-error {
        &:before {
            right: 22px;
        }
    }
    input {
        padding-right: 20px;
    }
}