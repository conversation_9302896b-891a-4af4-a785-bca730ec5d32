//#header-logo {
//    background-color: @app-header-bg-color-dark;
//    min-width: 15px + 90px + 5px;
//    .background-ximage('@{common-image-path}/header/header-logo.png', '@{common-image-path}/header/<EMAIL>', 86px);
//    background-repeat: no-repeat;
//    background-position: 15px center;
//    cursor: pointer;
//}

//#header-documentcaption {
//    width: 100%;
//    max-width: 100px;
//    overflow: hidden;
//    text-overflow: ellipsis;
//    background-color: @app-header-bg-color;
//    padding: 0 7px + @app-header-height / 3;
//    cursor: default;
//
//    div {
//        display: inline-block;
//        padding: 2px 8px;
//
//        &.renamed:hover {
//            background-color: @app-header-bg-color-dark;
//        }
//    }
//}

.toolbar {
    .extra {
        svg.icon {
            fill: @icon-toolbar-header-ie;
            fill: @icon-toolbar-header;
        }

        .btn-slot {
            width: auto;
        }
    }
}

.extra {
    > section {
        height: 100%;
        display: flex;
    }

    &.left {
        //min-width: 126px;
    }

    &.right {
        flex-grow: 1;
        flex-shrink: 0;
        overflow: hidden;
    }

    .status-label {
        padding: 0 10px;
    }

    label {
        color: @text-toolbar-header-ie;
        color: @text-toolbar-header;
    }

    .dropdown-menu {
        label {
            color: @text-normal-ie;
            color: @text-normal;
        }
    }

    .btn-users,
    .btn-header {
        &:hover {
            &:not(.disabled):not(.icon) {
                background-color: @highlight-header-button-hover-ie;
                background-color: @highlight-header-button-hover;
            }
        }

        &:active, &.active {
            &:not(.disabled):not(.icon) {
                background-color: @highlight-header-button-pressed-ie;
                background-color: @highlight-header-button-pressed;
            }
        }

        &.icon {
            pointer-events:none;
        }
    }

    #box-doc-name {
        flex-grow: 1;
        display: flex;
        justify-content: center;
        padding: 4px 2px;
        overflow: hidden;
        flex-shrink: 0;
    }

    #rib-doc-name {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        min-width: 50px;
        text-align: center;
        color: @text-toolbar-header-ie;
        color: @text-toolbar-header;
        background-color: transparent;
        border: 0 none;
        padding: 1px 5px;
        cursor: default;
        line-height: 24px;

        &:hover:not(:disabled),&:focus {
            box-shadow: 0 0 0 @scaled-one-px-value-ie @highlight-header-button-hover-ie;
            box-shadow: 0 0 0 @scaled-one-px-value @highlight-header-button-hover;
            border-radius: 1px;
            cursor: text;
        }
    }

    #rib-save-status {
        white-space: nowrap;
        &.locked {
            text-decoration: none;
            color: @border-preview-select-ie;
            color: @border-preview-select;
            cursor: default;
        }

        &:after {
            display: block;
            content: attr(data-width);
            height: 1px;
            overflow: hidden;
            visibility: hidden;
        }
    }

    #header-logo {
        max-width: 200px;
        height: 100%;
        padding: 6px 24px 6px 12px;

        i {
            cursor: pointer;
            width: 86px;
            height: 20px;
            display: inline-block;
            vertical-align: middle;

            background-image: ~"url('@{common-image-const-path}/header/header-logo_s.svg')";
            background-repeat: no-repeat;
        }

        &.link img {
            cursor: pointer;
        }

        #box-document-title & {
            padding: 4px 24px 4px 12px;
        }
    }
}

#tlb-box-users {
}

#tlb-change-rights {
    margin-top: 15px;
}

.hedset {
    font-size: 0;
    display: flex;

    .btn-group {
        height: 100%;
    }

}

.btn-header {
    height: 100%;
    background-color: transparent;
    width: 40px;

    .icon {
        width: 20px;
        height: 20px;
        display: inline-block;
        position: relative;

        &.icon--inverse {
            background-position-x: @button-header-normal-icon-offset-x-ie;
            background-position-x: @button-header-normal-icon-offset-x;
        }
    }

    .btn& {
        &:not(:disabled) .icon {
            opacity: @header-component-normal-icon-opacity;
        }

        &:active, &.active {
            .icon.toolbar__icon {
                &.icon--inverse {
                    background-position-x: @button-header-active-icon-offset-x-ie;
                    opacity: @header-component-active-icon-opacity;
                }
            }
        }

        &:hover:not(:disabled) {
            .icon {
                opacity: @header-component-hover-icon-opacity;
            }
        }
    }

    svg.icon {
        vertical-align: middle;

        @media
        only screen and (-webkit-min-device-pixel-ratio: 1.5),
        only screen and (min-resolution: 1.5dppx),
        only screen and (min-resolution: 144dpi) {
            width:calc(~"28px/1.5");
            height:calc(~"28px/1.5");
        }
    }

    .btn&[disabled],
    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }

    &:hover {
        &:not(.disabled) {
            background-color: @highlight-header-button-hover-ie;
            background-color: @highlight-header-button-hover;
        }
    }

    &:active {
        &:not(.disabled) {
            background-color: @highlight-header-button-pressed-ie;
            background-color: @highlight-header-button-pressed;
        }
    }

    &.no-caret {
        .inner-box-caret {
            display: none;
        }
    }
}

.btn-users {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0 8px 0 6px;
    height: 100%;

    .inner-box-icon {
        width: 28px;
        position: relative;

        > svg {
            position: absolute;
            width: 28px;
            height: 20px;
            top: 50%;
            margin-top: -10px;
            fill: @icon-toolbar-header-ie;
            fill: @icon-toolbar-header;
        }
    }

    .caption {
        cursor: pointer;
        font-size: 9px;
        margin-left: -18px;
        width: 16px;
        text-align: center;
        overflow: hidden;
        pointer-events: none;
    }

    &:not(:disabled) {
        .inner-box-icon, .caption {
            opacity: @header-component-normal-icon-opacity;
        }
    }

    &:hover:not(:disabled)  {
        .inner-box-icon, .caption {
            opacity: @header-component-hover-icon-opacity;
        }
    }

    &:active:not(:disabled), &.active:not(:disabled) {
        .inner-box-icon, .caption {
            opacity: @header-component-active-icon-opacity;
        }
    }

    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
        pointer-events: none;
    }
}

.btn-header-share {
    display: flex;
    align-items: center;
    padding: 0 12px;
    width: auto;

    .caption {
        margin: 2px 0 0 2px;
        color: @text-toolbar-header-ie;
        color: @text-toolbar-header;
    }

    &:not(:disabled) {
        .caption {
            opacity: @header-component-normal-icon-opacity;
        }
    }

    &:hover:not(:disabled)  {
        .caption {
            opacity: @header-component-hover-icon-opacity;
        }
    }

    &:active:not(:disabled), &.active:not(:disabled) {
        .caption {
            opacity: @header-component-active-icon-opacity;
        }
    }
}

.btn-current-user {
    display: flex;
    align-items: center;
    height: 100%;
    width: 40px;
    padding: 1px 3px;
    border: var(--scaled-one-pixel, 1px) solid transparent;
}

.btn-current-user, .btn-header {
    &:not(:disabled) .color-user-name {
        opacity: @header-component-normal-icon-opacity;
    }

    &:active, &.active .color-user-name {
        opacity: @header-component-active-icon-opacity;
    }

    &:hover:not(:disabled) .color-user-name {
        opacity: @header-component-hover-icon-opacity;
    }

    .color-user-name {
        width: 20px;
        height: 20px;
        border-radius: 20px;
        background-color: @icon-toolbar-header-ie;
        background-color: @icon-toolbar-header;
        color: @toolbar-header-text-on-background-ie;
        color: @toolbar-header-text-on-background;
        font-size: 10px;
        line-height: 20px;
        overflow: hidden;
        margin: 0 6px;
        text-align: center;
    }
}

.cousers-menu {
    position: fixed;
    top: @height-tabs - 8px;
    left: 100%;
    margin-left: -285px;

    padding: 14px;
    width: 285px;
    font-size: 12px;

    z-index: 1042;

    .top-title & {
        top: @height-title + @height-tabs - 8px;
    }

    > label {
        white-space: normal;
    }

    label {
        color: @text-normal-ie;
        color: @text-normal;
    }

    .cousers-list {
        margin-top: 15px;

        ul {
            margin: 0;
            padding: 0;
            overflow: hidden;
            max-height: 195px;
            position: relative;

            li {
                list-style: none;
                padding: 2px 0;
                overflow: hidden;

                &.offline, &.viewmode {
                    display: none;
                }
            }
        }

        .color {
            width: 12px;
            height: 12px;
            display: inline-block;
            vertical-align: middle;
            border: @scaled-one-px-value solid @border-toolbar;
            margin: 0 5px 1px 0;
        }

        .user-name {
            color: @text-normal-ie;
            color: @text-normal;
            font-size: 12px;
            .font-weight-bold();
            white-space: nowrap;
            cursor: default;

            label {
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: middle;
                max-width: 200px;
                .font-weight-bold();
            }
        }
    }
}

#box-document-title {
    background-color: @header-background-color-ie;
    background-color: @header-background-color;
    display: flex;
    height: 100%;
    color: @text-toolbar-header-ie;
    color: @text-toolbar-header;
    position: relative;
    z-index: 1;

    .btn-slot {
        display: inline-block;
    }

    svg.icon {
        fill: @icon-toolbar-header-ie;
        fill: @icon-toolbar-header;

        &.icon-save {
            &.btn-save-coauth, &.btn-synch {
                use:first-child {
                    display: none;
                }
            }

            &:not(.btn-save-coauth) {
                use#coauth {
                    display: none;
                }
            }
            &:not(.btn-synch) {
                use#sync {
                    display: none;
                }
            }
        }
    }

    #id-box-doc-name {
        display: flex;
        justify-content: center;
        overflow: hidden;
        padding: 4px 2px;
    }

    #title-doc-name {
        white-space: pre;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: center;
        font-size: 12px;
        line-height: 24px;
        padding: 1px 5px;
        background-color: transparent;
        border: 0 none;
        cursor: default;
        .user-select(text);

        &:hover:not(:disabled),&:focus{
            box-shadow: 0 0 0 @scaled-one-px-value-ie @highlight-header-button-hover-ie;
            box-shadow: 0 0 0 @scaled-one-px-value @highlight-header-button-hover;
            border-radius: 1px;
            cursor: text;
        }

   }

    .lr-separator {
        flex-grow: 1;
    }
}

#box-doc-name, #box-document-title {
    .inner-box-icon.crypted {
        width: 20px;
        position: relative;
        margin-right: 1px;
        flex-shrink: 0;
        flex-grow: 0;
        > svg {
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            margin-top: -10px;
        }
    }
}


