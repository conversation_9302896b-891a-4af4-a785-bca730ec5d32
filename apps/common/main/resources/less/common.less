label {
    &.link {
        border-bottom: @scaled-one-px-value-ie dotted @text-secondary-ie;
        border-bottom: @scaled-one-px-value dotted @text-secondary;
        cursor: pointer;

        &.disabled {
            cursor: default;
        }
    }

    &.link-solid {
        border-bottom: @scaled-one-px-value-ie solid @text-secondary-ie;
        border-bottom: @scaled-one-px-value solid @text-secondary;
        cursor: pointer;

        &.disabled {
            cursor: default;
        }
    }

    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }

    &.fixed {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}

.menu-shapes {
    .item {
        .box-shadow(none);
    }
}

.combobox.fonts {
    > .dropdown-menu {
        max-height: 388px;
    }
}

.user-select {
    .user-select(text);
}

.tool-menu {
    height: 100%;
    display: block;

    &.left {
        overflow: hidden;

        .tool-menu-btns {
            border-right: @scaled-one-px-value-ie solid @border-toolbar-ie;
            border-right: @scaled-one-px-value solid @border-toolbar;
        }
    }

    &.right {
        .tool-menu-btns {
            //position: absolute;
            border-left: @scaled-one-px-value-ie solid @border-toolbar-ie;
            border-left: @scaled-one-px-value solid @border-toolbar;
            background-color: @background-toolbar-ie;
            background-color: @background-toolbar;
            right: 0;
            overflow: hidden;
        }
    }
}

.tool-menu-btns {
    width: 40px;
    height: 100%;
    display: inline-block;
    position: absolute;
    padding-top: 15px;

    button {
        margin-bottom: 8px;
    }
}

.left-panel {
    padding-left: 40px;
    height: 100%;
    border-right: @scaled-one-px-value-ie solid @border-toolbar-ie;
    border-right: @scaled-one-px-value solid @border-toolbar;

    #left-panel-chat {
        height: 100%;
    }

    #left-panel-comments {
        height: 100%;
    }

    #left-panel-search {
        height: 100%;
    }
}

.right-panel {
    width: 220px;
    height: 100%;
    display: none;
    padding: 0 10px 0 15px;
    position: relative;
    overflow: hidden;
    border-left: @scaled-one-px-value-ie solid @border-toolbar-ie;
    border-left: @scaled-one-px-value solid @border-toolbar;
    line-height: 15px;
}

.statusbar {
    height: 25px;
    background-color: @background-toolbar-ie;
    background-color: @background-toolbar;
    .box-inner-shadow(0 @scaled-one-px-value-ie 0 @border-toolbar-ie);
    .box-inner-shadow(0 @scaled-one-px-value 0 @border-toolbar);

    .status-label {
        .font-weight-bold();
        color: @text-normal-ie;
        color: @text-normal;
        white-space: nowrap;
    }
}

#file-menu-panel {
    > div {
        height: 100%;
    }

    .panel-menu {
        width: 260px;
        max-height: 100%;
        position: relative;
        overflow: hidden;
        float: left;
        border-right: @scaled-one-px-value-ie solid @border-toolbar-ie;
        border-right: @scaled-one-px-value solid @border-toolbar;
        background-color: @background-toolbar-ie;
        background-color: @background-toolbar;
    }

    .flex-settings {
        &.bordered {
            border-bottom: @scaled-one-px-value-ie solid @border-toolbar-ie;
            border-bottom: @scaled-one-px-value solid @border-toolbar;
        }
        overflow: hidden;
        position: relative;
    }

}

.settings-panel {
    display: none;
    overflow: visible;
    margin-top: 7px;

    & > table {
        width: 100%;
    }

    &.active {
        display: block;
    }

    .padding-very-small {
        padding-bottom: 4px;
    }
    .padding-small {
        padding-bottom: 8px;
    }

    .padding-large {
        padding-bottom: 16px;
    }

    .finish-cell {
        height: 15px;
    }

    label {
        .font-size-normal();
        font-weight: normal;

        &.input-label{
            margin-bottom: 0;
            vertical-align: middle;
        }

        &.header {
            .font-weight-bold();
        }
    }

    .separator { width: 100%;}

    .settings-hidden {
        display: none;
    }

    textarea {
        .user-select(text);
        width: 100%;
        resize: none;
        margin-bottom: 5px;
        border: @scaled-one-px-value-ie solid @border-regular-control-ie;
        border: @scaled-one-px-value solid @border-regular-control;
        height: 100%;

        &.disabled {
            opacity: @component-disabled-opacity-ie;
            opacity: @component-disabled-opacity;
            cursor: default !important;
        }
    }
}

.dropdown-menu {
    > li > a {
        color: @text-normal-ie;
        color: @text-normal;
    }
}

textarea {
    background-color: @background-normal-ie;
    background-color: @background-normal;
    color: @text-normal-ie;
    color: @text-normal;
    &:-ms-input-placeholder {
        color: @text-tertiary-ie;
    }
    .placeholder();
}

.btn-edit-table,
.btn-change-shape {
    .background-ximage-v2('right-panels/rowscols_icon.png', 56px);
    margin-right: 2px !important;
    margin-bottom: 1px !important;

    background-position-x: calc(@button-small-normal-icon-offset-x - 8px);

    .btn-group.open &,
        button.active:not(.disabled) &,
        button:active:not(.disabled) &
    {
        background-position-x: calc(@button-small-active-icon-offset-x - 8px);
    }
}

.btn-edit-table {
    background-position-y: 0;

    button.over & {
        //background-position: -28px 0;
    }
}

.btn-change-shape {
    background-position-y: -16px;

    button.over & {
        //background-position: -28px -16px;
    }
}

.doc-content-color {
    background-color: @canvas-content-background;
}

a {
    color: @text-link;

    &:hover {
        color: @text-link-hover;
    }

    &:active {
        color: @text-link-active;
    }

    &:visited {
        color: @text-link-visited;
    }
}

body {
    &.pixel-ratio__1_75 {
        image-rendering: crisp-edges; // FF only
    }

    font-family: @font-family-sans-serif;
    font-family: @font-family-base;
}