.checkbox-indeterminate {
    padding-left: 22px;
    margin-bottom: 0;
    .font-size-normal();
    font-weight: normal;
    position: relative;
    min-height: 1em;

    input[type=checkbox] {
        display: none;

        + label {
            width: 14px;
            height: 14px;
            background: @background-normal-ie;
            background: @background-normal;
            border: @scaled-one-px-value-ie solid @border-regular-control-ie;
            border: @scaled-one-px-value solid @border-regular-control;
            border-radius: 2px;
            position: absolute;
            left: 0;
            margin-top: auto;

            + span {
                outline: @scaled-one-px-value-ie dotted transparent;
                outline: @scaled-one-px-value dotted transparent;
                display: inline-block;
            }
        }

        &:checked:not(:indeterminate) {
            + label {
                &::before {
                    content: '';
                    position: absolute;
                    border: 2px solid @text-normal-ie;
                    border: @scaled-two-px-value solid @text-normal;
                    border-top: 0 none;
                    border-left: 0 none;
                    transform: rotate(45deg);
                    width: 5px;
                    height: 9px;
                    left: 4px;
                }
            }
        }

        &:indeterminate {
            + label {
                &::before {
                    content: '';
                    position: absolute;
                    border: @scaled-one-px-value-ie solid @text-normal-ie;
                    border: @scaled-one-px-value solid @text-normal;
                    background: @icon-normal-ie;
                    background: @icon-normal;
                    width: 8px;
                    height: 8px;
                    left: 2px;
                    top: 2px;
                }
            }
        }

        &.disabled,
        &:disabled {

            + label {
                &::before {
                }
            }
        }
    }

    &:focus:not(.disabled) {
        input[type=checkbox] {
            + label {
                border-color: @border-control-focus-ie;
                border-color: @border-control-focus;

                + span {
                    outline-color: @border-control-focus-ie;
                    outline-color: @border-control-focus;
                }
            }
        }
    }
}
