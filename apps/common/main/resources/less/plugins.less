#plugins-box {
    position: relative;
    width: 100%;
    height: 100%;

    #plugins-header {
        position: absolute;
        height: 20px;
        left: 0;
        top: 0;
        .font-weight-bold();
        margin-top: 10px;
        margin-left: 12px;

    }

    #plugins-list {
        height: 100%;
        overflow: hidden;
        padding: 30px 0 10px 0;

        .item {
            display: block;
            width: 100%;
            .box-shadow(none);
            margin: 0;

            &:hover,
            &.over {
                background-color: @highlight-button-hover-ie;
                background-color: @highlight-button-hover;
            }

            &.selected {
                background-color: @highlight-button-pressed-ie;
                background-color: @highlight-button-pressed;
                color: #fff;
            }
        }

        .item-plugins {
            width: 100%;
            padding: 7px 1px 7px 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .plugin-icon {
            width: 40px;
            height: 40px;
            display: inline-block;
            .box-shadow(0 0 0 1px transparent);
            vertical-align: middle;
            background-position: 0 0;
            background-size: cover;
            margin-right: 10px;
            margin-top: -1px;

            .background-ximage-v2('plugin/icon_add_on_default.png', 40px);
        }

        .plugin-caret {
            //float: right;
            width: 16px;
            height: 16px;
            background-position: -1px -274px;
            margin: 7px;
            display: inline-block;
            position: absolute;
            right: 0;
        }
    }
}

#current-plugin-box {
    position: relative;
    width: 100%;
    height: 100%;

    #current-plugin-header {
        position: absolute;
        height: 45px;
        left: 0;
        top: 0;
        right: 0;
        padding: 12px;
        overflow: hidden;
        border-bottom: @scaled-one-px-value-ie solid @border-toolbar-ie;
        border-bottom: @scaled-one-px-value solid @border-toolbar;

        label {
            width: 100%;
            margin-top: 2px;
            padding-right: 20px;
            font-size: 12px;
            .font-weight-bold();
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .close {
        position: absolute;
        width: 20px;
        height: 20px;
        top: 12px;
        right: 12px;
    }

    #current-plugin-frame {
        width: 100%;
        height: 100%;
        padding-top: 45px;
    }
}

#plugins-mask {
    position: absolute;
    top: 0;
    left: 0px;
    width: 100%;
    height: 100%;
    opacity: 0.4;
    background-color: @background-toolbar-ie;
    background-color: @background-toolbar;
    z-index: @zindex-tooltip + 1;
}

#plugins-panel {
    .x-huge.icon-top {
        .caption {
            text-overflow: ellipsis;
            max-width: 160px;
        }
    }

    .dropdown-menu {
        min-width: 100px;
    }

    .separator:first-child {
        display: none;
    }
}