.open-dlg {

  .body {
    height: auto;
    top: 34px;

    & > div {
      width: 100%;
    }

    .box > div {
      display: inline-block;
      height: 100%;
    }

    .content-panel {
      vertical-align: top;
      padding: 15px 15px 0;
      width: 100%;

      .inner-content {
        padding-left: 10px;
        padding-right: 10px;
      }

      label {
        .font-size-normal();
        font-weight: normal;

        &.input-label{
          margin-bottom: 0;
          vertical-align: middle;
        }

        &.header {
          .font-weight-bold();
        }

      }

      #id-preview {
        width: 100%;
        height: 110px !important;
        position:relative;
        overflow:hidden;
        border: @border-regular-control-ie solid @scaled-one-px-value-ie;
        border: @border-regular-control solid @scaled-one-px-value;
      }

      #id-preview-data {
        width: 100%;
        height: 108px !important;
        position:relative;
        overflow:hidden;
      }

      table {
        min-height: 108px;
      }

      td {
        padding: 1px 8px 1px 0;
        border-right: @border-regular-control-ie solid @scaled-one-px-value-ie;
        border-right: @border-regular-control solid @scaled-one-px-value;
        min-width: 30px;
        height: 17px;
        white-space: nowrap;
      }

      td:last-child {
        border-right: none;
      }

      tr:last-child {
        td {
          padding-bottom: 8px;
        }
      }

      .icon.warn {
        float: left;
        width: 40px;
        height: 40px;
        background: ~"url('@{common-image-const-path}/controls/warnings_s.svg#attention')" no-repeat center;

      }
    }
  }
  .footer {
    padding-top: 15px;

    &.center {
      text-align: center;
    }

    &.justify {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}