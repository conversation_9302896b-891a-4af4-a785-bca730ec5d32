.extended-color-dlg {
    .color-box {
        padding: 16px 16px 5px 16px;

        & > div {
            display: inline-block;
        }

        .hsb-colorpicker {
            display: inline;
            width:225px;
            height:200px;
        }

        label {
            font-weight: normal;
            font-size: 12px;
            margin-bottom: 0;
            vertical-align: middle;

            &.color-label {
                text-align: center;
                width: 100%;
            }

            &.input-label {
                width: 12px;
            }
        }

        input {
            font-weight: normal;
            font-size: 12px;
            background-color: @background-normal;
        }

        .color-cnt {
            width: 63px;
            height: 20px;
            background-color: transparent;
            border: @scaled-one-px-value-ie solid @border-color-shading-ie;
            border: @scaled-one-px-value solid @border-color-shading;
            &.top {
                border-bottom: none;
            }
            &.bottom {
                border-top: none;
            }
        }

        .color-spin {
            display: inline-block;
            vertical-align:middle;
        }

        .color-transparent {
            &:before {
                height: 64px;
                transform: translate(29px, -22px) rotate(73deg);
            }
        }
    }


    .footer {
        height: 37px;
        width: 100%;
        bottom: 0;
        text-align: center;
        padding: 7px 0 0 0;

        button {
            width: 75px;
            height: 22px;

            &:not(:first-child) {
                margin-left: 10px;
            }
        }
    }
}