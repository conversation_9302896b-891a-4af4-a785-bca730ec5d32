
:root {
    .theme-dark {
        --toolbar-header-document: #2a2a2a;
        --toolbar-header-spreadsheet: #2a2a2a;
        --toolbar-header-presentation: #2a2a2a;

        --text-toolbar-header-on-background-document: #2a2a2a;
        --text-toolbar-header-on-background-spreadsheet: #2a2a2a;
        --text-toolbar-header-on-background-presentation: #2a2a2a;

        --background-normal: #333;
        --background-toolbar: #404040;
        --background-toolbar-additional: #505050;
        --background-primary-dialog-button: #ddd;
        --background-accent-button: #486F9E;
        --background-tab-underline: #ddd;
        --background-notification-popover: #3e5968;
        --background-notification-badge: #ffd112;
        --background-scrim: fade(black, 60%);
        --background-loader: fade(#181818, 90%);
        --background-alt-key-hint: #FFD938;
        --background-contrast-popover: #313131;

        --highlight-button-hover: #555;
        --highlight-button-pressed: #707070;
        --highlight-button-pressed-hover: #808080;
        --highlight-primary-dialog-button-hover: #fcfcfc;
        --highlight-accent-button-hover: #75a2d6;
        --highlight-accent-button-pressed: #89afdc;
        --highlight-header-button-hover: fade(#fff, 5%);
        --highlight-header-button-pressed: fade(#fff, 15%);
        --highlight-toolbar-tab-underline: #ddd;
        --highlight-text-select: #96c8fd;

        --border-toolbar: #616161;
        --border-toolbar-active-panel-top: var(--background-toolbar);
        --border-divider: #505050;
        --border-regular-control: #666;
        --border-toolbar-button-hover: #5a5a5a;
        --border-preview-hover: #757575;
        --border-preview-select: #bdbdbd;
        --border-control-focus: #ccc;
        --border-color-shading: fade(#fff, 10%);
        --border-error: #f62211;
        --border-contrast-popover: #666;

        --text-normal: fade(#fff, 80%);
        --text-normal-pressed: fade(#fff, 80%);
        --text-secondary: fade(#fff, 60%);
        --text-tertiary: fade(#fff, 40%);
        --text-link: #b5e4ff;
        --text-link-hover: #b5e4ff;
        --text-link-active: #b5e4ff;
        --text-link-visited: #b5e4ff;
        --text-inverse: #333;
        --text-toolbar-header: fade(#fff, 80%);
        --text-contrast-background: #fff;
        --text-alt-key-hint: #2a2a2a;

        --icon-normal: fade(#fff, 80%);
        --icon-normal-pressed: fade(#fff, 80%);
        --icon-inverse: #444;
        --icon-toolbar-header: fade(#fff, 80%);
        --icon-notification-badge: #000;
        --icon-contrast-popover: #fff;
        --icon-success: #090;

        // Canvas

        --canvas-background: #555;
        --canvas-content-background: #fff;
        --canvas-page-border: #555;

        --canvas-ruler-background: #555;
        --canvas-ruler-border: #2A2A2A;
        --canvas-ruler-margins-background: #444;
        --canvas-ruler-mark: #b6b6b6;
        --canvas-ruler-handle-border: #b6b6b6;
        --canvas-ruler-handle-border-disabled: #808080;

        --canvas-high-contrast: #fff;
        --canvas-high-contrast-disabled: #ccc;

        --canvas-cell-border: fade(#000, 10%);
        --canvas-cell-title: #d9d9d9;
        --canvas-cell-title-border: #757575;
        --canvas-cell-title-border-hover: #858585;
        --canvas-cell-title-border-selected: #9e9e9e;
        --canvas-cell-title-hover: #787878;
        --canvas-cell-title-selected: #939393;

        --canvas-dark-cell-title: #55B27B;
        --canvas-dark-cell-title-hover: #7AFFAF;
        --canvas-dark-cell-title-selected: #6EE59F;
        --canvas-dark-cell-title-border: #282828;
        --canvas-dark-cell-title-border-hover: #191919;
        --canvas-dark-cell-title-border-selected: #474747;

        --canvas-scroll-thumb: #404040;
        --canvas-scroll-thumb-hover: #999;
        --canvas-scroll-thumb-pressed: #adadad;
        --canvas-scroll-thumb-border: #2a2a2a;
        --canvas-scroll-thumb-border-hover: #999;
        --canvas-scroll-thumb-border-pressed: #adadad;
        --canvas-scroll-arrow: #999;
        --canvas-scroll-arrow-hover: #404040;
        --canvas-scroll-arrow-pressed: #404040;
        --canvas-scroll-thumb-target: #999;
        --canvas-scroll-thumb-target-hover: #404040;
        --canvas-scroll-thumb-target-pressed: #404040;

        // Others

        --button-small-normal-icon-offset-x: -20px;
        --button-small-active-icon-offset-x: -20px;
        --button-large-normal-icon-offset-x: -31px;
        --button-large-active-icon-offset-x: -31px;
        --button-huge-normal-icon-offset-x: -40px;
        --button-huge-active-icon-offset-x: -40px;
        --button-xhuge-normal-icon-offset-x: -28px;
        --button-xhuge-active-icon-offset-x: -28px;
        //--button-xhuge-normal-icon-offset-x: -37px;
        //--button-xhuge-active-icon-offset-x: -37px;

        --modal-window-mask-opacity: 0.6;
        --image-border-types-filter: invert(100%) brightness(4);
        --image-border-types-filter-selected: invert(100%) brightness(4);
        --component-normal-icon-filter: invert(100%);

        --component-normal-icon-opacity: .8;
        --component-hover-icon-opacity: .8;
        --component-active-icon-opacity: 1;
        --component-active-hover-icon-opacity: 1;
        --component-disabled-opacity: .4;

        --header-component-normal-icon-opacity: .8;
        --header-component-hover-icon-opacity: .8;
        --header-component-active-icon-opacity: 1;
        --header-component-active-hover-icon-opacity: 1;

        --menu-icon-item-checked-offset-x: -20px;
    }
}
