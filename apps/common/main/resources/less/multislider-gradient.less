.multi-slider-gradient {
    height: 35px;
    background-color: transparent;

    .thumb {
        width: 13px;
        height: 15px;
        top: 18px;
        background: none;

        .thumb-top {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 9px;
            height: 9px;
            background-color: @background-normal-ie;
            background-color: @background-normal;
            -moz-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            -webkit-transform: rotate(45deg);
            -o-transform: rotate(45deg);
            transform: rotate(45deg);
            border-top: solid @scaled-one-px-value-ie @border-regular-control-ie;
            border-top: solid @scaled-one-px-value @border-regular-control;
            border-left: solid @scaled-one-px-value-ie @border-regular-control-ie;
            border-left: solid @scaled-one-px-value @border-regular-control;
            border-radius: 0 3px;
            box-sizing: content-box;
            .thumb-top-inner {
                border-top: solid @scaled-one-px-value-ie @background-normal-ie;
                border-top: solid @scaled-one-px-value @background-normal;
                border-left: solid @scaled-one-px-value-ie @background-normal-ie;
                border-left: solid @scaled-one-px-value @background-normal;
                height: 100%;
            }
        }

        .thumb-bottom {
            position: absolute;
            top: 6px;
            left: 1px;
            width: 10px;
            height: 9px;
            background-color: @background-normal-ie;
            background-color: @background-normal;
            border: solid @scaled-one-px-value-ie @border-regular-control-ie;
            border: solid @scaled-one-px-value @border-regular-control;
            border-top: none;
            border-radius: 2px;
            box-sizing: content-box;
            .thumb-bottom-inner {
                border: solid @scaled-one-px-value-ie @background-normal-ie;
                border: solid @scaled-one-px-value @background-normal;
                border-top: none;
                height: 100%;
            }
        }

        &.active {
            .thumb-top {
                border-top: solid @scaled-one-px-value-ie @border-control-focus-ie;
                border-top: solid @scaled-one-px-value @border-control-focus;
                border-left: solid @scaled-one-px-value-ie @border-control-focus-ie;
                border-left: solid @scaled-one-px-value @border-control-focus;
            }
            .thumb-bottom {
                border: solid @scaled-one-px-value-ie @border-control-focus-ie;
                border: solid @scaled-one-px-value @border-control-focus;
                border-top: none;
            }
        }

        &:hover {
            .thumb-bottom {
                box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.32);
            }
        }

        &.remove {
            opacity: 0.5;
        }
    }

    .track {
        background: #ededed; /* Old browsers */
//        background: -webkit-gradient(linear, left top, right top, color-stop(0%,#000000), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(left, #000000 0%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
        background: -moz-linear-gradient(left, #000000 0%,#ffffff 100%); /* FF3.6+ */
        background: -o-linear-gradient(left,  #000000 0%,#ffffff 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(left,  #000000 0%,#ffffff 100%); /* IE10+ */
        background: linear-gradient(to right,  #000000 0%,#ffffff 100%); /* W3C */
//        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#000000', endColorstr='#ffffff',GradientType=1 ); /* IE6-9 */

        background-position: 0 0;
        outline: @scaled-one-px-value-ie solid @border-control-focus-ie;
        outline: @scaled-one-px-value solid @border-control-focus;
        border: @scaled-one-px-value-ie solid @border-regular-control-ie;
        border: @scaled-one-px-value solid @border-regular-control;
        cursor: copy;
    }
}


