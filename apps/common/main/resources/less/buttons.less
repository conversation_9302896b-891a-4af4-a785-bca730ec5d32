@x-huge-btn-height: 52px;
@x-huge-btn-icon-size: 28px;

.btn {
    border-radius: 1px;
    color: @text-normal-ie;
    color: @text-normal;
    border-width: @scaled-one-px-value-ie;
    border-width: @scaled-one-px-value;

    .btnsize(@value) {
        min-width: @value;
        height: @value;
        line-height: @value - 2px;
    }

    &.small {
        .btnsize(20px);
    }

    &.normal {
        height: 22px;
    }

    &.large {
        .btnsize(30px);
    }

    &.huge {
        .btnsize(37px);
    }

    &.huge-1 {
        .btnsize(40px);
    }

    &.x-huge {
        .btnsize(@x-huge-btn-height);
    }

    &[disabled], &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }
    
    &:focus {
        outline: 0;
        outline-offset: 0;
        color: @text-normal-ie;
        color: @text-normal;
    }

    &:hover {
        color: @text-normal-ie;
        color: @text-normal;
    }

    &:active, &.active {
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    .caret {
        width: 4px;
        height: 4px;
        border: solid 1px @icon-normal-ie;
        border: solid 1px @icon-normal;
        border-bottom: none;
        border-right: none;
        background-image: none;

        transition: transform 0.2s ease;
        transform: rotate(-135deg) translate(1px,1px);
        &.compact-caret {
            display: none;
        }
    }

    //&:active,
    &:active:not(.disabled),
    &.active:not(.disabled){
        .caret {
            transform: rotate(45deg);
            border-color: @icon-normal-pressed-ie;
            border-color: @icon-normal-pressed;
        }
    }

    &:active,
    &.active {
        &:focus,
        &.focus {
            outline: none;
        }
    }

    &.icon-top {
        display: inline-flex;
        flex-direction: column;
        align-items: center;

        .icon {
            //flex-grow: 1;
        }

        .caption {
            line-height: 18px;
            padding: 0 4px;
        }
    }

    &.notify {
        .icon {
            &::after {
                content: ' ';
                position: absolute;
                border: 1px solid @icon-notification-badge-ie;
                border: 1px solid @icon-notification-badge;
                border-left: 0 none;
                border-right: 0 none;
                width: 6px;
                height: 3px;
                bottom: 4px;
                right: 2px;
            }

            &::before {
                content: ' ';
                position: absolute;
                width: 10px;
                height: 7px;
                bottom: 2px;
                background-color: @background-notification-badge-ie;
                background-color: @background-notification-badge;
                border-radius: 2px;
                right: 0;
            }
        }
    }

    .icon {
        &.caret-up, &.caret-down {
            &::after {
                content: '';
                position: absolute;
                left: 6px;
                width: 8px;
                height: 8px;
                background-color: transparent;
                border: solid 1px @text-normal-ie;
                border: solid 1px @text-normal;
                border-bottom: none;
                border-right: none;
            }
        }

        &.caret-up {
            &::after {
                top: 8px;
                -moz-transform: rotate(45deg);
                -ms-transform: rotate(45deg);
                -webkit-transform: rotate(45deg);
                -o-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }
        &.caret-down {
            &::after {
                top: 4px;
                -moz-transform: rotate(225deg);
                -ms-transform: rotate(225deg);
                -webkit-transform: rotate(225deg);
                -o-transform: rotate(225deg);
                transform: rotate(225deg);
            }
        }
    }

    &:not(:disabled) {
        .icon {
            opacity: @component-normal-icon-opacity;
        }
    }

    &:hover:not(.disabled) {
        .icon {
            opacity: @component-hover-icon-opacity;
        }
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        .caret-up, .caret-down {
            &::after {
                border-color: @text-normal-pressed-ie;
                border-color: @text-normal-pressed;
            }
        }

        .icon {
            opacity: @component-active-icon-opacity;
        }

        &:hover .icon {
            opacity: @component-active-hover-icon-opacity;
        }
    }
}

.btn.icon-top, .btn-group.icon-top {
    display: inline-flex;
    flex-direction: column;

    &.open,
    &.over {
        &.split {
            > .inner-box-icon {
                .box-shadow(none);
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }
            > .inner-box-caption {
                border-top-left-radius: 0;
                border-top-right-radius: 0;
            }
        }
    }

    &.x-huge {
        @icon-size: 28px;

        min-width: 35px;
        height: @x-huge-btn-height;

        img {
            width: @x-huge-btn-icon-size;
            height: @x-huge-btn-icon-size;
        }

        .inner-box-caption {
            line-height: 18px;
            padding: 1px 4px 1px 3px;
            display: flex;
            //align-items: center;
            align-items: start;
            height: 24px;

            .caption {
                max-width: 160px;//85px;
                max-height: 24px;
                text-overflow: ellipsis;

                white-space: pre;
                line-height: 11px;
                padding: 0 2px;

                .caret {
                    margin: 0 1px 0 4px;
                }
            }
        }

        .icon:not(svg) {
            width: @x-huge-btn-icon-size;
            height: @x-huge-btn-icon-size;
            min-width: 0;
        }

        svg.icon {
            width: 28px;
            height: 28px;

            @media
            only screen and (-webkit-min-device-pixel-ratio: 1.5),
            only screen and (min-resolution: 1.5dppx),
            only screen and (min-resolution: 144dpi) {
                //width:calc(~"40px/1.5");
                //height:calc(~"40px/1.5");
            }
        }

        button.small .icon {
            width: 20px;
            height: 20px;
            min-width: 0;
        }

        &.dropdown-toggle {
            .caption {
                //max-width: 100px;
            }
        }

    }

    .inner-box-icon {
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 20px;
    }

    .btn-fixflex-hcenter {
        flex-grow: 1;
    }

    .btn-fixflex-vcenter {
        display: flex;
        align-items: center;

        .caret {
            vertical-align: inherit;
        }
    }

    .inner-box-caption {
        margin: 0;
        height: 20px;
    }

    div.inner-box-icon {
        height: 28px;           // TODO: clear. Firefox bug 984869. fixed. isn't released.
    }
}

.icon-top.btn-group {
    &.x-huge, .x-huge {
        .inner-box-caption {
            padding-right: 4px;
        }
    }
}

.icon-top {
    &.x-huge  {
        .btn&, .btn-group& .btn-toolbar {
            .mx-button-otherstates-icon2(@button-xhuge-active-icon-offset-x-ie);
            .mx-button-otherstates-icon2(@button-xhuge-active-icon-offset-x);
        }

        .btn.small {
            .mx-button-otherstates-icon2(@button-small-active-icon-offset-x);
        }
    }
}

.btn-toolbar {
    display: inline-block;
    position: relative;
    margin: 0;
    padding: 0;
    border: 0;
    .border-radius(1px);
    background-color: transparent;

    .masked:not(.statusbar) & {
        &:disabled {
            opacity: 1;
        }
    }

    .btn.small;

    &.bg-white {
        width: 22px;
        height: 22px;

        background-color: @background-normal-ie;
        background-color: @background-normal;
        border: @scaled-one-px-value-ie solid @border-regular-control-ie;
        border: @scaled-one-px-value solid @border-regular-control;
        .border-radius(@border-radius-small);

        &:focus:not(.disabled) {
            border-color: @border-control-focus-ie;
            border-color: @border-control-focus;
        }
    }

    &:before,
    &:after {
        content: "";
        display: none;
    }

    &:hover:not(.disabled),
    .over:not(.disabled) {
        background-color: @highlight-button-hover-ie;
        background-color: @highlight-button-hover;
    }


    &:active:not(.disabled),
    &.active:not(.disabled) {
        color: @text-normal-pressed-ie;
        color: @text-normal-pressed;
        background-color: @highlight-button-pressed-ie;
        background-color: @highlight-button-pressed;
    }

    &:not(.icon-top)
    .caption:not(:empty) {
        padding: 0 4px;
    }

    .icon {
        padding: 0;
        margin: 0;
        display: inline-block;
        background-repeat: no-repeat;
    }

    &:not(.x-huge) {
        .icon {
            width: 20px;
            height: 20px;
            line-height: 20px;

        }

        svg.icon {
            @media
            only screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.9),
            only screen and (min-resolution: 1.5dppx) and (max-resolution: 1.9dppx),
            only screen and (min-resolution: 144dpi) and (max-resolution: 143dpi) {
                width:calc(~"28px/1.5");
                height:calc(~"28px/1.5");
            }
        }
    }

    &.dropdown-toggle {
        min-width: 7px;
    }

    // Large size
    // ------------------------
    &.large {
        .icon {
            width: 30px !important;
            height: 30px;
            line-height: 20px;
        }
    }

    // Button has color line
    // ------------------------
    .btn-color-value-line {
        pointer-events: none;
        position: absolute;
        left: 3px;
        bottom: 1px;
        width: 14px;
        height: 3px;
        background-color: red;
        &.bordered {
            border: @scaled-one-px-value-ie solid @border-regular-control-ie;
            border: @scaled-one-px-value solid @border-regular-control;
        }


        .pixel-ratio__1_5 & {
            height: 4px;
        }
    }

    &.no-caret {
        .inner-box-caret {
            display: none;
        }
    }

    &.align-left {
        text-align: left;
    }
}

.btn-category {
    .btn.large;
    width: 100%;
    padding: 0;
    display: block;
    position: relative;
    .border-radius(0);
    .font-weight-bold();
    background-color: transparent;


    &:hover:not(.disabled),
    .over:not(.disabled) {
        background-color: @highlight-button-hover-ie;
        background-color: @highlight-button-hover;
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        background-color: @highlight-button-pressed-ie;
        background-color: @highlight-button-pressed;
        color: @text-normal-pressed-ie;
        color: @text-normal-pressed;
    }

    .icon {
        display: inline-block;
        position: relative;
        width: 20px;
        height: 20px;
        line-height: 20px;
    }

    &[disabled],
    &.disabled {
        //color: #000;  btn-category has no text
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }
}

// The clickable button for toggling the menu
// Remove the gradient and set the same inset shadow as the :active state
.btn-group {
    > .btn-toolbar {
        float: none;
    }

    > .btn {
        + .dropdown-toggle {
            padding: 0 4px 0 3px;
        }
    }

    > .dropdown-toggle:first-child {
        .inner-box-caret {
            padding: 0 4px 0 3px;
        }
    }

    .caption {
        &:empty {
            display: none;

            + .caret {
                margin-left: 2px;
            }
        }
    }

    &.open {
        > .btn-toolbar {
                //color: @text-normal;

                // Show no shadow for `.btn-link` since it has no other button styles.
                &.btn-link {
                    .box-shadow(none);
                }
        }

        .dropdown-toggle {
            .box-shadow(none);
        }
    }

    &.over {
        > button {
            //background-color: @highlight-button-hover;
        }
    }

    &:not(.split) {
        .btn-toolbar {
                &.dropdown-toggle:not(.x-huge):not(.no-caret) {
                    width: 100%;
                    min-width: 28px;
                }

                &.btn-text-value {
                    .caret {
                        margin: 6px 3px 6px 0;
                    }
                    .caption {
                        vertical-align: middle;
                    }
                    &[disabled],
                    &.disabled {
                        opacity: @component-disabled-opacity-ie;
                        opacity: @component-disabled-opacity;
                    }
                }

            &.large {
                &.dropdown-toggle {
                    min-width: 40px;

                    .caret {
                        float: right;
                    }
                }
            }
        }
        .dropdown-menu {
            &.scale-menu {
                li.disabled {
                    opacity: @component-disabled-opacity-ie;
                    opacity: @component-disabled-opacity;
                }
            }
        }
    }

    &.split {

        &.over:not(.disabled),
        &.open {
            box-shadow: inset 0 0 0 @scaled-one-px-value-ie @highlight-button-hover-ie;
            box-shadow: inset 0 0 0 @scaled-one-px-value @highlight-button-hover;

            button:not(.active):not(.btn-text-split-default) {
                background-color: transparent;
            }
        }

        &.over:not(.disabled) {
            button {
                &:not(.active) {
                    &:hover {
                        background-color: @highlight-button-hover-ie;
                        background-color: @highlight-button-hover;
                    }
                }

                &:active,
                &:active:hover {
                    background-color: @highlight-button-pressed-ie;
                    background-color: @highlight-button-pressed;
                }
            }
        }

        &.open {
            > button:not(.active) {
                &:last-of-type {
                    background-color: @highlight-button-pressed-ie;
                    background-color: @highlight-button-pressed;
                }
            }

            .caret {
                background-position: @arrow-small-offset-x - 7px @arrow-small-offset-y;
            }
        }

        button.active:not(.disabled) {
            background-color: @highlight-button-pressed-ie;
            background-color: @highlight-button-pressed;
        }

        .btn + .btn {
            margin: 0;
        }
    }
}

.btn-color {
    width: 45px;
    height: 22px;
    padding: @scaled-one-px-value-ie 11px @scaled-one-px-value-ie @scaled-one-px-value-ie;
    padding: @scaled-one-px-value 11px @scaled-one-px-value @scaled-one-px-value;
    border:@scaled-one-px-value-ie solid @border-regular-control-ie;
    border:@scaled-one-px-value solid @border-regular-control;
    .border-radius(@border-radius-small);

    span:nth-child(1) {
        float: left;
        width: 100%;
        height: 100%;
        border: @scaled-one-px-value-ie solid @border-color-shading-ie;
        border: @scaled-one-px-value solid @border-color-shading;
        background-color: transparent;
    }

    .inner-box-caret {
        position: absolute;
        right: 0;
        top: 2px;
    }

    &,
    &:hover,
    .over,
    &:active,
    &.active {
        background: @background-normal-ie !important;
        background: @background-normal !important;
        .box-shadow(none)  !important;
    }

    &[disabled],
    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }

    .color-transparent {
        &:before {
            border-right: @scaled-one-px-value-ie solid red;
            border-right: @scaled-one-px-value solid red;
            height: 34px;
            transform: translate(16px, -9px) rotate(62deg);
            left: 0;
        }
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        .caret {
            border-color: @icon-normal-ie;
            border-color: @icon-normal;
        }
    }

    &:not(.disabled) {
        &:focus, .btn-group.open &, .btn-group:active & {
            border-color: @border-control-focus-ie;
            border-color: @border-control-focus;
        }
    }
}

// for color button auto color
.dropdown-menu {
    li > a.selected,
    li > a:hover {
        span.color-auto {
            outline: @scaled-one-px-value-ie solid @icon-normal-ie;
            outline: @scaled-one-px-value solid @icon-normal;
            border: @scaled-one-px-value-ie solid @background-normal-ie;
            border: @scaled-one-px-value solid @background-normal;
        }
    }
}


.btn-options {
    padding: 0;
    margin:0;
    border: 0 none;
    //.box-shadow(none);
    background-color: transparent;

    &.bg-white {
        background-color: @background-normal-ie;
        background-color: @background-normal;
    }

    .icon {
        position: relative;
        min-width: inherit;
        height: inherit;
        line-height: inherit;
        padding: 0;
        margin: 0;
        display: inline-block;
        background-repeat: no-repeat;
    }

    box-shadow: inset 0 0 0 @scaled-one-px-value-ie @border-regular-control-ie;
    box-shadow: inset 0 0 0 @scaled-one-px-value @border-regular-control;
    .border-radius(@border-radius-small);

    &:hover,
    &.over {
        background-color: @highlight-button-hover-ie;
        background-color: @highlight-button-hover;
    }

    &:active,
    &.active {
        background-color: @highlight-button-pressed-ie;
        background-color: @highlight-button-pressed;
    }

    &.large {
        min-width: 28px;
        width: 28px;
        height: 28px;
    }

    &:focus:not(.disabled) {
        box-shadow: inset 0 0 0 @scaled-one-px-value-ie @border-control-focus-ie;
        box-shadow: inset 0 0 0 @scaled-one-px-value @border-control-focus;
    }

    &.border-off {
        box-shadow: none;
    }
}

.btn-text-default {
    width: 75px;
    height: 22px;
    background: @background-normal-ie;
    background: @background-normal;
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;
    .border-radius(@border-radius-small);

    &.auto {
        width: auto;
        padding-left: 10px;
        padding-right: 10px;
    }

    &:focus:not(.disabled) {
        border-color: @border-control-focus-ie;
        border-color: @border-control-focus;
    }

    &:hover:not(.disabled),
    .over:not(.disabled) {
        background-color: @highlight-button-hover-ie !important;
        background-color: @highlight-button-hover !important;
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        background-color: @highlight-button-pressed-ie !important;
        background-color: @highlight-button-pressed !important;
        border-color: @highlight-button-pressed-ie;
        border-color: @highlight-button-pressed;
        color: @text-normal-pressed-ie;
        color: @text-normal-pressed;
    }

    &[disabled],
    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }
}

.btn-text-menu-default {
    width: 100%;
    height: 22px;
    background: @background-normal-ie;
    background: @background-normal;
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;
    .border-radius(@border-radius-small);

    button {
        width: 100%;
    }

    .caret {
        position: absolute;
        right: 5px;
        margin-top: 5px;
    }

    &:hover:not(.disabled),
    .over:not(.disabled) {
        background-color: @highlight-button-hover-ie !important;
        background-color: @highlight-button-hover !important;
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        background-color: @highlight-button-pressed-ie !important;
        background-color: @highlight-button-pressed !important;
        color: @text-normal-pressed-ie;
        color: @text-normal-pressed;
    }

    &[disabled],
    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }
}

.btn-text-split-default {
    width: 75px;
    height: 22px;
    background: @background-normal-ie;
    background: @background-normal;
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;
    .border-radius(@border-radius-small);

    &.auto {
        width: auto;
    }

    &.dropdown-toggle {
        width: 13px;
    }

    &:not(.dropdown-toggle) {
        border-right: none;
    }

    &:hover:not(.disabled),
    .over:not(.disabled) {
        background-color: @highlight-button-hover-ie !important;
        background-color: @highlight-button-hover !important;
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        background-color: @highlight-button-pressed-ie !important;
        background-color: @highlight-button-pressed !important;
        border-color: @highlight-button-pressed-ie;
        border-color: @highlight-button-pressed;
        color: @text-normal-pressed-ie;
        color: @text-normal-pressed;
    }

    &[disabled],
    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }
}

.btn-icon-default {
    width: 45px;
    height: 22px;
    background: @background-normal-ie;
    background: @background-normal;
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;
    .border-radius(@border-radius-small);

    .icon {
        width: 28px;
        padding: 0;
        margin: 0;
        position: relative;
        display: inline-block;
        background-repeat: no-repeat;
    }

    &:not(.btn-toolbar).dropdown-toggle:first-child .inner-box-caret {
      padding: 0 1px 0 2px;
    }

    &:hover:not(.disabled),
    .over:not(.disabled) {
        background-color: @highlight-button-hover-ie !important;
        background-color: @highlight-button-hover !important;
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        background-color: @highlight-button-pressed-ie !important;
        background-color: @highlight-button-pressed !important;
    }

    &[disabled],
    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }
}

.btn-large-dataview {
    display: inline-block;
    width: 72px;
    height: 58px;
    padding: 2px;
    margin:0;
    box-shadow: none;
    background-color: @background-normal-ie;
    background-color: @background-normal;
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;
    .border-radius(@border-radius-small);

    &.template-table {
        width: 92px;
    }

    .icon-template-slicer.icon {
        min-width: 36px;
        height: 55px;
        line-height: 53px;
        border: none;
    }

    .icon {
        display: inline-block;
        .btn.btnsize(52px);
        padding: 0;
        margin-right: 3px;
        background-color: transparent;
        background-repeat: no-repeat;
        border: @scaled-one-px-value-ie solid @border-regular-control-ie;
        border: @scaled-one-px-value solid @border-regular-control;
        position: relative;
    }

    &.sheet-template-table {
        width: 83px;
        height: 54px;

        .icon {
            border: none;
            .box-shadow(0 0 0 @scaled-one-px-value-ie @border-regular-control-ie);
            .box-shadow(0 0 0 @scaled-one-px-value @border-regular-control);
        }
    }

    svg.icon {
        vertical-align: middle;
        width: 52px;
        padding: 5px;

        fill: @icon-normal-ie;
        fill: @icon-normal;

        .btn& {
            opacity: 1;
        }
    }

    &:hover,
    .over,
    &:active,
    &.active {
        background-color: @background-normal-ie !important;
        background-color: @background-normal !important;

        &:not(:disabled) {
            svg.icon {
                opacity: inherit;
            }
        }
    }

    &:active:not(.disabled),
    &.active:not(.disabled) {
        .caret {
            background-position: @arrow-small-offset-x @arrow-small-offset-y;
            border-color: @icon-normal-ie;
            border-color: @icon-normal;
        }
    }

    &[disabled],
    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }

    &:not(:disabled) {
        .icon {
            opacity: 1;
        }

        &:hover {
            .icon {
                .icon();
            }
        }
    }
}

// Dialog buttons
// ------------------------

.dlg-btn {
    .font-weight-bold();
    width: 86px;
    color: @text-normal-ie;
    color: @text-normal;
    background-color: @background-normal-ie;
    background-color: @background-normal;
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;

    &:hover:not(.disabled),
    &.hover:not(.disabled) {
        background-color: @highlight-button-hover-ie;
        background-color: @highlight-button-hover;
    }

    &.primary {
        color: @text-inverse-ie;
        color: @text-inverse;
        background-color: @background-primary-dialog-button-ie;
        background-color: @background-primary-dialog-button;
        border: 0 none;

        &:hover:not(.disabled),
        &.hover:not(.disabled) {
            background-color: @highlight-primary-dialog-button-hover-ie;
            background-color: @highlight-primary-dialog-button-hover;
        }
    }

    &[disabled] {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }

    &.custom {
        width: auto;
        min-width: 86px;
    }
}

@class100: zoom-int;
@class150: zoom-grit;
svg.icon {
    .@{class150} {
        display: none;
    }

    //@media
    //only screen and (-webkit-min-device-pixel-ratio: 1.5),
    //only screen and (-webkit-min-device-pixel-ratio: 2.5),
    //only screen and (min-resolution: 1.5dppx),
    //only screen and (min-resolution: 2.5dppx),
    //only screen and (min-resolution: 144dpi),
    //only screen and (min-resolution: 240dpi) {
    //    .@{class100} {
    //        display: none;
    //    }
    //
    //    .@{class150} {
    //        display: block;
    //    }
    //}

    @media
    only screen and (-webkit-min-device-pixel-ratio: 2) and (-webkit-max-device-pixel-ratio: 2.4),
    only screen and (-webkit-min-device-pixel-ratio: 4),
    only screen and (min-resolution: 2dppx) and (max-resolution: 2.4dppx),
    only screen and (min-resolution: 4dppx),
    only screen and (min-resolution: 192dpi) and (max-resolution: 239dpi),
    only screen and (min-resolution: 384dpi) {
        .@{class100} {
            display: block;
        }

        .@{class150} {
            display: none;
        }
    }
}