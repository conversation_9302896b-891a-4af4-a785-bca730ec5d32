/**
 *      Window.less
 *
 *      Describes styles for base class 'Window'
 *
 *      Created by <PERSON> on 24 January 2014
 *      Copyright (c) 2018 Ascensio System SIA. All rights reserved.
 *
 */

.modals-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.2;
    background-color: rgb(0,0,0);
    z-index: @zindex-modal - 1;

    .theme-type-dark & {
        opacity: 0.6;
    }
}
.tool {
    float: right;
    width: 16px;
    height: 16px;
    cursor: pointer;
    overflow: hidden;
    padding: 0px;
    margin: 4px 3px 0px 0px;

    &.close {
        position: relative;
        opacity: 0.7;

        &:hover {
            opacity: 1;
        }

        &.disabled {
            cursor: default;
        }

        &:before, &:after {
            content: ' ';
            position: absolute;
            left: 8px;
            left: calc(8px / @pixel-ratio-factor);
            top: @scaled-one-px-value-ie;
            top: @scaled-one-px-value;
            height: 14px;
            width: @scaled-one-px-value-ie;
            width: @scaled-one-px-value;
            background-color: @icon-normal-ie;
            background-color: @icon-normal;
        }

        &:before {
            transform: rotate(45deg);
        }

        &:after {
            transform: rotate(-45deg);
        }
    }
}

.asc-window {
    min-height: 50px;
    min-width: 150px;
    box-sizing: border-box;
    z-index: @zindex-modal-background;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border-radius: 5px;
    background-color: @background-normal-ie;
    background-color: @background-normal;
    border: solid @scaled-one-px-value-ie @border-toolbar-ie;
    border: solid @scaled-one-px-value @border-toolbar;

    left: 0;
    top: 0;
    opacity: 0;

    position: fixed;
    cursor: default;

    > .header {
        height: 34px;
        border-radius: 5px 5px 0 0;
        position: absolute;
        padding: 5px 6px 6px;
        left: 0;
        right: 0;
        top: 0;
        text-overflow: ellipsis;

        color: @text-normal-ie;
        color: @text-normal;
        text-align: center;
        font-size: 12px;
        font-weight: 700;
        letter-spacing: 0.01em;
        vertical-align: bottom;
        line-height: 26px;

        background: @background-toolbar-ie;
        background: @background-toolbar;
        border-bottom: solid @scaled-one-px-value-ie @border-toolbar-ie;
        border-bottom: solid @scaled-one-px-value @border-toolbar;

        cursor: move;

        .tools {
            position: absolute;
            right: 0;
            padding-right: 6px;

            &.left {
                left: 0;
                right: auto;
                padding-left: 6px;
            }
        }

        .tool.help {
            width: 20px;
            margin-right:0;
            line-height: 14px;
            font-size: 14px;
            .font-weight-bold();
            color: @text-normal-ie;
            color: @text-normal;
            opacity: 0.7;

            &:hover {
                opacity: 1;
            }

            &.disabled {
                opacity: @component-disabled-opacity-ie;
                opacity: @component-disabled-opacity;
                cursor: default;
            }
        }


        &.resizing {
            cursor: inherit !important;
        }

        .title {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    > .body {
        border-radius: 0 0 5px 5px;
        position: absolute;
        top: 34px;
        width: 100%;
        background-color: @background-normal-ie;
        background-color: @background-normal;
    }

    &.modal {
        z-index: @zindex-modal;
        -ms-touch-action: none;
        -moz-user-select:none;
        -khtml-user-select:none;
        user-select:none;
    }

    &.dethrone {
        z-index: @zindex-modal - 2;
    }

    .icon {
        &.warn {
            background: ~"url('@{common-image-const-path}/controls/warnings_s.svg#attention')" no-repeat center;
        }

        &.error, &.info, &.confirm, &.warn {
            width: 40px;
            height: 40px;
        }

        &.error {
            background: ~"url('@{common-image-const-path}/controls/warnings_s.svg#warning')" no-repeat center;
        }

        &.info {
            background: ~"url('@{common-image-const-path}/controls/warnings_s.svg#info')" no-repeat center;
        }

        &.confirm {
            background: ~"url('@{common-image-const-path}/controls/warnings_s.svg#done')" no-repeat center;
        }
    }

    &.alert {
        .icon {
            float: left;
            margin: 0 0 0 10px;
        }

        min-height: 90px;
        min-width: 230px;

        .body {
            .info-box {
                padding: 20px 20px 20px 10px;
                height: 50px;

                .text {
                    padding-left: 60px;

                    span {
                        font-size: 12px;
                        white-space: nowrap;

                        a {
                            text-decoration: underline;
                            color: @text-normal-ie;
                            color: @text-normal;

                            &:focus, &:hover {
                                color: @text-normal-ie;
                                color: @text-normal;
                            }
                        }
                    }

                    .dont-show-checkbox {
                        margin-top: 13px; text-align: left;
                    }
                }
            }
        }

        .footer {
            height: 37px;
            width: 100%;
            bottom: 0;
            text-align: center;
            padding: 7px 0 0 0;

            &.dontshow {
                height: 55px;
                padding: 15px;
            }
            
            button {
                &:not(:first-child) {
                    margin-left: 10px;
                }
            }
        }
    }

    &.notransform {
        -webkit-transform: none !important;
        -webkit-transition: none !important;
        -moz-transform: none !important;
        -moz-transition: none !important;
        -ms-transition: none !important;
        -ms-transform: none !important;
        -o-transform: none !important;
        -o-transition: none !important;
    }

    &.plain {
        border: none;
        box-shadow: none;
        border-radius: 0;

        .body, .resize-border {
            border-radius: 0 !important;
        }
    }

    .resize-border {
        position: absolute;
        width: 5px;
        height: 5px;
        z-index: 1;
        background: @background-toolbar-ie;
        background: @background-toolbar;
        border: @scaled-one-px-value-ie none @border-toolbar-ie;
        border: @scaled-one-px-value none @border-toolbar;

        &.left {
            left: 0;
        }
        &.right {
            right: 0;
        }
        &.top {
            top: 0;
        }
        &.bottom {
            bottom: 0;
        }

        &.resizing {
            cursor: inherit !important;
        }
    }

    .footer {
        button {
            &:not(:first-child) {
                margin-left: 10px;
            }
        }
    }
}

.modal-dlg {
    .body {
        height: auto;
        padding: 15px;

        .input-row {
            height: @input-height-base;

            & > label:not(.checkbox-indeterminate)  {
                line-height: @input-height-base;
            }

            & > div {
                display: inline-block;
            }
        }
    }

    .footer {
        padding-top: 15px;

        &.center {
            text-align: center;
        }

        &.right {
            text-align: right;
        }
    }
}

// CopyWarningDialog

.copy-warning {
    .body {
        padding: 15px 0;

        & > div {
            width: 100%;
            padding: 0 20px;
        }

        .box {
            text-align: center;
            padding-bottom: 15px;
        }
        .message{
            text-align: justify;
        }
        .hotkeys {
            padding-top: 5px;

            > div {
                display: inline-block;
            }

            > div:first-child {
                margin-right: 35px;
            }

            > div:last-child {
                margin-left: 35px;
            }

            .hotkey {
                font-size: 32px;
            }

            .message {
                text-align: center;
            }
        }

        .footer {
            padding: 20px;
        }
    }
}