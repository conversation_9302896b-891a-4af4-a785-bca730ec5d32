.slider {
    position: relative;
    height: 18px;

    &.single-slider {
        padding-top: 7px;

        .track {
            @track-height: 4px;
            height: @track-height;
            border: (@track-height / 2) solid @border-regular-control-ie;
            border: (@track-height / 2) solid @border-regular-control;
            border-radius: (@track-height / 2);
            background-color: @border-regular-control-ie;
            background-color: @border-regular-control;
            width: calc(100% + @track-height);
            margin-left: (-@track-height / 2);
        }

        .thumb {
            @thumb-width: 12px;
            width: @thumb-width;
            height: @thumb-width;

            border: @scaled-one-px-value-ie solid @icon-normal-ie;
            border: @scaled-one-px-value solid @icon-normal;
            background-color: @background-normal-ie;
            background-color: @background-normal;
            border-radius: (@thumb-width / 2);

            top: 3px;
            margin-left: (@thumb-width / -2);

            &.active {
            }
        }

        &.vertical {
            height: auto;
            width: 18px;
            padding-top: 0;
            padding-left: 7px;
            .track {
                position: absolute;
                @track-height: 4px;
                height: calc(100% + @track-height);
                width: @track-height;
                margin-left: 0;
                margin-top: (-@track-height / 2);
            }
            .thumb {
                @thumb-width: 12px;
                top: auto;
                left: 3px;
                margin-left: 0;
                margin-top: (@thumb-width / -2);
            }
        }
    }

    .thumb {
        position: absolute;
        left: 0;
        margin-left: -7px;
        cursor: default;

        &.active {
        }
    }

    .track {
        height: 18px;
        background-color: transparent;

        div {
            width: 7px;
            height: 18px;
            display: inline-block;
        }

    }

    &.disabled {
        opacity: @component-disabled-opacity-ie;
        opacity: @component-disabled-opacity;
    }
}