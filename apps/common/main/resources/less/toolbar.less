@height-title: 28px;
@height-tabs: 32px;
@height-controls: 67px;
@img-equition-filter: var(--image-border-types-filter);


.toolbar {
    position: relative;

    &:not(.folded) {
        height: @height-tabs + @height-controls;
    }

    &.folded {
        height: @height-tabs;
        transition: height .2s;
        overflow: hidden;

        &.expanded {
            height: @height-tabs + @height-controls;
            overflow: visible;
        }

        &:not(.expanded):not(.cover){
            .ribtab.active {
                > a {
                    font-weight: normal;
                }
            }
        }
    }

    .box-tabs {
        height: @height-tabs;
        font-size: 12px;

        display: flex;
        align-items: stretch;

        .extra {
            background-color: @header-background-color-ie;
            background-color: @header-background-color;

            box-shadow: inset 0 @minus-px 0 0 @border-toolbar-active-panel-top;
        }

    //&::after {
    //  content: '';
    //  position: absolute;
    //  width: 100%;
    //  border-top: 1px solid @gray-dark;
    //  top: @height-tabs - 1;
    //  left: 0;
    //  z-index: 1;
    //}
    }

    --toolbar-tabs-scroller-height: 38px;
    @tabs-scroller-height: var(--toolbar-tabs-scroller-height, 38px);

    .tabs {
        //flex-grow: 1;
        background-color: @header-background-color-ie;
        background-color: @header-background-color;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-shrink: 1;

        box-shadow: inset 0 @minus-px 0 0 @border-toolbar-active-panel-top;

        > ul {
            padding: 4px 0 0;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            list-style: none;
            font-size: 0;
        }

        li {
            display: inline-flex;
            align-items: center;
            height: 100%;
            position: relative;
            &:hover {
                background-color: @highlight-header-button-hover-ie;
                background-color: @highlight-header-button-hover;

                box-shadow: inset 0 @minus-px 0 0 @border-toolbar-active-panel-top;
            }

            &.active {
                background-color: @background-toolbar-ie;
                background-color: @background-toolbar;

                box-shadow: inset @minus-px 0px 0 0 @border-toolbar-active-panel-top, inset @scaled-one-px-value @scaled-one-px-value 0 0 @border-toolbar-active-panel-top;
            }


            > a {
                display: inline-block;
                padding: 0 12px;
                text-decoration: none;
                cursor: default;
                font-size: 12px;
                text-align: center;
                color: @text-toolbar-header-ie;
                color: @text-toolbar-header;
            }

            &.active {
                > a {
                    color: @text-normal-ie;
                    color: @text-normal;
                }
            }
        }

        &:not(.short) {
            .scroll {
                display: none;
            }
        }

        &:not(.style-off-tabs *) {
            &.short {
                li {
                    &:after {
                        content: '';
                        position: absolute;
                        background: @border-toolbar-active-panel-top;
                        height: @scaled-one-px-value;
                        bottom: 0;
                        left: 1px;
                        right: 1px;
                        z-index: 2;
                    }

                    &.active {
                        &:after {
                            background: @background-toolbar;
                        }
                    }
                }
            }
        }

        .scroll {
            line-height: @height-tabs;
            min-width: 20px;
            z-index: 1;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;

            &:hover {
                text-decoration: none;
            }

            &:not(:hover) {
                &:after {
                    opacity: .8;
                }
            }

            &.left{
                box-shadow: 5px 0 20px 5px @header-background-color-ie;
                box-shadow: 18px calc(@tabs-scroller-height - @scaled-one-px-value) 0 10px @border-toolbar-active-panel-top, 5px 0 20px 5px @header-background-color;

                &:after {
                    transform: rotate(135deg);
                    margin-left: 8px;
                }
            }
            &.right{
                box-shadow: -5px 0 20px 5px @header-background-color-ie;
                box-shadow: -10px calc(@tabs-scroller-height - @scaled-one-px-value) 0 10px @border-toolbar-active-panel-top, -5px 0 20px 5px @header-background-color;

                &:after {
                    transform: rotate(-45deg);
                    margin-left: 4px;
                }
            }

            @arrow-length: 8px;
            &:after {
                content: ' ';
                width: @arrow-length;
                height: @arrow-length;
                border: solid white;
                border-width: 0 2px 2px 0;
            }
        }
    }

    .box-controls {
        //height: @height-controls;         // button has strange offset in IE when odd height
        padding: 7px 0;
        display: flex;

        //background-color: #F2CBBF;

        .panel {
            display: table;
            height: 100%;
        }
        //width: 100%;
    }

    .box-panels {
        flex-grow: 1;
        -ms-flex: 1;
        padding-right: 6px;

        .panel:not(.active) {
            display: none;
        }

        /* ##adopt-panel-width */
        .panel .compactwidth {
            .btn-group, .btn-toolbar {
                &.x-huge {
                    .caption {
                        display: none;
                    }

                    .inner-box-caption {
                        justify-content: center;
                        align-items: center;
                        padding: 0 2px;
                    }

                    .compact-caret {
                        display: block;
                    }
                }
            }
        }
        /**/
    }

    .more-container {
        background-color: @background-toolbar-ie;
        background-color: @background-toolbar;
        min-width:auto;
        padding: 12px 10px 7px 0;
        border-radius: 0;
        z-index:999;
        .compactwidth {
            .btn-group, .btn-toolbar {
                &.x-huge {
                    .caption {
                        display: none;
                    }

                    .inner-box-caption {
                        justify-content: center;
                        align-items: center;
                    }
                    .compact-caret {
                        display: block;
                    }
                }
            }
        }
        .group {
            height: 52px !important;
            &.flex {
                vertical-align: bottom;
            }
        }
        &[data-tab=pivot] {
            padding: 5px 10px 0 0;
            .group {
                height: 60px !important;
            }
            .separator {
                margin-top: 4px;
                margin-bottom: 4px;
            }
        }
    }

    .more-box {
        background-color: @background-toolbar-ie;
        background-color: @background-toolbar;
    }

    background-color: @background-toolbar-ie;
    background-color: @background-toolbar;
    @minus-px: calc(-1 * @scaled-one-px-value);
    .box-inner-shadow(0 -1px @border-toolbar-ie);
    .box-inner-shadow(0 @minus-px @border-toolbar);

    .group {
        position: relative;
        display: table-cell;
        vertical-align: middle;
        white-space: nowrap;
        padding-left: 6px;
        font-size: 0;

        &.small {
            padding-left: 10px;

            + .separator:not(.invisible) {
                margin-left: 10px;
            }
        }

        //&.no-group-mask {
        //    .elset {
        //        position: relative;
        //    }
        //}
    }

    .elset {
        height: 20px;
        font-size: 0;

        &:not(:first-child) {
            margin-top: 8px;
        }

        &.font-normal {
            .font-size-normal();
        }
    }

    .separator {
        margin-left: 6px;

        &.close {
            margin-left: 5px;
        }

        &.invisible {
            margin-left: 0;
            border: none;
        }

        &.long {
            height: 52px;
        }

        &.short {
            height: 20px;
        }
    }

    .btn-slot {
        display: inline-block;
        width: 20px;
        height: auto;

        &.split {
            width: 31px;
        }

        &.split-small {
            width: 26px;
        }

        &.text {
            width: auto;
        }

        &.border {
            border: 1px solid @border-regular-control-ie;
            border: 1px solid @border-regular-control;
            .border-radius(1px);
            width: 22px;
            height: 22px;
        }

        .checkbox-indeterminate {
            margin-top: 3px;
        }
    }

    .toolbar-mask {
        position: absolute;
        top: 32px;
        left: 48px;
        right: 0;
        bottom: 0;
        opacity: 0;
        background-color: @background-toolbar-ie;
        background-color: @background-toolbar;
        /* z-index: @zindex-tooltip + 1; */
    }

    &.toolbar-view {
        --toolbar-tabs-scroller-height: 42px;

        .toolbar-mask {
            left: 0;
        }
    }

    .top-title > & {
        &:not(.folded) {
            height: 28 + @height-controls;
        }

        &.folded {
            height: 28px;

            &.expanded {
                height: 28 + @height-controls;
            }
        }

        .tabs > ul {
            padding-top: 0;
        }

        .box-tabs {
            height: 28px;
        }

        .toolbar-mask {
            left: 0;
        }
    }
}

.style-off-tabs {
    .toolbar {
        @underscore_height: 3px;

        .tabs, .extra {
            background-color: transparent;
            box-shadow: inset 0 -1px 0 0 @border-regular-control-ie;
            box-shadow: inset 0 -1px 0 0 @border-regular-control;
        }

        .tabs {
            ul {
                padding: 0;
            }

            li {
                position: relative;

                &:after {
                    //transition: opacity .1s;
                    //transition: bottom .1s;
                    border-top: @underscore_height solid @text-normal-ie;
                    border-top: @underscore_height solid @text-normal;
                    content: '';
                    position: absolute;
                    width: 100%;
                    bottom: -@underscore_height;
                    opacity: 0;
                }

                &.active {
                    background-color: transparent;
                    box-shadow: none;
                    //> a {
                    //    padding: 0 12px;
                    //}
                    &:after {
                        opacity: 1;
                        bottom: 0;
                    }
                }

                &:hover:not(.active) {
                    background-color: rgba(0, 0, 0, .05);
                    box-shadow: none;

                    .theme-type-dark & {
                        background-color: rgba(255, 255, 255, .05);
                    }

                    //background-color: @highlight-button-hover-ie;
                    //background-color: @highlight-button-hover;
                }

                > a {
                    color: @text-normal-ie;
                    color: @text-normal;
                    &::after {
                        display:block;
                        content:attr(data-title);
                        font-weight:bold;
                        height:1px;
                        color:transparent;
                        overflow:hidden;
                        visibility:hidden;
                        margin-bottom:-1px;
                    }
                }
            }
        }

        .theme-type-light & {
            .extra {
                #header-logo {
                    i {
                        background-image: ~"url('@{common-image-const-path}/header/dark-logo_s.svg')";
                        background-repeat: no-repeat;
                    }
                }
            }
        }

        .tabs .scroll {
            &.left {
                box-shadow: 5px 0 20px 5px @background-toolbar-ie;
                box-shadow: 5px 0 20px 5px @background-toolbar;
            }

            &.right {
                box-shadow: -5px 0 20px 5px @background-toolbar-ie;
                box-shadow: -5px 0 20px 5px @background-toolbar;
            }

            &:after {
                border-color: @text-normal-ie;
                border-color: @text-normal;
            }
        }

        // TODO: move to appropriate module
        .btn-current-user, .btn-header  {
            .color-user-name {
                background-color: @text-normal-ie;
                background-color: @text-normal;
                color: @text-inverse-ie;
                color: @text-inverse;
            }
        }

        .btn-header, .btn-users, .btn-header-share {
            .icon {
                &.icon--inverse {
                    background-position-x: 0 !important;
                    background-position-x: @button-small-normal-icon-offset-x !important;
                }
            }

            &:active, &.active {
                &:not(.disabled) .icon.toolbar__icon {
                    &.icon--inverse {
                        background-position-x: @button-small-active-icon-offset-x !important;
                    }
                }
            }

            svg.icon {
                fill: @icon-toolbar-header-ie;
                fill: @icon-toolbar-header;
            }

            .caption {
                color: @text-normal-ie;
                color: @text-normal;
            }

            .inner-box-icon {
                > svg {
                    fill: @text-normal-ie;
                    fill: @text-normal;
                }
            }

            &:hover:not(.disabled) {
                background-color: @highlight-button-hover-ie;
                background-color: @highlight-button-hover;
            }

            &:active, &.active {
                &:not(.disabled) {
                    background-color: @highlight-button-pressed-ie;
                    background-color: @highlight-button-pressed;

                    svg.icon {
                        fill: @icon-toolbar-header-ie;
                        fill: @icon-toolbar-header;
                    }

                    .caption {
                        color: @text-normal-pressed-ie;
                        color: @text-normal-pressed;
                    }

                    .inner-box-icon {
                        > svg {
                            fill: @text-normal-pressed-ie;
                            fill: @text-normal-pressed;
                        }
                    }

                    .color-user-name {
                        background-color: @text-normal-pressed-ie;
                        background-color: @text-normal-pressed;
                        color: @highlight-button-pressed-ie;
                        color: @highlight-button-pressed;
                    }
                }
            }
        }

        #rib-doc-name {
            color: @text-normal-ie;
            color: @text-normal;
            &:hover:not(:disabled),&:focus {
                box-shadow: 0 0 0 @scaled-one-px-value-ie @highlight-button-hover-ie;
                box-shadow: 0 0 0 @scaled-one-px-value @highlight-button-hover;
            }
        }

        &.editor-native-color {
            .tabs li:after {
                border-color: @header-background-color-ie;
                border-color: @header-background-color;
            }
        }
    }

    &.style-skip-docname .toolbar {
        #box-doc-name > input {
            display: none;
        }
    }
}

.toolbar-fullview-panel {
    position: absolute;
    bottom: 0;
    width: 100%;
    z-index: @zindex-navbar + 3;
}

.toolbar {
    &.cover {
        ul {
            z-index: @zindex-navbar + 4;
        }
    }

    &:not(.cover):not(.z-clear) {
        z-index: @zindex-navbar + 1;
    }
}

.btn-toolbar {
    &:active,
    &.active {
        &:not(.disabled) {
            svg.icon {
                fill: @icon-normal-pressed-ie;
                fill: @icon-normal-pressed;
            }
        }
    }

    svg.icon {
        background-image: none;

        fill: @icon-normal-ie;
        fill: @icon-normal;
    }

    &.borders--small {
        border-radius: 2px;

        &:not(:active) {
            //box-shadow: inset 0 0 0 @scaled-one-px-value-ie @border-regular-control-ie;
            //box-shadow: inset 0 0 0 @scaled-one-px-value @border-regular-control;
        }

        & {
            width: 21px;
            height: 21px;
        }

        .icon {
            width: 22px;
            height: 22px;

            .pixel-ratio__1_25 &,
            .pixel-ratio__1_75 & {
                width: 20px;
                height: 20px;
            }
        }
    }
}

[applang=ru] {
    .dropdown-menu.toc-menu {
        --bckgHOffset: -144px;
    }
}

#slot-combo-insertshape {
    width: 150px;
    height: 46px;
    .view {
        padding-right: 14px;
    }
    .dataview.field-picker {
        height: 100%;
        margin: 0;
        padding: 2px;
        .item {
            margin: 0;
            -webkit-box-shadow: none;
            box-shadow: none;
        }

        &:not(.disabled) {
            .item {
                &:hover {
                    background-color: @highlight-button-hover-ie;
                    background-color: @highlight-button-hover;
                }
                &.active {
                    background-color: @highlight-button-pressed-ie;
                    background-color: @highlight-button-pressed;

                    svg.icon {
                        fill: @icon-normal-pressed-ie;
                        fill: @icon-normal-pressed;
                    }
                }
            }
        }
    }
    .button {
        width: 14px;
        .caret {
            width: 4px;
            height: 4px;
        }
    }
}

.item-shape {
    .icon {
        width: 20px;
        height: 20px;
    }

    svg.icon {
        display: inline-block;
        vertical-align: middle;
        fill: @icon-normal-ie;
        fill: @icon-normal;
    }

    width: 20px;
    height: 20px;
}

.item-equation {
    border: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border: @scaled-one-px-value solid @border-regular-control;

    .equation-icon {
        .background-ximage-all('toolbar/math.png', 1500px, @commonimage: true);
        opacity: @component-normal-icon-opacity;

        .theme-type-dark & {
            -webkit-filter: @img-equition-filter;
            filter: @img-equition-filter;
        }
    }
}

// charts
.menu-insertchart {
    margin: 5px 5px 0 10px;

    .group-description {
        padding-left: 4px;
    }

    .group-items-container {
        float: left;
        position: relative;
    }
}

.item-chartlist {
    width: 40px;
    height: 40px;
    .icon {
        width: 40px;
        height: 40px;
    }

    svg.icon {
        display: inline-block;
        vertical-align: middle;
        fill: @icon-normal-ie;
        fill: @icon-normal;
    }
}

.color-schemas-menu {
    span {
        &.colors {
            display: inline-block;
            margin-right: 15px;
        }

        &.color {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 2px;
            border: @scaled-one-px-value-ie solid @border-color-shading-ie;
            border: @scaled-one-px-value solid @border-color-shading;
            vertical-align: middle;
        }

        &.text {
            vertical-align: middle;
        }
    }
    &.checked {
        &:before {
            display: none !important;
        }
        &, &:hover, &:focus {
            background-color: @highlight-button-pressed-ie;
            background-color: @highlight-button-pressed;
            color: @text-normal-pressed-ie;
            color: @text-normal-pressed;
            span.color {
                border-color: @icon-normal-ie;
                border-color: @icon-normal;
            }
        }
    }
}

.item-databar {
    .icon {
        width: 25px;
        height: 25px;
    }

    svg.icon {
        display: inline-block;
        vertical-align: middle;
        fill: @icon-normal-ie;
        fill: @icon-normal;
    }

    width: 25px;
    height: 25px;
}

.item-colorscale {
    .icon {
        width: 25px;
        height: 25px;
    }

    svg.icon {
        display: inline-block;
        vertical-align: middle;
        fill: @icon-normal-ie;
        fill: @icon-normal;
    }

    width: 25px;
    height: 25px;
}

.menu-iconsets {
    margin: 5px 5px 0 10px;

    .group-items-container > div {
        margin-right: 8px !important;
        &:not(:hover),
        &:not(.selected) {
            .box-shadow(none);
        }
    }
    .item-iconset {
        img {
            margin: 2px;
        }

        width: 100px;
        height: 20px;
    }
}

#slot-field-zoom {
    float: left;
    min-width: 46px;
}

section .field-styles {
    width: 100%;
}