.listview {
  border: @scaled-one-px-value-ie solid @border-regular-control-ie;
  border: @scaled-one-px-value solid @border-regular-control;
  .border-radius(@border-radius-small);
  line-height: 15px;

  &.inner {
    width: 100%;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    position: relative;

    .empty-text {
        text-align: center;
        width: 100%;
        height: 100%;
        color: @text-tertiary-ie;
        color: @text-tertiary;
        td {
          padding: 5px;
        }
    }
  }

  &:not(.no-focus):focus:not(.disabled) {
    border-color: @border-control-focus-ie;
    border-color: @border-control-focus;
  }

  & > .item {
//    display: block;
    text-overflow: ellipsis;
    padding: 3px 6px;
    cursor: pointer;
    white-space: pre-wrap;

    background-color: @background-normal-ie;
    background-color: @background-normal;
    border-color: @border-regular-control-ie;
    border-color: @border-regular-control;
    border-style: solid;
    border-width: @scaled-one-px-value-ie 0;
    border-width: @scaled-one-px-value 0;
    border-top-color: transparent;
  }

  &:not(.disabled) > .item {
    &:hover {
      background-color: @highlight-button-hover-ie;
      background-color: @highlight-button-hover;
      border-color: @highlight-button-hover-ie;
      border-color: @highlight-button-hover;
      border-style: solid;
      border-width: @scaled-one-px-value-ie 0;
      border-width: @scaled-one-px-value 0;
    }

    &.selected {
      background-color: @highlight-button-pressed-ie;
      background-color: @highlight-button-pressed;
      color: @text-normal-pressed-ie;
      color: @text-normal-pressed;
      border-color: @highlight-button-pressed-ie;
      border-color: @highlight-button-pressed;
      border-style: solid;
      border-width: @scaled-one-px-value-ie 0;
      border-width: @scaled-one-px-value 0;
    }
  }

  &.ps-container {
      overflow: hidden;
  }

  &.disabled {
    > .item {
      cursor: default;
      opacity: @component-disabled-opacity-ie;
      opacity: @component-disabled-opacity;
    }
  }

  .dbl-clickable& .list-item {
    pointer-events: none;
  }
}

.no-borders > .listview .item {
  border-color: transparent;
  border-top-color: transparent;
}