.spinner{
    @spin-height: 22px;
    @trigger-width: 17px;
    position: relative;

    input {
        height: @spin-height;
        text-align: right;
        padding-left: 1px;
        padding-right: @trigger-width + 2px;

        &:focus {
            // border-color: @border-preview-select;
        }
    }

    button {
        display: block;
        position: relative;
        width: @trigger-width;
        height: (@spin-height/2) - 1;
        height: calc(@spin-height/2 - 1px/@pixel-ratio-factor);
        padding: 0;
        margin: 0;
        border: none;
        background: none;
        line-height: 1;

        &.disabled {
            cursor: default;
        }

        &:focus {
            outline: 0;
            outline-offset: 0;
        }
    }

    .spinner-buttons {
        position: absolute;
        top: 0;
        right: 1px;
        right: @scaled-one-px-value;
        border-top: @scaled-one-px-value-ie solid transparent;
        border-top: @scaled-one-px-value solid transparent;
        border-bottom: @scaled-one-px-value-ie solid transparent;
        border-bottom: @scaled-one-px-value solid transparent;

        i {
            display: inline-block;
            width: 4px;
            height: 4px;

            border: solid @scaled-one-px-value-ie @icon-normal-ie;
            border: solid @scaled-one-px-value @icon-normal;
            border-bottom: none;
            border-right: none;
        }

        button {
            &.disabled {
                opacity: @component-disabled-opacity-ie;
                opacity: @component-disabled-opacity;
            }

            &:hover:not(:disabled):not(.disabled) {
                background-color: @highlight-button-hover-ie;
                background-color: @highlight-button-hover;
            }
        }
    }

    .spinner-up {
        i {
            transform: rotate(45deg) translate(-1px,-1px);
        }
    }

    .spinner-down {
        i {
            transform: rotate(-135deg) translate(2px,2px);
        }
    }
}
