.dataview {
    &.inner {
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;

        .empty-text {
            text-align: center;
            height: 100%;
            width: 100%;
            color: @text-tertiary-ie;
            color: @text-tertiary;
            td {
                padding: 5px;
            }
        }
    }

    > .item {
        display: inline-block;
        float: left;
        margin: 4px;
        cursor: pointer;

        .box-shadow(0 0 0 @scaled-one-px-value-ie @border-regular-control-ie);
        .box-shadow(0 0 0 @scaled-one-px-value @border-regular-control);

        &:hover,
        &.selected {
            .box-shadow(0 0 0 2px @border-preview-select-ie);
            .box-shadow(0 0 0 @scaled-two-px-value @border-preview-select);
        }

        .options__icon {
            opacity: @component-normal-icon-opacity;
        }
    }

    .grouped-data {
        clear: left;
        overflow: hidden;

        &.inline {
            display: inline-block;
            &.margin .group-items-container {
                margin-right: 20px;
            }
        }

        .group-items-container {
            overflow: hidden;
            margin-bottom: 5px;

            & > div {
                display: inline-block;
                float: left;
                margin: 2px 4px 4px;

                &:not(.disabled) {
                    cursor: pointer;
                }

                &.disabled {
                    opacity: @component-disabled-opacity-ie;
                    opacity: @component-disabled-opacity;
                }

                .box-shadow(0 0 0 @scaled-one-px-value-ie @border-regular-control-ie);
                .box-shadow(0 0 0 @scaled-one-px-value @border-regular-control);

                &:hover:not(.disabled),
                &.selected:not(.disabled) {
                    .box-shadow(0 0 0 2px @highlight-button-pressed-ie);
                    .box-shadow(0 0 0 @scaled-two-px-value @highlight-button-pressed);
                }
            }
        }
    }

    .header-name {
        padding: 10px 2px 12px 4px;
        .font-weight-bold();
        cursor: default;
    }

    &.bordered {
        border: @scaled-one-px-value-ie solid @input-border;
        border: @scaled-one-px-value solid @input-border;
        .border-radius(@border-radius-small);
    }
}

.menu-insert-shape, .menu-change-shape {
    width: 362px;
    padding: 10px 5px 10px 10px !important;
    .group-description {
        padding: 3px 0 3px 4px;
    }
    .dataview .grouped-data .group-items-container .item {
        box-shadow: none;
        margin: 2px 2px 2px;
    }
}