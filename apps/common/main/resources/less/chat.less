#chat-box {
    display: table;
    position: relative;
    width: 100%;
    height: 100%;
    border-collapse: collapse;

    > div {
        display: table-row;
    }

    .area {
        position: absolute;
        border-bottom: 1px solid @border-toolbar-ie;
        border-bottom: 1px solid @border-toolbar;
        overflow: hidden;
    }

    .ps-scrollbar-y-rail {
        margin-top: 5px;
        margin-bottom: 5px;
        right: 4px !important;
    }

    #chat-title {
        label {
            padding: 18px 20px;
            font-size: @font-size-large;
        }
    }

    #chat-users {
        left: 0;
        top: 0;
        right: 0;
        height: 70px;
        padding: 5px 0;

        ul {
            margin: 0;
            padding: 0;
            overflow: hidden;

            li {
                list-style: none;
                padding: 2px 0 2px 20px;
                overflow: hidden;

                &.offline {
                    display: none;
                }
            }
        }

        .color {
            display: inline-block;
            vertical-align: middle;
            margin: 0 5px 3px 0;
            width: 12px;
            height: 12px;
            border: 1px solid @border-toolbar-ie;
            border: 1px solid @border-toolbar;
        }

        .name {
            font-size: 12px;
            .font-weight-bold();
            display: block;
            padding: 0 10px 0 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            line-height: normal;
            height: 16px;
        }
    }

    #chat-messages {
        left: 0;
        top: 70px;
        right: 0;
        bottom: 110px;
        font-size: 12px;

        ul {
            margin: 0;
            padding: 0;

            li {
                list-style: none;

                padding: 5px 10px 8px 20px;
                white-space: pre-wrap;
                white-space: -moz-pre-wrap;
                white-space: -o-pre-wrap;
                word-wrap: break-word;

                .user {
                    .font-weight-bold();
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .user-name {
                    color: @text-normal-ie;
                    color: @text-normal;
                    font-size: 12px;
                    .font-weight-bold();
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .color {
                    width: 12px;
                    height: 12px;
                    border: 1px solid @border-toolbar-ie;
                    border: 1px solid @border-toolbar;
                    margin: 0 5px 3px 0;
                    vertical-align: middle;
                }

                .message {
                    word-wrap: break-word;
                    width: 100%;
                    &.user-select {
                        cursor: text;
                    }
                    //&.service {
                    //    text-align: center;
                    //    color: #CECECE;
                    //    text-shadow: 0 1px 0 #fff;
                    //    padding-right: 20px;
                    //}
                }
            }
        }
    }

    #chat-options {
        #chat-options-ct {
            padding: 10px 20px 40px 20px;
            height: 100%;
        }

        textarea {
            width: 100%;
            height: 100%;
            resize: none;
            margin-bottom: 5px;
            border: 1px solid @border-regular-control-ie;
            border: 1px solid @border-regular-control;
            font-size: 12px;

            &:focus {
                border-color: @border-control-focus-ie;
                border-color: @border-control-focus;
            }
        }
    }
}