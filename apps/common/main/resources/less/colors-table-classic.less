
:root {
    .theme-classic-light {
        --toolbar-header-document: #446995;
        --toolbar-header-spreadsheet: #40865c;
        --toolbar-header-presentation: #aa5252;

        --text-toolbar-header-on-background-document: #38567A;
        --text-toolbar-header-on-background-spreadsheet: #336B49;
        --text-toolbar-header-on-background-presentation: #8D4444;

        --background-normal: #fff;
        --background-toolbar: #f1f1f1;
        --background-toolbar-additional: #f1f1f1;
        --background-primary-dialog-button: #7d858c;
        --background-accent-button: #446995;
        --background-tab-underline: #444;
        --background-notification-popover: #fcfed7;
        --background-notification-badge: #ffd112;
        --background-scrim: fade(#000, 20%);
        --background-loader: fade(#000, 65%);
        --background-contrast-popover: #fff;

        --highlight-button-hover: #d8dadc;
        --highlight-button-pressed: #7d858c;
        --highlight-button-pressed-hover: #7d858c;
        --highlight-primary-dialog-button-hover: #666d73;
        --highlight-accent-button-hover: #375478;
        --highlight-accent-button-pressed: #293f59;
        --highlight-header-button-hover: fade(#fff, 20%);
        --highlight-header-button-pressed: fade(#000, 20%);
        --highlight-toolbar-tab-underline: #444;
        --highlight-text-select: #3494fb;

        --border-toolbar: #cbcbcb;
        --border-divider: #cbcbcb;
        --border-regular-control: #cfcfcf;
        --border-toolbar-button-hover: #d8dadc;
        --border-preview-hover: #cfcfcf;
        --border-preview-select: #848484;
        --border-control-focus: #848484;
        --border-color-shading: fade(#000, 20%);
        --border-error: #d9534f;
        --border-contrast-popover: #fff;

        --text-normal: #444;
        --text-normal-pressed: #fff;
        --text-secondary: #a5a5a5;
        --text-tertiary: #a5a5a5;
        --text-link: #445799;
        --text-link-hover: #445799;
        --text-link-active: #445799;
        --text-link-visited: #445799;
        --text-inverse: #fff;
        --text-toolbar-header: #fff;
        --text-contrast-background: #fff;
        --text-alt-key-hint: #444;

        --icon-normal: #444;
        --icon-normal-pressed: #fff;
        --icon-inverse: #444;
        --icon-toolbar-header: #fff;
        --icon-notification-badge: #000;
        --icon-contrast-popover: #fff;
        --icon-success: #5b9f27;

        // Canvas

        --canvas-background: #e2e2e2;
        --canvas-content-background: #fff;
        --canvas-page-border: #bbbec2;

        --canvas-ruler-background: #fff;
        --canvas-ruler-border: #cbcbcb;
        --canvas-ruler-margins-background: #d6d6d6;
        --canvas-ruler-mark: #585b5e;
        --canvas-ruler-handle-border: #555;
        --canvas-ruler-handle-border-disabled: #a9afb5;

        --canvas-high-contrast: #000;
        --canvas-high-contrast-disabled: #82878f;

        --canvas-cell-border: fade(#000, 10%);
        --canvas-cell-title: #444;
        --canvas-cell-title-hover: #d6d6d6;
        --canvas-cell-title-selected: #c1c1c1;
        --canvas-cell-title-border: #cdcdcd;
        --canvas-cell-title-border-hover: #afafaf;
        --canvas-cell-title-border-selected: #929292;

        --canvas-dark-cell-title: #666666;
        --canvas-dark-cell-title-hover: #999;
        --canvas-dark-cell-title-selected: #333;
        --canvas-dark-cell-title-border: #3d3d3d;
        --canvas-dark-cell-title-border-hover: #5c5c5c;
        --canvas-dark-cell-title-border-selected: #0f0f0f;

        --canvas-scroll-thumb: #f1f1f1;
        --canvas-scroll-thumb-hover: #cfcfcf;
        --canvas-scroll-thumb-pressed: #adadad;
        --canvas-scroll-thumb-border: #cfcfcf;
        --canvas-scroll-thumb-border-hover: #cfcfcf;
        --canvas-scroll-thumb-border-pressed: #adadad;
        --canvas-scroll-arrow: #adadad;
        --canvas-scroll-arrow-hover: #f1f1f1;
        --canvas-scroll-arrow-pressed: #f1f1f1;
        --canvas-scroll-thumb-target: #cfcfcf;
        --canvas-scroll-thumb-target-hover: #f1f1f1;
        --canvas-scroll-thumb-target-pressed: #f1f1f1;

        // Others

        --button-small-normal-icon-offset-x: 0;
        --button-small-active-icon-offset-x: -20px;
        --button-large-normal-icon-offset-x: 0;
        --button-large-active-icon-offset-x: -31px;
        --button-huge-normal-icon-offset-x: 0;
        --button-huge-active-icon-offset-x: -40px;
        --button-xhuge-normal-icon-offset-x: 0;
        --button-xhuge-active-icon-offset-x: -28px;
        //--button-xhuge-normal-icon-offset-x: -37px;
        //--button-xhuge-active-icon-offset-x: -37px;

        --modal-window-mask-opacity: 0.6;
        --image-border-types-filter: none;
        --image-border-types-filter-selected: invert(100%) brightness(4);
        --component-normal-icon-filter: none;

        --component-normal-icon-opacity: .8;
        --component-hover-icon-opacity: .8;
        --component-active-icon-opacity: 1;
        --component-active-hover-icon-opacity: 1;
        --component-disabled-opacity: .4;

        --header-component-normal-icon-opacity: .8;
        --header-component-hover-icon-opacity: .8;
        --header-component-active-icon-opacity: 1;
        --header-component-active-hover-icon-opacity: 1;

        --menu-icon-item-checked-offset-x: -20px;
    }
}

