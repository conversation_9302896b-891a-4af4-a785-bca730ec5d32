#symbol-table-scrollable-div, #symbol-table-recent {
  div{
    display: inline-block;
    vertical-align: top;
  }

  .cell{
    width: 31px;
    height: 33px;
    border-right: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border-right: @scaled-one-px-value solid @border-regular-control;
    border-bottom: @scaled-one-px-value-ie solid @border-regular-control-ie;
    border-bottom: @scaled-one-px-value solid @border-regular-control;
    background: @background-normal-ie;
    background: @background-normal;
    align-content: center;
    vertical-align: middle;
    text-align: center;
    font-size: 22px;
    -khtml-user-select: none;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    cursor: default;
    overflow:hidden;
    display: inline-block;
  }

  .cell-selected{
    background-color: @border-preview-select-ie;
    background-color: @border-preview-select;
    color: white;
  }
}

#symbol-table-recent {
  width: 100%;
  height: 32px;
  overflow: hidden;
  border: @border-regular-control-ie solid @scaled-one-px-value-ie;
  border: @border-regular-control solid @scaled-one-px-value;
}

#symbol-table-scrollable-div {
  #id-preview {
    width: 100%;
    height: 132px;
    position:relative;
    overflow:hidden;
    border: @border-regular-control-ie solid @scaled-one-px-value-ie;
    border: @border-regular-control solid @scaled-one-px-value;
  }

  #id-preview-data {
    width: 100%;
    height: 132px;
    position:relative;
    overflow:hidden;
  }
}
