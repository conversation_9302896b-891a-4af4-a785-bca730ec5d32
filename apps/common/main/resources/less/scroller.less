.ps-container.oo {
    .ps-scrollbar-x-rail {
        z-index: 1;
        bottom: 1px;
        height: 9px;
        margin-right: 1px;
        margin-left: 1px;

        &.always-visible-x {
            opacity: 1 !important;
        }

        .ps-scrollbar-x {
            background-color: @background-toolbar-ie;
            background-color: @background-toolbar;

            &.always-visible-x {
                bottom: 0px;
                height: 9px;

                background-color: @background-toolbar-ie;
                background-color: @background-toolbar;
                background-image: data-uri('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAOCAYAAAD0f5bSAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAuSURBVChTY6AfOH/+/H9SaSYQg1RAlqZhCT5+/AgOSlJoOgY50DqSNZJhEwMDACkvNZLpune5AAAAAElFTkSuQmCC');
                background-repeat: no-repeat;

                @media
                only screen and (-webkit-min-device-pixel-ratio: 2),
                only screen and (min-resolution: 2dppx),
                only screen and (min-resolution: 192dpi) {
                    background-image: data-uri('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAcCAMAAABIzV/hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMAUExURQAAAAEBAQICAgMDAwQEBAUFBQYGBgcHBwgICAkJCQoKCgsLCwwMDA0NDQ4ODg8PDxAQEBERERISEhMTExQUFBUVFRYWFhcXFxgYGBkZGRoaGhsbGxwcHB0dHR4eHh8fHyAgICEhISIiIiMjIyQkJCUlJSYmJicnJygoKCkpKSoqKisrKywsLC0tLS4uLi8vLzAwMDExMTIyMjMzMzQ0NDU1NTY2Njc3Nzg4ODk5OTo6Ojs7Ozw8PD09PT4+Pj8/P0BAQEFBQUJCQkNDQ0REREVFRUZGRkdHR0hISElJSUpKSktLS0xMTE1NTU5OTk9PT1BQUFFRUVJSUlNTU1RUVFVVVVZWVldXV1hYWFlZWVpaWltbW1xcXF1dXV5eXl9fX2BgYGFhYWJiYmNjY2RkZGVlZWZmZmdnZ2hoaGlpaWpqamtra2xsbG1tbW5ubm9vb3BwcHFxcXJycnNzc3R0dHV1dXZ2dnd3d3h4eHl5eXp6ent7e3x8fH19fX5+fn9/f4CAgIGBgYKCgoODg4SEhIWFhYaGhoeHh4iIiImJiYqKiouLi4yMjI2NjY6Ojo+Pj5CQkJGRkZKSkpOTk5SUlJWVlZaWlpeXl5iYmJmZmZqampubm5ycnJ2dnZ6enp+fn6CgoKGhoaKioqOjo6SkpKWlpaampqenp6ioqKmpqaqqqqurq6ysrK2tra6urq+vr7CwsLGxsbKysrOzs7S0tLW1tba2tre3t7i4uLm5ubq6uru7u7y8vL29vb6+vr+/v8DAwMHBwcLCwsPDw8TExMXFxcbGxsfHx8jIyMnJycrKysvLy8zMzM3Nzc7Ozs/Pz9DQ0NHR0dLS0tPT09TU1NXV1dbW1tfX19jY2NnZ2dra2tvb29zc3N3d3d7e3t/f3+Dg4OHh4eLi4uPj4+Tk5OXl5ebm5ufn5+jo6Onp6erq6uvr6+zs7O3t7e7u7u/v7/Dw8PHx8fLy8vPz8/T09PX19fb29vf39/j4+Pn5+fr6+vv7+/z8/P39/f7+/v///+KwXX0AAAABdFJOUwBA5thmAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAANklEQVQoU93IoREAQAjEwOu/OWqhg1dBBYv4zKxJLqtayYIsyIIsyBqf1r2SBVmQBVmQNa5KHuCckwGi77ddAAAAAElFTkSuQmCC');
                    background-size: auto 14px;
                }

                background-position: center 0;
                .border-radius(2px);
                border: @scaled-one-px-value-ie solid @border-regular-control-ie;
                border: @scaled-one-px-value solid @border-regular-control;
                -o-transition: background-color .2s linear;
                -webkit-transition: background-color .2s linear;
                -moz-transition: background-color .2s linear;
                transition: background-color .2s linear;
            }
        }

        &:hover,
        .hover {
            .ps-scrollbar-x {
                &.always-visible-x {
                    background-color: @canvas-scroll-thumb-hover-ie;
                    background-color: @canvas-scroll-thumb-hover;
                    background-position: center -7px;
                }
            }
        }

        &.in-scrolling {
            .ps-scrollbar-x {
                &.always-visible-x {
                    background-color: @canvas-scroll-thumb-hover-ie;
                    background-color: @canvas-scroll-thumb-hover;
                    border-color: @canvas-scroll-thumb-border-hover-ie;
                    border-color: @canvas-scroll-thumb-border-hover;
                    background-position: center -7px;
                }
            }
        }
    }

    .ps-scrollbar-y-rail {
        z-index: 1;
        right: 1px;
        width: 9px;
        margin-top: 1px;
        margin-bottom: 1px;

        &.always-visible-y {
            opacity: 1 !important;
        }

        .ps-scrollbar-y {
            background-color: @background-toolbar-ie;
            background-color: @background-toolbar;

            &.always-visible-y {
                right: 0px;
                width: 9px;

                background-color: @background-toolbar-ie;
                background-color: @background-toolbar;
                .background-ximage('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAANCAQAAAAz1Zf0AAAAIUlEQVR42mNgAILz/0GQAQo+/gdBBqLAqE5ydH5k+sgEANHgUH2JtDRHAAAAAElFTkSuQmCC',
                        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAaAgMAAADZOtQaAAAACVBMVEUAAADPz8/x8fFVrc9qAAAAAXRSTlMAQObYZgAAABZJREFUeNpjYAgNYOBaxcDEgAsMLXkA/sUJfm1m4l8AAAAASUVORK5CYII=', 14px);

                .pixel-ratio__1_5 & {
                    //background-image: ~"url(@{common-image-const-path}/controls/<EMAIL>)";
                    background-image: data-uri('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAATAgMAAAAG1X4lAAAACVBMVEUAAADPz8/x8fFVrc9qAAAAAXRSTlMAQObYZgAAABNJREFUeNpjYAx14FrFgAboLAgAVgQJB86JyMQAAAAASUVORK5CYII=');
                    background-size: 15px auto;
                }

                background-repeat: no-repeat;
                background-position: 0 center;
                .border-radius(2px);
                border: @scaled-one-px-value-ie solid @border-regular-control-ie;
                border: @scaled-one-px-value solid @border-regular-control;
                -o-transition: background-color .2s linear;
                -webkit-transition: background-color .2s linear;
                -moz-transition: background-color .2s linear;
                transition: background-color .2s linear;
            }
        }

        &:hover,
        .hover {
            background-color: @background-toolbar-additional-ie;
            background-color: @background-toolbar-additional;

            .ps-scrollbar-y {
                &.always-visible-y {
                    background-color: @canvas-scroll-thumb-hover-ie;
                    background-color: @canvas-scroll-thumb-hover;
                    background-position: -7px center;
                }
            }
        }

        &.in-scrolling {
            background-color: @background-toolbar-additional-ie;
            background-color: @background-toolbar-additional;

            .ps-scrollbar-y {
                &.always-visible-y {
                    background-color: @canvas-scroll-thumb-hover-ie;
                    background-color: @canvas-scroll-thumb-hover;
                    border-color: @canvas-scroll-thumb-border-hover-ie;
                    border-color: @canvas-scroll-thumb-border-hover;
                    background-position: -7px center;
                }
            }
        }
    }

    .ps-container:hover .ps-scrollbar-y-rail,
    .ps-container.hover .ps-scrollbar-y-rail,
    .ps-container .ps-scrollbar-y-rail:hover,
    .ps-container .ps-scrollbar-y-rail.hover,
    .ps-container .ps-scrollbar-y-rail.in-scrolling,
    .ps-container:hover .ps-scrollbar-x-rail,
    .ps-container.hover .ps-scrollbar-x-rail,
    .ps-container .ps-scrollbar-x-rail:hover,
    .ps-container .ps-scrollbar-x-rail.hover,
    .ps-container .ps-scrollbar-x-rail.in-scrolling {
        background-color: transparent !important;
        opacity: 1 !important;
    }
}