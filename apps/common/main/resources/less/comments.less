#comments-box {
    display: table;
    position: relative;
    border-collapse: collapse;

    > div {
        display: table-row;
    }

    #comments-header {
        position: absolute;
        height: 45px;
        left: 0;
        top: 0;
        right: 0;
        padding: 12px;
        overflow: hidden;
        border-bottom: @scaled-one-px-value-ie solid @border-toolbar-ie;
        border-bottom: @scaled-one-px-value solid @border-toolbar;

        label {
            font-size: 12px;
            .font-weight-bold();
            margin-top: 2px;
        }
    }

    .messages-ct {
        position: absolute;
        overflow: hidden;
        left: 0;
        right: 0;
        bottom: 45px;
        height: 300px;
        padding-top: 45px;
        border-bottom: @scaled-one-px-value-ie solid @border-toolbar-ie;
        border-bottom: @scaled-one-px-value solid @border-toolbar;

        &.stretch {
            border-bottom: none;
        }

        .ps-scrollbar-y-rail {
            margin-top: 5px;
            margin-bottom: 5px;
            right: 4px !important;
        }

        .dataview-ct.inner {
            .empty-text {
                text-align: center;
                height: 100%;
                width: 100%;
                color: @text-tertiary-ie;
                color: @text-tertiary;
                tr {
                    vertical-align: top;
                    td {
                        padding-top: 18px;
                    }
                }
            }
        }
    }

    .add-link-ct {
        height: 45px;
        text-align: center;

        label {
            color: @text-normal-ie;
            color: @text-normal;
            font-size: 12px;
            line-height: normal;
            border-bottom: @scaled-one-px-value-ie dotted @text-normal-ie;
            border-bottom: @scaled-one-px-value dotted @text-normal;
            border-radius: 0;
            padding-top: 12px;
            outline: none;
            height: 29px;
            cursor: pointer;
        }
    }

    .new-comment-ct {
        height: 110px;
        display: none;

        .inner-ct{
            padding: 7px 20px 37px;
            height: 100%;
            margin-bottom: -32px;
        }

        textarea {
            font-size: 12px;
            width: 100%;
            resize: none;
            margin-bottom: 5px;
            border: @scaled-one-px-value-ie solid @border-regular-control-ie;
            border: @scaled-one-px-value solid @border-regular-control;
            height: 100%;
            &:focus {
                border-color: @border-control-focus-ie;
                border-color: @border-control-focus;
            }
        }
    }

    .btn {
        &.add {
            margin-left: 20px;
        }
        &.cancel {
            margin-left: 7px;
        }
    }
}

.dataview-ct {
    width: 100%;
    height: 100%;
    font-size: 12px;
    line-height: normal;
    position: relative;
    overflow: hidden;
    color: @border-preview-select-ie;
    color: @border-preview-select;

    textarea {
        width: 100%;
        height: 55px;
        resize: none;
        margin-bottom: 5px;
        border: @scaled-one-px-value-ie solid @border-regular-control-ie;
        border: @scaled-one-px-value solid @border-regular-control;
        word-break: break-all;
        line-height: 15px;
        color: @text-normal-ie;
        color: @text-normal;
        &:focus {
            border-color: @border-control-focus-ie;
            border-color: @border-control-focus;
        }
    }

    .btn-fix {
        margin-left: -3px;
    }

    .textarea-fix {
        margin-left: -3px;
        margin-top: -1px;
    }

    .separator-cmt {
        border-bottom: @scaled-one-px-value-ie solid @border-toolbar-ie;
        border-bottom: @scaled-one-px-value solid @border-toolbar;
        margin: 20px 0px 0px 0px;
    }

    .user-comment-item {
        position: relative;
        padding: 0px 20px 10px 20px;
    }

    .user-name {
        color: @text-normal-ie;
        color: @text-normal;
        font-size: 12px;
        .font-weight-bold();
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 10px 65px 0 0px;
        height: 26px;
        cursor: default;
    }

    .color {
        width: 12px;
        height: 12px;
        border: @scaled-one-px-value-ie solid @border-toolbar-ie;
        border: @scaled-one-px-value solid @border-toolbar;
        margin: 0 5px 3px 0;
        vertical-align: middle;
    }

    .user-name-colored {
        padding: 10px 0px 0 0px;
        cursor: default;
        max-width: 175px;
        span {
            display: inline-block;
            height: 20px;
            max-width: 175px;
            background-color: #ee3525;
            padding: 3px 10px;
            color: #ffffff;
            font: 11px arial;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .user-date {
        font-size: 11px;
        white-space: nowrap;
        padding: 0;
        height: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: default;
    }

    .user-quote {
        font-style: italic;
        margin-top: 10px;
        padding: 0px 5px 5px 5px;
        border-left: @scaled-one-px-value-ie solid #939393;
        border-left: @scaled-one-px-value solid #939393;
        word-break: break-all;
        white-space: pre-wrap;
        cursor: pointer;
    }

    .user-message {
        color: @text-normal-ie;
        color: @text-normal;
        padding: 9px 0px 0 0px;
        white-space: pre-wrap;
        word-wrap: break-word;
        cursor: pointer;

        &.limit-height {
            max-height: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: default;
        }
        
        &.user-select {
            cursor: text;
        }
        &.user-select::selection {
            background: #3494fb;
            color: white;
        }
    }

    .user-reply {
        color: @text-normal-ie;
        color: @text-normal;
        margin-top: 10px;
        white-space: pre-wrap;
        width: auto;
        border-bottom: @scaled-one-px-value-ie dotted @text-normal-ie;
        border-bottom: @scaled-one-px-value dotted @text-normal;
        height: 16px;
        cursor: pointer;
    }

    .msg-reply {
        max-height:150px;
        word-break: break-word !important;
    }

    .edit-ct {
        position: absolute;
        right: 0;
        top: 0;
        margin: 11px 21px 10px 10px;
        height: 20px;
    }

    .btns-reply-ct {
        position: absolute;
        right: 0;
        top: 0;
        height: 30px;
        margin-top: 11px;
    }

    .reply-item-ct {
        position: relative;
        padding-bottom: 10px;
        padding-left: 20px;
    }

    div[class^=btn-] {
        float: left;
        cursor: pointer;
    }

    .btn-edit,.btn-delete, .btn-resolve, .icon-resolve, .btn-resolve-check, .btn-accept, .btn-reject, .btn-goto {
        width: 16px;
        height: 16px;
        margin: 0 0 0 5px;
        background-color: transparent;
    }

    .icon-resolve {
        float: left;
    }

    .btn-edit {
        background-position: -2px -232px;
    }

    .btn-delete {
        background-position: -22px -232px;
    }

    
    .tool {
        float: right;
        width: 16px;
        height: 16px;
        cursor: pointer;
        overflow: hidden;
        padding: 0px;
        margin-right: 2px;

        &.btn-reject {
            position: relative;

            &.disabled {
                cursor: default;
            }

            &:before, &:after {
                content: ' ';
                position: absolute;
                left: 8px;
                top: 2px;
                height: 12px;
                width: 2px;
                background-color: @icon-normal-ie;
                background-color: @icon-normal;
            }

            &:before {
                transform: rotate(45deg);
            }

            &:after {
                transform: rotate(-45deg);
            }
        }

        &.help {
            width: 20px;
            margin-right:0;
            line-height: 14px;
            font-size: 14px;
            .font-weight-bold();
            color: @text-normal-ie;
            color: @text-normal;
            opacity: 0.7;

            &:hover {
                opacity: 1;
            }

            &.disabled {
                opacity: @component-disabled-opacity-ie;
                opacity: @component-disabled-opacity;
                cursor: default;
            }
        }
    }

    .btn-resolve, .btn-accept, .icon-resolve {
        position: relative;

        &:after {
            content: '';
            position: absolute;
            border: solid @text-normal-ie;
            border: solid @text-normal;
            border-width: 0 2px 2px 0;
            transform: rotate(40deg);
            width: 7px;
            height: 12px;
            left: 6px;
            top: 0px;
        }

        &.comment-resolved, &.i-comment-resolved {
            &:after {
                border-color: @icon-success-ie;
                border-color: @icon-success;
            }
        }
    }

    .btn-resolve-check {
        background-position: -42px -234px;
    }

    .btn-goto {
        background-position: -22px -272px;
    }

    .inner-edit-ct {
        padding: 7px 0px 0px 0px;

        .btn-inner-close {
            margin-left: 7px;
        }
    }

    .reply-ct {
        padding: 10px 0px 0px 0px;

        .btn-close {
            margin-left: 7px;
        }
    }
    .reply-inner-ct {
        padding: 0px 20px 0px 30px;
    }

    .reply-arrow {
        background-color: transparent;
        background-position: -60px -232px;
        width: 16px;
        height: 16px;
        margin-top: 10px;
        position: absolute;
    }

    .lock-area {
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        background-color: #F4F4F4;
        margin-top: 2px;
        margin-bottom: -2px;
        opacity: .5;
        cursor: pointer;
    }

    .lock-author {
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        max-width: 150px;
        height: 20px;
        line-height: @line-height-base;
        background-color: #EE3525;
        margin: 10px 18px;
        padding: 2px 10px;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 100px;
        text-align: center;
        cursor: pointer;
        font-size: 11px;
        font-family: @font-family-base;
    }

    .resolved {
        width: 18px;
        height: 16px;
        float: left;
        margin: 5px 5px 0 0;
        background-color: transparent;
        background-position: -40px -232px;
    }
}

.comments-caret {
    width: 7px;
    height: 7px;
    border: 0;
    background-position: @arrow-small-offset-x @arrow-small-offset-y;
    margin: 10px 8px 0 -2px;
    display: inline-block;
    vertical-align: middle;
}

// POPOVER

.comments-popover {
    width:100%;
    position: relative;
    overflow-y: hidden;
    //margin-bottom: 5px;

    .dataview-ct.inner {
        overflow: visible;
    }
}

.comments-arrow {
    position: absolute;
    overflow: hidden;

    &:after {
        content: '';
        position: absolute;
        top: 5px;
        left: 2px;
        width: 15px;
        height: 15px;
        background-color: @background-normal-ie;
        background-color: @background-normal;
        -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        -webkit-transform: rotate(45deg);
        -o-transform: rotate(45deg);
        transform: rotate(45deg);
        border: solid @scaled-one-px-value-ie @border-toolbar-ie;
        border: solid @scaled-one-px-value @border-toolbar;
    }

    &.left {
        left: -10px;
        top: 20px;
        width: 10px;
        height: 30px;
    }

    &.right {
        left: 100%;
        top: 20px;
        width: 10px;
        height: 30px;

        &:after {
            left: -8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);

            body.safari &,
            body.chrome & {
                box-shadow: none;
            }
        }
    }

    &.top {
        left: 20px;
        top: -10px;
        height: 10px;
        width: 30px;

        &:after {
            top: 2px;
            left: 5px;
        }
    }

    &.bottom {
        left: 20px;
        top: auto;
        bottom: -10px;
        height: 10px;
        width: 30px;

        &:after {
            top: -7px;
            left: 5px;
        }
    }
}
