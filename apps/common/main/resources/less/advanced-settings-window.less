.advanced-settings-dlg {
    .body {
        height: auto;
        top: 34px;

        & > div {
            width: 100%;
        }

        .box > div {
            display: inline-block;
            height: 100%;
        }

        .menu-panel {
            width: 160px;
            padding-top: 2px;

            .btn-category {
                text-align: left;
                padding: 9px 2px 9px 12px;
                line-height: normal;
                height: auto;
                white-space: normal;
            }
        }

        .content-panel {
            vertical-align: top;
            padding: 5px 15px 0 10px;

            .inner-content {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    }

    .footer {
        padding: 15px 15px 0;

        &.center {
            text-align: center;
        }

        &.right {
            text-align: right;
        }

        &.justify {
            padding-left: 30px;
            padding-right: 30px;
        }
    }
}

.combo-arrow-style {
    .form-control {
        cursor: pointer;

        .image {
            width: 100%;
            height: 100%;
            display: block;
            background-color: transparent;
            margin: 0 0 0 -3px;
        }
    }
}

.img-arrows {
    .background-ximage-v2('right-panels/Begin-EndStyle.png', 480px);
    -webkit-filter: @img-border-type-filter;
    filter: @img-border-type-filter;
}

.item-arrow {
    width:44px;
    height:20px;
}
