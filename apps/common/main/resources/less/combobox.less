
.combobox {
    display: block;

    &.input-group-sm .btn { .input-sm(); }
    &.input-group-lg .btn { .input-lg(); }
    &.input-group-nr > .form-control,
    &.input-group-nr > .btn {
        height: @input-height-base;
        padding:  @padding-base-vertical @padding-base-horizontal;
        font-size:  @font-size-base;
        line-height: @line-height-base;

        -o-transition: none;
        -webkit-transition: none;
        -moz-transition: none;
        transition: none;

        border-top-right-radius: 2px !important;
        border-bottom-right-radius: 2px !important;
    }

    &.input-group-nr > .form-control {
        padding-right: 7px + 2 * @padding-base-horizontal;
    }

    .form-control {
        border-right: 0;
        position: static;
        z-index: auto;
        float: none;

        .image {
            -webkit-filter: @img-border-type-filter;
            filter: @img-border-type-filter;
        }

        .text {
            line-height: 20px;
            padding-left: 2px;
        }
    }

    .btn {
        border-left: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-color: @border-regular-control-ie;
        border-color: @border-regular-control;
        background-color: transparent;
        margin-left: -1px;
        position: absolute;
        top: 0;
        right: 0;
    }

    .btn-default:not(.disabled),
    &.open .dropdown-toggle.btn-default {
        background-color: @background-normal-ie;
        background-color: @background-normal;
        border-color: @border-regular-control-ie;
        border-color: @border-regular-control;
        padding-right: 5px;
    }

    .btn-default.disabled {
        padding-right: 5px;
        border-top-color: transparent;
        border-bottom-color: transparent;

        &:hover, &:focus {
            background-color: transparent;
            border-right-color: @border-regular-control-ie;
            border-right-color: @border-regular-control;
        }
    }

    &.input-group-nr.open {
        & > .form-control,
        & > .btn {
            .caret {
                transform: rotate(45deg);
            }
        }

        &:not(.no-highlighted) > .form-control,
        &:not(.no-highlighted) > .btn {
            border-color: @border-control-focus-ie;
            border-color: @border-control-focus;
        }
    }

    &.input-group-nr:not(.no-highlighted) > .form-control {
        &:focus,
        &:focus ~ button.dropdown-toggle {
            border-color: @border-control-focus-ie;
            border-color: @border-control-focus;
        }
    }
    &.input-group-nr.no-highlighted > .form-control {
        &:focus,
        &:focus ~ button.dropdown-toggle {
            border-color: @border-regular-control-ie;
            border-color: @border-regular-control;
        }
    }

    &.input-group-nr {
        .btn {
            &:active {
                .caret {
                    border-color: @icon-normal-ie;
                    border-color: @icon-normal;
                }
            }
        }
    }

    li {
        a {
            white-space: pre;
        }

        &.selected {
            background-color: @highlight-button-pressed-ie;
            background-color: @highlight-button-pressed;

            a {
                color: @text-normal-pressed-ie;
                color: @text-normal-pressed;

                &:hover,
                &.hover,
                &:focus,
                &.focus {
                    background-color: @highlight-button-pressed-ie;
                    background-color: @highlight-button-pressed;
                }
            }
        }
    }

    // Font combobox
    // ------------------------
    li {
        img {
            -webkit-filter: @img-border-type-filter;
            filter: @img-border-type-filter;
        }
        .font-item canvas {
            -webkit-filter: @img-border-type-filter;
            filter: @img-border-type-filter;
        }

        &.selected {
            img {
                -webkit-filter: @img-border-type-filter-selected;
                filter: @img-border-type-filter-selected;
            }
            .font-item canvas {
                -webkit-filter: @img-border-type-filter-selected;
                filter: @img-border-type-filter-selected;
            }
        }

        &.divider:first-child {
            display: none;
        }
    }

    .font-item {
        vertical-align:middle;
        margin: 0 0 0 -10px;
        padding-top: 0;
        padding-bottom: 0;
    }

    .dropdown-menu.menu-absolute {
        position: fixed;
    }

    .dropdown-menu.show-top {
        top: auto;
        bottom: 100%;
    }

    .masked & {
        &.disabled {
            .form-control, .btn-default {
                opacity: 1;
            }
        }
    }
}

.open > .combobox.combo-dataview-menu {
    &.input-group-nr:not(.no-highlighted) {
        & > .form-control,
        & > .btn {
            border-color: @border-control-focus-ie;
            border-color: @border-control-focus;
        }
    }

    &.input-group-nr {
        & > .form-control,
        & > .btn {
            .caret {
                transform: rotate(45deg);
            }
        }
    }
}
