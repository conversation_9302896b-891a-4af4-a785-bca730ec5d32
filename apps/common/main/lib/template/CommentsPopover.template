<% if (!hide) { %>
<div id="<%=id%>" class="user-comment-item">

    <!-- comment block -->

    <div class="user-name">
        <div class="color" style="display: inline-block; background-color: <% if (usercolor!==null) { %><%=usercolor%><% } else { %> #cfcfcf <% } %>; " ></div><%= scope.getEncodedName(parsedName) %>
    </div>
    <div class="user-date"><%=date%></div>
    <% if (!editTextInPopover || (hint && !fullInfoInHint) || scope.viewmode) { %>
        <div oo_editor_input="true" tabindex="-1" class="user-message user-select"><%=scope.pickLink(comment)%></div>
    <% } else { %>
        <div class="inner-edit-ct">
            <textarea class="msg-reply user-select" maxlength="maxCommLength" spellcheck="false" <% if (!!dummy) { %> placeholder="textMentionComment"<% } %>><%=comment%></textarea>
            <% if (hideAddReply) { %>
                <button class="btn normal dlg-btn primary btn-inner-edit" id="id-comments-change-popover">textAdd</button>
            <% } else { %>
                <button class="btn normal dlg-btn primary btn-inner-edit" id="id-comments-change-popover">textEdit</button>
            <% } %>
            <button class="btn normal dlg-btn btn-inner-close">textCancel</button>
        </div>
    <% } %>

    <!-- replys elements -->

    <% var add_arrow = true; %>
    <% if (replys.length) { %>
        <% _.each(replys, function (item) { %>
            <% if (!item.get("hide")) { %>
            <% if (add_arrow) { add_arrow = false; %>
                <div class="reply-arrow img-commonctrl"></div>
            <% } %>
            <div class="reply-item-ct">
                <div class="user-name">
                    <div class="color" style="display: inline-block; background-color: <% if (item.get("usercolor")!==null) { %><%=item.get("usercolor")%><% } else { %> #cfcfcf <% } %>; " ></div><%= scope.getEncodedName(item.get("parsedName")) %>
                </div>
                <div class="user-date"><%=item.get("date")%></div>
                <% if (!item.get("editTextInPopover")) { %>
                    <div oo_editor_input="true" tabindex="-1" class="user-message user-select"><%=scope.pickLink(item.get("reply"))%></div>
                    <% if ((fullInfoInHint || !hint) && !scope.viewmode) { %>
                        <div class="btns-reply-ct">
                            <% if (item.get("editable")) { %>
                                <div class="btn-edit img-commonctrl" data-value="<%=item.get("id")%>"></div>
                            <%}%>
                            <% if (item.get("removable")) { %>
                               <div class="btn-delete img-commonctrl" data-value="<%=item.get("id")%>"></div>
                            <%}%>
                        </div>
                    <%}%>
                <% } else { %>
                    <div class="inner-edit-ct">
                        <textarea class="msg-reply textarea-fix user-select" maxlength="maxCommLength" spellcheck="false"><%=item.get("reply")%></textarea>
                        <button class="btn normal dlg-btn primary btn-inner-edit btn-fix" id="id-comments-change-popover">textEdit</button>
                        <button class="btn normal dlg-btn btn-inner-close">textClose</button>
                    </div>
                <% } %>
            </div>
            <% } %>
        <% }); %>
    <% } %>

    <!-- add reply button -->

    <% if (!showReplyInPopover && !hideAddReply && (fullInfoInHint || !hint) && !scope.viewmode) { %>
        <% if (replys.length && !add_arrow) { %>
            <label class="user-reply" style="margin-left: 20px; margin-top: 5px;" role="presentation" tabindex="-1">textAddReply</label>
        <% } else { %>
            <label class="user-reply" role="presentation" tabindex="-1">textAddReply</label>
        <% } %>
    <% } %>

    <!-- edit buttons -->

    <% if (!editTextInPopover && !lock) { %>
        <div class="edit-ct">
            <% if ((fullInfoInHint || !hint) && !scope.viewmode) { %>
                <% if (editable) { %>
                    <div class="btn-edit img-commonctrl"></div>
                <% } %>
                <% if (removable) { %>
                    <div class="btn-delete img-commonctrl"></div>
                <% } %>
            <% } %>
            <% if (editable && (fullInfoInHint || !hint) && !scope.viewmode) { %>
                <div class="btn-resolve <% if (resolved) print('comment-resolved') %>" data-toggle="tooltip"></div>
            <% } else if ((fullInfoInHint || !hint) && (!editable || scope.viewmode) && resolved) { %>
                <div class="icon-resolve i-comment-resolved" data-toggle="tooltip"></div>
            <% } %>
        </div>
    <% } %>

    <!-- reply -->

    <% if (showReplyInPopover) { %>
        <div class="reply-ct">
            <textarea class="msg-reply user-select" placeholder="textMentionReply" maxlength="maxCommLength" spellcheck="false"></textarea>
            <button class="btn normal dlg-btn primary btn-reply" id="id-comments-change-popover">textReply</button>
            <button class="btn normal dlg-btn btn-close">textClose</button>
        </div>
    <% } %>

    <!-- locked user -->

    <% if (lock) { %>
        <div class="lock-area" style="cursor: default;"></div>
        <div class="lock-author" style="cursor: default;"><%=lockuserid%></div>
    <% } %>

</div>
<% } %>