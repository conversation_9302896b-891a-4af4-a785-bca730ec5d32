/*
 *
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

/**
 *  extendes.js
 *
 *  Created by Alexander Yuzhin on 10/14/16
 *  Copyright (c) 2018 Ascensio System SIA. All rights reserved.
 *
 */

define(
    ['jquery','underscore','framework7'],
    function () {
        //Extend String
        if (!String.prototype.format) {
            String.prototype.format = function() {
                var args = arguments;
                return this.replace(/{(\d+)}/g, function(match, number) {
                    return typeof args[number] != 'undefined' ? args[number] : match;
                });
            };
        }

        //Extend jQuery functions
        jQuery.fn.extend( {
            single: function(types, selector, data, fn) {
                return this.off(types).on(types, selector, data, fn);
            }
        });

        //Extend Dom7 functions
        var methods = ['addClass', 'toggleClass', 'removeClass'];

        _.each(methods, function (method, index) {
            var originalMethod = Dom7.fn[method];

            Dom7.fn[method] = function(className) {
                var result = originalMethod.apply(this, arguments);
                this.trigger(method, className);
                return result;
            };
        });

        //Extend Underscope functions
        _.buffered = function(func, buffer, scope, args) {
            var timerId;

            return function() {
                var callArgs = args || Array.prototype.slice.call(arguments, 0),
                    me = scope || this;

                if (timerId) {
                    clearTimeout(timerId);
                }

                timerId = setTimeout(function(){
                    func.apply(me, callArgs);
                }, buffer);
            };
        };
    }
);
