<!-- Root view -->
<div id="collaboration-root-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="center sliding"><%= scope.textCollaboration %></div>
            <div class="right sliding close-collaboration"><% if (phone) { %><a href="#" class="link icon-only close-picker"><i class="icon icon-expand-down"></i></a><% } %></div>
        </div>
    </div>
    <div class="pages">
        <div class="page" data-page="collaboration-root-view">
            <div class="page-content">
                <div class="list-block">
                    <ul>
                        <li id="item-edit-users">
                            <a id="list-edit-users" class="item-link" data-page="#edit-users-view">
                                <div class="item-content">
                                    <div class="item-media">
                                        <i class="icon icon-users"></i>
                                    </div>
                                    <div class="item-inner">
                                        <div class="item-title"><%= scope.textEditUsers %></div>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li id="item-comments">
                            <a id="comments-settings" class="item-link" data-page="#comments-view">
                                <div class="item-content">
                                    <div class="item-media">
                                        <i class="icon icon-insert-comment"></i>
                                    </div>
                                    <div class="item-inner">
                                        <div class="item-title"><%= scope.textСomments %></div>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <% if (editor === 'DE') { %>
                        <li>
                            <a id="reviewing-settings" class="item-link" data-page="#reviewing-settings-view">
                                <div class="item-content">
                                    <div class="item-media">
                                        <i class="icon icon-review"></i>
                                    </div>
                                    <div class="item-inner">
                                        <div class="item-title"><%= scope.textReviewing %></div>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <% } %>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Editable Users view -->
<div id="edit-users-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textEditUsers %></div>
            <div class="right sliding"><% if (phone) { %><a href="#" class="link icon-only close-picker"><i class="icon icon-expand-down"></i></a><% } %></div>
        </div>
    </div>
    <div class="pages">
        <div class="page page-change" data-page="edit-users-view">
            <div class="page-content">
                <div id="user-list" class="list-block">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reviewing Settings view -->
<div id="reviewing-settings-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textReviewing %></div>
            <div class="right sliding"><% if (phone) { %><a href="#" class="link icon-only close-picker"><i class="icon icon-expand-down"></i></a><% } %></div>
        </div>
    </div>
    <div class="pages">
        <div class="page" data-page="reviewing-settings-view">
            <div class="page-content">
                <div class="list-block">
                    <ul>
                        <li>
                            <div id="settings-review" class="item-content">
                                <div class="item-inner">
                                    <div class="item-title"><%= scope.textReview %></div>
                                    <div class="item-after">
                                        <label class="label-switch">
                                            <input type="checkbox">
                                            <div class="checkbox"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a id="display-mode-settings" class="item-link" data-page="#display-mode-view">
                                <div class="item-content">
                                    <div class="item-inner">
                                        <div class="item-title"><%= scope.textDisplayMode %></div>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="list-block">
                    <ul>
                        <li>
                            <a id="change-settings" class="item-link" data-page="#change-view">
                                <div class="item-content">
                                    <div class="item-media">
                                        <i class="icon icon-review-changes"></i>
                                    </div>
                                    <div class="item-inner">
                                        <div class="item-title"><%= scope.textChange %></div>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li>
                            <div id="settings-accept-all" class="item-content">
                                <div class="item-media">
                                    <i class="icon icon-accept-changes"></i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title"><%= scope.textAcceptAllChanges %></div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div id="settings-reject-all" class="item-content">
                                <div class="item-media">
                                    <i class="icon icon-reject-changes"></i>
                                </div>
                                <div class="item-inner">
                                    <div class="item-title"><%= scope.textRejectAllChanges %></div>
                                </div>
                            </div>
                        </li>

                    </ul>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Display Mode view -->
<div id="display-mode-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textDisplayMode %></div>
            <div class="right sliding"><% if (phone) { %><a href="#" class="link icon-only close-picker"><i class="icon icon-expand-down"></i></a><% } %></div>
        </div>
    </div>
    <div class="pages">
        <div class="page page-display-mode" data-page="display-mode-view">
            <div class="page-content">
                <div class="list-block">
                    <ul>
                        <li class="media-item">
                            <label class="label-radio item-content">
                                <input type="radio" name="doc-orientation" value="markup">
                                <% if (android) { %><div class="item-media"><i class="icon icon-form-radio"></i></div><% } %>
                                <div class="item-inner">
                                    <div class="item-title-row">
                                        <div class="item-title"><%= scope.textMarkup %></div>
                                    </div>
                                    <div class="item-subtitle"><%= scope.textAllChangesEditing %></div>
                                </div>
                            </label>
                        </li>
                        <li class="media-item">
                            <label class="label-radio item-content">
                                <input type="radio" name="doc-orientation" value="final">
                                <% if (android) { %><div class="item-media"><i class="icon icon-form-radio"></i></div><% } %>
                                <div class="item-inner">
                                    <div class="item-title-row">
                                        <div class="item-title"><%= scope.textFinal %></div>
                                    </div>
                                    <div class="item-subtitle"><%= scope.textAllChangesAcceptedPreview %></div>
                                </div>
                            </label>
                        </li>
                        <li class="media-item">
                            <label class="label-radio item-content">
                                <input type="radio" name="doc-orientation" value="original">
                                <% if (android) { %><div class="item-media"><i class="icon icon-form-radio"></i></div><% } %>
                                <div class="item-inner">
                                    <div class="item-title-row">
                                        <div class="item-title"><%= scope.textOriginal %></div>
                                    </div>
                                    <div class="item-subtitle"><%= scope.textAllChangesRejectedPreview %></div>
                                </div>
                            </label>
                        </li>
                    </ul>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Change view -->
<div id="change-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textChange %></div>
            <div class="right sliding"><% if (phone) { %><a href="#" class="link icon-only close-picker"><i class="icon icon-expand-down"></i></a><% } %></div>
        </div>
    </div>
    <div class="pages">
        <div class="page page-change" data-page="change-view">
            <div class="page-content">
                <div class="content-block block-btn">
                <span class="change-buttons">
                <span class="accept-reject">
                <a href="#" id="btn-accept-change" class="link icon-only"><%= scope.textAccept %></a>
                <a href="#" id="btn-reject-change" class="link icon-only"><%= scope.textReject %></a>
                </span>
                <a href="#" id="btn-goto-change" class="link icon-only" style="display: none;"><i class="icon icon-goto"></i></a>
                </span>
                <span class="next-prev">
                <a href="#" id="btn-prev-change" class="link icon-only"><i class="icon icon-prev-change"></i></a>
                <a href="#" id="btn-next-change" class="link icon-only"><i class="icon icon-next-change"></i></a>
                </span>
                </div>
                <div id="current-change" class="content-block block-description">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Comments view -->
<div id="comments-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textСomments %></div>
            <div class="right sliding"><% if (phone) { %><a href="#" class="link icon-only close-picker"><i class="icon icon-expand-down"></i></a><% } %></div>
        </div>
    </div>
    <div class="pages">
        <div class="page page-comments" data-page="comments-view">
            <div class="page-content">
                <div class="list-block">
                    <ul id="comments-list"></ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Comments edit-view -->
<div id="comments-edit-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textEditСomment %></div>
            <div class="right sliding"><a id="edit-comment"><% if (android) { %><i class="icon icon-done-comment-white"></i><% } else { %><%= scope.textDone %><% } %></a></div>
        </div>
    </div>
    <div class="pages">
        <div class="page page-edit-comment" data-page="comments-edit-view">
            <div class="page-content">

            </div>
        </div>
    </div>
</div>

<!-- Comments add-reply-view -->
<div id="comments-add-reply-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textAddReply %></div>
            <div class="right sliding"><a id="add-new-reply"><% if (android) { %><i class="icon icon-done-comment-white"></i><% } else { %><%= scope.textDone %><% } %></a></div>
        </div>
    </div>
    <div class="pages">
        <div class="page page-add-reply" data-page="comments-add-reply-view">
            <div class="page-content">

            </div>
        </div>
    </div>
</div>

<!-- Comments edit-reply-view -->
<div id="comments-edit-reply-view">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="left sliding"><a href="#" class="back link"> <i class="icon icon-back"></i><% if (!android) { %><span><%= scope.textBack %></span><% } %></a></div>
            <div class="center sliding"><%= scope.textEditReply %></div>
            <div class="right sliding"><a id="edit-reply"><% if (android) { %><i class="icon icon-done-comment-white"></i><% } else { %><%= scope.textDone %><% } %></a></div>
        </div>
    </div>
    <div class="pages">
        <div class="page page-edit-reply" data-page="comments-edit-reply-view">
            <div class="page-content">

            </div>
        </div>
    </div>
</div>
