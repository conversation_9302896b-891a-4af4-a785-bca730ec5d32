.device-ios {
  .wrap-comment {
    height: calc(100% - 60px);
  }
  .add-comment-popup, .add-reply-popup, .add-comment-dialog, .add-reply-dialog {
    .wrap-textarea {
      height: calc(100% - 34px);
    }
  }
  .edit-comment-popup, .edit-reply-popup, .edit-comment-dialog, .edit-reply-dialog {
    .wrap-textarea {
      height: calc(100% - 52px);
    }
  }
  .comment-list {
    .reply-item {
      .item-inner:after {
        content: none !important;
      }
      &:before {
        content: '';
        position: absolute;
        left: auto;
        bottom: 0;
        right: auto;
        top: 0;
        height: 1px;
        width: 100%;
        background-color: var(--f7-list-item-border-color);
        display: block;
        z-index: 15;
        -webkit-transform-origin: 50% 100%;
        transform-origin: 50% 100%;
      }
    }
  }
}