// @common-image-path - defined in webpack config

.device-ios {
    .theme-type-dark {
        i.icon {
            &.icon-logo {
                background: ~"url(@{common-image-path}/header/logo-android.svg)" no-repeat center;
            }
        }
    }
    i.icon {
        &.icon_mask {
            background-color: white;
        }
        &.icon-logo {
            width: 100px;
            height: 14px;
            background: ~"url(@{common-image-path}/header/logo-ios.svg)" no-repeat center;
        }
        &.icon-prev {
            width: 22px;
            height: 22px;
            .encoded-svg-mask('<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 22 22" fill="@{brandColor}"><g><path d="M16,20.5L15,21.5L4.5,11l0,0l0,0L15,0.5L16,1.5L6.6,11L16,20.5z"/></g></svg>');
            &:after {
              display: none;
            }
        }
        &.icon-next {
            width: 22px;
            height: 22px;
            .encoded-svg-mask('<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 22 22" fill="@{brandColor}"><g><path d="M15.5,11L6,1.5l1.1-1.1L17.5,11l0,0l0,0L7.1,21.5L6,20.5L15.5,11z"/></g></svg>');
            &:after {
                display: none;
            }
        }
        &.icon-edit {
            width: 22px;
            height: 22px;
            .encoded-svg-mask('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 22 22" fill="@{brandColor}"><g><path d="M0,20h22v1H0V20z"/><polygon points="19.3,5.3 6.1,18.4 4.6,16.9 17.8,3.8 17.1,3.1 3.5,16.7 3,20 6.3,19.5 19.9,5.9"/><path d="M20.5,5.3L22,3.8c0,0-0.2-1.2-0.9-1.9C20.4,1.1,19.2,1,19.2,1l-1.5,1.5L20.5,5.3z"/></g></svg>');
        }
    }
}
