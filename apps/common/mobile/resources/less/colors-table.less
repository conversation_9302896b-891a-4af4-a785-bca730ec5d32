
:root {
    --brand-word: #446995;
    --brand-cell: #40865C;
    --brand-slide: #AA5252;
    --brand-primary: #3880BE;
    --brand-secondary: #ED7309;
    --brand-text-on-brand: #FFF;

    --background-primary: #FFF;
    --background-secondary: #FFF;
    --background-tertiary: #EFF0F5;
    --background-menu-divider: fade(#3C3C43, 15%);
    --background-button: #EFF0F5;

    --text-normal: #000000;
    --text-secondary: fade(#000, 60%);
    --text-tertiary: fade(#000, 40%);
    --text-link: #007AFF;
    --text-error: #FF3B30;

    --fill-black: #000;
    --fill-white: #FFF;

    --toolbar-background: #446995;
    --toolbar-icons: #FFF;
    --toolbar-segment: #446995;
    --toolbar-tab-normal:fade(#FFF, 50%);

    --component-disabled-opacity: .4;

    --active-opacity-word: fade(#446995, 30%);
    --active-opacity-slide: fade(#AA5252, 30%);
    --active-opacity-cell: fade(#40865C, 30%);

    --image-border-types-filter: none;

    // Canvas

    --canvas-background: #eee;
    --canvas-content-background: #fff;
    --canvas-page-border: #ccc;

    --canvas-ruler-background: #fff;
    --canvas-ruler-border: #cbcbcb;
    --canvas-ruler-margins-background: #d9d9d9;
    --canvas-ruler-mark: #555;
    --canvas-ruler-handle-border: #555;
    --canvas-ruler-handle-border-disabled: #aaa;

    --canvas-high-contrast: #000;
    --canvas-high-contrast-disabled: #666;

    --canvas-cell-border: fade(#000, 10%);
    --canvas-cell-title-hover: #dfdfdf;
    --canvas-cell-title-selected: #cfcfcf;
    --canvas-cell-title-border: #d8d8d8;
    --canvas-cell-title-border-hover: #c9c9c9;
    --canvas-cell-title-border-selected: #bbb;

    --canvas-dark-cell-title: #444;
    --canvas-dark-cell-title-hover: #666 ;
    --canvas-dark-cell-title-selected: #111;
    --canvas-dark-cell-title-border: #3d3d3d;
    --canvas-dark-cell-title-border-hover: #5c5c5c;
    --canvas-dark-cell-title-border-selected: #0f0f0f;
    --canvas-dark-content-background: #3a3a3a;
    --canvas-dark-page-border: #2a2a2a;

    --canvas-scroll-thumb: #f7f7f7;
    --canvas-scroll-thumb-hover: #c0c0c0;
    --canvas-scroll-thumb-pressed: #adadad;
    --canvas-scroll-thumb-border: #cbcbcb;
    --canvas-scroll-thumb-border-hover: #cbcbcb;
    --canvas-scroll-thumb-border-pressed: #adadad;
    --canvas-scroll-arrow: #adadad;
    --canvas-scroll-arrow-hover: #f7f7f7;
    --canvas-scroll-arrow-pressed: #f7f7f7;
    --canvas-scroll-thumb-target: #c0c0c0;
    --canvas-scroll-thumb-target-hover: #f7f7f7;
    --canvas-scroll-thumb-target-pressed: #f7f7f7;
}

@brand-word: var(--brand-word);
@brand-cell: var(--brand-cell);
@brand-slide: var(--brand-slide);
@brand-primary: var(--brand-primary);
@brand-secondary: var(--brand-secondary);
@brand-text-on-brand: var(--brand-text-on-brand);

@background-primary: var(--background-primary);
@background-secondary: var(--background-secondary);
@background-tertiary: var(--background-tertiary);
@background-menu-divider: var(--background-menu-divider);
@background-button: var(--background-button);

@text-normal: var(--text-normal);
@text-secondary: var(--text-secondary);
@text-tertiary: var(--text-tertiary);
@text-link: var(--text-link);
@text-error: var(--text-error);

@fill-black: var(--fill-black);
@fill-white: var(--fill-white);

@toolbar-icons: var(--toolbar-icons);
@toolbar-segment: var(--toolbar-segment);
@toolbar-tab-normal: var(--toolbar-tab-normal);

@component-disabled-opacity: var(--component-disabled-opacity);
