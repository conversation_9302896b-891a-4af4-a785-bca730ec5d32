// @text-normal: #000;
// @common-image-path - defined in webpack config

.about {
    .page-content {
        text-align: center /*rtl:ignore*/;
        display: flex;
        flex-direction: column;
    }

    &_ios {
        padding-top: 120px;
        padding-bottom: 30px;
    }

    &_android {
        padding-top: 60px;
        padding-bottom: 16px;
    }

    &_tablet {
        padding-top: 12px;
        padding-bottom: 12px;
    } 

    &__text {
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
        color: @text-normal;
        margin: 0;
    }

    a {
        color: @brandColor;
    }

    .logo-block {
        &__elem {
            display: flex;
            justify-content: center;
            img {
                max-width: 100%;
                height: auto;
            }
        }
    }

    &__logo {
        width: 100%;
        height: 80px;
        background: ~"url(@{common-image-path}/about/logo-new.svg) no-repeat center";
    }

    &__editor {
        flex-grow: 1;
        margin-top: 20px;
    }

    &__contacts {
        text-align: center;
        padding-left: 72px;
        padding-right: 72px;
    }

    &__licensor, &__customer {
        text-align: center;
        margin-top: 12px;
    }
}

.theme-type-dark {
    .about__logo {
        background: ~"url(@{common-image-path}/about/logo-new-white.svg) no-repeat center";
    }
}