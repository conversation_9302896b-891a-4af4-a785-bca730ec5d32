// Data view

.dataview {
  .row {
    justify-content: space-around;
  }

  ul {
    padding: 0 10px;
    list-style: none;

    li {
      display: inline-block;
    }
  }

  .row.list:last-child li:not(.active):last-child::after {
    content: none;
  }

  .row.list:last-child li.active:last-child, .active {
    position: relative;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      width: 22px;
      height: 22px;
      right: -5px;
      left: auto;
      bottom: -5px;
      transform: none;
      .encoded-svg-mask('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 22 22" fill="none"><g><circle cx="11" cy="11" r="11"/><path fill="#fff" d="M11,21A10,10,0,1,1,21,11,10,10,0,0,1,11,21h0ZM17.4,7.32L17.06,7a0.48,0.48,0,0,0-.67,0l-7,6.84L6.95,11.24a0.51,0.51,0,0,0-.59.08L6,11.66a0.58,0.58,0,0,0,0,.65l3.19,3.35a0.38,0.38,0,0,0,.39,0L17.4,8a0.48,0.48,0,0,0,0-.67h0Z"/></g></svg>');
    }
  }
}