@import "../../../../common/main/resources/less/asc-mixins.less";

// Bootstrap overwrite
@import "../../../../common/main/resources/less/variables.less";
@import "../../../../common/main/resources/less/colors-table.less";
@import "../../../../common/main/resources/less/colors-table-classic.less";
@import "../../../../common/main/resources/less/colors-table-dark.less";
@import "../../../../common/main/resources/less/colors-table-dark-contrast.less";

// Core variables and mixins
//@import "../../../../../vendor/bootstrap/less/variables.less";

@icon-font-path: "../../../../../vendor/bootstrap/dist/fonts/";

@import "../../../../../vendor/bootstrap/less/mixins.less";

// Reset
@import "../../../../../vendor/bootstrap/less/normalize.less";
//@import "../../../../../vendor/bootstrap/less/print.less";

// Core CSS
@import "../../../../../vendor/bootstrap/less/scaffolding.less";
@import "../../../../../vendor/bootstrap/less/type.less";
//@import "code.less";
//@import "grid.less";
//@import "tables.less";
@import "../../../../../vendor/bootstrap/less/forms.less";
@import "../../../../../vendor/bootstrap/less/buttons.less";

// Components
@import "../../../../../vendor/bootstrap/less/component-animations.less";
//@import "../../../../../vendor/bootstrap/less/glyphicons.less";
//@import "dropdowns.less";
//@import "button-groups.less";
//@import "input-groups.less";
//@import "navs.less";
//@import "navbar.less";
//@import "breadcrumbs.less";
//@import "pagination.less";
//@import "pager.less";
//@import "../../../../../vendor/bootstrap/less/labels.less";
//@import "badges.less";
//@import "jumbotron.less";
//@import "thumbnails.less";
//@import "../../../../../vendor/bootstrap/less/alerts.less";
//@import "progress-bars.less";
//@import "media.less";
//@import "list-group.less";
//@import "panels.less";
//@import "wells.less";
//@import "../../../../../vendor/bootstrap/less/close.less";

// Components w/ JavaScript
//@import "../../../../../vendor/bootstrap/less/modals.less";
@import "../../../../../vendor/bootstrap/less/tooltip.less";
//@import "../../../../../vendor/bootstrap/less/popovers.less";
@import "../../../../../vendor/bootstrap/less/dropdowns.less";
@import "../../../../../vendor/bootstrap/less/button-groups.less";
@import "../../../../../vendor/bootstrap/less/input-groups.less";
//@import "carousel.less";

// Utility classes
@import "../../../../../vendor/bootstrap/less/utilities.less";
@import "../../../../../vendor/bootstrap/less/responsive-utilities.less";

@import "../../../../common/main/resources/less/buttons.less";
@import "../../../../common/main/resources/less/dropdown-menu.less";
@import "../../../../common/main/resources/less/dropdown-submenu.less";
@import "../../../../common/main/resources/less/separator.less";
@import "../../../../common/main/resources/less/input.less";
@import "../../../../common/main/resources/less/combobox.less";
@import "../../../../common/main/resources/less/window.less";
@import "../../../../common/main/resources/less/loadmask.less";
@import "../../../../common/main/resources/less/dataview.less";
@import "../../../../common/main/resources/less/tooltip.less";
@import "../../../../common/main/resources/less/scroller.less";
@import "../../../../common/main/resources/less/synchronize-tip.less";
@import "../../../../common/main/resources/less/common.less";
@import "../../../../common/main/resources/less/winxp_fix.less";
@import "../../../../common/main/resources/less/calendar.less";
@import "../../../../common/main/resources/less/spinner.less";
@import "../../../../common/main/resources/less/checkbox.less";
@import "../../../../common/main/resources/less/opendialog.less";
@import "../../../../common/main/resources/less/advanced-settings-window.less";
@import "../../../../common/main/resources/less/searchdialog.less";

@toolbarBorderColor:            @border-toolbar-ie;
@toolbarBorderColor:            @border-toolbar;
@toolbarTopColor:               @background-toolbar-ie;
@toolbarTopColor:               @background-toolbar;
@toolbarFontSize:               12px;

@iconSpriteCommonPath:          "../../../../common/forms/resources/img/glyphicons.png";
@icon-socnet-size: 40px;

@loadmask-zindex: 10000;

.embed-body {
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;

    font-size: @toolbarFontSize;
    overflow: hidden;

    color: @text-normal-ie;
    color: @text-normal;
}

// Document Viewer
// -------------------------
.viewer {
    position: absolute;
    margin: 0;
    padding: 0;
    left: 0;
    right: 0;

    &.top {
        top: 40px;
        bottom: 0;
    }

    &.bottom {
        top: 0;
        bottom: 46px;
    }
}

// Toolbar
// -------------------------
.toolbar {
    position: fixed;
    font-size: @toolbarFontSize;
    min-width: 340px;
    z-index: 100;
    background-color: @toolbarTopColor;

    display: flex;
    align-items: center;
    white-space: nowrap;

    &.top {
        top: 0;
        left: 0;
        width: 100%;
        height: 40px;

        .box-inner-shadow(0 -1px 0 @toolbarBorderColor);
    }

    .group {
        margin: 0;
        padding: 0;

        .item {
            input {
                font-size: inherit;
                padding: 0;
                text-align: right;
            }

            .text {
                cursor: default;
            }
        }

        &.left {
            left: 0;
            padding-left: 10px;

            .item {
                float: left;
            }

            & > div {
                display: inline-block;
                vertical-align: middle;
            }
        }

        &.right {
            right: 0;
            padding-right: 10px;

            .item {
                display: inline-block;
            }
        }

        &.center {
            display: flex;
            justify-content: center;
            flex-grow: 1;
            overflow: hidden;
            padding: 0 20px;

            #title-doc-name {
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .separator {
            height: 22px;
            margin: 0 9px;
            border-left: 1px solid @border-divider-ie;
            border-left: 1px solid @border-divider;
        }
    }

    .margin-right-small {
        margin-right: 8px;
    }
    .margin-right-large {
        margin-right: 12px;
    }
    .margin-left-small {
        margin-left: 8px;
    }
    .margin-left-large {
        margin-left: 12px;
    }
}

// Logo
// -------------------------
.brand-logo {
    display: block;
    width: 100px;
    height: 20px;
    background: data-uri('../../../../common/main/resources/img/header/dark-logo_s.svg') no-repeat;
}

.theme-type-dark {
    .brand-logo {
        background: data-uri('../../../../common/main/resources/img/header/header-logo_s.svg') no-repeat;
    }
}

.btn-text-default {
    width: auto;
    &.colored {
        padding: 0 16px;
        height: 28px;
        background-color: @background-accent-button-ie;
        background-color: @background-accent-button;
        border: 1px solid transparent;
        border-radius: 3px;
        color: @text-contrast-background-ie !important;
        color: @text-contrast-background !important;
        font-weight: 700;

        &:hover:not(.disabled),
        .over:not(.disabled) {
            background-color: @highlight-accent-button-hover-ie !important;
            background-color: @highlight-accent-button-hover !important;
        }

        &:active:not(.disabled),
        &.active:not(.disabled) {
            background-color: @highlight-accent-button-pressed-ie !important;
            background-color: @highlight-accent-button-pressed !important;
        }
    }
}

// Overlay control
// -------------------------
.overlay-controls {
    position: absolute;
    bottom: 55px;
    z-index: 10;
    left: 50%;

    ul {
        padding: 0;
        list-style-type: none;
        margin: 0 auto;

        li {
            display: inline-block;

            &:first-child {
                margin-right: 5px;
            }

            &:last-child {
                margin-left: 5px;
            }
        }
    }

    .overlay {
        width: 32px;
        height: 32px;

        display: inline-block;
        cursor: pointer;
        background-color: black;
        border: 5px solid black;
        border-radius: 50%;
        outline: none;
        opacity: 0.3;

        background-origin: content-box;
        padding: 1px 0 0 1px;

        .box-shadow(0 0 0 2px rgba(255,255,255,0.3));

        &:hover {
            opacity: .6;
        }

        &.active, &:active {
            opacity: .8;
        }
    }
}

// Error mask
// -------------------------
.errormask {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    border: none;
    background-color: #f4f4f4;
    z-index: 30002;

    .error-body {
        position: relative;
        top: 40%;
        width: 400px;
        margin: 0 auto;
        padding: 20px;
        background-color: #FFFFFF;
        border: 1px solid #C0C0C0;

        .title {
            font-weight: bold;
            font-size: 1.6em;
            padding-bottom: 10px;
        }
    }
}

// Modals
// -------------------------
.share-buttons {
    ul {
        width: 244px;
        //height: 25px;
        list-style-type: none;
        margin: 5px 0 0;
        overflow: hidden;

        li {
            display: inline-block;
            float: left;
            margin: 1px 5px 0 0;
            vertical-align: middle;

            &.share-mail {
                float: right;
                padding-right: 1px;
                margin: 0;

                a {
                    min-width: 64px;
                }

                .glyphicon {
                    margin-right: 4px;
                }
            }

            &.share-twitter {
                max-width: 93px;
            }
        }
    }
}

.size-manual {
    margin-bottom: 10px;

    .right {
        float: right;
    }

    .caption {
        margin-top: 2px;
        margin-right: 8px;
    }

    input {
        display: inline-block;
        font-size: 1em;
        padding: 0 4px;
        //border-radius: 0;
        margin: 0;
        margin-top: -1px;

        &.input-xs {
            width: 50px;
        }
    }
}

.socnet-btn(@index) {
    background-position: -@icon-socnet-size*@index 0;

    &:hover {
        background-position: -@icon-socnet-size*@index -@icon-socnet-size;
    }
}

.svg {
    background: data-uri('../../../../common/forms/resources/img/icon-social-sprite.svg');

    width: 40px;
    height: 40px;
    background-size: @icon-socnet-size*4 @icon-socnet-size*2;

    &.big-facebook:hover {
        background-position: 0 -@icon-socnet-size;
    }

    &.big-twitter { .socnet-btn(1); }
    &.big-gplus {   .socnet-btn(2); }
    &.big-email {   .socnet-btn(3); }
}

@icon-width: 20px;
@icon-height: 20px;

.theme-type-dark {
    @neg-value: -@icon-height;
    --icon-normal-top: @neg-value;
}
@icon-normal-top: var(--icon-normal-top, 0);

.svg-icon {
    background: data-uri('../../../../common/forms/resources/img/icon-menu-sprite.svg') no-repeat;
    background-size: @icon-width*29 @icon-height*2;

    &.download {
        background-position: -@icon-width 0;
        background-position: -@icon-width @icon-normal-top;
    }
    &.share {
        background-position: -@icon-width*2 0;
        background-position: -@icon-width*2 @icon-normal-top;
    }
    &.embed {
        background-position: -@icon-width*3 0;
        background-position: -@icon-width*3 @icon-normal-top;
    }
    &.fullscr {
        background-position: -@icon-width*4 0;
        background-position: -@icon-width*4 @icon-normal-top;
    }
    &.zoom-in {
        background-position: -@icon-width*5 0;
        background-position: -@icon-width*5 @icon-normal-top;
    }
    &.zoom-out {
        background-position: -@icon-width*6 0;
        background-position: -@icon-width*6 @icon-normal-top;
    }
    &.zoom-up {
        background-position: -@icon-width*5 -@icon-height;
    }
    &.zoom-down {
        background-position: -@icon-width*6 -@icon-height;
    }
    &.slide-prev {
        background-position: -@icon-width*7 -@icon-height;
    }
    &.slide-next {
        background-position: -@icon-width*8 -@icon-height;
    }

    &.play {
        background-position: -@icon-width*9 -@icon-height;
    }

    &.pause {
        background-position: -@icon-width*10 -@icon-height;
    }

    &.print {
        background-position: -@icon-width*11 0;
        background-position: -@icon-width*11 @icon-normal-top;
    }

    &.arrow-up {
        background-position: -@icon-width*17 0;
        background-position: -@icon-width*17 @icon-normal-top;
    }
    &.arrow-down {
        background-position: -@icon-width*16 0;
        background-position: -@icon-width*16 @icon-normal-top;
    }
    &.clear-style {
        background-position: -@icon-width*12 0;
        background-position: -@icon-width*12 @icon-normal-top;
    }
    &.go-to-location {
        background-position: -@icon-width*15 0;
        background-position: -@icon-width*15 @icon-normal-top;
    }
    &.more-vertical {
        background-position: -@icon-width*14 0;
        background-position: -@icon-width*14 @icon-normal-top;
    }
    &.cut {
        background-position: -@icon-width*19 0;
        background-position: -@icon-width*19 @icon-normal-top;
    }
    &.copy {
        background-position: -@icon-width*20 0;
        background-position: -@icon-width*20 @icon-normal-top;
    }
    &.paste {
        background-position: -@icon-width*21 0;
        background-position: -@icon-width*21 @icon-normal-top;
    }
    &.undo {
        background-position: -@icon-width*22 0;
        background-position: -@icon-width*22 @icon-normal-top;
    }
    &.redo {
        background-position: -@icon-width*23 0;
        background-position: -@icon-width*23 @icon-normal-top;
    }
    &.search {
        background-position: -@icon-width*24 0;
        background-position: -@icon-width*24 @icon-normal-top;
    }
    &.btn-sheet-view {
        background-position: -@icon-width*25 0;
        background-position: -@icon-width*25 @icon-normal-top;
    }
    &.hide-password {
        background-position: -@icon-width*26 0;
        background-position: -@icon-width*26 @icon-normal-top;
    }
    &.search-close {
        background-position: -@icon-width*18 0;
        background-position: -@icon-width*18 @icon-normal-top;
    }
    &.search-arrow-up {
        background-position: -@icon-width*27 0;
        background-position: -@icon-width*27 @icon-normal-top;
    }
    &.search-arrow-down {
        background-position: -@icon-width*28 0;
        background-position: -@icon-width*28 @icon-normal-top;
    }
}

.btn {
    &.active, &:active {
        &:not(:disabled):not(.disabled) {
            .icon {
                @btn-active-icon-offset: -20px;
                background-position-y: @btn-active-icon-offset;
                background-position-y: var(--button-small-active-icon-offset-x, 0);
            }
        }
    }
}

.mi-icon {
    width: @icon-width;
    height: @icon-height;
    float: left;
    margin: -3px 4px 0 -24px;
}

.modal-dlg {
    textarea {
        .user-select(text);
        width: 100%;
        resize: none;
        margin-bottom: 5px;
        border: @scaled-one-px-value-ie solid @border-regular-control-ie;
        border: @scaled-one-px-value solid @border-regular-control;
        height: 100%;

        &.disabled {
            opacity: @component-disabled-opacity-ie;
            opacity: @component-disabled-opacity;
            cursor: default !important;
        }
    }

    label {
        .font-size-normal();
        font-weight: normal;

        &.input-label{
            margin-bottom: 0;
            vertical-align: middle;
        }

        &.header {
            font-weight: bold;
        }
    }

    .form-control[readonly] {
        cursor: auto;
    }

    .share-buttons {
        height: 40px;
        text-align: center;

        span {
            display: inline-block;
            margin: 0 7px;
            cursor: pointer;
        }
    }
}

.masked {
    background-color: transparent;
    border-color: transparent;
    .box-shadow(none);
}

#pages {
    cursor: pointer;
    display: inline-block;
    line-height: 22px;
    padding: 0 0 0 3px;
    vertical-align: middle;
}

#page-number {
    .masked {
        cursor: pointer;
        line-height: 22px;
    }
}

.submit-tooltip {
    position: absolute;
    z-index: 1000;
    top: 58px;
    right: 15px;

    padding: 7px 15px;
    border-radius: 5px;
    background-color: @background-notification-popover-ie;
    background-color: @background-notification-popover;
    .box-shadow(0 4px 15px -2px rgba(0, 0, 0, 0.5));
    font-size: 11px;
}

.menu-zoom {
    line-height: @line-height-base;

    .title {
        padding: 5px 5px 5px 28px;
        float: left;
        font-weight: normal;
        font-size: 11px;
        margin: 0px;
        text-overflow: ellipsis;
    }

    .zoom {
        padding: 5px 3px;
        float: right;
        min-width: 40px;
        text-align: center;
        font-weight: normal;
        font-size: 11px;
        padding-bottom: 0px;
    }
    .mi-icon  {
        margin: 0;
    }
}

.font-size-small {
    .fontsize(@font-size-small);
}

.font-size-normal {
    .fontsize(@font-size-base);
}

.font-size-large {
    .fontsize(@font-size-large);
}

.search-bar {
    z-index: 50;
}