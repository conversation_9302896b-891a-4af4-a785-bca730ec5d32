{"Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON>", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON><PERSON> mesej anda di sini", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "<PERSON><PERSON><PERSON><PERSON> telah dinyahdaya kerana ia sedang diedit oleh pengguna yang lain.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "<PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "<PERSON><PERSON><PERSON><PERSON> telah dinyahdaya kerana ia sedang diedit oleh pengguna yang lain.", "Common.Controllers.ExternalOleEditor.warningTitle": "<PERSON><PERSON>", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStackedPer": "<PERSON><PERSON><PERSON> 100%", "Common.define.chartData.textBar": "Bar", "Common.define.chartData.textBarNormal": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal3d": "Lajur bergugus 3-D", "Common.define.chartData.textBarNormal3dPerspective": "Lajur 3-D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarStacked3d": "Lajur bertindan 3-D", "Common.define.chartData.textBarStackedPer": "<PERSON><PERSON><PERSON> 100%", "Common.define.chartData.textBarStackedPer3d": "<PERSON><PERSON><PERSON> 100% 3-D", "Common.define.chartData.textCharts": "Carta", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Kombo", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON> be<PERSON> – lajur bergugus", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON><PERSON> - garis", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON><PERSON> - garis pada paksi kedua", "Common.define.chartData.textComboCustom": "Kombinasi tersuai", "Common.define.chartData.textDoughnut": "Donut", "Common.define.chartData.textHBarNormal": "Bar bergugus", "Common.define.chartData.textHBarNormal3d": "Bar bergugus 3-D", "Common.define.chartData.textHBarStacked": "Bar bertindan", "Common.define.chartData.textHBarStacked3d": "Bar bertindan 3-D", "Common.define.chartData.textHBarStackedPer": "Bar bertindan 100%", "Common.define.chartData.textHBarStackedPer3d": "Bar bertindan 100% 3-D", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Baris 3-D", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> dengan penanda", "Common.define.chartData.textLineStacked": "<PERSON><PERSON> be<PERSON>", "Common.define.chartData.textLineStackedMarker": "<PERSON><PERSON> bertindan dengan penanda", "Common.define.chartData.textLineStackedPer": "<PERSON><PERSON> 100%", "Common.define.chartData.textLineStackedPerMarker": "<PERSON><PERSON> be<PERSON> 100% dengan penanda", "Common.define.chartData.textPie": "<PERSON><PERSON>", "Common.define.chartData.textPie3d": "Pai 3-D", "Common.define.chartData.textPoint": "XY (Serak)", "Common.define.chartData.textScatter": "Serak", "Common.define.chartData.textScatterLine": "<PERSON><PERSON> dengan garis rata", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON> dengan garis rata dan penanda", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON> dengan garis rata", "Common.define.chartData.textScatterSmoothMarker": "<PERSON><PERSON> dengan garis rata dan penanda", "Common.define.chartData.textStock": "Stok", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textAcross": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textAppear": "Muncul", "Common.define.effectData.textArcDown": "Lengkung Bawah", "Common.define.effectData.textArcLeft": "Lengkung <PERSON>", "Common.define.effectData.textArcRight": "Lengkung <PERSON>", "Common.define.effectData.textArcs": "<PERSON>g<PERSON><PERSON>", "Common.define.effectData.textArcUp": "Lengkung Atas", "Common.define.effectData.textBasic": "<PERSON><PERSON>", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON>", "Common.define.effectData.textBasicZoom": "<PERSON><PERSON>", "Common.define.effectData.textBean": "Kacang", "Common.define.effectData.textBlinds": "Buta", "Common.define.effectData.textBlink": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBoldFlash": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBoldReveal": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBoomerang": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounce": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounceRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBox": "Kotak", "Common.define.effectData.textBrushColor": "<PERSON><PERSON>", "Common.define.effectData.textCenterRevolve": "Berputar Di <PERSON>", "Common.define.effectData.textCheckerboard": "Papan Dam", "Common.define.effectData.textCircle": "Bulatan", "Common.define.effectData.textCollapse": "Run<PERSON><PERSON><PERSON>", "Common.define.effectData.textColorPulse": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textComplementaryColor": "<PERSON>na komplimen", "Common.define.effectData.textComplementaryColor2": "Warna komplimen 2", "Common.define.effectData.textCompress": "Mampat", "Common.define.effectData.textContrast": "Kontras", "Common.define.effectData.textContrastingColor": "<PERSON><PERSON>", "Common.define.effectData.textCredits": "Kredit", "Common.define.effectData.textCrescentMoon": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurveDown": "Melengkung Ke Bawah", "Common.define.effectData.textCurvedSquare": "Segi Empat Melengkung", "Common.define.effectData.textCurvedX": "Melengkung X", "Common.define.effectData.textCurvyLeft": "<PERSON><PERSON>", "Common.define.effectData.textCurvyRight": "<PERSON><PERSON>", "Common.define.effectData.textCurvyStar": "Bintang Berlengkok", "Common.define.effectData.textCustomPath": "<PERSON><PERSON>", "Common.define.effectData.textCuverUp": "Melengkung Ke Atas", "Common.define.effectData.textDarken": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDecayingWave": "Gelombang Pereputan", "Common.define.effectData.textDesaturate": "Nyahtepu", "Common.define.effectData.textDiagonalDownRight": "<PERSON><PERSON>", "Common.define.effectData.textDiagonalUpRight": "<PERSON><PERSON>", "Common.define.effectData.textDiamond": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDisappear": "Hilang", "Common.define.effectData.textDissolveIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDissolveOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDown": "<PERSON>", "Common.define.effectData.textDrop": "Jatuh<PERSON>", "Common.define.effectData.textEmphasis": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textEntrance": "<PERSON><PERSON>", "Common.define.effectData.textEqualTriangle": "Sama Dengan Segi Tiga", "Common.define.effectData.textExciting": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textExit": "<PERSON><PERSON>", "Common.define.effectData.textExpand": "Kembangkan", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFigureFour": "Rajah 8 Empat", "Common.define.effectData.textFillColor": "<PERSON><PERSON>", "Common.define.effectData.textFlip": "Balikkan", "Common.define.effectData.textFloat": "Apungan", "Common.define.effectData.textFloatDown": "Apungan Bawah", "Common.define.effectData.textFloatIn": "Apungan Ma<PERSON>k", "Common.define.effectData.textFloatOut": "Apungan <PERSON>", "Common.define.effectData.textFloatUp": "Apungan Atas", "Common.define.effectData.textFlyIn": "Terbang masuk", "Common.define.effectData.textFlyOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFontColor": "<PERSON><PERSON>", "Common.define.effectData.textFootball": "<PERSON><PERSON>", "Common.define.effectData.textFromBottom": "<PERSON><PERSON> bawah", "Common.define.effectData.textFromBottomLeft": "<PERSON><PERSON>", "Common.define.effectData.textFromBottomRight": "<PERSON><PERSON>", "Common.define.effectData.textFromLeft": "<PERSON><PERSON>", "Common.define.effectData.textFromRight": "<PERSON><PERSON>", "Common.define.effectData.textFromTop": "<PERSON><PERSON>", "Common.define.effectData.textFromTopLeft": "<PERSON><PERSON>", "Common.define.effectData.textFromTopRight": "<PERSON><PERSON>", "Common.define.effectData.textFunnel": "Corong", "Common.define.effectData.textGrowShrink": "Mengembang/Mengecut", "Common.define.effectData.textGrowTurn": "Mengembang & Pusing", "Common.define.effectData.textGrowWithColor": "Mengembang Dengan Warna", "Common.define.effectData.textHeart": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHeartbeat": "<PERSON><PERSON><PERSON> j<PERSON>ung", "Common.define.effectData.textHexagon": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHorizontal": "Melintang", "Common.define.effectData.textHorizontalFigure": "Rajah 8 Melintang", "Common.define.effectData.textHorizontalIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textHorizontalOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textIn": "<PERSON><PERSON>", "Common.define.effectData.textInFromScreenCenter": "<PERSON><PERSON>", "Common.define.effectData.textInSlightly": "<PERSON>", "Common.define.effectData.textInToScreenBottom": "<PERSON> ke Bawah Skrin", "Common.define.effectData.textInvertedSquare": "<PERSON><PERSON> Em<PERSON> Sam<PERSON>ang", "Common.define.effectData.textInvertedTriangle": "Segi Tiga Songsang", "Common.define.effectData.textLeft": "<PERSON><PERSON>", "Common.define.effectData.textLeftDown": "<PERSON>", "Common.define.effectData.textLeftUp": "<PERSON><PERSON>", "Common.define.effectData.textLighten": "<PERSON><PERSON><PERSON> c<PERSON>", "Common.define.effectData.textLineColor": "<PERSON><PERSON>", "Common.define.effectData.textLines": "<PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON>", "Common.define.effectData.textLoopDeLoop": "Gelung ke Gelung", "Common.define.effectData.textLoops": "Gelung", "Common.define.effectData.textModerate": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textObjectColor": "<PERSON><PERSON>", "Common.define.effectData.textOctagon": "Oktagon", "Common.define.effectData.textOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOutFromScreenBottom": "<PERSON><PERSON><PERSON> Bawah Skrin", "Common.define.effectData.textOutSlightly": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textOutToScreenCenter": "<PERSON><PERSON><PERSON>gah Skrin", "Common.define.effectData.textParallelogram": "Parallelogram", "Common.define.effectData.textPath": "<PERSON><PERSON>", "Common.define.effectData.textPathCurve": "Melengkung", "Common.define.effectData.textPathLine": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "<PERSON><PERSON>", "Common.define.effectData.textPeanut": "<PERSON><PERSON>g tanah", "Common.define.effectData.textPeekIn": "Intai Ke Dalam", "Common.define.effectData.textPeekOut": "Intai Ke Luar", "Common.define.effectData.textPentagon": "Pentagon", "Common.define.effectData.textPinwheel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPlus": "Tambah", "Common.define.effectData.textPointStar": "Bintang Mata", "Common.define.effectData.textPointStar4": "Bintang 4 Mata", "Common.define.effectData.textPointStar5": "Bintang 5 Mata", "Common.define.effectData.textPointStar6": "Bintang 6 Mata", "Common.define.effectData.textPointStar8": "Bintang 8 Mata", "Common.define.effectData.textPulse": "Denyut", "Common.define.effectData.textRandomBars": "Bar Rawak", "Common.define.effectData.textRight": "<PERSON><PERSON>", "Common.define.effectData.textRightDown": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRightTriangle": "Segi Tiga Kanan", "Common.define.effectData.textRightUp": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRiseUp": "Bangkit", "Common.define.effectData.textSCurve1": "Lengkok S 1", "Common.define.effectData.textSCurve2": "Lengkok S 2", "Common.define.effectData.textShape": "Bentuk", "Common.define.effectData.textShapes": "Bentuk", "Common.define.effectData.textShimmer": "Berk<PERSON><PERSON>", "Common.define.effectData.textShrinkTurn": "Susut & Pusing", "Common.define.effectData.textSineWave": "Gelombang Sin", "Common.define.effectData.textSinkDown": "Tenggelam", "Common.define.effectData.textSlideCenter": "Slaid Tengah", "Common.define.effectData.textSpecial": "Khas", "Common.define.effectData.textSpin": "Putar", "Common.define.effectData.textSpinner": "P<PERSON>utar", "Common.define.effectData.textSpiralIn": "<PERSON><PERSON>", "Common.define.effectData.textSpiralLeft": "<PERSON><PERSON>", "Common.define.effectData.textSpiralOut": "<PERSON><PERSON>", "Common.define.effectData.textSpiralRight": "<PERSON><PERSON>", "Common.define.effectData.textSplit": "Pisah", "Common.define.effectData.textSpoke1": "1 Spoke", "Common.define.effectData.textSpoke2": "2 Spokes", "Common.define.effectData.textSpoke3": "3 Spokes", "Common.define.effectData.textSpoke4": "4 Spokes", "Common.define.effectData.textSpoke8": "8 Spokes", "Common.define.effectData.textSpring": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSquare": "<PERSON>asa dua", "Common.define.effectData.textStairsDown": "Tangga Ke Bawah", "Common.define.effectData.textStretch": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textStrips": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSubtle": "Tak ketara", "Common.define.effectData.textSwivel": "Pusing", "Common.define.effectData.textSwoosh": "<PERSON><PERSON>", "Common.define.effectData.textTeardrop": "Titisan air mata", "Common.define.effectData.textTeeter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textToBottom": "<PERSON>", "Common.define.effectData.textToBottomLeft": "<PERSON>", "Common.define.effectData.textToBottomRight": "<PERSON>", "Common.define.effectData.textToLeft": "<PERSON>", "Common.define.effectData.textToRight": "<PERSON>", "Common.define.effectData.textToTop": "<PERSON>", "Common.define.effectData.textToTopLeft": "<PERSON>", "Common.define.effectData.textToTopRight": "<PERSON>", "Common.define.effectData.textTransparency": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textTrapezoid": "Trapezoid", "Common.define.effectData.textTurnDown": "Pusing <PERSON> Bawa<PERSON>", "Common.define.effectData.textTurnDownRight": "Pusing <PERSON>", "Common.define.effectData.textTurns": "Pusing", "Common.define.effectData.textTurnUp": "Pusing <PERSON>", "Common.define.effectData.textTurnUpRight": "Pusing Ke <PERSON>", "Common.define.effectData.textUnderline": "<PERSON><PERSON> bawah", "Common.define.effectData.textUp": "<PERSON>", "Common.define.effectData.textVertical": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textVerticalFigure": "Rajah 8 Menegak", "Common.define.effectData.textVerticalIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textVerticalOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWave": "Gelombang", "Common.define.effectData.textWedge": "<PERSON><PERSON>", "Common.define.effectData.textWheel": "Roda", "Common.define.effectData.textWhip": "Putar", "Common.define.effectData.textWipe": "Sapu", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "<PERSON><PERSON>", "Common.Translation.textMoreButton": "Selanjutnya", "Common.Translation.warnFileLocked": "<PERSON><PERSON> sedang diedit dalam apl yang lain. <PERSON>a boleh terus mengedit dan simpan ia sebagai Salinan.", "Common.Translation.warnFileLockedBtnEdit": "Cipta <PERSON>", "Common.Translation.warnFileLockedBtnView": "Buka untuk paparan", "Common.UI.ButtonColored.textAutoColor": "Automatik", "Common.UI.ButtonColored.textNewColor": "Tambah Warna Tersuai Baharu", "Common.UI.ComboBorderSize.txtNoBorders": "T<PERSON>da sempanan", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "T<PERSON>da sempanan", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON> gaya", "Common.UI.ExtendedColorDialog.addButtonText": "Tambah", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai di antara 000000 dan FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai be<PERSON>ka di antara 0 dan 255.", "Common.UI.HSBColorPicker.textNoColor": "Tiada Warna", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Sembunyikan kata laluan", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Tunjuk kata laluan", "Common.UI.SearchBar.textFind": "<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Keputusan seterusnya", "Common.UI.SearchBar.tipOpenAdvancedSettings": "B<PERSON> tetapan lan<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Keputusan sebelum", "Common.UI.SearchDialog.textHighlight": "Keputusan Sorotan Penting", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "Common.UI.SearchDialog.textReplaceDef": "Masukkan teks gantian", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> teks anda di sini", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON> dan <PERSON>", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>an sahaja", "Common.UI.SearchDialog.txtBtnHideReplace": "Sembunyikan Ganti", "Common.UI.SearchDialog.txtBtnReplace": "Gantikan", "Common.UI.SearchDialog.txtBtnReplaceAll": "Gantikan Semua", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON> tunjukkan mesej ini semula", "Common.UI.SynchronizeTip.textSynchronize": "Dokumen telah ditukar dengan pengguna yang lain.<br><PERSON>la klik untuk simpan perubahan anda dan muat semula kemas kini.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "Warna Standard", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasik <PERSON>", "Common.UI.Themes.txtThemeContrastDark": "Kontras Gelap", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Sama seperti Sistem", "Common.UI.Window.cancelButtonText": "Batalkan", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Tidak", "Common.UI.Window.okButtonText": "<PERSON><PERSON>", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON> tunjukkan mesej ini semula", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Informasi", "Common.UI.Window.textWarning": "<PERSON><PERSON>", "Common.UI.Window.yesButtonText": "Ya", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "<PERSON><PERSON>", "Common.Views.About.txtAddress": "<PERSON><PERSON><PERSON>", "Common.Views.About.txtLicensee": "LESENKAN", "Common.Views.About.txtLicensor": "PEMBERI LESEN", "Common.Views.About.txtMail": "e-mel:", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>h", "Common.Views.About.txtTel": "Tel.:", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Tambah", "Common.Views.AutoCorrectDialog.textApplyText": "<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Teks AutoBetul", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat Semasa <PERSON>", "Common.Views.AutoCorrectDialog.textBulleted": "<PERSON><PERSON><PERSON> bulet automatik", "Common.Views.AutoCorrectDialog.textBy": "Mengikut", "Common.Views.AutoCorrectDialog.textDelete": "Padam", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Tambah noktah dengan jarak-berganda", "Common.Views.AutoCorrectDialog.textFLCells": "<PERSON><PERSON><PERSON> huruf pertama sel jadual", "Common.Views.AutoCorrectDialog.textFLSentence": "<PERSON><PERSON><PERSON> huruf pertama ayat", "Common.Views.AutoCorrectDialog.textHyperlink": "<PERSON><PERSON> dan rang<PERSON>an dengan hiperpautan", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON>da sempang (--) dengan tanda sengkang (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematik", "Common.Views.AutoCorrectDialog.textNumbered": "Senarai nombor automatik", "Common.Views.AutoCorrectDialog.textQuotes": "\"Tanda petik lurus\" dengan \"tanda petik pintar\"", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Ungkapan berikut adalah ungkapan kira-kira yang dicam. <PERSON>ya akan dipaparkan secara automatik dalam huruf italik.", "Common.Views.AutoCorrectDialog.textReplace": "Gantikan", "Common.Views.AutoCorrectDialog.textReplaceText": "Gantikan Semasa <PERSON>", "Common.Views.AutoCorrectDialog.textReplaceType": "Gantikan teks semasa anda taip", "Common.Views.AutoCorrectDialog.textReset": "<PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON> ke lalai", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoBaiki", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON>gsi dikenalpasti mestilah mengandungi hanya huruf A hingga Z, huruf besar atau huruf kecil.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Mana-mana eks<PERSON><PERSON>i yang anda tambah akan dikeluarkan dan mana telah dikeluarkan akan dipulihkan semula. <PERSON><PERSON><PERSON> anda mahu men<PERSON>kan?", "Common.Views.AutoCorrectDialog.warnReplace": "Entri autobetul untuk %1 telah wujud. <PERSON><PERSON><PERSON> anda mahu gantikannya?", "Common.Views.AutoCorrectDialog.warnReset": "Mana-mana pembetulan auto yang anda tambah akan dibuang dan mana telah berubah akan dipulihkan kepada nilai asal. <PERSON><PERSON><PERSON> anda mahu men<PERSON>kan?", "Common.Views.AutoCorrectDialog.warnRestore": "Entri autobetul untuk %1 akan diset semula ke nilai aslinya. <PERSON><PERSON><PERSON> anda mahu gantikannya?", "Common.Views.Chat.textSend": "Hantar", "Common.Views.Comments.mniAuthorAsc": "Pengarang A ke Z", "Common.Views.Comments.mniAuthorDesc": "Pengarang Z ke A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Terbaharu", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON> atas", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON> bawah", "Common.Views.Comments.textAdd": "Tambah", "Common.Views.Comments.textAddComment": "Tambah Komen", "Common.Views.Comments.textAddCommentToDoc": "Tambah Komen pada Dokumen", "Common.Views.Comments.textAddReply": "Tambah Balasan", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "Batalkan", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Tutup komen", "Common.Views.Comments.textComments": "Komen", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> komen anda di sini", "Common.Views.Comments.textHintAddComment": "Tambah komen", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Diselesaikan", "Common.Views.Comments.textSort": "<PERSON><PERSON> komen", "Common.Views.Comments.textViewResolved": "Anda tidak mempunyai keizinan untuk membuka semula komen", "Common.Views.Comments.txtEmpty": "Tidak ada komen di dalam dokumen.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON> tunjukkan mesej ini semula", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON> salin, potong dan tampal menggunakan bar alatan editor dan konteks tindakan menu akan dilakukan dalam tab editor ini sahaja.<br><br>Untuk menyalin atau menampal keapda atau daripada aplikasi di luar tab editor guna kombinasi papan kekunci yang berikut:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> dan <PERSON>", "Common.Views.CopyWarningDialog.textToCopy": "untuk Salinan", "Common.Views.CopyWarningDialog.textToCut": "untuk Potong", "Common.Views.CopyWarningDialog.textToPaste": "untuk Tampal", "Common.Views.DocumentAccessDialog.textLoading": "Memuatkan", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON>", "Common.Views.ExternalDiagramEditor.textTitle": "Editor <PERSON><PERSON>", "Common.Views.ExternalOleEditor.textTitle": "Editor <PERSON><PERSON><PERSON>", "Common.Views.Header.labelCoUsersDescr": "<PERSON><PERSON><PERSON> yang mengedit fail:", "Common.Views.Header.textAddFavorite": "Tanda sebagai kegemaran", "Common.Views.Header.textAdvSettings": "Seting lanjutan", "Common.Views.Header.textBack": "Buka lokasi fail", "Common.Views.Header.textCompactView": "Sembunyikan Bar Alat", "Common.Views.Header.textHideLines": "Sembunyikan Pembaris", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "Sembunyikan Bar Status", "Common.Views.Header.textRemoveFavorite": "<PERSON><PERSON> k<PERSON>", "Common.Views.Header.textSaveBegin": "Sedang menyimpan…", "Common.Views.Header.textSaveChanged": "Diubahsuai", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON> disimpan", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON> disimpan", "Common.Views.Header.textShare": "Kong<PERSON>", "Common.Views.Header.textZoom": "<PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Uruskan hak akses dokumen", "Common.Views.Header.tipDownload": "Muat turun fail", "Common.Views.Header.tipGoEdit": "Edit fail semasa", "Common.Views.Header.tipPrint": "<PERSON>ail cetakan", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Simpan", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON>t semula", "Common.Views.Header.tipUndock": "<PERSON><PERSON>ar dok ke tetingkap be<PERSON>ingan", "Common.Views.Header.tipUsers": "<PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON> seting", "Common.Views.Header.tipViewUsers": "Lihat pengguna dan uruskan hak akses dokumen", "Common.Views.Header.txtAccessRights": "Ubah hak akses", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHide": "Run<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Kembangkan", "Common.Views.History.textShowAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON> terperi<PERSON>i", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Tampal URL imej:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Medan ini diperlukan", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Medan ini perlu sebagai URL dalam format \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "<PERSON>a perlu menentukan bilangan baris dan lajur yang sah.", "Common.Views.InsertTableDialog.txtColumns": "Bilangan <PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON> maksimum bagi medan ini ialah {0}.", "Common.Views.InsertTableDialog.txtMinText": "<PERSON>lai minimum bagi medan ini ialah {0}.", "Common.Views.InsertTableDialog.txtRows": "Bilangan Baris", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "Pi<PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Pilih Bahasa dokumen", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromUrl": "Dari URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON> bulet", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewImage": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNone": "Tiada", "Common.Views.ListSettingsDialog.txtOfText": "Teks %", "Common.Views.ListSettingsDialog.txtSize": "Saiz", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> pada", "Common.Views.ListSettingsDialog.txtSymbol": "Simbol", "Common.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Pengekodan", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON>a laluan tidak betul.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON><PERSON> kata laluan untuk membuka fail", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON><PERSON> anda masukkan kata laluan dan buka fail, kata laluan semasa kepada fail akan diset semula.", "Common.Views.OpenDialog.txtTitle": "Pilih pilihan %1", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtDescription": "Tetapkan kata laluan untuk lindungi dokumen", "Common.Views.PasswordDialog.txtIncorrectPwd": "Pengesahan kata laluan tidak identical", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "Ulang kata laluan", "Common.Views.PasswordDialog.txtTitle": "Tetapkan Kata <PERSON>", "Common.Views.PasswordDialog.txtWarning": "Amaran: <PERSON><PERSON> anda hilang atau lupa kata laluan, ia tidak dapat dipulihkan. Sila simpan ia dalam tempat selamat.", "Common.Views.PluginDlg.textLoading": "Memuatkan", "Common.Views.Plugins.groupCaption": "Plug masuk", "Common.Views.Plugins.strPlugins": "Plug masuk", "Common.Views.Plugins.textClosePanel": "Tutup pasang masuk", "Common.Views.Plugins.textLoading": "Memuatkan", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Sulitkan dengan kata laluan", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> atau padam kata laluan", "Common.Views.Protection.hintSignature": "Tambah tandatangan digital atau garis tandatangan", "Common.Views.Protection.txtAddPwd": "Tambah kata laluan", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON> kata laluan", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON> kata laluan", "Common.Views.Protection.txtEncrypt": "Sulitkan", "Common.Views.Protection.txtInvisibleSignature": "Tambah tandatangan digital", "Common.Views.Protection.txtSignature": "Tandatangan", "Common.Views.Protection.txtSignatureLine": "Tam<PERSON> garis tandatangan", "Common.Views.RenameDialog.textName": "<PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "Nama fail tidak boleh mengandungi sebarang aksara yang berikut:", "Common.Views.ReviewChanges.hintNext": "<PERSON>", "Common.Views.ReviewChanges.hintPrev": "<PERSON>han sebelumnya", "Common.Views.ReviewChanges.strFast": "Pantas", "Common.Views.ReviewChanges.strFastDesc": "Editing-be<PERSON><PERSON> masa-se<PERSON><PERSON>. <PERSON><PERSON><PERSON> per<PERSON>han telah disimpan secara automatic.", "Common.Views.ReviewChanges.strStrict": "Tegas", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON><PERSON> butang ‘Save’ untuk menyegerakkan perubahan yang anda dan yang lain lakukan.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipCoAuthMode": "Tetapkan mod pengeditan-bersama", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON> keluar komen", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON><PERSON> keluar komen semasa", "Common.Views.ReviewChanges.tipCommentResolve": "Selesaikan komen", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Selesaikan komen semasa", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> sejarah versi", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON> semasa", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON> mod yang anda mahu perubahan untuk dipaparkan", "Common.Views.ReviewChanges.tipSetDocLang": "Tetapkan Bahasa dokumen", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipSharing": "Uruskan hak akses dokumen", "Common.Views.ReviewChanges.txtAccept": "Terima", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON> semua per<PERSON>han", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtChat": "Sembang", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON><PERSON> keluar komen semasa", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON> k<PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON>ai<PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtDocLang": "Bahasa", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON><PERSON> (Pratonton)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> (Pengeditan)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Seterusnya", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> (Pratonton)", "Common.Views.ReviewChanges.txtOriginalCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtPrev": "Sebelumnya", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSharing": "<PERSON>kon<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "Tambah", "Common.Views.ReviewPopover.textAddReply": "Tambah Balasan", "Common.Views.ReviewPopover.textCancel": "Batalkan", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textMention": "+sebutan akan memberikan akses terhadap dokumen dan menghantar e-mel", "Common.Views.ReviewPopover.textMentionNotify": "+sebutan akan memaklumkan pengguna melalui e-mel", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Anda tidak mempunyai keizinan untuk membuka semula komen", "Common.Views.ReviewPopover.txtDeleteTip": "Padam", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.SaveAsDlg.textLoading": "Memuatkan", "Common.Views.SaveAsDlg.textTitle": "Folder untuk simpan", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON> dan <PERSON>", "Common.Views.SearchPanel.textMatchUsingRegExp": "Sepadan menggunakan ung<PERSON>pan biasa", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON><PERSON> padanan", "Common.Views.SearchPanel.textNoSearchResults": "Tiada carian kep<PERSON>n", "Common.Views.SearchPanel.textReplace": "Gantikan", "Common.Views.SearchPanel.textReplaceAll": "Gantikan Semua", "Common.Views.SearchPanel.textReplaceWith": "Gantikan dengan", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON> telah <PERSON>", "Common.Views.SearchPanel.textSearchResults": "Keputusan carian: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Terlalu banyak keputusan untuk dipaparkan di sini", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>an sahaja", "Common.Views.SearchPanel.tipNextResult": "Keputusan seterusnya", "Common.Views.SearchPanel.tipPreviousResult": "Keputusan sebelum", "Common.Views.SelectFileDlg.textLoading": "Memuatkan", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ubah", "Common.Views.SignDialog.textInputName": "<PERSON><PERSON> pen<PERSON> input", "Common.Views.SignDialog.textItalic": "Italik", "Common.Views.SignDialog.textNameError": "<PERSON>a penanda<PERSON>gan tidak boleh kosong.", "Common.Views.SignDialog.textPurpose": "Tu<PERSON>an menandatangani dokumen ini", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "Tandatangan kelihatan se<PERSON>i", "Common.Views.SignDialog.textTitle": "<PERSON><PERSON><PERSON> tan<PERSON>", "Common.Views.SignDialog.textUseImage": "Atau klik ‘select Image’ untuk guna gambar sebagai tandatangan", "Common.Views.SignDialog.textValid": "Sah dari %1 to %2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "<PERSON>z F<PERSON>", "Common.Views.SignSettingsDialog.textAllowComment": "Benarkan penandatangan untuk menambah komen dalam dialok tandatangan", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mel", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Tajuk <PERSON>", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON><PERSON> un<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarikh tanda dalam baris tandatangan", "Common.Views.SignSettingsDialog.textTitle": "Menetapkan Tandatangan", "Common.Views.SignSettingsDialog.txtEmpty": "Medan ini diperlukan", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "<PERSON><PERSON> unikod HEX", "Common.Views.SymbolTableDialog.textCopyright": "Tanda Hak Milik", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Tanda sengkang Em", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON> E<PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "Tanda sengkang En", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textFont": "Fon", "Common.Views.SymbolTableDialog.textNBHyphen": "Tiada Tanda Sempang tidak-berpecah", "Common.Views.SymbolTableDialog.textNBSpace": "<PERSON>iada-<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textPilcrow": "Tanda Pilcrow", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 <PERSON>", "Common.Views.SymbolTableDialog.textRange": "Julat", "Common.Views.SymbolTableDialog.textRecent": "Simbol digunakan baru-baru ini", "Common.Views.SymbolTableDialog.textRegistered": "Tandatangan Berdaftar", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSection": "Bahagian Tanda", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "Tanda sempang lembut", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simbol", "Common.Views.SymbolTableDialog.textTitle": "Simbol", "Common.Views.SymbolTableDialog.textTradeMark": "Simbol Cap Dagang", "Common.Views.UserNameDialog.textDontShow": "<PERSON>an tanya saya lagi", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label tidak boleh kosong.", "PE.Controllers.LeftMenu.leavePageText": "<PERSON><PERSON>a perubahan yang tidak disimpan dalam dokumen ini akan hilang.<br> <PERSON><PERSON> <PERSON>Cancel” kem<PERSON>an “Save” untuk menyimpannya. <PERSON><PERSON> “OK” untuk membuang semua perubahan yang tidak disimpan.", "PE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON><PERSON> tanpa nama", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "<PERSON><PERSON>", "PE.Controllers.LeftMenu.requestEditRightsText": "Meminta hak pengeditan", "PE.Controllers.LeftMenu.textLoadHistory": "<PERSON><PERSON> sejarah dimua<PERSON>…", "PE.Controllers.LeftMenu.textNoTextFound": "Data yang andaa sedang cari tidak dijumpai. Sila laras pilihan carian.", "PE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON><PERSON>an telah dilakukan. {0} kejadian telah dilangkau.", "PE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> telah <PERSON>. Kejadian digantikan: {0}", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "PE.Controllers.Main.applyChangesTextText": "Data dimuatkan…", "PE.Controllers.Main.applyChangesTitleText": "Data Dimuatkan", "PE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON><PERSON> masa tamat <PERSON>.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> \"OK\" untuk kembali ke senarai dokumen.", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "<PERSON>at turun telah gagal.", "PE.Controllers.Main.downloadTextText": "<PERSON><PERSON><PERSON><PERSON>...", "PE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.errorAccessDeny": "Anda sedang cuba untuk melakukan tindakan yang anda tidak mempunyai hak untuk.<br><PERSON><PERSON>, hubungi pentadbir Pelayan Dokumen anda.", "PE.Controllers.Main.errorBadImageUrl": "Imej URL tidak betul", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Pelayan sambungan hilang. Dokumen tidak dapat diedit buat masa sekarang.", "PE.Controllers.Main.errorComboSeries": "Untuk mencipta kombinasi carta, pilih sekurang-kurangnya dua siri data.", "PE.Controllers.Main.errorConnectToServer": "Dokumen tidak dapat disimpan. <PERSON><PERSON> semak seting sambungan atau hubungi pentadbir anda.<br><PERSON><PERSON><PERSON><PERSON> anda klik butang ‘OK’, anda akan diminta untuk muat turun dokumen.", "PE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON> luaran.<br><PERSON><PERSON> sambungan data asas. <PERSON>la hubungi sokongan sekiranya ralat masih berlaku.", "PE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON> per<PERSON>han yang telah di<PERSON>, mereka tidak dapat dita<PERSON>.", "PE.Controllers.Main.errorDataRange": "Julat data tidak betul.", "PE.Controllers.Main.errorDefaultMessage": "Kod ralat: %1", "PE.Controllers.Main.errorEditingDownloadas": "Terdapat ralat yang berlaku semasa bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> pilihan 'Download as' untuk menyimpan salinan fail sandaran ke pemacu keras komputer anda.", "PE.Controllers.Main.errorEditingSaveas": "Terdapat ralat yang berlaku semasa bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> pilihan 'Save as...' untuk menyimpan salinan fail sandaran ke pemacu keras komputer anda.", "PE.Controllers.Main.errorEmailClient": "Tiada e-mel klien yang dijumpai.", "PE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON> dilindungi kata laluan dan tidak boleh dibuka.", "PE.Controllers.Main.errorFileSizeExceed": "Saiz fail me<PERSON><PERSON><PERSON> had ditetapkan untuk pelayan anda.<br><PERSON><PERSON>, hubungi pentadbir <PERSON> anda untuk butiran.", "PE.Controllers.Main.errorForceSave": "Terdapat ralat yang berlaku semasa menyimpan fail. <PERSON><PERSON> gunakan pilihan 'Download as' untuk menyimpan salinan fail sandaran ke pemacu keras komputer anda dan cuba semula nanti.", "PE.Controllers.Main.errorKeyEncrypt": "Pemerihal kunci tidak diketahui.", "PE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON><PERSON> kunci tamat tempoh", "PE.Controllers.Main.errorLoadingFont": "Fon tidak dimuatkan.<br><PERSON><PERSON> hubungi pentadbir Pelayan <PERSON> anda.", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON> disimpan.", "PE.Controllers.Main.errorServerVersion": "Versi editor te<PERSON> di<PERSON>. <PERSON><PERSON> akan dimuat semula untuk menggunakan perubahan.", "PE.Controllers.Main.errorSessionAbsolute": "Sesi pengeditan dokumen telah tamat tempoh. Sila muat semula halaman.", "PE.Controllers.Main.errorSessionIdle": "<PERSON><PERSON><PERSON> tidak diedit buat masa yang lama. <PERSON>la muat semula halaman.", "PE.Controllers.Main.errorSessionToken": "Sambungan kepada pelayan telah terganggu. <PERSON>la muat semula halaman.", "PE.Controllers.Main.errorSetPassword": "Kata laluan tidak dapat ditetapkan.", "PE.Controllers.Main.errorStockChart": "Urutan baris tidak betul. Untuk membina carta stok, tempatkan data pada helaian mengikut urutan:<br> harga bukaan, harga mak, harga min, harga penutup.", "PE.Controllers.Main.errorToken": "Dokumen token keselamatan kini tidak dibentuk.<br><PERSON><PERSON> hubungi pentadbir Pelayan Dokumen.", "PE.Controllers.Main.errorTokenExpire": "Dokumen token keselamatan telah tamat tempoh.<br><PERSON><PERSON> hubungi pentadbir Pelayan Dokumen.", "PE.Controllers.Main.errorUpdateVersion": "<PERSON><PERSON><PERSON> fail telah berubah. <PERSON><PERSON> akan dimuatkan semula.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Sambungan telah dipulihkan, dan versi fail telah berubah.<br><PERSON><PERSON><PERSON> anda boleh terus bekerja, anda perlu memuat turun fail atau menyalin kandungannya untuk memastikan tiada ada yang hilang, dan kemudian muat semula halaman ini.", "PE.Controllers.Main.errorUserDrop": "<PERSON>ail tidak boleh diakses sekarang.", "PE.Controllers.Main.errorUsersExceed": "Bilangan pengguna yang didizinkan mengikut pelan pembayaran telah lebih", "PE.Controllers.Main.errorViewerDisconnect": "Sambungan telah hilang. <PERSON>a masih boleh melihat dokumen,<br>tetapi tidak dapat muat turun atau cetaknya sehingga sambungan dipulihkan dan halaman dimuat semua.", "PE.Controllers.Main.leavePageText": "Anda mempunyai perubahan yang tidak disimpan dalam persembahab ini. Klik \"Stay on This Page\" kemudian \"Save\" untuk menyimpannya. Klik \"Leave This Page\" untuk membuang semua perubahan yang tidak disimpan.", "PE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON><PERSON> perubahan yang tidak disimpan dalam persembahan ini akan hilang.<br> <PERSON><PERSON> <PERSON>Cancel” kem<PERSON>an “Save” untuk menyimpannya. Klik \"OK\" untuk membuang semua perubahan yang tidak disimpan.", "PE.Controllers.Main.loadFontsTextText": "Data dimuatkan…", "PE.Controllers.Main.loadFontsTitleText": "Data Dimuatkan", "PE.Controllers.Main.loadFontTextText": "Data dimuatkan…", "PE.Controllers.Main.loadFontTitleText": "Data Dimuatkan", "PE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON><PERSON>…", "PE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadImageTextText": "<PERSON><PERSON><PERSON>…", "PE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadingDocumentTextText": "<PERSON><PERSON><PERSON><PERSON> dim<PERSON>…", "PE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.loadThemeTextText": "<PERSON><PERSON> dim<PERSON>…", "PE.Controllers.Main.loadThemeTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON>", "PE.Controllers.Main.openErrorText": "<PERSON><PERSON> telah berlaku semasa membuka fail.", "PE.Controllers.Main.openTextText": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>…", "PE.Controllers.Main.openTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.printTextText": "<PERSON><PERSON><PERSON><PERSON>…", "PE.Controllers.Main.printTitleText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON>", "PE.Controllers.Main.requestEditFailedMessageText": "Seseorang sedang mengedit persembahan ini sekarang ini. Sila cuba semula nanti.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.saveErrorText": "<PERSON><PERSON> telah berlaku semasa men<PERSON> fail.", "PE.Controllers.Main.saveErrorTextDesktop": "<PERSON>ail ini tidak boleh disimpan atau dicipta.<br><PERSON><PERSON><PERSON><PERSON><PERSON> alasannya adalah: <br>1. <PERSON>ail ini ialah untuk dibaca-sahaja. <br>2. <PERSON><PERSON> sedang diedit oleh pengguna lain. <br>3. <PERSON><PERSON><PERSON> telah penuh atau rosak.", "PE.Controllers.Main.saveTextText": "<PERSON><PERSON><PERSON><PERSON> persemba<PERSON>…", "PE.Controllers.Main.saveTitleText": "<PERSON><PERSON><PERSON><PERSON> perse<PERSON>", "PE.Controllers.Main.scriptLoadError": "Sambungan terlalu per<PERSON>, beberapa komponen tidak dapat dimuatkan. Sila muat semula halaman.", "PE.Controllers.Main.splitDividerErrorText": "Jumlah baris mestilah pembahagi bagi %1.", "PE.Controllers.Main.splitMaxColsErrorText": "<PERSON><PERSON><PERSON> lajur mestilah kurang dari %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Ju<PERSON>lah baris mestilah kurang dari %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "<PERSON>a kepada semua", "PE.Controllers.Main.textBuyNow": "<PERSON><PERSON> web", "PE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON> disimpan", "PE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Klik untuk tutup petua", "PE.Controllers.Main.textContactUs": "<PERSON><PERSON><PERSON><PERSON> jualan", "PE.Controllers.Main.textConvertEquation": "Per<PERSON><PERSON><PERSON> telah dicipta dengan versi lama editor per<PERSON><PERSON><PERSON> yang tidal lagi disokong. <PERSON><PERSON><PERSON> men<PERSON>, tukar persamaan kepada format Office Math ML.<br><PERSON><PERSON>?", "PE.Controllers.Main.textCustomLoader": "Sila ambil maklum bahawa menurut kepada terma lesen yang anda tidak berhak untuk ubah pemuat.<br><PERSON>la hubungi Jabatan Jualan kami untuk mendapatkan sebut harga.", "PE.Controllers.Main.textDisconnect": "Sambungan telah hilang", "PE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "<PERSON>ail mengandungi makro automatik.<br><PERSON><PERSON><PERSON> anda mahu jalankan makro?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON> nama yang kurang daripada 128 aksara.", "PE.Controllers.Main.textNoLicenseTitle": "Had lesen telah di<PERSON>ai", "PE.Controllers.Main.textPaidFeature": "<PERSON><PERSON>", "PE.Controllers.Main.textReconnect": "Sambungan dipulihkan", "PE.Controllers.Main.textRemember": "Ingat pilihan saya untuk semua fail", "PE.Controllers.Main.textRememberMacros": "<PERSON>gati pilihan saya untuk semua makro", "PE.Controllers.Main.textRenameError": "<PERSON>a pengguna tidak boleh kosong.", "PE.Controllers.Main.textRenameLabel": "<PERSON><PERSON><PERSON>n nama untuk digunakan bagi kerjasama", "PE.Controllers.Main.textRequestMacros": "Makro membuat permintaan ke URL. Adakah anda mahu membenarkan permintaan kepada %1?", "PE.Controllers.Main.textShape": "Bentuk", "PE.Controllers.Main.textStrict": "Mod tegas", "PE.Controllers.Main.textText": "Teks", "PE.Controllers.Main.textTryUndoRedo": "Fungsi Buat asal/Buat semula dinyahdayakan bagi mod pengeditan bersama.<br><PERSON><PERSON> butang ‘Strict mode’ untuk menukar kepada mod pengeditan Bersama Tegas untuk mengedit fail tanpa gangguan pengguna lain dan menghantar perubahan anda hanya selepas anda menyimp<PERSON>ya. <PERSON>a boleh bertukar di antara mod-mod pengeditan Bersama mennguna<PERSON> seting Lanjutan editor.", "PE.Controllers.Main.textTryUndoRedoWarn": "Fungsi Buat asal/Buat semula dinyahdayakan bagi mod pengeditan Bersama <PERSON>.", "PE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON> tamat tempoh", "PE.Controllers.Main.titleServerVersion": "Editor di<PERSON><PERSON>i", "PE.Controllers.Main.txtAddFirstSlide": "Klik untuk tambah slaid pertama", "PE.Controllers.Main.txtAddNotes": "Klik untuk tambah nota", "PE.Controllers.Main.txtArt": "<PERSON><PERSON> anda di sini", "PE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "<PERSON><PERSON>", "PE.Controllers.Main.txtCharts": "Carta", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "<PERSON><PERSON><PERSON> dan masa", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Tajuk <PERSON>", "PE.Controllers.Main.txtEditingMode": "Tetapkan mod pengeditan…", "PE.Controllers.Main.txtErrorLoadHistory": "<PERSON><PERSON><PERSON><PERSON> sejarah gagal", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON>", "PE.Controllers.Main.txtFooter": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtHeader": "Pen<PERSON>pal<PERSON>", "PE.Controllers.Main.txtImage": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Memuatkan", "PE.Controllers.Main.txtMath": "Matematik", "PE.Controllers.Main.txtMedia": "Media", "PE.Controllers.Main.txtNeedSynchronize": "Anda mempunyai kemas kini", "PE.Controllers.Main.txtNone": "Tiada", "PE.Controllers.Main.txtPicture": "Gambar", "PE.Controllers.Main.txtRectangles": "Segi Empat Tepat", "PE.Controllers.Main.txtSeries": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Petak Bual Garis 1 (<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Petak Bual Garis 2 (<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Petak Bual Garis 3 (<PERSON><PERSON>)", "PE.Controllers.Main.txtShape_accentCallout1": "Petak Bual Garis 1 (<PERSON>)", "PE.Controllers.Main.txtShape_accentCallout2": "Petak Bual Garis 2 (<PERSON>)", "PE.Controllers.Main.txtShape_accentCallout3": "Petak Bual Garis 3 (<PERSON>)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Kembali atau Butang Sebelumnya", "PE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonDocument": "Butang Dokumen", "PE.Controllers.Main.txtShape_actionButtonEnd": "Butang Tam<PERSON>", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON> atau <PERSON> Seterusnya", "PE.Controllers.Main.txtShape_actionButtonHelp": "Butang Bantu", "PE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonInformation": "Butang Informasi", "PE.Controllers.Main.txtShape_actionButtonMovie": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonReturn": "Butang <PERSON>", "PE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_arc": "<PERSON>g<PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentArrow": "Anak <PERSON>", "PE.Controllers.Main.txtShape_bentConnector5": "Penyambung Siku", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Penyambung Anak Panah Siku", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Penyambung Anak <PERSON>-<PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentUpArrow": "Anak Panah Lengkung Ke Atas", "PE.Controllers.Main.txtShape_bevel": "Serong", "PE.Controllers.Main.txtShape_blockArc": "Lengkung Blok", "PE.Controllers.Main.txtShape_borderCallout1": "Petak Bual Garis 1", "PE.Controllers.Main.txtShape_borderCallout2": "Petak Bual Garis 2", "PE.Controllers.Main.txtShape_borderCallout3": "Petak Bual Garis 3", "PE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_callout1": "Petak Bual Garis 1 (Tiada Sempadan)", "PE.Controllers.Main.txtShape_callout2": "Petak Bual Garis 2 (Tiada Sempadan)", "PE.Controllers.Main.txtShape_callout3": "Petak Bual Garis 3 (Tiada Sempadan)", "PE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_circularArrow": "Anak <PERSON>", "PE.Controllers.Main.txtShape_cloud": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Penyambung Anak Panah <PERSON>kung", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Penyambung Anak <PERSON>-Be<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedDownArrow": "Anak <PERSON> Melengkung Ke Bawah", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Anak <PERSON>", "PE.Controllers.Main.txtShape_curvedRightArrow": "Anak <PERSON> Kanan", "PE.Controllers.Main.txtShape_curvedUpArrow": "Anak <PERSON>ng Ke Atas", "PE.Controllers.Main.txtShape_decagon": "Dekagon", "PE.Controllers.Main.txtShape_diagStripe": "Belang Pepenjuru", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_donut": "Donat", "PE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_downArrow": "<PERSON>k panah ke bawah", "PE.Controllers.Main.txtShape_downArrowCallout": "Petak Bual Anak Panah Ke Bawah", "PE.Controllers.Main.txtShape_ellipse": "Elips", "PE.Controllers.Main.txtShape_ellipseRibbon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_ellipseRibbon2": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Carta Aliran: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartCollate": "Carta Aliran: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartConnector": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDecision": "Carta Aliran: Keputusan", "PE.Controllers.Main.txtShape_flowChartDelay": "Carta Aliran: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDisplay": "Carta Aliran: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDocument": "Carta Aliran: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartExtract": "Carta Aliran: Ekstrak", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Carta Aliran: Data", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Carta Aliran: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Carta Aliran: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartManualInput": "Carta Aliran: Input Manual", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Carta Aliran: Operasi Manual", "PE.Controllers.Main.txtShape_flowChartMerge": "Carta Aliran: Cantum", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Carta Aliran: Multidokumen", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Carta Aliran: Penyambung Luar-halaman", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Carta Aliran: Data Disimpan", "PE.Controllers.Main.txtShape_flowChartOr": "Carta Aliran: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Carta Aliran: <PERSON><PERSON>rata<PERSON>", "PE.Controllers.Main.txtShape_flowChartPreparation": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartProcess": "Carta Aliran: Proses", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Carta Aliran: Kad", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Carta Aliran: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartSort": "Carta Aliran: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Carta Aliran: Simpang Penambahan", "PE.Controllers.Main.txtShape_flowChartTerminator": "Carta Aliran: <PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_frame": "Rangka", "PE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_heptagon": "Heptagon", "PE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_homePlate": "Pentagon", "PE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_irregularSeal1": "Ledakan 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Ledakan 2", "PE.Controllers.Main.txtShape_leftArrow": "Anak panak kiri", "PE.Controllers.Main.txtShape_leftArrowCallout": "Petak Bual Anak <PERSON>", "PE.Controllers.Main.txtShape_leftBrace": "Tanda Kurung Dakap <PERSON>", "PE.Controllers.Main.txtShape_leftBracket": "Tanda Sempang Kiri", "PE.Controllers.Main.txtShape_leftRightArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Petak Bual Anak <PERSON>", "PE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftUpArrow": "Anak Panak ke Kiri At<PERSON>", "PE.Controllers.Main.txtShape_lightningBolt": "Panahan Petir", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>ah", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathDivide": "Bahagian", "PE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Dar<PERSON>", "PE.Controllers.Main.txtShape_mathNotEqual": "Tidak Sama", "PE.Controllers.Main.txtShape_mathPlus": "Tambah", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "Simbol \"Tiada\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Anak <PERSON> ke Kanan <PERSON>", "PE.Controllers.Main.txtShape_octagon": "Oktagon", "PE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "PE.Controllers.Main.txtShape_pentagon": "Pentagon", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plus": "Tambah", "PE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline2": "Bentuk <PERSON>", "PE.Controllers.Main.txtShape_quadArrow": "Anak <PERSON> Ganda Empat", "PE.Controllers.Main.txtShape_quadArrowCallout": "Petak Bual Anak Panah Ganda Empat", "PE.Controllers.Main.txtShape_rect": "Segi Empat Tepat", "PE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon2": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightArrowCallout": "Petak Bual Anak <PERSON>", "PE.Controllers.Main.txtShape_rightBrace": "Tanda Kurung <PERSON>", "PE.Controllers.Main.txtShape_rightBracket": "Tanda Kurung Kanan", "PE.Controllers.Main.txtShape_round1Rect": "Segi Empat Tepat Sudut Tunggal Bundar", "PE.Controllers.Main.txtShape_round2DiagRect": "Segi Empat Tepat Sudut Pepenjuru Bundar", "PE.Controllers.Main.txtShape_round2SameRect": "Segi Empat Tepat Sudut Sisi Sama Bundar", "PE.Controllers.Main.txtShape_roundRect": "Segi Empat Tepat Sudut Bundar", "PE.Controllers.Main.txtShape_rtTriangle": "Segi Tiga Kanan", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON> se<PERSON>um", "PE.Controllers.Main.txtShape_snip1Rect": "Segi Empat Tepat Sudut Tunggal Digunting", "PE.Controllers.Main.txtShape_snip2DiagRect": "Segi Empat Tepat Sudut Pepenjuru Digunting", "PE.Controllers.Main.txtShape_snip2SameRect": "Segi Empat Tepat Sudut Sisi Sama Digunting", "PE.Controllers.Main.txtShape_snipRoundRect": "Segi Empat Tepat Sudut Tunggal Digunting dan <PERSON>", "PE.Controllers.Main.txtShape_spline": "Melengkung", "PE.Controllers.Main.txtShape_star10": "Bintang 10-<PERSON>", "PE.Controllers.Main.txtShape_star12": "Bintang 12-<PERSON>", "PE.Controllers.Main.txtShape_star16": "Bintang 16-<PERSON>", "PE.Controllers.Main.txtShape_star24": "Bintang 24-<PERSON>", "PE.Controllers.Main.txtShape_star32": "Bintang 32-<PERSON>", "PE.Controllers.Main.txtShape_star4": "Bintang 4-<PERSON>", "PE.Controllers.Main.txtShape_star5": "Bintang 5-<PERSON>", "PE.Controllers.Main.txtShape_star6": "Bintang 6-<PERSON>", "PE.Controllers.Main.txtShape_star7": "Bintang 7-<PERSON>", "PE.Controllers.Main.txtShape_star8": "Bintang 8-<PERSON>", "PE.Controllers.Main.txtShape_stripedRightArrow": "Anak <PERSON> ke Kanan <PERSON>", "PE.Controllers.Main.txtShape_sun": "Matahari", "PE.Controllers.Main.txtShape_teardrop": "Titisan air mata", "PE.Controllers.Main.txtShape_textRect": "Kotak Teks", "PE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "PE.Controllers.Main.txtShape_triangle": "Segi tiga", "PE.Controllers.Main.txtShape_upArrow": "Anak panah ke atas", "PE.Controllers.Main.txtShape_upArrowCallout": "Petak Bual Anak Panah <PERSON> Atas", "PE.Controllers.Main.txtShape_upDownArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_uturnArrow": "Anak <PERSON> Pusingan U", "PE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wave": "Gelombang", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Petak Bual Segi Empat Tepat", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Petak Bual Segi Empat Tepat Bulat", "PE.Controllers.Main.txtSldLtTBlank": "Kosong", "PE.Controllers.Main.txtSldLtTChart": "Carta", "PE.Controllers.Main.txtSldLtTChartAndTx": "<PERSON>ta dan <PERSON>", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "<PERSON><PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "<PERSON><PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTCust": "Tersuai", "PE.Controllers.Main.txtSldLtTDgm": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTFourObj": "Empat Objek", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media & Teks", "PE.Controllers.Main.txtSldLtTObj": "Tajuk dan <PERSON>", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON> Objek", "PE.Controllers.Main.txtSldLtTObjAndTx": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objek melimpahi Teks", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON>", "PE.Controllers.Main.txtSldLtTPicTx": "<PERSON><PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTSecHead": "Bahagian Pengepala", "PE.Controllers.Main.txtSldLtTTbl": "Jadual", "PE.Controllers.Main.txtSldLtTTitle": "Tajuk", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Teks <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Dua Objek dan <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "<PERSON>a Objek dan <PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Dua Objek melimpahi Teks", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Dua Teks dan <PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTx": "Teks", "PE.Controllers.Main.txtSldLtTTxAndChart": "<PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "<PERSON><PERSON> dan <PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Te<PERSON> dan Media", "PE.Controllers.Main.txtSldLtTTxAndObj": "<PERSON><PERSON> dan <PERSON>", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "<PERSON>ks dan <PERSON>", "PE.Controllers.Main.txtSldLtTTxOverObj": "Teks melepasi Objek", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Tajuk dan <PERSON>", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Tajuk dan Teks Menegak di Atas Carta", "PE.Controllers.Main.txtSldLtTVertTx": "<PERSON><PERSON>", "PE.Controllers.Main.txtSlideNumber": "Nombor slaid", "PE.Controllers.Main.txtSlideSubtitle": "Subtajuk slaid", "PE.Controllers.Main.txtSlideText": "Teks slaid", "PE.Controllers.Main.txtSlideTitle": "Tajuk slaid", "PE.Controllers.Main.txtStarsRibbons": "Bintang & Reben", "PE.Controllers.Main.txtTheme_basic": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_blank": "Kosong", "PE.Controllers.Main.txtTheme_classic": "Klasik", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "Office", "PE.Controllers.Main.txtTheme_office_theme": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_official": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_pixel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "<PERSON><PERSON>", "PE.Controllers.Main.txtXAxis": "Paksi X", "PE.Controllers.Main.txtYAxis": "Paksi X", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> t<PERSON>.", "PE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> anda tidak disokong.", "PE.Controllers.Main.uploadImageExtMessage": "Format imej yang tidak diketahui.", "PE.Controllers.Main.uploadImageFileCountMessage": "Tiada Imej dimuat naik.", "PE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON><PERSON> terlalu besar. <PERSON><PERSON> maksimum adalah 25 MB.", "PE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON> dimuat naik…", "PE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON> dimuat naik", "PE.Controllers.Main.waitText": "Sila, tunggu…", "PE.Controllers.Main.warnBrowserIE9": "Aplikasi memiliki Ke<PERSON>an yang rendah pada IE9. Guna IE10 atau lebih tinggi", "PE.Controllers.Main.warnBrowserZoom": "Seting zum semasa pelayar anda tidak disokong sepenuhnya. <PERSON><PERSON> set semula kepada zum lalai dengan menekan Ctrl+0.", "PE.Controllers.Main.warnLicenseExceeded": "<PERSON><PERSON> <PERSON> had pengguna untuk sambungan serentak kepada editor %1. Do<PERSON>men ini akan dibuka untuk dilihat sahaja.<br>Hubung<PERSON> pentadbir anda untuk ketahui selanjutnya.", "PE.Controllers.Main.warnLicenseExp": "Lesen anda telah tamat tempoh.<br><PERSON><PERSON> kemas kini lesen anda dan segarkan halaman.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "<PERSON>en tamat tempoh.<br><PERSON>a tidak mempunyai akses terhadap fungsi pengeditan dokumen.<br><PERSON><PERSON> hubungi pentadbir anda.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Lesen perlu untuk diperbaharui.<br>Anda mempunyai akses terhad kepada fungi pengeditan dokumen.<br><PERSON>la hubungi pentadbir anda untuk mendapatkan akses penuh", "PE.Controllers.Main.warnLicenseUsersExceeded": "<PERSON><PERSON> te<PERSON> had pengguna untuk editor %1. <PERSON><PERSON><PERSON><PERSON> pentadbir anda untuk ketahui selanjutnya.", "PE.Controllers.Main.warnNoLicense": "<PERSON><PERSON> had pengguna untuk sambungan serentak kepada editor %1. Dokumen ini akan dibuka untuk dilihat sahaja.<br>Hubungi pasukan jualan %1 untuk naik taraf terma peribadi.", "PE.Controllers.Main.warnNoLicenseUsers": "<PERSON><PERSON> had pengguna untuk editor %1. Hubungi pasukan jualan %1 untuk naik taraf terma peribadi.", "PE.Controllers.Main.warnProcessRightsChange": "<PERSON>a telah di<PERSON>lak hak untuk edit fail.", "PE.Controllers.Search.notcriticalErrorTitle": "<PERSON><PERSON>", "PE.Controllers.Search.textNoTextFound": "Data yang andaa sedang cari tidak dijumpai. Sila laras pilihan carian.", "PE.Controllers.Search.textReplaceSkipped": "<PERSON><PERSON>an telah dilakukan. {0} kejadian telah dilangkau.", "PE.Controllers.Search.textReplaceSuccess": "<PERSON><PERSON> telah dilakukan. Kejadian {0} telah digantikan", "PE.Controllers.Search.warnReplaceString": "{0} bukan aksara khas yang sah untuk Gantikan Dengan kotak.", "PE.Controllers.Statusbar.textDisconnect": "<b>Sambungan telah hilang</b><br>Sedang cuba untuk menyambung. Sila semak seting sambungan.", "PE.Controllers.Statusbar.zoomText": "Zum {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Fon yang anda akan simpan tidak tersedia pada peranti semasa.<br>Gaya teks akan dipaparkan menggunakan satu daripada fon system, fon yang disimpan akan digunakan apabila ia tersedia.<br><PERSON><PERSON><PERSON> anda mahu teruskan?", "PE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textBracket": "<PERSON>da kurung", "PE.Controllers.Toolbar.textEmptyImgUrl": "<PERSON>a perlu menentukan imej URL.", "PE.Controllers.Toolbar.textFontSizeErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai berangka di antara 1 dan 300", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textInsert": "<PERSON>sip<PERSON>", "PE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Operator <PERSON>", "PE.Controllers.Toolbar.textLimitAndLog": "Had dan logaritma", "PE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON>", "PE.Controllers.Toolbar.textOperator": "Operator", "PE.Controllers.Toolbar.textRadical": "Radikal", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Simbol", "PE.Controllers.Toolbar.textWarning": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Anak panah kiri-kanan atas", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Anak panah arah kiri atas", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Anak panah arah kanan atas", "PE.Controllers.Toolbar.txtAccent_Bar": "Bar", "PE.Controllers.Toolbar.txtAccent_BarBot": "Bar bawah", "PE.Controllers.Toolbar.txtAccent_BarTop": "Melepasi bar", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Formula berkotak (dengan ruang letak)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula berkotak (contoh)", "PE.Controllers.Toolbar.txtAccent_Check": "Semak", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON><PERSON><PERSON> bawah", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Pendakap atas", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Dengan Bar Atas", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y dengan bar atas", "PE.Controllers.Toolbar.txtAccent_DDDot": "Bintik Ganda Tiga", "PE.Controllers.Toolbar.txtAccent_DDot": "Titik berganda", "PE.Controllers.Toolbar.txtAccent_Dot": "Titik", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Bar atas berkembar", "PE.Controllers.Toolbar.txtAccent_Grave": "Grava", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Kumpulkan aksara di bawah", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Kumpulkan aksara di atas", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Tempuling arah kiri atas", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Tempulin<PERSON> <PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON>i", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON> (dua keadaan)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON> (tiga keadaan)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON> kes", "PE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON>li Binomial", "PE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON>li Binomial", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON>da kurung dengan pemisah", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON>da kurung", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Tanda kurung tunggal", "PE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Pembezaan", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Pembezaan", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Pembezaan", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Pembezaan", "PE.Controllers.Toolbar.txtFractionHorizontal": "Pecahan Linear", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi melebihi 2", "PE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON>han kecil", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON>gsi kosinus <PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Fungsi kosinus hiperbola songsang", "PE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON>gsi kotangen songsang", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Fungsi kotangen hiperbola songsang", "PE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON>i kosekan songsang", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Fungsi kosekan hiperbola songsang", "PE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sekan <PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Fungsi sekan hiperbola songsang", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Fungsi sinus songsang", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Fungsi sinus hiperbola songsang", "PE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON>i tangen songsang", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Fungsi tangen hiperbola songsang", "PE.Controllers.Toolbar.txtFunction_Cos": "Fungsi kosinus", "PE.Controllers.Toolbar.txtFunction_Cosh": "Fungsi kosinus hiperbola", "PE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON>i kotangen", "PE.Controllers.Toolbar.txtFunction_Coth": "Fungsi kotangen hiperbola", "PE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Csch": "Fungsi kosekan hiperbola", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Theta sin", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Kos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Formula Tangen", "PE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON> sekan", "PE.Controllers.Toolbar.txtFunction_Sech": "Fungsi sekan hiperbola", "PE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sinh": "Fungsi sinus hiperbola", "PE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON>i tangen", "PE.Controllers.Toolbar.txtFunction_Tanh": "Fungsi tangen hiperbola", "PE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Pembezaan theta", "PE.Controllers.Toolbar.txtIntegral_dx": "Pembezaan x", "PE.Controllers.Toolbar.txtIntegral_dy": "Pembezaan y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralTriple": "Ka<PERSON>an Ganda tiga", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Ka<PERSON>an Ganda tiga", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Ka<PERSON>an Ganda tiga", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Produk-sampingan", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Produk-sampingan", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Produk-sampingan", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Produk-sampingan", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Produk-sampingan", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Kesatuan", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produk", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Kesatuan", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Kesatuan", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Kesatuan", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Kesatuan", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Kesatuan", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON> had", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Had", "PE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logarit<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarit<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 matriks kosong", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> garis asas", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Titik pepenjuru", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Bintik menegak", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 matriks identiti", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 matriks identiti", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 matriks identiti", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 matriks identiti", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON>k panah kiri-kanan bawah", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Anak panah kiri-kanan atas", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Anak panah arah kiri bawah", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Anak panah arah kiri atas", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Anak panah arah kanan atas", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Sama titik bertindih", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Yield", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta yield", "PE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON> dengan mengikut definisi", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta sama dengan", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON>k panah kiri-kanan bawah", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Anak panah kiri-kanan atas", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Anak panah arah kiri bawah", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Anak panah arah kiri atas", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Anak panah arah kanan atas", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON>a sama", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON> sama", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Tambah sama", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> kuasa dua dengan darjah", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON>asa dua", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radikal dengan darjah", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> kuasa dua", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Subskrip", "PE.Controllers.Toolbar.txtScriptSubSup": "Subskrip-superskrip", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "<PERSON><PERSON>-Superskrip", "PE.Controllers.Toolbar.txtScriptSup": "Superskrip", "PE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_additional": "Komplimen", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON> sama kepada", "PE.Controllers.Toolbar.txtSymbol_ast": "Operator asterik", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_bullet": "Bulet operator", "PE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Punca Kuasa Tiga", "PE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON> mendatar perten<PERSON> baris", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "Ci", "PE.Controllers.Toolbar.txtSymbol_cong": "Anggaran sama kepada", "PE.Controllers.Toolbar.txtSymbol_cup": "Kesatuan", "PE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_degree": "Darjah", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON> bah<PERSON>an", "PE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON>k panah ke bawah", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Set kosong", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON> kepada", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Di sana wujud", "PE.Controllers.Toolbar.txtSymbol_factorial": "Pemfaktoran", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Darjah Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "Untuk semua", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> besar dari atau sama dengan", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> besar da<PERSON>", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON> besar dari", "PE.Controllers.Toolbar.txtSymbol_in": "Elemen bagi", "PE.Controllers.Toolbar.txtSymbol_inc": "Kenaikan", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infiniti", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Anak panak kiri", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Anak panah ke kiri-kanan", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> da<PERSON>ada atau sama dengan", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON><PERSON> kurang da<PERSON>", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Tolak Tambah", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Tidak sama kepada", "PE.Controllers.Toolbar.txtSymbol_ni": "Dikandung sebagai ahli", "PE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON> petanda", "PE.Controllers.Toolbar.txtSymbol_notexists": "Di sana tidak wujud", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omikron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Perbezaan separa", "PE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Tambah", "PE.Controllers.Toolbar.txtSymbol_pm": "Tambah Tolak", "PE.Controllers.Toolbar.txtSymbol_propto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Punca kuasa empat", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> bukti", "PE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_rho": "Ro", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Tanda Radikal", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> itu", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Anak panah ke atas", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Varian Epsilon", "PE.Controllers.Toolbar.txtSymbol_varphi": "V<PERSON>", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Varian <PERSON>", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Varian theta", "PE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Muat kepada Slaid", "PE.Controllers.Viewport.textFitWidth": "Muat kepada <PERSON>", "PE.Views.Animation.str0_5": "0.5 s (Sangat Pantas)", "PE.Views.Animation.str1": "1 s (Pantas)", "PE.Views.Animation.str2": "2 s (Medium)", "PE.Views.Animation.str20": "20s (<PERSON><PERSON><PERSON><PERSON>)", "PE.Views.Animation.str3": "3 s (<PERSON><PERSON><PERSON>)", "PE.Views.Animation.str5": "5 s (<PERSON><PERSON>)", "PE.Views.Animation.strDelay": "Tangguh", "PE.Views.Animation.strDuration": "Tempoh", "PE.Views.Animation.strRepeat": "Ulang", "PE.Views.Animation.strRewind": "<PERSON><PERSON><PERSON> semula", "PE.Views.Animation.strStart": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strTrigger": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textAutoPreview": "AutoPratonton", "PE.Views.Animation.textMoreEffects": "Tunjuk <PERSON>", "PE.Views.Animation.textMoveEarlier": "<PERSON><PERSON>", "PE.Views.Animation.textMoveLater": "<PERSON><PERSON>", "PE.Views.Animation.textMultiple": "Pelbagai", "PE.Views.Animation.textNone": "Tiada", "PE.Views.Animation.textNoRepeat": "(tiada)", "PE.Views.Animation.textOnClickOf": "Pada Klik bagi", "PE.Views.Animation.textOnClickSequence": "Pada Klik Jujukan", "PE.Views.Animation.textStartAfterPrevious": "Selepas <PERSON>", "PE.Views.Animation.textStartOnClick": "Pada Klik", "PE.Views.Animation.textStartWithPrevious": "<PERSON><PERSON>", "PE.Views.Animation.textUntilEndOfSlide": "Hingga Ke Akhir Slaid", "PE.Views.Animation.textUntilNextClick": "Hingga Klik Seterusnya", "PE.Views.Animation.txtAddEffect": "Tambah animasi", "PE.Views.Animation.txtAnimationPane": "Panel Animasi", "PE.Views.Animation.txtParameters": "Parameter", "PE.Views.Animation.txtPreview": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "<PERSON><PERSON>", "PE.Views.AnimationDialog.textTitle": "<PERSON><PERSON>", "PE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "PE.Views.ChartSettings.textChartType": "U<PERSON>", "PE.Views.ChartSettings.textEditData": "Edit Data", "PE.Views.ChartSettings.textHeight": "Ketinggian", "PE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "PE.Views.ChartSettings.textSize": "Saiz", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON>", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "Teks Berselang", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Perihalan", "PE.Views.ChartSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka memahami dengan lebih baik maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Tajuk", "PE.Views.ChartSettingsAdvanced.textCenter": "Pusat", "PE.Views.ChartSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHeight": "Ketinggian", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Melintang", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "PE.Views.ChartSettingsAdvanced.textPlacement": "Tempatkan", "PE.Views.ChartSettingsAdvanced.textPosition": "Kedudukan", "PE.Views.ChartSettingsAdvanced.textSize": "Saiz", "PE.Views.ChartSettingsAdvanced.textTitle": "Carta – Seting Lanjutan", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Tetapkan format lalai bagi {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Tetapkan sebagai lalai", "PE.Views.DateTimeDialog.textFormat": "Format", "PE.Views.DateTimeDialog.textLang": "Bahasa", "PE.Views.DateTimeDialog.textUpdate": "<PERSON><PERSON> kini secara automatik", "PE.Views.DateTimeDialog.txtTitle": "Tarikh & Masa", "PE.Views.DocumentHolder.aboveText": "Di atas", "PE.Views.DocumentHolder.addCommentText": "Tambah Komen", "PE.Views.DocumentHolder.addToLayoutText": "Tambah kepada Talaletak", "PE.Views.DocumentHolder.advancedImageText": "Seting <PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.advancedParagraphText": "<PERSON>enggan Seting Lanjutan", "PE.Views.DocumentHolder.advancedShapeText": "Bentuk Seting Lanjutan", "PE.Views.DocumentHolder.advancedTableText": "Jadual <PERSON>ing Lanju<PERSON>", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.belowText": "<PERSON>", "PE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.cellText": "<PERSON>l", "PE.Views.DocumentHolder.centerText": "Pusat", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.deleteTableText": "Padam Jadual", "PE.Views.DocumentHolder.deleteText": "Padam", "PE.Views.DocumentHolder.direct270Text": "Putar Teks Ke Atas", "PE.Views.DocumentHolder.direct90Text": "Putar Teks Ke Bawah", "PE.Views.DocumentHolder.directHText": "Melintang", "PE.Views.DocumentHolder.directionText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.editChartText": "Edit Data", "PE.Views.DocumentHolder.editHyperlinkText": "<PERSON>", "PE.Views.DocumentHolder.hyperlinkText": "Hiperpautan", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowAboveText": "Di Atas Baris", "PE.Views.DocumentHolder.insertRowBelowText": "Di Bawah Baris", "PE.Views.DocumentHolder.insertRowText": "Sisipkan Baris", "PE.Views.DocumentHolder.insertText": "<PERSON>sip<PERSON>", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.leftText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.loadSpellText": "Varian dimuatkan…", "PE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.mniCustomTable": "Sisipkan Jadual Tersuai", "PE.Views.DocumentHolder.moreText": "V<PERSON> selanju<PERSON>…", "PE.Views.DocumentHolder.noSpellVariantsText": "<PERSON><PERSON><PERSON> varian", "PE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.splitCellTitleText": "Pi<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "Jadual", "PE.Views.DocumentHolder.textArrangeBack": "Hantar ke Latar Belakang", "PE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON> ke belakang", "PE.Views.DocumentHolder.textArrangeForward": "Bawa Ke <PERSON>", "PE.Views.DocumentHolder.textArrangeFront": "Bawa ke Tanah", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "Potong", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Muat", "PE.Views.DocumentHolder.textCut": "Potong", "PE.Views.DocumentHolder.textDistributeCols": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textEditPoints": "<PERSON>", "PE.Views.DocumentHolder.textFlipH": "Balikka<PERSON>", "PE.Views.DocumentHolder.textFlipV": "Balikkan Secar<PERSON>", "PE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFromUrl": "Dari URL", "PE.Views.DocumentHolder.textNextPage": "Slaid Seterusnya", "PE.Views.DocumentHolder.textPaste": "<PERSON>l", "PE.Views.DocumentHolder.textPrevPage": "Slaid Sebelumnya", "PE.Views.DocumentHolder.textReplace": "Gantikan imej", "PE.Views.DocumentHolder.textRotate": "Putar", "PE.Views.DocumentHolder.textRotate270": "Putar 90° Lawan Arah Jam", "PE.Views.DocumentHolder.textRotate90": "Putar 90° Ikut Arah Jam", "PE.Views.DocumentHolder.textShapeAlignBottom": "Jajarka<PERSON>", "PE.Views.DocumentHolder.textShapeAlignCenter": "Jajarkan <PERSON>", "PE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Jajarkan <PERSON>", "PE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignTop": "Jajarkan <PERSON>", "PE.Views.DocumentHolder.textSlideSettings": "Seting Slaid", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>t semula", "PE.Views.DocumentHolder.tipIsLocked": "Elemen ini kini sedang diedit oleh pengguna lain.", "PE.Views.DocumentHolder.toDictionaryText": "Tam<PERSON>", "PE.Views.DocumentHolder.txtAddBottom": "Tambah sempadan bawah", "PE.Views.DocumentHolder.txtAddFractionBar": "Tambah bar pecahan", "PE.Views.DocumentHolder.txtAddHor": "Tam<PERSON> garis melintang", "PE.Views.DocumentHolder.txtAddLB": "Tambah sempadan bawah kiri", "PE.Views.DocumentHolder.txtAddLeft": "Tambah sempadan kiri", "PE.Views.DocumentHolder.txtAddLT": "Tambah garis atas kiri", "PE.Views.DocumentHolder.txtAddRight": "Tambah sempadan kanan", "PE.Views.DocumentHolder.txtAddTop": "Tambah sempadan atas", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON> garis menegak", "PE.Views.DocumentHolder.txtAlign": "Jajar", "PE.Views.DocumentHolder.txtAlignToChar": "Jajarkan ke aksara", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtBottom": "<PERSON>wa<PERSON>", "PE.Views.DocumentHolder.txtChangeLayout": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtChangeTheme": "U<PERSON> tema", "PE.Views.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> saiz argument", "PE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON> argument", "PE.Views.DocumentHolder.txtDeleteBreak": "Padam pemisah manual", "PE.Views.DocumentHolder.txtDeleteChars": "<PERSON><PERSON> aksara <PERSON>n", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON> aksara lampiran dan pemisah", "PE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON> char", "PE.Views.DocumentHolder.txtDeleteRadical": "Padam radikal", "PE.Views.DocumentHolder.txtDeleteSlide": "Padam Slaid", "PE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDuplicateSlide": "Slaid Pendua", "PE.Views.DocumentHolder.txtFractionLinear": "Ubah ke pecahan linear", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> kepada pecahan terpencong", "PE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> ke pecahan bertindan", "PE.Views.DocumentHolder.txtGroup": "Ku<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtGroupCharOver": "<PERSON>ksara melebihi teks", "PE.Views.DocumentHolder.txtGroupCharUnder": "Aksara di bawah teks", "PE.Views.DocumentHolder.txtHideBottom": "Sembunyikan sempadan bawah", "PE.Views.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had bawah", "PE.Views.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON> tanda kurung tersemb<PERSON>yi", "PE.Views.DocumentHolder.txtHideDegree": "Sembunyikan darjah", "PE.Views.DocumentHolder.txtHideHor": "Sembunyikan garis melintang", "PE.Views.DocumentHolder.txtHideLB": "Sembunyikan garis kiri bawah", "PE.Views.DocumentHolder.txtHideLeft": "Sembunyikan sempadan kiri", "PE.Views.DocumentHolder.txtHideLT": "Sembunyikan garis kiri atas", "PE.Views.DocumentHolder.txtHideOpenBracket": "Sembunyikan bukaan tanda kurung", "PE.Views.DocumentHolder.txtHidePlaceholder": "Sembunyikan pemegang tempat", "PE.Views.DocumentHolder.txtHideRight": "Sembunyikan sempadan kanan", "PE.Views.DocumentHolder.txtHideTop": "Sembunyikan sempadan atas", "PE.Views.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had atas", "PE.Views.DocumentHolder.txtHideVer": "Sembunyikan garis menegak", "PE.Views.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON><PERSON> saiz argument", "PE.Views.DocumentHolder.txtInsertArgAfter": "Sisipkan argumen selepas", "PE.Views.DocumentHolder.txtInsertArgBefore": "Sisipkan argumen sebelum", "PE.Views.DocumentHolder.txtInsertBreak": "Sisipkan pemisah manual", "PE.Views.DocumentHolder.txtInsertEqAfter": "<PERSON>si<PERSON><PERSON> persa<PERSON>an se<PERSON>pas", "PE.Views.DocumentHolder.txtInsertEqBefore": "<PERSON>sip<PERSON> persamaan sebelum", "PE.Views.DocumentHolder.txtKeepTextOnly": "Kekalkan teks sahaja", "PE.Views.DocumentHolder.txtLimitChange": "Ubah lokasi had", "PE.Views.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON> had teks", "PE.Views.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON><PERSON> had teks", "PE.Views.DocumentHolder.txtMatchBrackets": "Sepadankan tanda kurung kepada ketinggian argument.", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON><PERSON> matriks", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "<PERSON><PERSON> slaid ke Hujung", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "<PERSON><PERSON> slaid ke Permulaan", "PE.Views.DocumentHolder.txtNewSlide": "Slaid Baharu", "PE.Views.DocumentHolder.txtOverbar": "Bar melepasi teks", "PE.Views.DocumentHolder.txtPasteDestFormat": "<PERSON><PERSON> tema destinasi", "PE.Views.DocumentHolder.txtPastePicture": "Gambar", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Kekalkan sumber pemformatan", "PE.Views.DocumentHolder.txtPressLink": "<PERSON><PERSON> {0} dan klik pautan", "PE.Views.DocumentHolder.txtPreview": "<PERSON><PERSON><PERSON> perse<PERSON><PERSON> slaid", "PE.Views.DocumentHolder.txtPrintSelection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON> keluar bar pecahan", "PE.Views.DocumentHolder.txtRemLimit": "<PERSON><PERSON> k<PERSON> had", "PE.Views.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON> keluar aksara aksen", "PE.Views.DocumentHolder.txtRemoveBar": "<PERSON><PERSON> keluar bar", "PE.Views.DocumentHolder.txtRemScripts": "<PERSON><PERSON> keluar skrip", "PE.Views.DocumentHolder.txtRemSubscript": "<PERSON><PERSON> keluar subskrip", "PE.Views.DocumentHolder.txtRemSuperscript": "<PERSON><PERSON> keluar superskrip", "PE.Views.DocumentHolder.txtResetLayout": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtScriptsAfter": "Skrip selepas teks", "PE.Views.DocumentHolder.txtScriptsBefore": "Skrip sebelum teks", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had bawah", "PE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> tanda kurung penutup", "PE.Views.DocumentHolder.txtShowDegree": "Tunjukkan darjah", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON> tanda kurung bukaan", "PE.Views.DocumentHolder.txtShowPlaceholder": "Tunjuk pemegang tempat", "PE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had atas", "PE.Views.DocumentHolder.txtSlide": "Slaid", "PE.Views.DocumentHolder.txtSlideHide": "Sembunyikan Slaid", "PE.Views.DocumentHolder.txtStretchBrackets": "Regang<PERSON> tanda <PERSON>", "PE.Views.DocumentHolder.txtTop": "Atas", "PE.Views.DocumentHolder.txtUnderbar": "Bar di bawah teks", "PE.Views.DocumentHolder.txtUngroup": "Leraikan", "PE.Views.DocumentHolder.txtWarnUrl": "Mengklik pautan ini boleh membahayakan peranti dan data anda.<br><PERSON><PERSON><PERSON> anda mahu men<PERSON>?", "PE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.goToSlideText": "<PERSON><PERSON> ke Slaid", "PE.Views.DocumentPreview.slideIndexText": "Slaid {0} of {1}", "PE.Views.DocumentPreview.txtClose": "<PERSON><PERSON><PERSON> perse<PERSON><PERSON> slaid", "PE.Views.DocumentPreview.txtEndSlideshow": "Tayangan slaid tamat", "PE.Views.DocumentPreview.txtExitFullScreen": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtFinalMessage": "Berakhirnya pratonton slaid. Klik untuk keluar.", "PE.Views.DocumentPreview.txtFullScreen": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtNext": "Slaid seterusnya", "PE.Views.DocumentPreview.txtPageNumInvalid": "Nombor slaid tidak sah", "PE.Views.DocumentPreview.txtPause": "<PERSON><PERSON>", "PE.Views.DocumentPreview.txtPlay": "<PERSON><PERSON><PERSON> per<PERSON>", "PE.Views.DocumentPreview.txtPrev": "Slaid sebelumnya", "PE.Views.DocumentPreview.txtReset": "<PERSON>", "PE.Views.FileMenu.btnAboutCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnBackCaption": "Buka lokasi fail", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnDownloadCaption": "Muat turun sebagai", "PE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnHelpCaption": "Bantu", "PE.Views.FileMenu.btnHistoryCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnInfoCaption": "Maklumat Persembahan", "PE.Views.FileMenu.btnPrintCaption": "Cetak", "PE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnReturnCaption": "<PERSON><PERSON><PERSON> kep<PERSON>", "PE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveAsCaption": "Simpan sebagai", "PE.Views.FileMenu.btnSaveCaption": "Simpan", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Simpan SALINAN sebagai", "PE.Views.FileMenu.btnSettingsCaption": "Seting <PERSON>", "PE.Views.FileMenu.btnToEditCaption": "<PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Tambah Pengarang", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tambah Teks", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Pengarang", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Ubah hak akses", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komen", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Diciptakan", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Pemilik", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Orang yang mempunyai hak", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subjek", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Tajuk", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON> naik", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Ubah hak akses", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Orang yang mempunyai hak", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>gan kata laluan", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON> tan<PERSON>n", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON> perse<PERSON>han", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Pengeditan akan membuang tandatangan daripada persembahan.<br><PERSON><PERSON><PERSON>?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Perse<PERSON>han ini telah dilindungi oleh kata laluan.", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Tandatangan yang sah telah ditambah pada persembahan. <PERSON><PERSON><PERSON><PERSON> dilind<PERSON> daripada editing.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Beberapa tandatangan digital dalam persembahan adalah tidak sah atau tidak dapat disahkan. Persembahan dilindungi daripada editing.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFast": "Pantas", "PE.Views.FileMenuPanels.Settings.strFontRender": "Pembayang Fon", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "<PERSON><PERSON><PERSON>n per<PERSON>an dalam HURUF BESAR", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Abaikan perkataan dengan nombor", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strPasteButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> butang <PERSON> di mana kandungan ditampalkan", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON>han daripada pengguna lain", "PE.Views.FileMenuPanels.Settings.strStrict": "Tegas", "PE.Views.FileMenuPanels.Settings.strTheme": "<PERSON>ma antara muka", "PE.Views.FileMenuPanels.Settings.strUnit": "Unit Pengukuran", "PE.Views.FileMenuPanels.Settings.strZoom": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Setiap 10 Minit", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Setiap 30 Minit", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Setiap 5 Minit", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Setiap Jam", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Panduan <PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Autopulih", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Autosimpan", "PE.Views.FileMenuPanels.Settings.textDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON><PERSON><PERSON><PERSON> versi per<PERSON>", "PE.Views.FileMenuPanels.Settings.textMinute": "Setiap <PERSON>", "PE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "<PERSON><PERSON>han AutoBaiki…", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtCm": "Sentimeter", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Pen<PERSON><PERSON>n dan men<PERSON>an", "PE.Views.FileMenuPanels.Settings.txtFastTip": "<PERSON><PERSON><PERSON><PERSON> bersama masa n<PERSON>, <PERSON><PERSON><PERSON> disimpan secara automatik.", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Muat kepada Slaid", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Muat kepada <PERSON>", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtInch": "Inci", "PE.Views.FileMenuPanels.Settings.txtLast": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtMac": "sebagai OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Mendayakan semua makro tanpa pemberitahuan", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Nyahdayakan semua makro tanpa pemberitahuan", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "<PERSON><PERSON> butang “Simpan” untuk segerakkan perubahan yang anda dan orang lain lakukan", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Guna kunci Alt untuk navigasi pengguna", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Guna kunci Pilihan untuk navigasi antara muka pengguna menggunakan papan kekunci", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Tunjuk Pemberitahuan", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Nyahdayakan semua makro dengan pemberitahuan", "PE.Views.FileMenuPanels.Settings.txtWin": "sebagai Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON> kerja", "PE.Views.HeaderFooterDialog.applyAllText": "<PERSON><PERSON> kepada <PERSON>", "PE.Views.HeaderFooterDialog.applyText": "<PERSON><PERSON>", "PE.Views.HeaderFooterDialog.diffLanguage": "Anda tidak boleh guna format tarikh dalam Bahasa berbeza dari slaid induk.<br><PERSON>tuk mengubah induk, klik 'Apply to all' berbanding 'Apply'", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "<PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textDateTime": "<PERSON><PERSON><PERSON> dan masa", "PE.Views.HeaderFooterDialog.textFixed": "Tetapkan", "PE.Views.HeaderFooterDialog.textFooter": "Teks dalam pengaki", "PE.Views.HeaderFooterDialog.textFormat": "Format", "PE.Views.HeaderFooterDialog.textLang": "Bahasa", "PE.Views.HeaderFooterDialog.textNotTitle": "<PERSON><PERSON> tunjukkan pada tajuk slaid", "PE.Views.HeaderFooterDialog.textPreview": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textSlideNum": "Nombor slaid", "PE.Views.HeaderFooterDialog.textTitle": "Seting <PERSON>", "PE.Views.HeaderFooterDialog.textUpdate": "<PERSON><PERSON> kini secara automatik", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON> kepada", "PE.Views.HyperlinkSettingsDialog.textDefault": "Serpihan teks terpilih", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>en di sini", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON><PERSON> pautan di sini", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Ma<PERSON>kkan petua alatan di sini", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slaid Dalam <PERSON> Ini", "PE.Views.HyperlinkSettingsDialog.textSlides": "Slaid", "PE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTitle": "Seting hiperpautan", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Medan ini diperlukan", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Slaid Pertama", "PE.Views.HyperlinkSettingsDialog.txtLast": "Slaid Terakhir", "PE.Views.HyperlinkSettingsDialog.txtNext": "Slaid Seterusnya", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Medan ini perlu sebagai URL dalam format \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Slaid Sebelumnya", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Medan ini terhad kepada 2083 aksara", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slaid", "PE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "PE.Views.ImageSettings.textCrop": "Potong", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Muat", "PE.Views.ImageSettings.textCropToShape": "Potong ke bentuk", "PE.Views.ImageSettings.textEdit": "Edit", "PE.Views.ImageSettings.textEditObject": "<PERSON>", "PE.Views.ImageSettings.textFitSlide": "Muat kepada Slaid", "PE.Views.ImageSettings.textFlip": "Balikkan", "PE.Views.ImageSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON>", "PE.Views.ImageSettings.textFromUrl": "Dari URL", "PE.Views.ImageSettings.textHeight": "Ketinggian", "PE.Views.ImageSettings.textHint270": "Putar 90° Lawan Arah Jam", "PE.Views.ImageSettings.textHint90": "Putar 90° Ikut Arah Jam", "PE.Views.ImageSettings.textHintFlipH": "Balikka<PERSON>", "PE.Views.ImageSettings.textHintFlipV": "Balikkan Secar<PERSON>", "PE.Views.ImageSettings.textInsert": "Gantikan Imej", "PE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON>", "PE.Views.ImageSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>-baru ini", "PE.Views.ImageSettings.textRotate90": "Putar 90°", "PE.Views.ImageSettings.textRotation": "<PERSON><PERSON>", "PE.Views.ImageSettings.textSize": "Saiz", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Teks Berselang", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Perihalan", "PE.Views.ImageSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka memahami dengan lebih baik maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Tajuk", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Pusat", "PE.Views.ImageSettingsAdvanced.textFlipped": "Dibalikkan", "PE.Views.ImageSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHeight": "Ketinggian", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Melintang", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Melintang", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "Tempatkan", "PE.Views.ImageSettingsAdvanced.textPosition": "Kedudukan", "PE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textSize": "Saiz", "PE.Views.ImageSettingsAdvanced.textTitle": "Imej – Seting <PERSON>", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipChat": "Sembang", "PE.Views.LeftMenu.tipComments": "Komen", "PE.Views.LeftMenu.tipPlugins": "Plug masuk", "PE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipSlides": "Slaid", "PE.Views.LeftMenu.tipSupport": "Maklum Balas & Sokongan", "PE.Views.LeftMenu.tipTitles": "Tajuk", "PE.Views.LeftMenu.txtDeveloper": "MOD PEMAJU", "PE.Views.LeftMenu.txtEditor": "Editor <PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtLimit": "<PERSON>", "PE.Views.LeftMenu.txtTrial": "MOD PERCUBAAN", "PE.Views.LeftMenu.txtTrialDev": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "Sebelum", "PE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "PE.Views.ParagraphSettings.textAt": "Pada", "PE.Views.ParagraphSettings.textAtLeast": "Sekurang-kurangnya", "PE.Views.ParagraphSettings.textAuto": "Pelbagai", "PE.Views.ParagraphSettings.textExact": "Tepat sekali", "PE.Views.ParagraphSettings.txtAutoText": "Auto", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Tab yang tertentu akan muncul dalam medan ini", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndent": "Inden", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Sebelum", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Khas", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fon", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Inden & Penjarakan", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subskrip", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superskrip", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "PE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Pelbagai", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "J<PERSON>k <PERSON>", "PE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "Tepat sekali", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON> pertama", "PE.Views.ParagraphSettingsAdvanced.textHanging": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(tiada)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON> k<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textSet": "Tentukan", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Pusat", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Perenggan – Seting Lanjutan", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "PE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON>", "PE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON> <PERSON>", "PE.Views.RightMenu.txtParagraphSettings": "Seting per<PERSON>", "PE.Views.RightMenu.txtShapeSettings": "<PERSON>ing bentuk", "PE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON>pan <PERSON>", "PE.Views.RightMenu.txtSlideSettings": "Seting Slaid", "PE.Views.RightMenu.txtTableSettings": "Seting <PERSON>", "PE.Views.RightMenu.txtTextArtSettings": "Seting <PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strBackground": "<PERSON><PERSON> latar belakang", "PE.Views.ShapeSettings.strChange": "Ubah Autobentuk", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "PE.Views.ShapeSettings.strPattern": "Pola", "PE.Views.ShapeSettings.strShadow": "Tunjukkan bayang", "PE.Views.ShapeSettings.strSize": "Saiz", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Kelegapan", "PE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai di antara 0 pt dan 1584 pt.", "PE.Views.ShapeSettings.textColor": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textEmptyPattern": "Tiada Pola", "PE.Views.ShapeSettings.textFlip": "Balikkan", "PE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textFromUrl": "Dari URL", "PE.Views.ShapeSettings.textGradient": "<PERSON> gradien", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textHint270": "Putar 90° Lawan Arah Jam", "PE.Views.ShapeSettings.textHint90": "Putar 90° Ikut Arah Jam", "PE.Views.ShapeSettings.textHintFlipH": "Balikka<PERSON>", "PE.Views.ShapeSettings.textHintFlipV": "Balikkan Secar<PERSON>", "PE.Views.ShapeSettings.textImageTexture": "Gambar atau Tekstur", "PE.Views.ShapeSettings.textLinear": "Linear", "PE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPatternFill": "Pola", "PE.Views.ShapeSettings.textPosition": "Kedudukan", "PE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>-baru ini", "PE.Views.ShapeSettings.textRotate90": "Putar 90°", "PE.Views.ShapeSettings.textRotation": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.tipAddGradientPoint": "Tambah mata gradien", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "<PERSON><PERSON> keluar mata gradien", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "Pelapik Teks", "PE.Views.ShapeSettingsAdvanced.textAlt": "Teks Berselang", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Perihalan", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka memahami dengan lebih baik maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Tajuk", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>ah", "PE.Views.ShapeSettingsAdvanced.textAutofit": "AutoMuat", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBevel": "Serong", "PE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON> h<PERSON> be<PERSON>", "PE.Views.ShapeSettingsAdvanced.textCenter": "Pusat", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Bilangan <PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Saiz <PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Dibalikkan", "PE.Views.ShapeSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHeight": "Ketinggian", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Melintang", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Melintang", "PE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "PE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "Tidak Automuat", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Tempatkan", "PE.Views.ShapeSettingsAdvanced.textPosition": "Kedudukan", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON> ke muatkan teks", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShrink": "Teks", "PE.Views.ShapeSettingsAdvanced.textSize": "Saiz", "PE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON>jar<PERSON><PERSON> antara lajur", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON>asa dua", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Kotak Teks", "PE.Views.ShapeSettingsAdvanced.textTitle": "Bentuk – Seting Lanjutan", "PE.Views.ShapeSettingsAdvanced.textTop": "Atas", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Berat & Anak <PERSON>", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "Tiada", "PE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON>", "PE.Views.SignatureSettings.strDelete": "<PERSON><PERSON>", "PE.Views.SignatureSettings.strDetails": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strInvalid": "Tandatangan tidak sah", "PE.Views.SignatureSettings.strSign": "<PERSON><PERSON>", "PE.Views.SignatureSettings.strSignature": "Tandatangan", "PE.Views.SignatureSettings.strValid": "Tandatangan sah", "PE.Views.SignatureSettings.txtContinueEditing": "Edit juga", "PE.Views.SignatureSettings.txtEditWarning": "Pengeditan akan membuang tandatangan daripada persembahan.<br><PERSON><PERSON><PERSON>?", "PE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON><PERSON> anda mahu alih keluar tandatangan ini?<br>Ia tidak boleh dibuat asal.", "PE.Views.SignatureSettings.txtSigned": "Tandatangan yang sah telah ditambah pada persembahan. <PERSON><PERSON><PERSON><PERSON> dilind<PERSON> daripada editing.", "PE.Views.SignatureSettings.txtSignedInvalid": "Beberapa tandatangan digital dalam persembahan adalah tidak sah atau tidak dapat disahkan. Persembahan dilindungi daripada editing.", "PE.Views.SlideSettings.strBackground": "<PERSON><PERSON> latar belakang", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "PE.Views.SlideSettings.strFill": "<PERSON><PERSON>", "PE.Views.SlideSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "PE.Views.SlideSettings.strPattern": "Pola", "PE.Views.SlideSettings.strSlideNum": "Tunjukkan Nombor Slaid", "PE.Views.SlideSettings.strTransparency": "Kelegapan", "PE.Views.SlideSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "<PERSON><PERSON>", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "Tiada Pola", "PE.Views.SlideSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromStorage": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromUrl": "Dari URL", "PE.Views.SlideSettings.textGradient": "<PERSON> gradien", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.SlideSettings.textImageTexture": "Gambar atau Tekstur", "PE.Views.SlideSettings.textLinear": "Linear", "PE.Views.SlideSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPatternFill": "Pola", "PE.Views.SlideSettings.textPosition": "Kedudukan", "PE.Views.SlideSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textReset": "<PERSON>", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "<PERSON><PERSON>", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON>", "PE.Views.SlideSettings.tipAddGradientPoint": "Tambah mata gradien", "PE.Views.SlideSettings.tipRemoveGradientPoint": "<PERSON><PERSON> keluar mata gradien", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "<PERSON><PERSON>", "PE.Views.SlideshowSettings.textLoop": "Gelung berterusan sehingga ‘Esc’ ditekan", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Lanskap", "PE.Views.SlideSizeSettings.strPortrait": "Potret", "PE.Views.SlideSizeSettings.textHeight": "Ketinggian", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientasi Slide", "PE.Views.SlideSizeSettings.textSlideSize": "Saiz Slaid", "PE.Views.SlideSizeSettings.textTitle": "Seting Saiz <PERSON>", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "35 mm Slaid", "PE.Views.SlideSizeSettings.txtA3": "Kertas A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Kertas A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Kertas B4 (ICO) (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "Kertas B5 (ICO) (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "<PERSON>and<PERSON>", "PE.Views.SlideSizeSettings.txtCustom": "Tersuai", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON><PERSON> (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "<PERSON><PERSON><PERSON> (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Melepasi kepala", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "<PERSON><PERSON><PERSON> lebar", "PE.Views.Statusbar.goToPageText": "<PERSON><PERSON> ke Slaid", "PE.Views.Statusbar.pageIndexText": "Slaid {0} of {1}", "PE.Views.Statusbar.textShowBegin": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.textShowCurrent": "Tunjuk dari Slaid Semasa", "PE.Views.Statusbar.textShowPresenterView": "Tunjukkan Pan<PERSON> Penyampai", "PE.Views.Statusbar.tipAccessRights": "Uruskan hak akses dokumen", "PE.Views.Statusbar.tipFitPage": "Muat Kepada slaid", "PE.Views.Statusbar.tipFitWidth": "Muat kepada kelebaran", "PE.Views.Statusbar.tipPreview": "<PERSON><PERSON><PERSON> perse<PERSON><PERSON> slaid", "PE.Views.Statusbar.tipSetLang": "Tetapkan bahasa teks", "PE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON>", "PE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON> masuk", "PE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON> keluar", "PE.Views.Statusbar.txtPageNumInvalid": "Nombor slaid tidak sah", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON>", "PE.Views.TableSettings.deleteTableText": "Padam Jadual", "PE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON>", "PE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.splitCellTitleText": "Pi<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "PE.Views.TableSettings.textBackColor": "<PERSON><PERSON> latar belakang", "PE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON>", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textEdit": "Baris & Lajur", "PE.Views.TableSettings.textEmptyTemplate": "Tiada templat", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "Pen<PERSON>pal<PERSON>", "PE.Views.TableSettings.textHeight": "Ketinggian", "PE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON> sempadan yang anda mahu tukar menggunakan gaya yang dipilih di bawah", "PE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Tetapkan sempadan luaran dan semua sel dalaman", "PE.Views.TableSettings.tipBottom": "Tetapkan sempadan luaran", "PE.Views.TableSettings.tipInner": "Tetapkan garis dalaman sahaja", "PE.Views.TableSettings.tipInnerHor": "Tetapkan garis dalaman melintang sahaja", "PE.Views.TableSettings.tipInnerVert": "Tetapkan garis menegak dan melintang sahaja", "PE.Views.TableSettings.tipLeft": "Tetapkan sempadan luaran kiri sahaja", "PE.Views.TableSettings.tipNone": "Tetapkan tiada sempadan", "PE.Views.TableSettings.tipOuter": "Tetapkan sempadan luaran sahaja", "PE.Views.TableSettings.tipRight": "Tetapkan sempadan luaran kanan sahaja", "PE.Views.TableSettings.tipTop": "Tetapkan sempadan luaran atas sahaja", "PE.Views.TableSettings.txtNoBorders": "T<PERSON>da sempanan", "PE.Views.TableSettings.txtTable_Accent": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON>", "PE.Views.TableSettings.txtTable_MediumStyle": "Gaya Medium", "PE.Views.TableSettings.txtTable_NoGrid": "Tiada Grid", "PE.Views.TableSettings.txtTable_NoStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_TableGrid": "Jadual Grid", "PE.Views.TableSettings.txtTable_ThemedStyle": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAlt": "Teks Berselang", "PE.Views.TableSettingsAdvanced.textAltDescription": "Perihalan", "PE.Views.TableSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka memahami dengan lebih baik maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Tajuk", "PE.Views.TableSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "PE.Views.TableSettingsAdvanced.textCenter": "Pusat", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Guna margin lalai", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textFrom": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHeight": "Ketinggian", "PE.Views.TableSettingsAdvanced.textHorizontal": "Melintang", "PE.Views.TableSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "PE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textPlacement": "Tempatkan", "PE.Views.TableSettingsAdvanced.textPosition": "Kedudukan", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "Saiz", "PE.Views.TableSettingsAdvanced.textTitle": "Jadual – Seting <PERSON>", "PE.Views.TableSettingsAdvanced.textTop": "Atas", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "<PERSON><PERSON> latar belakang", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "PE.Views.TextArtSettings.strPattern": "Pola", "PE.Views.TextArtSettings.strSize": "Saiz", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Kelegapan", "PE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai di antara 0 pt dan 1584 pt.", "PE.Views.TextArtSettings.textColor": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textEmptyPattern": "Tiada Pola", "PE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textFromUrl": "Dari URL", "PE.Views.TextArtSettings.textGradient": "<PERSON> gradien", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textImageTexture": "Gambar atau Tekstur", "PE.Views.TextArtSettings.textLinear": "Linear", "PE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPatternFill": "Pola", "PE.Views.TextArtSettings.textPosition": "Kedudukan", "PE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "Templat", "PE.Views.TextArtSettings.textTexture": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "Transformasi", "PE.Views.TextArtSettings.tipAddGradientPoint": "Tambah mata gradien", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "<PERSON><PERSON> keluar mata gradien", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGranite": "Granit", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "PE.Views.Toolbar.capAddSlide": "Tambah Slaid", "PE.Views.Toolbar.capBtnAddComment": "Tambah Komen", "PE.Views.Toolbar.capBtnComment": "Komen", "PE.Views.Toolbar.capBtnDateTime": "Tarikh & Masa", "PE.Views.Toolbar.capBtnInsHeader": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnInsSymbol": "Simbol", "PE.Views.Toolbar.capBtnSlideNum": "Nombor Slaid", "PE.Views.Toolbar.capInsertAudio": "Audio", "PE.Views.Toolbar.capInsertChart": "Carta", "PE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertHyperlink": "Hiperpautan", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertShape": "Bentuk", "PE.Views.Toolbar.capInsertTable": "Jadual", "PE.Views.Toolbar.capInsertText": "Kotak Teks", "PE.Views.Toolbar.capInsertTextArt": "Lukisan Teks", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "Fail", "PE.Views.Toolbar.capTabHome": "<PERSON><PERSON>", "PE.Views.Toolbar.capTabInsert": "<PERSON>sip<PERSON>", "PE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniCustomTable": "Sisipkan Jadual Tersuai", "PE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON>j daripada URL", "PE.Views.Toolbar.mniInsertSSE": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniLowerCase": "huruf k<PERSON>il", "PE.Views.Toolbar.mniSentenceCase": "<PERSON><PERSON><PERSON> besar pada permulaan ayat.", "PE.Views.Toolbar.mniSlideAdvanced": "Seting <PERSON>", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniToggleCase": "hURUF tOGOL", "PE.Views.Toolbar.mniUpperCase": "HURUF BESAR", "PE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textAlignBottom": "Jajarkan teks ke bawah", "PE.Views.Toolbar.textAlignCenter": "Teks tengah", "PE.Views.Toolbar.textAlignJust": "Laraskan", "PE.Views.Toolbar.textAlignLeft": "Jajarkan teks kiri", "PE.Views.Toolbar.textAlignMiddle": "Jajarkan teks ke tengah", "PE.Views.Toolbar.textAlignRight": "Jajarkan teks kanan", "PE.Views.Toolbar.textAlignTop": "Jajarkan teks ke atas", "PE.Views.Toolbar.textArrangeBack": "Hantar ke Latar Belakang", "PE.Views.Toolbar.textArrangeBackward": "<PERSON><PERSON> ke belakang", "PE.Views.Toolbar.textArrangeForward": "Bawa Ke <PERSON>", "PE.Views.Toolbar.textArrangeFront": "Bawa ke Tanah", "PE.Views.Toolbar.textBold": "<PERSON><PERSON>", "PE.Views.Toolbar.textColumnsCustom": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsThree": "Lajur Tiga", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "PE.Views.Toolbar.textItalic": "Italik", "PE.Views.Toolbar.textListSettings": "<PERSON><PERSON>", "PE.Views.Toolbar.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>-baru ini", "PE.Views.Toolbar.textShapeAlignBottom": "Jajarka<PERSON>", "PE.Views.Toolbar.textShapeAlignCenter": "Jajarkan <PERSON>", "PE.Views.Toolbar.textShapeAlignLeft": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignMiddle": "Jajarkan <PERSON>", "PE.Views.Toolbar.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignTop": "Jajarkan <PERSON>", "PE.Views.Toolbar.textShowBegin": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textShowCurrent": "Tunjuk dari Slaid Semasa", "PE.Views.Toolbar.textShowPresenterView": "Tunjukkan Pan<PERSON> Penyampai", "PE.Views.Toolbar.textShowSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textStrikeout": "<PERSON><PERSON>", "PE.Views.Toolbar.textSubscript": "Subskrip", "PE.Views.Toolbar.textSuperscript": "Superskrip", "PE.Views.Toolbar.textTabAnimation": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabFile": "Fail", "PE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "PE.Views.Toolbar.textTabInsert": "<PERSON>sip<PERSON>", "PE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabTransitions": "Transisi", "PE.Views.Toolbar.textTabView": "Lihat", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON>", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON> bawah", "PE.Views.Toolbar.tipAddSlide": "Tambah slaid", "PE.Views.Toolbar.tipBack": "Kembali", "PE.Views.Toolbar.tipChangeCase": "<PERSON><PERSON> huruf", "PE.Views.Toolbar.tipChangeChart": "U<PERSON>", "PE.Views.Toolbar.tipChangeSlide": "Ubah tataletak slaid", "PE.Views.Toolbar.tipClearStyle": "Kosong<PERSON> gaya", "PE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON> skema warna", "PE.Views.Toolbar.tipColumns": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON>", "PE.Views.Toolbar.tipCut": "Potong", "PE.Views.Toolbar.tipDateTime": "<PERSON><PERSON><PERSON><PERSON> tarikh dan masa semasa", "PE.Views.Toolbar.tipDecFont": "Pengurangan saiz fon", "PE.Views.Toolbar.tipDecPrLeft": "Kurangkan inden", "PE.Views.Toolbar.tipEditHeader": "<PERSON> penga<PERSON>", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> fon", "PE.Views.Toolbar.tipFontName": "Fon", "PE.Views.Toolbar.tipFontSize": "Saiz fon", "PE.Views.Toolbar.tipHAligh": "<PERSON><PERSON><PERSON> melintang", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON>", "PE.Views.Toolbar.tipIncFont": "Kenaikan saiz font", "PE.Views.Toolbar.tipIncPrLeft": "Menaikkan inden", "PE.Views.Toolbar.tipInsertAudio": "Sisipkan audio", "PE.Views.Toolbar.tipInsertChart": "Sisipkan carta", "PE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertHyperlink": "Tambah hiperpautan", "PE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertShape": "Sisipkan autobentuk", "PE.Views.Toolbar.tipInsertSymbol": "<PERSON><PERSON><PERSON> simbol", "PE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> j<PERSON>", "PE.Views.Toolbar.tipInsertText": "Sisip kotak teks", "PE.Views.Toolbar.tipInsertTextArt": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertVideo": "Sisip video", "PE.Views.Toolbar.tipLineSpace": "<PERSON><PERSON><PERSON> garis", "PE.Views.Toolbar.tipMarkers": "Bullet", "PE.Views.Toolbar.tipMarkersArrow": "<PERSON><PERSON> anak panah", "PE.Views.Toolbar.tipMarkersCheckmark": "Bulet tanda semak", "PE.Views.Toolbar.tipMarkersDash": "Bulet tanda sengkang", "PE.Views.Toolbar.tipMarkersFRhombus": "<PERSON><PERSON> rombus diisi", "PE.Views.Toolbar.tipMarkersFRound": "<PERSON><PERSON> bulat diisi", "PE.Views.Toolbar.tipMarkersFSquare": "Bulet segi empat diisi", "PE.Views.Toolbar.tipMarkersHRound": "<PERSON><PERSON>", "PE.Views.Toolbar.tipMarkersStar": "<PERSON><PERSON> bintang", "PE.Views.Toolbar.tipNone": "Tiada", "PE.Views.Toolbar.tipNumbers": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPaste": "<PERSON>l", "PE.Views.Toolbar.tipPreview": "<PERSON><PERSON><PERSON> perse<PERSON><PERSON> slaid", "PE.Views.Toolbar.tipPrint": "Cetak", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "Simpan", "PE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> per<PERSON> anda untuk pengguna lain melihatnya.", "PE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipShapeArrange": "<PERSON><PERSON>", "PE.Views.Toolbar.tipSlideNum": "<PERSON><PERSON><PERSON> nombor slaid", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON> saiz slaid", "PE.Views.Toolbar.tipSlideTheme": "Tema slaid", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON>t semula", "PE.Views.Toolbar.tipVAligh": "Jajarka<PERSON>", "PE.Views.Toolbar.tipViewSettings": "<PERSON><PERSON> seting", "PE.Views.Toolbar.txtDistribHor": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtDistribVert": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtDuplicateSlide": "Slaid Pendua", "PE.Views.Toolbar.txtGroup": "Ku<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtObjectsAlign": "Jajarkan Obje<PERSON>", "PE.Views.Toolbar.txtScheme1": "Office", "PE.Views.Toolbar.txtScheme10": "Median", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme15": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme17": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme18": "Teknik", "PE.Views.Toolbar.txtScheme19": "Trek", "PE.Views.Toolbar.txtScheme2": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme20": "Bandar", "PE.Views.Toolbar.txtScheme21": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme22": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme3": "Puncak", "PE.Views.Toolbar.txtScheme4": "Aspek", "PE.Views.Toolbar.txtScheme5": "Sivik", "PE.Views.Toolbar.txtScheme6": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme7": "Ekuiti", "PE.Views.Toolbar.txtScheme8": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme9": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtSlideAlign": "Jajarkan ke Slaid", "PE.Views.Toolbar.txtUngroup": "Leraikan", "PE.Views.Transitions.strDelay": "Tangguh", "PE.Views.Transitions.strDuration": "Tempoh", "PE.Views.Transitions.strStartOnClick": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textBlack": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textBottom": "<PERSON>wa<PERSON>", "PE.Views.Transitions.textBottomLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textBottomRight": "Kanan-<PERSON>", "PE.Views.Transitions.textClock": "Jam", "PE.Views.Transitions.textClockwise": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textCounterclockwise": "Melawan arah jam", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalOut": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textLeft": "<PERSON><PERSON>", "PE.Views.Transitions.textNone": "Tiada", "PE.Views.Transitions.textPush": "<PERSON><PERSON>", "PE.Views.Transitions.textRight": "<PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "<PERSON><PERSON>", "PE.Views.Transitions.textSplit": "Pisah", "PE.Views.Transitions.textTop": "Atas", "PE.Views.Transitions.textTopLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textTopRight": "Kanan<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalOut": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textWedge": "<PERSON><PERSON>", "PE.Views.Transitions.textWipe": "Sapu", "PE.Views.Transitions.textZoom": "<PERSON><PERSON>", "PE.Views.Transitions.textZoomIn": "<PERSON><PERSON>", "PE.Views.Transitions.textZoomOut": "<PERSON><PERSON>", "PE.Views.Transitions.textZoomRotate": "<PERSON>um dan <PERSON>ar", "PE.Views.Transitions.txtApplyToAll": "Guna kepada Semua S<PERSON>", "PE.Views.Transitions.txtParameters": "Parameter", "PE.Views.Transitions.txtPreview": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textAlwaysShowToolbar": "<PERSON><PERSON><PERSON> tun<PERSON>n bar alatan", "PE.Views.ViewTab.textFitToSlide": "Muat Kepada Slaid", "PE.Views.ViewTab.textFitToWidth": "Muat kepada <PERSON>", "PE.Views.ViewTab.textInterfaceTheme": "<PERSON>ma antara muka", "PE.Views.ViewTab.textNotes": "<PERSON>a", "PE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textStatusBar": "Bar Status", "PE.Views.ViewTab.textZoom": "<PERSON><PERSON>", "PE.Views.ViewTab.tipFitToSlide": "Muat kepada Slaid", "PE.Views.ViewTab.tipFitToWidth": "Muat kepada <PERSON>", "PE.Views.ViewTab.tipInterfaceTheme": "<PERSON>ma antara muka"}