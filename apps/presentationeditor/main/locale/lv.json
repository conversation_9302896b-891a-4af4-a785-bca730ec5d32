{"Common.Controllers.Chat.notcriticalErrorTitle": "Warning", "Common.Controllers.Chat.textEnterMessage": "Enter your message here", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalDiagramEditor.textClose": "Close", "Common.Controllers.ExternalDiagramEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Warning", "Common.UI.ButtonColored.textAutoColor": "Automātisks", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "No borders", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "No borders", "Common.UI.ComboDataView.emptyComboText": "No styles", "Common.UI.ExtendedColorDialog.addButtonText": "Add", "Common.UI.ExtendedColorDialog.textCurrent": "Current", "Common.UI.ExtendedColorDialog.textHexErr": "The entered value is incorrect.<br>Please enter a value between 000000 and FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "The entered value is incorrect.<br>Please enter a numeric value between 0 and 255.", "Common.UI.HSBColorPicker.textNoColor": "No Color", "Common.UI.SearchDialog.textHighlight": "Highlight results", "Common.UI.SearchDialog.textMatchCase": "Case sensitive", "Common.UI.SearchDialog.textReplaceDef": "Enter the replacement text", "Common.UI.SearchDialog.textSearchStart": "Enter your text here", "Common.UI.SearchDialog.textTitle": "Find and Replace", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "Whole words only", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "Replace All", "Common.UI.SynchronizeTip.textDontShow": "Don't show this message again", "Common.UI.SynchronizeTip.textSynchronize": "The document has been changed by another user.<br>Please click to save your changes and reload the updates.", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON>", "Common.UI.Window.cancelButtonText": "Cancel", "Common.UI.Window.closeButtonText": "Close", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Don't show this message again", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Warning", "Common.UI.Window.yesButtonText": "Yes", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "address: ", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.Chat.textSend": "Send", "Common.Views.Comments.textAdd": "Add", "Common.Views.Comments.textAddComment": "Add Comment", "Common.Views.Comments.textAddCommentToDoc": "Add Comment to Document", "Common.Views.Comments.textAddReply": "Add Reply", "Common.Views.Comments.textAnonym": "Guest", "Common.Views.Comments.textCancel": "Cancel", "Common.Views.Comments.textClose": "Close", "Common.Views.Comments.textComments": "Comments", "Common.Views.Comments.textEdit": "Edit", "Common.Views.Comments.textEnterCommentHint": "Enter your comment here", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Open Again", "Common.Views.Comments.textReply": "Reply", "Common.Views.Comments.textResolve": "Resolve", "Common.Views.Comments.textResolved": "Resolved", "Common.Views.CopyWarningDialog.textDontShow": "Don't show this message again", "Common.Views.CopyWarningDialog.textMsg": "Copy, cut and paste actions using the editor toolbar buttons and context menu actions will be performed within this editor tab only.<br><br>To copy or paste to or from applications outside the editor tab use the following keyboard combinations:", "Common.Views.CopyWarningDialog.textTitle": "Copy, Cut and Paste Actions", "Common.Views.CopyWarningDialog.textToCopy": "for Co<PERSON>", "Common.Views.CopyWarningDialog.textToCut": "for Cut", "Common.Views.CopyWarningDialog.textToPaste": "for Paste", "Common.Views.DocumentAccessDialog.textLoading": "Loading...", "Common.Views.DocumentAccessDialog.textTitle": "Sharing Settings", "Common.Views.ExternalDiagramEditor.textTitle": "Chart Editor", "Common.Views.Header.labelCoUsersDescr": "Šobrīd dokumentu rediģē vairāki lietotāji.", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "Go to Documents", "Common.Views.Header.textCompactView": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveBegin": "Saglabā ...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Visas izmaiņas saglabātas", "Common.Views.Header.textSaveExpander": "Visas izmaiņas saglabātas", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipGoEdit": "Rediģēt šībrīža failu", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "Atsaukt", "Common.Views.Header.tipViewUsers": "<PERSON><PERSON><PERSON><PERSON>t lietotājus un pārvaldīt dokumentu piekļ<PERSON>", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ImageFromUrlDialog.textUrl": "Paste an image URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "This field is required", "Common.Views.ImageFromUrlDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "Common.Views.InsertTableDialog.textInvalidRowsCols": "You need to specify valid rows and columns number.", "Common.Views.InsertTableDialog.txtColumns": "Number of Columns", "Common.Views.InsertTableDialog.txtMaxText": "The maximum value for this field is {0}.", "Common.Views.InsertTableDialog.txtMinText": "The minimum value for this field is {0}.", "Common.Views.InsertTableDialog.txtRows": "Number of Rows", "Common.Views.InsertTableDialog.txtTitle": "Table Size", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Izvēlēties dokumenta valodu", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> failu", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "Parole nav pareiza.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON>eva<PERSON>t paroli, lai atvērtu failu", "Common.Views.OpenDialog.txtPassword": "Parole", "Common.Views.OpenDialog.txtTitle": "Izvēlēties %1 iespējas", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fails", "Common.Views.PasswordDialog.txtDescription": "<PERSON> pasa<PERSON>tu <PERSON>o dokumentu, uz<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtIncorrectPwd": "Aps<PERSON>rinājuma <PERSON>", "Common.Views.PasswordDialog.txtPassword": "Parole", "Common.Views.PasswordDialog.txtRepeat": "Atk<PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtWarning": "Uzmanību! Pazaudētu vai aizmirstu paroli nevar atgūt. Glabājiet drošā vietā.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar paroli", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vai d<PERSON>st paroli", "Common.Views.Protection.hintSignature": "<PERSON><PERSON><PERSON> para<PERSON>tu vai paraksta līniju", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignature": "Paraksts", "Common.Views.Protection.txtSignatureLine": "Paraksta līnija", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "<PERSON>aila nosaukums nedrīkst saturēt šādas z<PERSON>:", "Common.Views.ReviewChanges.hintNext": "Uz nākamo izmaiņu", "Common.Views.ReviewChanges.hintPrev": "Uz iepriekšē<PERSON> izmaiņu", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Kopīga rediģēšana reāllaikā. Visas izmaiņas tiek automātiski saglabātas.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"Saglabā<PERSON>\" tausti<PERSON><PERSON>, lai sinhronizētu sevis un citu veiktās izmaiņas.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON>g<PERSON> rediģēšanas režīmu", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> versiju vēst<PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON><PERSON><PERSON> š<PERSON>brīža izmaiņas", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.tipReviewView": "Izvēlēties režīmu, kurā vēlaties atainot izmaiņas", "Common.Views.ReviewChanges.tipSetDocLang": "Uzst<PERSON><PERSON><PERSON>t dokumenta valodu", "Common.Views.ReviewChanges.tipSetSpelling": "Pareizrakstī<PERSON>", "Common.Views.ReviewChanges.tipSharing": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Pieņemt visas izmaiņas", "Common.Views.ReviewChanges.txtAcceptChanges": "Pieņemt izmaiņas", "Common.Views.ReviewChanges.txtAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.txtChat": "Čats", "Common.Views.ReviewChanges.txtClose": "Aizvērt", "Common.Views.ReviewChanges.txtCoAuthMode": "Ko<PERSON>īgā<PERSON> rediģēšanas režīms", "Common.Views.ReviewChanges.txtDocLang": "Valoda", "Common.Views.ReviewChanges.txtFinal": "Visas izmaiņ<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtFinalCap": "Gala", "Common.Views.ReviewChanges.txtHistory": "Versiju vēsture", "Common.Views.ReviewChanges.txtMarkup": "Visa<PERSON> <PERSON><PERSON><PERSON> (rediģēšana)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Visas izmai<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtOriginalCap": "Oriģināls", "Common.Views.ReviewChanges.txtPrev": "Atpakaļ", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Noraidīt visas izmaiņas", "Common.Views.ReviewChanges.txtRejectChanges": "Nora<PERSON><PERSON><PERSON> izmaiņ<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON><PERSON><PERSON> š<PERSON>brīža izmaiņas", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Pareizrakstī<PERSON>", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.txtView": "Attēlošanas režī<PERSON>", "Common.Views.SignDialog.textBold": "Treknraksts", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Ievadiet parakstīt<PERSON><PERSON> vārdu", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textPurpose": "Šī dokumenta parakstīšanas mērķis", "Common.Views.SignDialog.textSelect": "Izvēlēties", "Common.Views.SignDialog.textSelectImage": "Izvēlēties attēlu", "Common.Views.SignDialog.textSignature": "Kā izskatās paraksts", "Common.Views.SignDialog.textTitle": "Parakstīt dokumentu", "Common.Views.SignDialog.textUseImage": "vai spiediet \"Izvē<PERSON>ē<PERSON> attēlu\", lai i<PERSON><PERSON>tu attēlu kā parakstu", "Common.Views.SignDialog.textValid": "Derīgs no %1 līdz %2", "Common.Views.SignDialog.tipFontName": "Fonts", "Common.Views.SignDialog.tipFontSize": "Fonta izmērs", "Common.Views.SignSettingsDialog.textAllowComment": "Atļaut parakstī<PERSON><PERSON><PERSON>m pievienot komentāru paraksta logā", "Common.Views.SignSettingsDialog.textInfoEmail": "E-pasts", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Parakstī<PERSON><PERSON><PERSON> amats", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> datumu paraksta līnijā", "Common.Views.SignSettingsDialog.textTitle": "Paraksta uzstādīšana", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> la<PERSON> ir j<PERSON>", "PE.Controllers.LeftMenu.newDocumentTitle": "Unnamed presentation", "PE.Controllers.LeftMenu.requestEditRightsText": "Requesting editing rights...", "PE.Controllers.LeftMenu.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "PE.Controllers.Main.applyChangesTextText": "Loading data...", "PE.Controllers.Main.applyChangesTitleText": "Loading Data", "PE.Controllers.Main.convertationTimeoutText": "Conversion timeout exceeded.", "PE.Controllers.Main.criticalErrorExtText": "Press \"OK\" to return to document list.", "PE.Controllers.Main.criticalErrorTitle": "Error", "PE.Controllers.Main.downloadErrorText": "Download failed.", "PE.Controllers.Main.downloadTextText": "Downloading presentation...", "PE.Controllers.Main.downloadTitleText": "Downloading Presentation", "PE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON><PERSON> mēģināt veikt da<PERSON>, kuru nedrīkstat veikt.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "PE.Controllers.Main.errorBadImageUrl": "Nav pareizs attēla URL", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Server connection lost. The document cannot be edited right now.", "PE.Controllers.Main.errorConnectToServer": "Dokumentu neizdevās nog<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet savienojuma uzstādījumus vai sazinieties ar savu administratoru.<br><PERSON><PERSON><PERSON><PERSON><PERSON> \"OK\", jūs varēsit lejupielādēt dokumentu.", "PE.Controllers.Main.errorDatabaseConnection": "External error.<br>Database connection error. Please contact support in case the error persists.", "PE.Controllers.Main.errorDataRange": "Incorrect data range.", "PE.Controllers.Main.errorDefaultMessage": "Error code: %1", "PE.Controllers.Main.errorFilePassProtect": "Fails ir aizsargāts ar paroli un to nevar atvērt.", "PE.Controllers.Main.errorForceSave": "Faila noglab<PERSON><PERSON><PERSON> laikā radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> iespēju \"Lejupielādēt kā\", lai noglabātu failu datora cietajā diskā, vai mēģiniet vēlāk vēlreiz.", "PE.Controllers.Main.errorKeyEncrypt": "Unknown key descriptor", "PE.Controllers.Main.errorKeyExpire": "Key descriptor expired", "PE.Controllers.Main.errorProcessSaveResult": "Saving failed.", "PE.Controllers.Main.errorServerVersion": "Redaktora versija ir at<PERSON>. Lapa tiks pā<PERSON>, lai pie<PERSON>rotu i<PERSON>.", "PE.Controllers.Main.errorSessionAbsolute": "Beidzies dokumenta rediģēšana sesijas laiks. <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON><PERSON> lapu.", "PE.Controllers.Main.errorSessionIdle": "Dokuments nav ticis rediģēts ilgāku laiku. <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON><PERSON> lapu.", "PE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON><PERSON><PERSON>ts savienojums serverim. <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON><PERSON> lapu.", "PE.Controllers.Main.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "PE.Controllers.Main.errorToken": "Nav pareizi noformēts dokumenta drošības marķieri.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumenta servera administratoru.", "PE.Controllers.Main.errorTokenExpire": "Ir beid<PERSON>t dokumenta drošības marķiera termiņš. <br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "PE.Controllers.Main.errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "PE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "PE.Controllers.Main.errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "PE.Controllers.Main.errorViewerDisconnect": "Pārtraukts savienojums. <PERSON><PERSON><PERSON>m varat aplūkot dokumentu,<br>ta<PERSON>u nevarē<PERSON>t lejupielādēt vai drukāt, līdz nav atjaunots savienojums.", "PE.Controllers.Main.leavePageText": "You have unsaved changes in this presentation. Click \"Stay on This Page\", then \"Save\" to save them. Click \"Leave This Page\" to discard all the unsaved changes.", "PE.Controllers.Main.loadFontsTextText": "Loading data...", "PE.Controllers.Main.loadFontsTitleText": "Loading Data", "PE.Controllers.Main.loadFontTextText": "Loading data...", "PE.Controllers.Main.loadFontTitleText": "Loading Data", "PE.Controllers.Main.loadImagesTextText": "Loading images...", "PE.Controllers.Main.loadImagesTitleText": "Loading Images", "PE.Controllers.Main.loadImageTextText": "Loading image...", "PE.Controllers.Main.loadImageTitleText": "Loading Image", "PE.Controllers.Main.loadingDocumentTextText": "Loading presentation...", "PE.Controllers.Main.loadingDocumentTitleText": "Loading presentation", "PE.Controllers.Main.loadThemeTextText": "Loading theme...", "PE.Controllers.Main.loadThemeTitleText": "Loading Theme", "PE.Controllers.Main.notcriticalErrorTitle": "Warning", "PE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON> k<PERSON>", "PE.Controllers.Main.openTextText": "Opening presentation...", "PE.Controllers.Main.openTitleText": "Opening Presentation", "PE.Controllers.Main.printTextText": "Printing presentation...", "PE.Controllers.Main.printTitleText": "Printing Presentation", "PE.Controllers.Main.reloadButtonText": "Reload Page", "PE.Controllers.Main.requestEditFailedMessageText": "Someone is editing this presentation right now. Please try again later.", "PE.Controllers.Main.requestEditFailedTitleText": "Access denied", "PE.Controllers.Main.saveErrorText": "<PERSON>aila nogla<PERSON><PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON>", "PE.Controllers.Main.saveTextText": "Prezentācijas <PERSON>", "PE.Controllers.Main.saveTitleText": "Prezentācijas <PERSON>", "PE.Controllers.Main.splitDividerErrorText": "The number of rows must be a divisor of %1.", "PE.Controllers.Main.splitMaxColsErrorText": "The number of columns must be less than %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "The number of rows must be less than %1.", "PE.Controllers.Main.textAnonymous": "Anonymous", "PE.Controllers.Main.textBuyNow": "Apmeklēt vietni", "PE.Controllers.Main.textChangesSaved": "Visas izmaiņas saglabātas", "PE.Controllers.Main.textCloseTip": "Click to close the tip", "PE.Controllers.Main.textContactUs": "Sazināties ar p<PERSON><PERSON>", "PE.Controllers.Main.textLoadingDocument": "Loading presentation", "PE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE pieslēguma ierobežojums", "PE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textStrict": "Strict mode", "PE.Controllers.Main.textText": "Teksts", "PE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "PE.Controllers.Main.titleLicenseExp": "Licencei beid<PERSON>", "PE.Controllers.Main.titleServerVersion": "Atjaunināts redaktors", "PE.Controllers.Main.txtAddFirstSlide": "Noklikšķiniet, lai pievienotu pirmo slaidu", "PE.Controllers.Main.txtAddNotes": "Noklikšķiniet, lai pievienotu piezīmes", "PE.Controllers.Main.txtArt": "Your text here", "PE.Controllers.Main.txtBasicShapes": "Basic Shapes", "PE.Controllers.Main.txtButtons": "Buttons", "PE.Controllers.Main.txtCallouts": "Callouts", "PE.Controllers.Main.txtCharts": "Charts", "PE.Controllers.Main.txtClipArt": "ClipArt", "PE.Controllers.Main.txtDateTime": "Datums un laiks", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Chart Title", "PE.Controllers.Main.txtEditingMode": "Set editing mode...", "PE.Controllers.Main.txtFiguredArrows": "Figured Arrows", "PE.Controllers.Main.txtFooter": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtHeader": "G<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLines": "Lines", "PE.Controllers.Main.txtLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ...", "PE.Controllers.Main.txtMath": "Math", "PE.Controllers.Main.txtMedia": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtNeedSynchronize": "You have updates", "PE.Controllers.Main.txtPicture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtRectangles": "Rectangles", "PE.Controllers.Main.txtSeries": "Series", "PE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTBlank": "Blank", "PE.Controllers.Main.txtSldLtTChart": "Chart", "PE.Controllers.Main.txtSldLtTChartAndTx": "Chart and Text", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art and Text", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art and Vertical Text", "PE.Controllers.Main.txtSldLtTCust": "Custom", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "Four Objects", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media and Text", "PE.Controllers.Main.txtSldLtTObj": "Title and Object", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Object and Two Objects", "PE.Controllers.Main.txtSldLtTObjAndTx": "Object and Text", "PE.Controllers.Main.txtSldLtTObjOnly": "Object", "PE.Controllers.Main.txtSldLtTObjOverTx": "Object over Text", "PE.Controllers.Main.txtSldLtTObjTx": "Title, Object, and Caption", "PE.Controllers.Main.txtSldLtTPicTx": "Picture and Caption", "PE.Controllers.Main.txtSldLtTSecHead": "Section Header", "PE.Controllers.Main.txtSldLtTTbl": "Table", "PE.Controllers.Main.txtSldLtTTitle": "Title", "PE.Controllers.Main.txtSldLtTTitleOnly": "Title Only", "PE.Controllers.Main.txtSldLtTTwoColTx": "Two Column Text", "PE.Controllers.Main.txtSldLtTTwoObj": "Two Objects", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Two Objects and Object", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Two Objects and Text", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Two Objects over Text", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Two Text and Two Objects", "PE.Controllers.Main.txtSldLtTTx": "Text", "PE.Controllers.Main.txtSldLtTTxAndChart": "Text and Chart", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Text and Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Text and Media", "PE.Controllers.Main.txtSldLtTTxAndObj": "Text and Object", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Text and Two Objects", "PE.Controllers.Main.txtSldLtTTxOverObj": "Text over Object", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Vertical Title and Text", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Vertical Title and Text Over Chart", "PE.Controllers.Main.txtSldLtTVertTx": "Vertical Text", "PE.Controllers.Main.txtSlideNumber": "<PERSON><PERSON><PERSON> numurs", "PE.Controllers.Main.txtSlideSubtitle": "Slaida <PERSON>š<PERSON>auku<PERSON>", "PE.Controllers.Main.txtSlideText": "<PERSON><PERSON><PERSON> te<PERSON>", "PE.Controllers.Main.txtSlideTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtStarsRibbons": "Stars & Ribbons", "PE.Controllers.Main.txtTheme_blank": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_classic": "Klasiskais", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtXAxis": "X Axis", "PE.Controllers.Main.txtYAxis": "Y Axis", "PE.Controllers.Main.unknownErrorText": "Unknown error.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Your browser is not supported.", "PE.Controllers.Main.uploadImageExtMessage": "Unknown image format.", "PE.Controllers.Main.uploadImageFileCountMessage": "No images uploaded.", "PE.Controllers.Main.uploadImageSizeMessage": "Maximum image size limit exceeded.", "PE.Controllers.Main.uploadImageTextText": "Uploading image...", "PE.Controllers.Main.uploadImageTitleText": "Uploading Image", "PE.Controllers.Main.warnBrowserIE9": "The application has low capabilities on IE9. Use IE10 or higher", "PE.Controllers.Main.warnBrowserZoom": "Your browser current zoom setting is not fully supported. Please reset to the default zoom by pressing Ctrl+0.", "PE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON><PERSON> licencei ir beid<PERSON> term<PERSON>.<br><PERSON><PERSON><PERSON><PERSON>, atjauniniet savu licenci un pārlādējiet lapu.", "PE.Controllers.Main.warnNoLicense": "Šai %1 editors versijai ir noteikti ierobežojumi saistībā ar vienlaicīgu pieslēgšanos dokumentu serverim.<br>Ja jums ir nepiecieša<PERSON> vair<PERSON>k, l<PERSON><PERSON><PERSON>, apsveriet Jūsu šībrīža licences līmeņa paaugstināšanu vai komerciālās licences iegādi.", "PE.Controllers.Main.warnNoLicenseUsers": "Šai %1 editors versijai ir noteikti ierobežojumi saistībā ar vairāku lietotāju vienlaicīgu darbību.<br>Ja jums ir nepiecieša<PERSON> vairāk, l<PERSON><PERSON><PERSON>, apsveriet paaugstināt šībrīža licences līmeni vai komerciālās licences iegādi.", "PE.Controllers.Main.warnProcessRightsChange": "You have been denied the right to edit the file.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the system fonts, the saved font will be used when it is available.<br>Do you want to continue?", "PE.Controllers.Toolbar.textAccent": "Uzsvari", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textEmptyImgUrl": "You need to specify image URL.", "PE.Controllers.Toolbar.textFontSizeErr": "The entered value is incorrect.<br>Please enter a numeric value between 1 and 300", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "Funkcijas", "PE.Controllers.Toolbar.textIntegral": "<PERSON>teg<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Lielie operatori", "PE.Controllers.Toolbar.textLimitAndLog": "Robežas un logaritmi", "PE.Controllers.Toolbar.textMatrix": "Matricas", "PE.Controllers.Toolbar.textOperator": "Operatori", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Simboli", "PE.Controllers.Toolbar.textWarning": "Warning", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON>z<PERSON>var<PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON>a augšā pa kreisi", "PE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON>a augšā pa kreisi", "PE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON>a augšā pa labi", "PE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Apakš<PERSON>la", "PE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Formula rāmī (ar vietturi)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula rāmī (piemērs)", "PE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Apak<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Apvie<PERSON>jo<PERSON><PERSON> iekava no augšas", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektors A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC ar aug<PERSON><PERSON><PERSON> j<PERSON>", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y ar jos<PERSON> pāri", "PE.Controllers.Toolbar.txtAccent_DDDot": "Trīspunkte", "PE.Controllers.Toolbar.txtAccent_DDot": "Divpunkte", "PE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> j<PERSON> p<PERSON>", "PE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Grupēt apakšējo <PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Grupēt au<PERSON>š<PERSON>", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa kreisi", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa labi", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON><PERSON><PERSON> ar <PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (divi no<PERSON><PERSON><PERSON><PERSON><PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (trī<PERSON>)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Steka objekts", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Steka objekts", "PE.Controllers.Toolbar.txtBracket_Custom_5": "G<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Atsevišķa iekava", "PE.Controllers.Toolbar.txtFractionDiagonal": "Diagon<PERSON><PERSON> dalījums", "PE.Controllers.Toolbar.txtFractionDifferential_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionDifferential_4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionPi_2": "<PERSON> dalīts ar divi", "PE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Apgriez<PERSON>s k<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperboliskais apgrieztais kosīnuss", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Apgrieztais kotange<PERSON>s", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperboliskais apgrieztais kotangenss", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Apgrieztais kose<PERSON>ss", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperboliskais apgrieztais kosekanss", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Apgrieztais se<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperboliskais apgrieztais sekanss", "PE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON>pg<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperboliskais apgrieztais sīnuss", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Apgriez<PERSON><PERSON> tan<PERSON>s", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperboliskais apgrieztais tangenss", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hiperboliskais kosīnuss", "PE.Controllers.Toolbar.txtFunction_Cot": "Kotangenss", "PE.Controllers.Toolbar.txtFunction_Coth": "Hiperboliskais kotangenss", "PE.Controllers.Toolbar.txtFunction_Csc": "Kosekanss", "PE.Controllers.Toolbar.txtFunction_Csch": "Hiperboliskā kosekanse", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensa formula", "PE.Controllers.Toolbar.txtFunction_Sec": "Sekanss", "PE.Controllers.Toolbar.txtFunction_Sech": "Hiperboliskais sekanss", "PE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sinh": "Hiperboliskais sīnuss", "PE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Tanh": "Hiperboliskais tangenss", "PE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Diferenci<PERSON><PERSON> dθ", "PE.Controllers.Toolbar.txtIntegral_dx": "Diferenciā<PERSON> x", "PE.Controllers.Toolbar.txtIntegral_dy": "Diferen<PERSON><PERSON><PERSON> y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "PE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Ķīlis", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Ķīlis", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Ķīlis", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Ķīlis", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Ķīlis", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Kopražojums", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Kopražojums", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Kopražojums", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Kopražojums", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Kopražojums", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkts", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-veida", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-veida", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-veida", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-veida", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-veida", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkts", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkts", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkts", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkts", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkts", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summa", "PE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limits", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Nat<PERSON><PERSON><PERSON>s logaritms", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritms", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritms", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimums", "PE.Controllers.Toolbar.txtMatrix_1_2": "Tukša matrica 1 x 2", "PE.Controllers.Toolbar.txtMatrix_1_3": "Tukša matrica 1 x 3", "PE.Controllers.Toolbar.txtMatrix_2_1": "Tukša matrica 2 x 1", "PE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON>kša matrica 2 x 2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "PE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON>kša matrica 2 x 3", "PE.Controllers.Toolbar.txtMatrix_3_1": "Tukša matrica 3 x 1", "PE.Controllers.Toolbar.txtMatrix_3_2": "Tukša matrica 3 x 2", "PE.Controllers.Toolbar.txtMatrix_3_3": "Tukša matrica 3 x 3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pamatlīnijas punkti", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Vid<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Retā matrica", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Retā matrica", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "Identitātes matrica 2 x 2", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Identitātes matrica 3 x 3", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Identitātes matrica 3 x 3", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Identitātes matrica 3 x 3", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Bulta apakšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON>a augšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Bulta apakšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON>a augšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Bulta apakšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON>a augšā pa labi", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Divpunktu vienāds", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Izeja", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Izejas delta", "PE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON><PERSON> pēc defin<PERSON>", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta vienāda ar", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Bulta apakšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON>a augšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Bulta apakšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON>a augšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Bulta apakšā pa kreisi", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON>a augšā pa labi", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Vienāds vienāds", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus vienāds", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar", "PE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar pak<PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Apakšteksts", "PE.Controllers.Toolbar.txtScriptSubSup": "Apakšraksts-augšraksts", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Kreisais apakšraksts-augšraksts", "PE.Controllers.Toolbar.txtScriptSup": "Augšraksts", "PE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> ar ", "PE.Controllers.Toolbar.txtSymbol_ast": "Operators-zvaigznīte", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Aizzīmes operators", "PE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "Viduslī<PERSON>jas horizontālā elipse", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "Hi", "PE.Controllers.Toolbar.txtSymbol_cong": "Aptuveni vienāds ar", "PE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON><PERSON>ša<PERSON>", "PE.Controllers.Toolbar.txtSymbol_ddots": "Diagon<PERSON><PERSON><PERSON> elipse lejā pa labi", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> le<PERSON>", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> r<PERSON>", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identisks ar", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_forall": "Visiem", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> par", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON> nekā", "PE.Controllers.Toolbar.txtSymbol_in": "elements", "PE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_infinity": "Bezgalība", "PE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Bulta pa kreisi", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Bulta pa labi un kreisi", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON>āk nekā vai vienāds ar", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON><PERSON><PERSON> nek<PERSON>", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON><PERSON> par", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Mīnuss pluss", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Nav vien<PERSON>ds ar", "PE.Controllers.Toolbar.txtSymbol_ni": "Satur kā dalībnieks", "PE.Controllers.Toolbar.txtSymbol_not": "Negatī<PERSON><PERSON> z<PERSON>me", "PE.Controllers.Toolbar.txtSymbol_notexists": "Nepastāv", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus un mīnus", "PE.Controllers.Toolbar.txtSymbol_propto": "<PERSON>por<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> sakne", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON> <PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_rddots": "Diagonā<PERSON>ā elipse augšā pa labi", "PE.Controllers.Toolbar.txtSymbol_rho": "Ro", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Bulta pa labi", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon (variants)", "PE.Controllers.Toolbar.txtSymbol_varphi": "Fi (variants)", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi (variants)", "PE.Controllers.Toolbar.txtSymbol_varrho": "Ro (variants)", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma (variants)", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Theta (variants)", "PE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elipse", "PE.Controllers.Toolbar.txtSymbol_xsi": "Ksi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Saskaņot ar s<PERSON>u", "PE.Controllers.Viewport.textFitWidth": "Saskaņot ar platumu", "PE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> i<PERSON>", "PE.Views.ChartSettings.textChartType": "Change Chart Type", "PE.Views.ChartSettings.textEditData": "Edit Data", "PE.Views.ChartSettings.textHeight": "Height", "PE.Views.ChartSettings.textKeepRatio": "Constant Proportions", "PE.Views.ChartSettings.textSize": "Size", "PE.Views.ChartSettings.textStyle": "Style", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.ChartSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.ChartSettingsAdvanced.textTitle": "Diagramma – papildu i<PERSON>ī<PERSON>", "PE.Views.DocumentHolder.aboveText": "Above", "PE.Views.DocumentHolder.addCommentText": "Add Comment", "PE.Views.DocumentHolder.advancedImageText": "Image Advanced Settings", "PE.Views.DocumentHolder.advancedParagraphText": "Text Advanced Settings", "PE.Views.DocumentHolder.advancedShapeText": "Shape Advanced Settings", "PE.Views.DocumentHolder.advancedTableText": "Table Advanced Settings", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.belowText": "Below", "PE.Views.DocumentHolder.cellAlignText": "Cell Vertical Alignment", "PE.Views.DocumentHolder.cellText": "Cell", "PE.Views.DocumentHolder.centerText": "Centrā", "PE.Views.DocumentHolder.columnText": "Column", "PE.Views.DocumentHolder.deleteColumnText": "Delete Column", "PE.Views.DocumentHolder.deleteRowText": "Delete Row", "PE.Views.DocumentHolder.deleteTableText": "Delete Table", "PE.Views.DocumentHolder.deleteText": "Delete", "PE.Views.DocumentHolder.direct270Text": "Rotate at 270°", "PE.Views.DocumentHolder.direct90Text": "Rotate at 90°", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "Text Direction", "PE.Views.DocumentHolder.editChartText": "Edit Data", "PE.Views.DocumentHolder.editHyperlinkText": "Edit Hyperlink", "PE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "Igno<PERSON><PERSON>t visu", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Column Left", "PE.Views.DocumentHolder.insertColumnRightText": "Column Right", "PE.Views.DocumentHolder.insertColumnText": "Insert Column", "PE.Views.DocumentHolder.insertRowAboveText": "Row Above", "PE.Views.DocumentHolder.insertRowBelowText": "Row Below", "PE.Views.DocumentHolder.insertRowText": "Insert Row", "PE.Views.DocumentHolder.insertText": "Insert", "PE.Views.DocumentHolder.langText": "Izvēlēties valodu", "PE.Views.DocumentHolder.leftText": "<PERSON> kreisi", "PE.Views.DocumentHolder.loadSpellText": "<PERSON><PERSON><PERSON><PERSON><PERSON> variantus...", "PE.Views.DocumentHolder.mergeCellsText": "Merge Cells", "PE.Views.DocumentHolder.moreText": "Vairāk variantu...", "PE.Views.DocumentHolder.noSpellVariantsText": "Nav variantu", "PE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.removeHyperlinkText": "Remove Hyperlink", "PE.Views.DocumentHolder.rightText": "Pa labi", "PE.Views.DocumentHolder.rowText": "Row", "PE.Views.DocumentHolder.selectText": "Select", "PE.Views.DocumentHolder.spellcheckText": "Pareizrakstī<PERSON>", "PE.Views.DocumentHolder.splitCellsText": "Split Cell...", "PE.Views.DocumentHolder.splitCellTitleText": "Split Cell", "PE.Views.DocumentHolder.tableText": "Table", "PE.Views.DocumentHolder.textArrangeBack": "Send to Background", "PE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeForward": "Pārnest uz priekšu", "PE.Views.DocumentHolder.textArrangeFront": "Bring To Foreground", "PE.Views.DocumentHolder.textCopy": "Copy", "PE.Views.DocumentHolder.textCut": "Cut", "PE.Views.DocumentHolder.textDistributeCols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.DocumentHolder.textFromFile": "No faila", "PE.Views.DocumentHolder.textFromUrl": "No URL", "PE.Views.DocumentHolder.textNextPage": "Next Slide", "PE.Views.DocumentHolder.textPaste": "Paste", "PE.Views.DocumentHolder.textPrevPage": "Previous Slide", "PE.Views.DocumentHolder.textReplace": "Aizvietot attēlu", "PE.Views.DocumentHolder.textShapeAlignBottom": "Align Bottom", "PE.Views.DocumentHolder.textShapeAlignCenter": "Align Center", "PE.Views.DocumentHolder.textShapeAlignLeft": "Align Left", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Align Middle", "PE.Views.DocumentHolder.textShapeAlignRight": "Align Right", "PE.Views.DocumentHolder.textShapeAlignTop": "Align Top", "PE.Views.DocumentHolder.textSlideSettings": "Slide Settings", "PE.Views.DocumentHolder.textUndo": "Atsaukt", "PE.Views.DocumentHolder.tipIsLocked": "This element is currently being edited by another user.", "PE.Views.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON>not līniju k<PERSON> apakšējā stūrī", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> lī<PERSON>ju kreisajā augšējā stūrī", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.DocumentHolder.txtAlign": "Align", "PE.Views.DocumentHolder.txtAlignToChar": "Saskaņot ar simbolu", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Background", "PE.Views.DocumentHolder.txtBorderProps": "Robežu parametri", "PE.Views.DocumentHolder.txtBottom": "Apakšā", "PE.Views.DocumentHolder.txtChangeLayout": "Change Layout", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON><PERSON> tēmu", "PE.Views.DocumentHolder.txtColumnAlign": "Kolonnas izlīdzināšana", "PE.Views.DocumentHolder.txtDecreaseArg": "Samazināt argumenta izmēru", "PE.Views.DocumentHolder.txtDeleteArg": "Dzēst argumentu", "PE.Views.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON><PERSON> man<PERSON><PERSON><PERSON> at<PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON><PERSON> iet<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON><PERSON> ietvero<PERSON> zīmes un atdalītājus", "PE.Views.DocumentHolder.txtDeleteEq": "Dzēst vienādojumu", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON><PERSON> simbolu", "PE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON><PERSON> radi<PERSON>", "PE.Views.DocumentHolder.txtDeleteSlide": "Delete Slide", "PE.Views.DocumentHolder.txtDistribHor": "Distribute Horizontally", "PE.Views.DocumentHolder.txtDistribVert": "Distribute Vertically", "PE.Views.DocumentHolder.txtDuplicateSlide": "Duplicate Slide", "PE.Views.DocumentHolder.txtFractionLinear": "<PERSON>īt uz lineāru da<PERSON>", "PE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON> uz <PERSON> da<PERSON>", "PE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON><PERSON> uz vert<PERSON>", "PE.Views.DocumentHolder.txtGroup": "Group", "PE.Views.DocumentHolder.txtGroupCharOver": "Simbols virs teksta", "PE.Views.DocumentHolder.txtGroupCharUnder": "Simbols zem teksta", "PE.Views.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideBottomLimit": "Nerādīt a<PERSON>šējo ierobežojumu", "PE.Views.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideLB": "Nerādīt kreiso apakšējo līniju", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.DocumentHolder.txtHideLT": "Nerā<PERSON><PERSON>t k<PERSON>o augšējo līniju", "PE.Views.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "PE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> labo <PERSON>", "PE.Views.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "PE.Views.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>š<PERSON> ierobežojumu", "PE.Views.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "PE.Views.DocumentHolder.txtIncreaseArg": "Palielināt argumenta izmēru", "PE.Views.DocumentHolder.txtInsertArgAfter": "<PERSON>ev<PERSON><PERSON> atgumentu pēc", "PE.Views.DocumentHolder.txtInsertArgBefore": "Ievietot argumentu pirms", "PE.Views.DocumentHolder.txtInsertBreak": "Ieviet<PERSON> man<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtInsertEqAfter": "Ievietot vien<PERSON><PERSON><PERSON><PERSON> p<PERSON>c", "PE.Views.DocumentHolder.txtInsertEqBefore": "Ievietot vienā<PERSON><PERSON><PERSON> pirms", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON><PERSON> tikai te<PERSON>tu", "PE.Views.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON> v<PERSON>u", "PE.Views.DocumentHolder.txtLimitOver": "Robeža p<PERSON>", "PE.Views.DocumentHolder.txtLimitUnder": "Robeža zem teksta", "PE.Views.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON><PERSON> argumenta augstumam", "PE.Views.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtNewSlide": "New Slide", "PE.Views.DocumentHolder.txtOverbar": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtPasteDestFormat": "Izmantot galamērķa dizainu", "PE.Views.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Atstāt s<PERSON>kotnējo formatējumu", "PE.Views.DocumentHolder.txtPressLink": "Press {0} and click link", "PE.Views.DocumentHolder.txtPreview": "Preview", "PE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "PE.Views.DocumentHolder.txtRemLimit": "Noņemt limitu", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Noņemt diakritisko zīmi", "PE.Views.DocumentHolder.txtRemoveBar": "Noņ<PERSON><PERSON> j<PERSON>", "PE.Views.DocumentHolder.txtRemScripts": "<PERSON><PERSON><PERSON><PERSON> sk<PERSON>tus", "PE.Views.DocumentHolder.txtRemSubscript": "Noņemt apakšrakstu", "PE.Views.DocumentHolder.txtRemSuperscript": "Noņemt augšrakstu", "PE.Views.DocumentHolder.txtScriptsAfter": "Skripti p<PERSON> te<PERSON>ta", "PE.Views.DocumentHolder.txtScriptsBefore": "Skripti pirms teksta", "PE.Views.DocumentHolder.txtSelectAll": "Select All", "PE.Views.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> ierobežojumu", "PE.Views.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "PE.Views.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "PE.Views.DocumentHolder.txtSlide": "Slide", "PE.Views.DocumentHolder.txtSlideHide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u", "PE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtTop": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtUnderbar": "Josla zem teksta", "PE.Views.DocumentHolder.txtUngroup": "Ungroup", "PE.Views.DocumentHolder.vertAlignText": "Vertical Alignment", "PE.Views.DocumentPreview.goToSlideText": "Go to Slide", "PE.Views.DocumentPreview.slideIndexText": "Slide {0} of {1}", "PE.Views.DocumentPreview.txtClose": "Close Preview", "PE.Views.DocumentPreview.txtEndSlideshow": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtExitFullScreen": "Exit Full Screen", "PE.Views.DocumentPreview.txtFinalMessage": "The end of slide preview. <PERSON>lick to exit.", "PE.Views.DocumentPreview.txtFullScreen": "Full Screen", "PE.Views.DocumentPreview.txtNext": "Next Slide", "PE.Views.DocumentPreview.txtPageNumInvalid": "Invalid slide number", "PE.Views.DocumentPreview.txtPause": "Pause Presentation", "PE.Views.DocumentPreview.txtPlay": "Start Presentation", "PE.Views.DocumentPreview.txtPrev": "Previous Slide", "PE.Views.DocumentPreview.txtReset": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnAboutCaption": "About", "PE.Views.FileMenu.btnBackCaption": "Go to Documents", "PE.Views.FileMenu.btnCloseMenuCaption": "Aizvērt izvēlni", "PE.Views.FileMenu.btnCreateNewCaption": "Create New", "PE.Views.FileMenu.btnDownloadCaption": "Download as", "PE.Views.FileMenu.btnHelpCaption": "Help", "PE.Views.FileMenu.btnInfoCaption": "Presentation Info", "PE.Views.FileMenu.btnPrintCaption": "Print", "PE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnRecentFilesCaption": "Open Recent", "PE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnReturnCaption": "Back to Presentation", "PE.Views.FileMenu.btnRightsCaption": "Access Rights", "PE.Views.FileMenu.btnSaveAsCaption": "Save as", "PE.Views.FileMenu.btnSaveCaption": "Save", "PE.Views.FileMenu.btnSettingsCaption": "Advanced Settings", "PE.Views.FileMenu.btnToEditCaption": "Edit Presentation", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Author", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Change access rights", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Location", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persons who have rights", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Presentation Title", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Change access rights", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Persons who have rights", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON> <PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Rediģēt prezentāciju", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Rediģēšana no prezentācijas noņems parakstus.<br> Vai tiešām vēlaties turpināt?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON><PERSON> a<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Prezentācijai ir pievienoti derīgi paraksti. Prezentāciju nevar rediģēt.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Da<PERSON><PERSON> no prezentācijas digitālajiem parakstiem ir nederīgi vai tos nevar pārbaudīt. Prezentāciju nevar rediģēt.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "PE.Views.FileMenuPanels.Settings.okButtonText": "Apply", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Co-editing mode", "PE.Views.FileMenuPanels.Settings.strFast": "Fast", "PE.Views.FileMenuPanels.Settings.strStrict": "Strict", "PE.Views.FileMenuPanels.Settings.strUnit": "Unit of Measurement", "PE.Views.FileMenuPanels.Settings.strZoom": "Default Zoom Value", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Every 10 Minutes", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Every 30 Minutes", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Every 5 Minutes", "PE.Views.FileMenuPanels.Settings.text60Minutes": "Every Hour", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Alignment Guides", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Automātiskā <PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Autosave", "PE.Views.FileMenuPanels.Settings.textDisabled": "Disabled", "PE.Views.FileMenuPanels.Settings.textForceSave": "Noglabāt serverī", "PE.Views.FileMenuPanels.Settings.textMinute": "Every Minute", "PE.Views.FileMenuPanels.Settings.txtAll": "View All", "PE.Views.FileMenuPanels.Settings.txtCm": "Centimeter", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Fit Slide", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Saskaņot ar platumu", "PE.Views.FileMenuPanels.Settings.txtInch": "Col<PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "View Last", "PE.Views.FileMenuPanels.Settings.txtPt": "Point", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Pareizrakstī<PERSON>", "PE.Views.HeaderFooterDialog.textPreview": "Priekšskatījums", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Display", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Link To", "PE.Views.HyperlinkSettingsDialog.textDefault": "Selected text fragment", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Enter caption here", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Enter link here", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Enter tooltip here", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "External Link", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slide In This Presentation", "PE.Views.HyperlinkSettingsDialog.textTipText": "ScreenTip Text", "PE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink Settings", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "This field is required", "PE.Views.HyperlinkSettingsDialog.txtFirst": "First Slide", "PE.Views.HyperlinkSettingsDialog.txtLast": "Last Slide", "PE.Views.HyperlinkSettingsDialog.txtNext": "Next Slide", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Previous Slide", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Slide", "PE.Views.ImageSettings.textAdvanced": "Show advanced settings", "PE.Views.ImageSettings.textEdit": "Rediģēt", "PE.Views.ImageSettings.textEditObject": "Rediģēt objektu", "PE.Views.ImageSettings.textFromFile": "From File", "PE.Views.ImageSettings.textFromUrl": "From URL", "PE.Views.ImageSettings.textHeight": "Height", "PE.Views.ImageSettings.textInsert": "Replace Image", "PE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textSize": "Size", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.ImageSettingsAdvanced.textHeight": "Height", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Constant Proportions", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textPlacement": "Novietojums", "PE.Views.ImageSettingsAdvanced.textPosition": "Position", "PE.Views.ImageSettingsAdvanced.textSize": "Size", "PE.Views.ImageSettingsAdvanced.textTitle": "Image - Advanced Settings", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "About", "PE.Views.LeftMenu.tipChat": "Cha<PERSON>", "PE.Views.LeftMenu.tipComments": "Comments", "PE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSearch": "Search", "PE.Views.LeftMenu.tipSlides": "Slides", "PE.Views.LeftMenu.tipSupport": "Feedback & Support", "PE.Views.LeftMenu.tipTitles": "Titles", "PE.Views.LeftMenu.txtDeveloper": "IZSTRĀDĀTĀJA REŽĪMS", "PE.Views.LeftMenu.txtTrial": "IZMĒĢINĀJUMA REŽĪMS", "PE.Views.ParagraphSettings.strLineHeight": "Line Spacing", "PE.Views.ParagraphSettings.strParagraphSpacing": "Spacing", "PE.Views.ParagraphSettings.strSpacingAfter": "After", "PE.Views.ParagraphSettings.strSpacingBefore": "Before", "PE.Views.ParagraphSettings.textAdvanced": "Show advanced settings", "PE.Views.ParagraphSettings.textAt": "At", "PE.Views.ParagraphSettings.textAtLeast": "At least", "PE.Views.ParagraphSettings.textAuto": "Multiple", "PE.Views.ParagraphSettings.textExact": "Exactly", "PE.Views.ParagraphSettings.txtAutoText": "Auto", "PE.Views.ParagraphSettingsAdvanced.noTabs": "The specified tabs will appear in this field", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "All caps", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Double strikethrough", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Right", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indents & Placement", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Small caps", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Strikethrough", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Alignment", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Character Spacing", "PE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Effects", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Remove", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remove All", "PE.Views.ParagraphSettingsAdvanced.textSet": "Specify", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "Left", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tab Position", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "Right", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraph - Advanced Settings", "PE.Views.RightMenu.txtChartSettings": "Chart Settings", "PE.Views.RightMenu.txtImageSettings": "Image Settings", "PE.Views.RightMenu.txtParagraphSettings": "Text Settings", "PE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON>", "PE.Views.RightMenu.txtSignatureSettings": "Paraksta uzstādījumi", "PE.Views.RightMenu.txtSlideSettings": "Slide Settings", "PE.Views.RightMenu.txtTableSettings": "Table Settings", "PE.Views.RightMenu.txtTextArtSettings": "Text Art Settings", "PE.Views.ShapeSettings.strBackground": "Background color", "PE.Views.ShapeSettings.strChange": "Change Autoshape", "PE.Views.ShapeSettings.strColor": "Color", "PE.Views.ShapeSettings.strFill": "Fill", "PE.Views.ShapeSettings.strForeground": "Foreground color", "PE.Views.ShapeSettings.strPattern": "Pattern", "PE.Views.ShapeSettings.strSize": "Size", "PE.Views.ShapeSettings.strStroke": "Stroke", "PE.Views.ShapeSettings.strTransparency": "Opacity", "PE.Views.ShapeSettings.strType": "Veids", "PE.Views.ShapeSettings.textAdvanced": "Show advanced settings", "PE.Views.ShapeSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "PE.Views.ShapeSettings.textColor": "Color Fill", "PE.Views.ShapeSettings.textDirection": "Direction", "PE.Views.ShapeSettings.textEmptyPattern": "No Pattern", "PE.Views.ShapeSettings.textFromFile": "From File", "PE.Views.ShapeSettings.textFromUrl": "From URL", "PE.Views.ShapeSettings.textGradient": "Gradient", "PE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "PE.Views.ShapeSettings.textImageTexture": "Picture or Texture", "PE.Views.ShapeSettings.textLinear": "Linear", "PE.Views.ShapeSettings.textNoFill": "No Fill", "PE.Views.ShapeSettings.textPatternFill": "Pattern", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textSelectTexture": "Select", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "Style", "PE.Views.ShapeSettings.textTexture": "From Texture", "PE.Views.ShapeSettings.textTile": "Tile", "PE.Views.ShapeSettings.txtBrownPaper": "Brown Paper", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "PE.Views.ShapeSettings.txtGrain": "Grain", "PE.Views.ShapeSettings.txtGranite": "Granite", "PE.Views.ShapeSettings.txtGreyPaper": "Gray Paper", "PE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "Leather", "PE.Views.ShapeSettings.txtNoBorders": "No Line", "PE.Views.ShapeSettings.txtPapyrus": "Papyrus", "PE.Views.ShapeSettings.txtWood": "<PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "Kolonnas", "PE.Views.ShapeSettingsAdvanced.strMargins": "Text Padding", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.ShapeSettingsAdvanced.textArrows": "Arrows", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Begin Style", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Bottom", "PE.Views.ShapeSettingsAdvanced.textCapType": "Cap Type", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON> s<PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "End Size", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "End Style", "PE.Views.ShapeSettingsAdvanced.textFlat": "Flat", "PE.Views.ShapeSettingsAdvanced.textHeight": "Height", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Join Type", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Constant Proportions", "PE.Views.ShapeSettingsAdvanced.textLeft": "Left", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Line Style", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRight": "Right", "PE.Views.ShapeSettingsAdvanced.textRound": "Round", "PE.Views.ShapeSettingsAdvanced.textSize": "Size", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Atstarpe starp kolonn<PERSON>m", "PE.Views.ShapeSettingsAdvanced.textSquare": "Square", "PE.Views.ShapeSettingsAdvanced.textTitle": "Shape - Advanced Settings", "PE.Views.ShapeSettingsAdvanced.textTop": "Top", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Weights & Arrows", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "None", "PE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strDelete": "Noņemt parakstu", "PE.Views.SignatureSettings.strDetails": "Paraksta de<PERSON>ļas", "PE.Views.SignatureSettings.strInvalid": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strSign": "Parakstīt", "PE.Views.SignatureSettings.strSignature": "Paraksts", "PE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.txtContinueEditing": "Vienalga rediģēt", "PE.Views.SignatureSettings.txtEditWarning": "Rediģēšana no prezentācijas noņems parakstus.<br> Vai tiešām vēlaties turpināt?", "PE.Views.SignatureSettings.txtSigned": "Prezentācijai ir pievienoti derīgi paraksti. Prezentāciju nevar rediģēt.", "PE.Views.SignatureSettings.txtSignedInvalid": "Da<PERSON><PERSON> no prezentācijas digitālajiem parakstiem ir nederīgi vai tos nevar pārbaudīt. Prezentāciju nevar rediģēt.", "PE.Views.SlideSettings.strBackground": "Background color", "PE.Views.SlideSettings.strColor": "Color", "PE.Views.SlideSettings.strFill": "Fill", "PE.Views.SlideSettings.strForeground": "Foreground color", "PE.Views.SlideSettings.strPattern": "Pattern", "PE.Views.SlideSettings.textAdvanced": "Show advanced settings", "PE.Views.SlideSettings.textColor": "Color Fill", "PE.Views.SlideSettings.textDirection": "Direction", "PE.Views.SlideSettings.textEmptyPattern": "No Pattern", "PE.Views.SlideSettings.textFromFile": "From File", "PE.Views.SlideSettings.textFromUrl": "From URL", "PE.Views.SlideSettings.textGradient": "Gradient", "PE.Views.SlideSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "PE.Views.SlideSettings.textImageTexture": "Picture or Texture", "PE.Views.SlideSettings.textLinear": "Linear", "PE.Views.SlideSettings.textNoFill": "No Fill", "PE.Views.SlideSettings.textPatternFill": "Pattern", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Reset Changes", "PE.Views.SlideSettings.textSelectTexture": "Select", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "Style", "PE.Views.SlideSettings.textTexture": "From Texture", "PE.Views.SlideSettings.textTile": "Tile", "PE.Views.SlideSettings.txtBrownPaper": "Brown Paper", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "PE.Views.SlideSettings.txtGrain": "Grain", "PE.Views.SlideSettings.txtGranite": "Granite", "PE.Views.SlideSettings.txtGreyPaper": "Gray Paper", "PE.Views.SlideSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "Leather", "PE.Views.SlideSettings.txtPapyrus": "Papyrus", "PE.Views.SlideSettings.txtWood": "<PERSON>", "PE.Views.SlideshowSettings.textLoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> <PERSON>k nospiests \"Esc\"", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Ainava", "PE.Views.SlideSizeSettings.strPortrait": "Portrets", "PE.Views.SlideSizeSettings.textHeight": "Height", "PE.Views.SlideSizeSettings.textSlideOrientation": "Slaida orientācija", "PE.Views.SlideSizeSettings.textSlideSize": "Slide Size", "PE.Views.SlideSizeSettings.textTitle": "Slide Size Settings", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "35 mm Slides", "PE.Views.SlideSizeSettings.txtA3": "A3 Paper (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Paper (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Paper (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Paper (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Custom", "PE.Views.SlideSizeSettings.txtLedger": "Ledger Paper (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Letter Paper (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Overhead", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.Statusbar.goToPageText": "Go to Slide", "PE.Views.Statusbar.pageIndexText": "Slide {0} of {1}", "PE.Views.Statusbar.tipAccessRights": "Manage document access rights", "PE.Views.Statusbar.tipFitPage": "Fit Slide", "PE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON>", "PE.Views.Statusbar.tipPreview": "Start Preview", "PE.Views.Statusbar.tipSetLang": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teksta valodu", "PE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.tipZoomIn": "Zoom In", "PE.Views.Statusbar.tipZoomOut": "Zoom Out", "PE.Views.Statusbar.txtPageNumInvalid": "Invalid slide number", "PE.Views.TableSettings.deleteColumnText": "Delete Column", "PE.Views.TableSettings.deleteRowText": "Delete Row", "PE.Views.TableSettings.deleteTableText": "Delete Table", "PE.Views.TableSettings.insertColumnLeftText": "Insert Column Left", "PE.Views.TableSettings.insertColumnRightText": "Insert Column Right", "PE.Views.TableSettings.insertRowAboveText": "Insert Row Above", "PE.Views.TableSettings.insertRowBelowText": "Insert Row Below", "PE.Views.TableSettings.mergeCellsText": "Merge Cells", "PE.Views.TableSettings.selectCellText": "Select Cell", "PE.Views.TableSettings.selectColumnText": "Select Column", "PE.Views.TableSettings.selectRowText": "Select Row", "PE.Views.TableSettings.selectTableText": "Select Table", "PE.Views.TableSettings.splitCellsText": "Split Cell...", "PE.Views.TableSettings.splitCellTitleText": "Split Cell", "PE.Views.TableSettings.textAdvanced": "Show advanced settings", "PE.Views.TableSettings.textBackColor": "Background color", "PE.Views.TableSettings.textBanded": "Banded", "PE.Views.TableSettings.textBorderColor": "Color", "PE.Views.TableSettings.textBorders": "Borders Style", "PE.Views.TableSettings.textCellSize": "Šūnas izmērs", "PE.Views.TableSettings.textColumns": "Columns", "PE.Views.TableSettings.textDistributeCols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "PE.Views.TableSettings.textEdit": "Rows & Columns", "PE.Views.TableSettings.textEmptyTemplate": "No templates", "PE.Views.TableSettings.textFirst": "First", "PE.Views.TableSettings.textHeader": "Header", "PE.Views.TableSettings.textHeight": "Augstums", "PE.Views.TableSettings.textLast": "Last", "PE.Views.TableSettings.textRows": "Rows", "PE.Views.TableSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "PE.Views.TableSettings.textTemplate": "Select From Template", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "Platums", "PE.Views.TableSettings.tipAll": "Set Outer Border and All Inner Lines", "PE.Views.TableSettings.tipBottom": "Set Outer Bottom Border Only", "PE.Views.TableSettings.tipInner": "Set Inner Lines Only", "PE.Views.TableSettings.tipInnerHor": "Set Horizontal Inner Lines Only", "PE.Views.TableSettings.tipInnerVert": "Set Vertical Inner Lines Only", "PE.Views.TableSettings.tipLeft": "Set Outer Left Border Only", "PE.Views.TableSettings.tipNone": "Set No Borders", "PE.Views.TableSettings.tipOuter": "Set Outer Border Only", "PE.Views.TableSettings.tipRight": "Set Outer Right Border Only", "PE.Views.TableSettings.tipTop": "Set Outer Top Border Only", "PE.Views.TableSettings.txtNoBorders": "No borders", "PE.Views.TableSettingsAdvanced.textAlt": "Alternatīvs teksts", "PE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAltTip": "Vizuā<PERSON>ās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, automātiska<PERSON><PERSON> figūrā, diagrammā vai tabulā.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "PE.Views.TableSettingsAdvanced.textBottom": "Bottom", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Use default margins", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textLeft": "Left", "PE.Views.TableSettingsAdvanced.textMargins": "<PERSON>", "PE.Views.TableSettingsAdvanced.textRight": "Right", "PE.Views.TableSettingsAdvanced.textTitle": "Table - Advanced Settings", "PE.Views.TableSettingsAdvanced.textTop": "Top", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "Background color", "PE.Views.TextArtSettings.strColor": "Color", "PE.Views.TextArtSettings.strFill": "Fill", "PE.Views.TextArtSettings.strForeground": "Foreground color", "PE.Views.TextArtSettings.strPattern": "Pattern", "PE.Views.TextArtSettings.strSize": "Size", "PE.Views.TextArtSettings.strStroke": "Stroke", "PE.Views.TextArtSettings.strTransparency": "Opacity", "PE.Views.TextArtSettings.strType": "Veids", "PE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "PE.Views.TextArtSettings.textColor": "Color Fill", "PE.Views.TextArtSettings.textDirection": "Direction", "PE.Views.TextArtSettings.textEmptyPattern": "No Pattern", "PE.Views.TextArtSettings.textFromFile": "From File", "PE.Views.TextArtSettings.textFromUrl": "From URL", "PE.Views.TextArtSettings.textGradient": "Gradient", "PE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "PE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "PE.Views.TextArtSettings.textLinear": "Linear", "PE.Views.TextArtSettings.textNoFill": "No Fill", "PE.Views.TextArtSettings.textPatternFill": "Pattern", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "Select", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "Style", "PE.Views.TextArtSettings.textTemplate": "Template", "PE.Views.TextArtSettings.textTexture": "From Texture", "PE.Views.TextArtSettings.textTile": "Tile", "PE.Views.TextArtSettings.textTransform": "Transform", "PE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "PE.Views.TextArtSettings.txtGrain": "Grain", "PE.Views.TextArtSettings.txtGranite": "Granite", "PE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "PE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "Leather", "PE.Views.TextArtSettings.txtNoBorders": "No Line", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "<PERSON>", "PE.Views.Toolbar.capAddSlide": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertChart": "Di<PERSON>ram<PERSON>", "PE.Views.Toolbar.capInsertEquation": "Vienādojums", "PE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "Tabula", "PE.Views.Toolbar.capInsertText": "Uzraksts", "PE.Views.Toolbar.capTabFile": "Fails", "PE.Views.Toolbar.capTabHome": "Sā<PERSON><PERSON>", "PE.Views.Toolbar.capTabInsert": "Insert", "PE.Views.Toolbar.mniCustomTable": "Insert Custom Table", "PE.Views.Toolbar.mniImageFromFile": "Picture from File", "PE.Views.Toolbar.mniImageFromUrl": "Picture from URL", "PE.Views.Toolbar.mniSlideAdvanced": "Advanced Settings", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.textAlignBottom": "Align text to the bottom", "PE.Views.Toolbar.textAlignCenter": "Center text", "PE.Views.Toolbar.textAlignJust": "Justify", "PE.Views.Toolbar.textAlignLeft": "Align text left", "PE.Views.Toolbar.textAlignMiddle": "Align text to the middle", "PE.Views.Toolbar.textAlignRight": "Align text right", "PE.Views.Toolbar.textAlignTop": "Align text to the top", "PE.Views.Toolbar.textArrangeBack": "Send to Background", "PE.Views.Toolbar.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textArrangeForward": "Pārnest uz priekšu", "PE.Views.Toolbar.textArrangeFront": "Bring To Foreground", "PE.Views.Toolbar.textBold": "Bold", "PE.Views.Toolbar.textItalic": "Italic", "PE.Views.Toolbar.textShapeAlignBottom": "Align Bottom", "PE.Views.Toolbar.textShapeAlignCenter": "Align Center", "PE.Views.Toolbar.textShapeAlignLeft": "Align Left", "PE.Views.Toolbar.textShapeAlignMiddle": "Align Middle", "PE.Views.Toolbar.textShapeAlignRight": "Align Right", "PE.Views.Toolbar.textShapeAlignTop": "Align Top", "PE.Views.Toolbar.textShowBegin": "<PERSON><PERSON><PERSON><PERSON><PERSON> no sākuma", "PE.Views.Toolbar.textShowCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON> no šī slaida", "PE.Views.Toolbar.textShowPresenterView": "<PERSON><PERSON><PERSON><PERSON><PERSON> prezentētāja re<PERSON>", "PE.Views.Toolbar.textShowSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textStrikeout": "Strikeout", "PE.Views.Toolbar.textSubscript": "Subscript", "PE.Views.Toolbar.textSuperscript": "Superscript", "PE.Views.Toolbar.textTabCollaboration": "Sad<PERSON>bī<PERSON>", "PE.Views.Toolbar.textTabFile": "Fails", "PE.Views.Toolbar.textTabHome": "Sā<PERSON><PERSON>", "PE.Views.Toolbar.textTabInsert": "Insert", "PE.Views.Toolbar.textTabProtect": "Aizsardzība", "PE.Views.Toolbar.textTitleError": "Error", "PE.Views.Toolbar.textUnderline": "Underline", "PE.Views.Toolbar.tipAddSlide": "Add Slide", "PE.Views.Toolbar.tipBack": "Back", "PE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "PE.Views.Toolbar.tipChangeSlide": "Change Slide Layout", "PE.Views.Toolbar.tipClearStyle": "Clear Style", "PE.Views.Toolbar.tipColorSchemas": "Change Color Scheme", "PE.Views.Toolbar.tipCopy": "Copy", "PE.Views.Toolbar.tipCopyStyle": "Copy Style", "PE.Views.Toolbar.tipDecPrLeft": "Decrease Indent", "PE.Views.Toolbar.tipFontColor": "Font color", "PE.Views.Toolbar.tipFontName": "Fonts", "PE.Views.Toolbar.tipFontSize": "Font Size", "PE.Views.Toolbar.tipHAligh": "Horizontal Align", "PE.Views.Toolbar.tipIncPrLeft": "Increase Indent", "PE.Views.Toolbar.tipInsertChart": "Insert Chart", "PE.Views.Toolbar.tipInsertEquation": "Ievietot vienā<PERSON><PERSON>", "PE.Views.Toolbar.tipInsertHyperlink": "Add Hyperlink", "PE.Views.Toolbar.tipInsertImage": "Insert Picture", "PE.Views.Toolbar.tipInsertShape": "Insert Autoshape", "PE.Views.Toolbar.tipInsertTable": "Insert Table", "PE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertTextArt": "Ievietot Text Art objektu", "PE.Views.Toolbar.tipLineSpace": "Line Spacing", "PE.Views.Toolbar.tipMarkers": "Bullets", "PE.Views.Toolbar.tipNumbers": "Numbering", "PE.Views.Toolbar.tipPaste": "Paste", "PE.Views.Toolbar.tipPreview": "Start Preview", "PE.Views.Toolbar.tipPrint": "Print", "PE.Views.Toolbar.tipRedo": "Redo", "PE.Views.Toolbar.tipSave": "Save", "PE.Views.Toolbar.tipSaveCoauth": "Save your changes for the other users to see them.", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON>", "PE.Views.Toolbar.tipShapeArrange": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSlideSize": "Select Slide Size", "PE.Views.Toolbar.tipSlideTheme": "Slide Theme", "PE.Views.Toolbar.tipUndo": "Undo", "PE.Views.Toolbar.tipVAligh": "Vertical Align", "PE.Views.Toolbar.tipViewSettings": "View Settings", "PE.Views.Toolbar.txtDistribHor": "Distribute Horizontally", "PE.Views.Toolbar.txtDistribVert": "Distribute Vertically", "PE.Views.Toolbar.txtGroup": "Group", "PE.Views.Toolbar.txtScheme1": "Office", "PE.Views.Toolbar.txtScheme10": "Median", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "Opulent", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Origin", "PE.Views.Toolbar.txtScheme16": "Paper", "PE.Views.Toolbar.txtScheme17": "Solstice", "PE.Views.Toolbar.txtScheme18": "Technic", "PE.Views.Toolbar.txtScheme19": "Trek", "PE.Views.Toolbar.txtScheme2": "Grayscale", "PE.Views.Toolbar.txtScheme20": "Urban", "PE.Views.Toolbar.txtScheme21": "Verve", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Aspect", "PE.Views.Toolbar.txtScheme5": "Civic", "PE.Views.Toolbar.txtScheme6": "Concourse", "PE.Views.Toolbar.txtScheme7": "Equity", "PE.Views.Toolbar.txtScheme8": "Flow", "PE.Views.Toolbar.txtScheme9": "Foundry", "PE.Views.Toolbar.txtUngroup": "Ungroup", "PE.Views.Transitions.strDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.strDuration": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.strStartOnClick": "Palaist ar klikšķi", "PE.Views.Transitions.textBlack": "<PERSON><PERSON><PERSON> melnu", "PE.Views.Transitions.textBottomLeft": "Apakšā pa kreisi", "PE.Views.Transitions.textBottomRight": "Apakšā pa labi", "PE.Views.Transitions.textClock": "<PERSON><PERSON>ks<PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "Pulks<PERSON><PERSON><PERSON> rā<PERSON><PERSON><PERSON><PERSON> vir<PERSON>", "PE.Views.Transitions.textCounterclockwise": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON> rādīt<PERSON><PERSON> vir<PERSON>", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textLeft": "<PERSON> kreisi", "PE.Views.Transitions.textPush": "Izvirzī<PERSON>", "PE.Views.Transitions.textRight": "Pa labi", "PE.Views.Transitions.textSmoothly": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSplit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textTop": "<PERSON><PERSON>", "PE.Views.Transitions.textTopLeft": "<PERSON><PERSON><PERSON> pa kreisi", "PE.Views.Transitions.textTopRight": "<PERSON><PERSON><PERSON> pa labi", "PE.Views.Transitions.textUnCover": "Atsegt", "PE.Views.Transitions.textVerticalIn": "<PERSON> vertikāli iek<PERSON>ā", "PE.Views.Transitions.textVerticalOut": "Pa vertikāli ārā", "PE.Views.Transitions.textWedge": "Ķīlis", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoomRotate": "<PERSON><PERSON><PERSON><PERSON><PERSON> un rotēt", "PE.Views.Transitions.txtApplyToAll": "Pie<PERSON>ē<PERSON> visiem slaidiem", "PE.Views.Transitions.txtPreview": "Priekšskatījums", "PE.Views.Transitions.txtSec": "s"}