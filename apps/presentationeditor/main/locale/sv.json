{"Common.Controllers.Chat.notcriticalErrorTitle": "Varning", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON><PERSON> ditt meddelande här", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalDiagramEditor.textClose": "Stäng", "Common.Controllers.ExternalDiagramEditor.warningText": "Objektet är inaktiverat eftersom det redigeras av en annan användare.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Varning", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalOleEditor.textClose": "Stäng", "Common.Controllers.ExternalOleEditor.warningText": "Objektet är inaktiverat eftersom det redigeras av en annan användare.", "Common.Controllers.ExternalOleEditor.warningTitle": "Varning", "Common.define.chartData.textArea": "<PERSON><PERSON>r<PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Staplad yta", "Common.define.chartData.textAreaStackedPer": "100% staplat område", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Grupperad kolumn", "Common.define.chartData.textBarNormal3d": "3-D grupperad kolumn", "Common.define.chartData.textBarNormal3dPerspective": "3-D kolumn", "Common.define.chartData.textBarStacked": "Staplad kolumn", "Common.define.chartData.textBarStacked3d": "3-D staplad kolumn", "Common.define.chartData.textBarStackedPer": "100% staplad kolumn", "Common.define.chartData.textBarStackedPer3d": "3-D 100% staplad kolumn", "Common.define.chartData.textCharts": "Diagram", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Staplat område - grupperad kolumn", "Common.define.chartData.textComboBarLine": "Grupperad kolumn - rad", "Common.define.chartData.textComboBarLineSecondary": "Grupperad kolumn - rad på andra axeln", "Common.define.chartData.textComboCustom": "Anpassad kombination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Grupperad stapel", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> grupperad stapel", "Common.define.chartData.textHBarStacked": "Stap<PERSON> stapel", "Common.define.chartData.textHBarStacked3d": "3-D staplad stapel", "Common.define.chartData.textHBarStackedPer": "100% staplad stapel", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% staplad stapel", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-<PERSON> linje", "Common.define.chartData.textLineMarker": "<PERSON>je med markö<PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON> linje", "Common.define.chartData.textLineStackedMarker": "Staplad linje med <PERSON>ingar", "Common.define.chartData.textLineStackedPer": "100% staplad linje", "Common.define.chartData.textLineStackedPerMarker": "100% staplad linje med markörer", "Common.define.chartData.textPie": "<PERSON><PERSON>", "Common.define.chartData.textPie3d": "3-<PERSON> paj", "Common.define.chartData.textPoint": "XY (Spridning)", "Common.define.chartData.textScatter": "Sprida ut", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON> med raka linjer", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON><PERSON> med raka linjer och marker<PERSON>r", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON><PERSON> med mjuka linjer", "Common.define.chartData.textScatterSmoothMarker": "<PERSON><PERSON><PERSON> med mjuka linjer och markeringar", "Common.define.chartData.textStock": "Lager", "Common.define.chartData.textSurface": "Yta", "Common.define.effectData.textAcross": "<PERSON><PERSON><PERSON><PERSON> ö<PERSON>", "Common.define.effectData.textAppear": "<PERSON>yka upp", "Common.define.effectData.textArcDown": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textArcLeft": "Båge vänster", "Common.define.effectData.textArcRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textArcUp": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBasic": "Grundläggande", "Common.define.effectData.textBasicSwivel": "<PERSON><PERSON> snurra", "Common.define.effectData.textBasicZoom": "<PERSON><PERSON> zoomning", "Common.define.effectData.textBean": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBlinds": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBlink": "Blinka", "Common.define.effectData.textBoldFlash": "<PERSON><PERSON> blixt", "Common.define.effectData.textBoldReveal": "<PERSON><PERSON> Rev<PERSON>", "Common.define.effectData.textBoomerang": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textBounce": "Studs<PERSON>", "Common.define.effectData.textBounceLeft": "Studsa vänster", "Common.define.effectData.textBounceRight": "<PERSON><PERSON><PERSON> h<PERSON>", "Common.define.effectData.textBox": "<PERSON><PERSON>", "Common.define.effectData.textBrushColor": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCenterRevolve": "Centrum rotera", "Common.define.effectData.textCheckerboard": "Schackbräde", "Common.define.effectData.textCircle": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCollapse": "Dra ihop", "Common.define.effectData.textColorPulse": "Färgpuls", "Common.define.effectData.textComplementaryColor": "Komplementfärg", "Common.define.effectData.textComplementaryColor2": "Komplementfärg 2", "Common.define.effectData.textCompress": "Dra ihop", "Common.define.effectData.textContrast": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textContrastingColor": "<PERSON><PERSON><PERSON><PERSON>f<PERSON><PERSON>", "Common.define.effectData.textCredits": "Lista över medverkande", "Common.define.effectData.textCrescentMoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCurveDown": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCurvedSquare": "Böjd k<PERSON>drat", "Common.define.effectData.textCurvedX": "böjt X", "Common.define.effectData.textCurvyLeft": "Kurvig vänster", "Common.define.effectData.textCurvyRight": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCurvyStar": "<PERSON><PERSON><PERSON><PERSON> stj<PERSON>", "Common.define.effectData.textCustomPath": "Anpassad sökväg", "Common.define.effectData.textCuverUp": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDarken": "Mörkna", "Common.define.effectData.textDecayingWave": "Sönderfallande våg", "Common.define.effectData.textDesaturate": "Omättad", "Common.define.effectData.textDiagonalDownRight": "<PERSON><PERSON><PERSON><PERSON> ner höger", "Common.define.effectData.textDiagonalUpRight": "Diagonalt upp höger", "Common.define.effectData.textDiamond": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDisappear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textDissolveIn": "Upplösa in", "Common.define.effectData.textDissolveOut": "Upplösa ut", "Common.define.effectData.textDown": "<PERSON><PERSON>", "Common.define.effectData.textDrop": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textEmphasis": "Betonings effekt", "Common.define.effectData.textEntrance": "Entréeffekt", "Common.define.effectData.textEqualTriangle": "Liks<PERSON><PERSON> triangel", "Common.define.effectData.textExciting": "Spännande", "Common.define.effectData.textExit": "Utgångseffekt", "Common.define.effectData.textExpand": "Expandera", "Common.define.effectData.textFade": "Blekna", "Common.define.effectData.textFigureFour": "Figur 8 fyra", "Common.define.effectData.textFillColor": "Fyllnadsfärg", "Common.define.effectData.textFlip": "Vänd", "Common.define.effectData.textFloat": "Flyt", "Common.define.effectData.textFloatDown": "<PERSON>t nedåt", "Common.define.effectData.textFloatIn": "<PERSON>t in", "Common.define.effectData.textFloatOut": "Flyt ut", "Common.define.effectData.textFloatUp": "Flyt upp", "Common.define.effectData.textFlyIn": "Flyg in", "Common.define.effectData.textFlyOut": "Flyg ut", "Common.define.effectData.textFontColor": "Teckensnittsfärg", "Common.define.effectData.textFootball": "Fotboll", "Common.define.effectData.textFromBottom": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFromBottomLeft": "Från vänster nederkant", "Common.define.effectData.textFromBottomRight": "<PERSON><PERSON><PERSON> höger <PERSON>", "Common.define.effectData.textFromLeft": "<PERSON><PERSON>n vänster", "Common.define.effectData.textFromRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFromTop": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFromTopLeft": "Från vänster ovankant", "Common.define.effectData.textFromTopRight": "<PERSON><PERSON><PERSON> h<PERSON>", "Common.define.effectData.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textGrowShrink": "Väx/Krymp", "Common.define.effectData.textGrowTurn": "Vä<PERSON> och vända", "Common.define.effectData.textGrowWithColor": "Väx med färg", "Common.define.effectData.textHeart": "Hjärta", "Common.define.effectData.textHeartbeat": "Hjärtslag", "Common.define.effectData.textHexagon": "Sexhörning", "Common.define.effectData.textHorizontal": "Horisontal", "Common.define.effectData.textHorizontalFigure": "Horisontal figur 8", "Common.define.effectData.textHorizontalIn": "Horisontal in", "Common.define.effectData.textHorizontalOut": "Horisontal ut", "Common.define.effectData.textIn": "In", "Common.define.effectData.textInFromScreenCenter": "In från skärmens mitt", "Common.define.effectData.textInSlightly": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textInvertedSquare": "Omvänd kvadrat", "Common.define.effectData.textInvertedTriangle": "<PERSON>mv<PERSON>nd triangel", "Common.define.effectData.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textLeftDown": "<PERSON><PERSON><PERSON><PERSON> ner", "Common.define.effectData.textLeftUp": "Vänster upp", "Common.define.effectData.textLighten": "Lätta", "Common.define.effectData.textLineColor": "<PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON> k<PERSON>va", "Common.define.effectData.textLoopDeLoop": "Loop de Loop", "Common.define.effectData.textModerate": "<PERSON><PERSON><PERSON><PERSON>g", "Common.define.effectData.textNeutron": "Neutron", "Common.define.effectData.textObjectCenter": "Objektets mitt", "Common.define.effectData.textObjectColor": "Objektets färg", "Common.define.effectData.textOctagon": "Oktogon", "Common.define.effectData.textOut": "Ut", "Common.define.effectData.textOutFromScreenBottom": "<PERSON>t från skärmens nederkant", "Common.define.effectData.textOutSlightly": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textParallelogram": "Parallellogram", "Common.define.effectData.textPath": "Rörelseväg", "Common.define.effectData.textPathCurve": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPeanut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textPeekIn": "<PERSON><PERSON>", "Common.define.effectData.textPeekOut": "<PERSON><PERSON>", "Common.define.effectData.textPentagon": "Femhörning", "Common.define.effectData.textPinwheel": "Vindsnurra", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textPointStar": "Stjärnpunkt", "Common.define.effectData.textPointStar4": "4 punktstjärna", "Common.define.effectData.textPointStar5": "5 punktstjärna", "Common.define.effectData.textPointStar6": "6 punktstjärna", "Common.define.effectData.textPointStar8": "8 punktstjärna", "Common.define.effectData.textPulse": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRandomBars": "Slumpmässiga staplar", "Common.define.effectData.textRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRightDown": "<PERSON><PERSON><PERSON> ner", "Common.define.effectData.textRightTriangle": "<PERSON><PERSON><PERSON> triangel", "Common.define.effectData.textRightUp": "<PERSON><PERSON><PERSON> upp", "Common.define.effectData.textRiseUp": "Stiga upp", "Common.define.effectData.textSCurve1": "S kurva 1", "Common.define.effectData.textSCurve2": "S kurva 2", "Common.define.effectData.textShape": "Form", "Common.define.effectData.textShapes": "Former", "Common.define.effectData.textShimmer": "Skimmer", "Common.define.effectData.textShrinkTurn": "Krymp och sväng", "Common.define.effectData.textSineWave": "<PERSON><PERSON>v<PERSON><PERSON>", "Common.define.effectData.textSinkDown": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSlideCenter": "Glid mot mitten", "Common.define.effectData.textSpecial": "Särskild", "Common.define.effectData.textSpin": "S<PERSON><PERSON>", "Common.define.effectData.textSpinner": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpiralIn": "S<PERSON><PERSON> innåt", "Common.define.effectData.textSpiralLeft": "Spiral vänster", "Common.define.effectData.textSpiralOut": "Spiral utåt", "Common.define.effectData.textSpiralRight": "<PERSON><PERSON><PERSON> höger", "Common.define.effectData.textSplit": "Dela", "Common.define.effectData.textSpoke1": "1 eker", "Common.define.effectData.textSpoke2": "2 ekrar", "Common.define.effectData.textSpoke3": "3 ekrar", "Common.define.effectData.textSpoke4": "4 ekrar", "Common.define.effectData.textSpoke8": "8 ekrar", "Common.define.effectData.textSpring": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSquare": "Fyrkant", "Common.define.effectData.textStairsDown": "Trappor nedåt", "Common.define.effectData.textStretch": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textStrips": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSubtle": "<PERSON><PERSON>", "Common.define.effectData.textSwivel": "S<PERSON><PERSON>", "Common.define.effectData.textSwoosh": "<PERSON><PERSON>", "Common.define.effectData.textTeardrop": "T<PERSON>r", "Common.define.effectData.textTeeter": "Gungbräda", "Common.define.effectData.textToBottom": "<PERSON>", "Common.define.effectData.textToBottomLeft": "Till vänster nederkant", "Common.define.effectData.textToBottomRight": "<PERSON> höger neder<PERSON>t", "Common.define.effectData.textToLeft": "<PERSON> v<PERSON>", "Common.define.effectData.textToRight": "<PERSON>", "Common.define.effectData.textToTop": "<PERSON> o<PERSON>t", "Common.define.effectData.textToTopLeft": "Till vänster överkant", "Common.define.effectData.textToTopRight": "<PERSON> höger överkant", "Common.define.effectData.textTransparency": "Genomskinlighet", "Common.define.effectData.textTrapezoid": "Trapets", "Common.define.effectData.textTurnDown": "<PERSON><PERSON><PERSON><PERSON> ner", "Common.define.effectData.textTurnDownRight": "dra ner <PERSON><PERSON> höger", "Common.define.effectData.textTurnUp": "Skruva upp", "Common.define.effectData.textTurnUpRight": "Skruva upp åt höger", "Common.define.effectData.textUnderline": "Understryka", "Common.define.effectData.textUp": "Upp", "Common.define.effectData.textVertical": "Vertikal", "Common.define.effectData.textVerticalFigure": "Vertikal figur 8", "Common.define.effectData.textVerticalIn": "Vertikal in", "Common.define.effectData.textVerticalOut": "Vertikal ut", "Common.define.effectData.textWave": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWedge": "<PERSON><PERSON>", "Common.define.effectData.textWheel": "H<PERSON><PERSON>", "Common.define.effectData.textWhip": "Piska", "Common.define.effectData.textWipe": "Torka", "Common.define.effectData.textZigzag": "Sicksack", "Common.define.effectData.textZoom": "Förstora", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.warnFileLocked": "Dokumentet används av ett annat program. Du kan fortsätta redigera och spara den som en kopia.", "Common.Translation.warnFileLockedBtnEdit": "Skapa en kopia", "Common.Translation.warnFileLockedBtnView": "Öppna skrivskyddad", "Common.UI.ButtonColored.textAutoColor": "Automatisk", "Common.UI.ButtonColored.textNewColor": "Lägg till ny egen färg", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> ramar", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> ramar", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON> stilar", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON> till", "Common.UI.ExtendedColorDialog.textCurrent": "Aktuell", "Common.UI.ExtendedColorDialog.textHexErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett värde mellan 000000 och FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Ny", "Common.UI.ExtendedColorDialog.textRGBErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett numeriskt värde mellan 0 och 255", "Common.UI.HSBColorPicker.textNoColor": "Ingen färg", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Visa lösenord", "Common.UI.SearchBar.textFind": "<PERSON>ö<PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Stäng sökning", "Common.UI.SearchBar.tipNextResult": "Nästa resultat", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Öppna avancerade inställningar", "Common.UI.SearchBar.tipPreviousResult": "Föregående resultat", "Common.UI.SearchDialog.textHighlight": "Markera resultat", "Common.UI.SearchDialog.textMatchCase": "Skiftlägeskänslig", "Common.UI.SearchDialog.textReplaceDef": "Ange ersättningstext", "Common.UI.SearchDialog.textSearchStart": "Skriv din text här", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON> och er<PERSON>t", "Common.UI.SearchDialog.textTitle2": "<PERSON>ö<PERSON>", "Common.UI.SearchDialog.textWholeWords": "Endast hela ord", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON><PERSON> alla", "Common.UI.SynchronizeTip.textDontShow": "Visa inte detta meddelande igen", "Common.UI.SynchronizeTip.textSynchronize": "Dokumentet har ändrats av en annan användare. <br> <PERSON><PERSON><PERSON> för att spara dina ändringar och ladda uppdateringarna.", "Common.UI.ThemeColorPalette.textRecentColors": "Senaste färger", "Common.UI.ThemeColorPalette.textStandartColors": "Standardfärger", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Mörk kontrast", "Common.UI.Themes.txtThemeDark": "M<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Samma som systemet", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Stäng", "Common.UI.Window.noButtonText": "Inga", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bekräftelse", "Common.UI.Window.textDontShow": "Visa inte detta meddelande igen", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Varning", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Views.About.txtAddress": "adress:", "Common.Views.About.txtLicensee": "LICENSINNEHAVARE", "Common.Views.About.txtLicensor": "LICENSGIVARE", "Common.Views.About.txtMail": "e-post:", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "Tel.:", "Common.Views.About.txtVersion": "Version", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON> till", "Common.Views.AutoCorrectDialog.textApplyText": "<PERSON><PERSON>gera när du skriver", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorrigering av text", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformat när du skriver", "Common.Views.AutoCorrectDialog.textBulleted": "Automatiska punktlistor", "Common.Views.AutoCorrectDialog.textBy": "Av", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Lägg till punkt med dubbla mellanslag", "Common.Views.AutoCorrectDialog.textFLCells": "Sätt den första bokstaven i tabellcellerna till en versal", "Common.Views.AutoCorrectDialog.textFLSentence": "<PERSON><PERSON> bokstav varje mening", "Common.Views.AutoCorrectDialog.textHyperlink": "Sökvägar för internet och nätverk med hyperlänk", "Common.Views.AutoCorrectDialog.textHyphens": "Bindes<PERSON>ck (--) med streck (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematisk autokorrigering", "Common.Views.AutoCorrectDialog.textNumbered": "Automatiska nummerlistor", "Common.Views.AutoCorrectDialog.textQuotes": "\"Raka citat\" med \"smarta citat\"", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Följande uttryck är igenkända matematiska uttryck. De kommer inte att kursiveras automatiskt.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON><PERSON> när du skriver", "Common.Views.AutoCorrectDialog.textReplaceType": "<PERSON><PERSON><PERSON><PERSON> text när du skriver", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "Återställ till standard", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Autokorrigering", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Erkända funktioner får endast innehålla bokstäverna A till Z, versaler eller gemener.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Alla uttryck du har lagt till kommer att tas bort och de borttagna kommer att återställas. Vill du fortsätta?", "Common.Views.AutoCorrectDialog.warnReplace": "Autokorrigeringsposten för %1 finns redan. Vill du ersätta den?", "Common.Views.AutoCorrectDialog.warnReset": "All autokorrigering som du lagt till kommer att tas bort och ändringar kommer att återställas till sina ursprungliga värden. Vill du fortsätta?", "Common.Views.AutoCorrectDialog.warnRestore": "Autokorrigeringen för %1 återställs till sitt ursprungliga värde. Vill du fortsätta?", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Författare A till Ö", "Common.Views.Comments.mniAuthorDesc": "Författare Ö till A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Nyaste", "Common.Views.Comments.mniFilterGroups": "Filtrera via grupp", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON> till", "Common.Views.Comments.textAddComment": "Lägg till kommentar", "Common.Views.Comments.textAddCommentToDoc": "Lägg till kommentar till dokumentet", "Common.Views.Comments.textAddReply": "Lägg till svar", "Common.Views.Comments.textAll": "<PERSON>a", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Stäng", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON>ng kommentarer", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Skriv din kommentar här", "Common.Views.Comments.textHintAddComment": "Lägg till kommentar", "Common.Views.Comments.textOpenAgain": "Öppna igen", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "So<PERSON>a kommentarer", "Common.Views.Comments.textViewResolved": "Du har inte behörighet att öppna kommentaren igen", "Common.Views.Comments.txtEmpty": "Det finns inga kommentarer i dokumentet.", "Common.Views.CopyWarningDialog.textDontShow": "Visa inte detta meddelande igen", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> ut och klistra in -åtgärder med redigeringsknapparna i verktygsfältet och snabbmenyn kommer endast att utföras inom denna flik.<br><PERSON><PERSON><PERSON> att kopiera eller klistra in från applikationer utanför fliken, använd följande kortkommandon:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, klipp ut och klistra in åtgärder", "Common.Views.CopyWarningDialog.textToCopy": "till kopia", "Common.Views.CopyWarningDialog.textToCut": "till urk<PERSON>p", "Common.Views.CopyWarningDialog.textToPaste": "till klistra in", "Common.Views.DocumentAccessDialog.textLoading": "Laddar...", "Common.Views.DocumentAccessDialog.textTitle": "Delningsinställningar", "Common.Views.ExternalDiagramEditor.textTitle": "Diagram editor", "Common.Views.Header.labelCoUsersDescr": "Dokumentet redigeras för närvarande av flera användare.", "Common.Views.Header.textAddFavorite": "Markera som favorit", "Common.Views.Header.textAdvSettings": "Avancerade inställningar", "Common.Views.Header.textBack": "Gå till dokument", "Common.Views.Header.textCompactView": "D<PERSON>lj verktygsrad", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textRemoveFavorite": "<PERSON> bort från favoriter", "Common.Views.Header.textSaveBegin": "Sparar...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Alla ändringar sparade", "Common.Views.Header.textSaveExpander": "Alla ändringar sparade", "Common.Views.Header.textShare": "Dela", "Common.Views.Header.textZoom": "Zooma", "Common.Views.Header.tipAccessRights": "Hantera åtkomsträttighet för dokument", "Common.Views.Header.tipDownload": "Ladda ner fil", "Common.Views.Header.tipGoEdit": "Redigera aktuell fil", "Common.Views.Header.tipPrint": "Skriv ut fil", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON> om", "Common.Views.Header.tipSave": "Spara", "Common.Views.Header.tipSearch": "<PERSON>ö<PERSON>", "Common.Views.Header.tipUndo": "Å<PERSON><PERSON>", "Common.Views.Header.tipUndock": "Docka ifrån till separata fönster", "Common.Views.Header.tipUsers": "Visa användare", "Common.Views.Header.tipViewSettings": "Visa inställningar", "Common.Views.Header.tipViewUsers": "Visa användare och hantera dokumentbehörigheter", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON>", "Common.Views.Header.txtRename": "Döp om", "Common.Views.History.textCloseHistory": "Stäng historik", "Common.Views.History.textHide": "Dra ihop", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON> ändringar", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Expandera", "Common.Views.History.textShowAll": "Visa detaljerade ändringar", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Klistra in en bilds URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Detta fält är obligatoriskt", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON>ta fält bör vara en URL i formatet \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Du måste ange giltiga rader och kolumnnummer.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "Det högsta värdet för detta fält är {0}.", "Common.Views.InsertTableDialog.txtMinText": "Det minsta värdet för detta fält är {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON> rader", "Common.Views.InsertTableDialog.txtTitle": "Tabellstorlek", "Common.Views.InsertTableDialog.txtTitleSplit": "Dela cell", "Common.Views.LanguageDialog.labelSelect": "Välj språk för dokumentet", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON><PERSON><PERSON> fil", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "Common.Views.ListSettingsDialog.textFromUrl": "Från URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "Ändra punktsymbol", "Common.Views.ListSettingsDialog.txtBullet": "Bullet", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Bild", "Common.Views.ListSettingsDialog.txtImport": "Importera", "Common.Views.ListSettingsDialog.txtNewBullet": "New bullet", "Common.Views.ListSettingsDialog.txtNewImage": "<PERSON><PERSON> bild", "Common.Views.ListSettingsDialog.txtNone": "ingen", "Common.Views.ListSettingsDialog.txtOfText": "% av text", "Common.Views.ListSettingsDialog.txtSize": "Storlek", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "Listinställningar", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "Stäng fil", "Common.Views.OpenDialog.txtEncoding": "Teckenkodning", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON><PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Skriv in lösenord för att öppna filen", "Common.Views.OpenDialog.txtPassword": "L<PERSON>senord", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON>r du har angett lösenordet och öppnat filen så återställs det aktuella lösenordet till filen.", "Common.Views.OpenDialog.txtTitle": "Välj %1 alternativ", "Common.Views.OpenDialog.txtTitleProtected": "Skyddad fil", "Common.Views.PasswordDialog.txtDescription": "Ange ett lösenord för att skydda detta dokument", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bekräftelse av lösenordet är inte identisk", "Common.Views.PasswordDialog.txtPassword": "L<PERSON>senord", "Common.Views.PasswordDialog.txtRepeat": "Repetera lösenord", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Varning! Om du glömmer lösenordet kan det inte återskapas.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Kryptera med lösenord", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> eller radera l<PERSON>", "Common.Views.Protection.hintSignature": "Lägg till digital signatur eller rad", "Common.Views.Protection.txtAddPwd": "Lägg till lösenord", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtEncrypt": "Kryptera", "Common.Views.Protection.txtInvisibleSignature": "Lägg till digital signatur", "Common.Views.Protection.txtSignature": "Signatur", "Common.Views.Protection.txtSignatureLine": "Lägg till signaturrad", "Common.Views.RenameDialog.textName": "Filnamn", "Common.Views.RenameDialog.txtInvalidName": "Filnamnet får inte innehålla något av följande tecken:", "Common.Views.ReviewChanges.hintNext": "Till nästa ä<PERSON>", "Common.Views.ReviewChanges.hintPrev": "Till föregående ändring", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>bb", "Common.Views.ReviewChanges.strFastDesc": "Realtidssamarbete. Alla ändringar sparas automatiskt.", "Common.Views.ReviewChanges.strStrict": "Strikt", "Common.Views.ReviewChanges.strStrictDesc": "Använd '<PERSON>ra'-knap<PERSON> för att synkronisera de förändringar du och andra gör.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Acceptera nuvarande ändring", "Common.Views.ReviewChanges.tipCoAuthMode": "Ställ in samredigeringsläge", "Common.Views.ReviewChanges.tipCommentRem": "Ta bort kommentarer", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Ta bort aktuella kommentarer", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON> kom<PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON><PERSON> aktuella kommentarer", "Common.Views.ReviewChanges.tipHistory": "Visa versionshistorik", "Common.Views.ReviewChanges.tipRejectCurrent": "Avvisa nuvarande ändring", "Common.Views.ReviewChanges.tipReview": "Spåra ändringar", "Common.Views.ReviewChanges.tipReviewView": " <PERSON><PERSON><PERSON><PERSON> det läge du vill att ändringarna ska visas", "Common.Views.ReviewChanges.tipSetDocLang": "Sätt dokumentspråk", "Common.Views.ReviewChanges.tipSetSpelling": "Stavningskontroll", "Common.Views.ReviewChanges.tipSharing": "Hantera åtkomsträttighet för dokument", "Common.Views.ReviewChanges.txtAccept": "Acceptera", "Common.Views.ReviewChanges.txtAcceptAll": "Acceptera alla ändringar", "Common.Views.ReviewChanges.txtAcceptChanges": "Acceptera ändringar", "Common.Views.ReviewChanges.txtAcceptCurrent": "Acceptera nuvarande ändring", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Stäng", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "Ta bort alla kommentarer", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Ta bort aktuella kommentarer", "Common.Views.ReviewChanges.txtCommentRemMy": "Ta bort mina kommentarer", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Ta bort mina aktuella kommentarer", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON> bort", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON> alla kommentarer", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON><PERSON> aktuella kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON> mina kommentarer", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "<PERSON><PERSON><PERSON> mina nuvarande kommentarer", "Common.Views.ReviewChanges.txtDocLang": "Språk", "Common.Views.ReviewChanges.txtFinal": "Alla ändringar accepterade (Förhandsvisning)", "Common.Views.ReviewChanges.txtFinalCap": "Slutlig", "Common.Views.ReviewChanges.txtHistory": "Versionshistorik", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> (redigering)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Alla ändringar avvisade (Förhandsgranska)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Föregående", "Common.Views.ReviewChanges.txtReject": "Avvisa", "Common.Views.ReviewChanges.txtRejectAll": "Avvisa alla ändringar", "Common.Views.ReviewChanges.txtRejectChanges": "Avvisa ändringar", "Common.Views.ReviewChanges.txtRejectCurrent": "Avvisa nuvarande ändring", "Common.Views.ReviewChanges.txtSharing": "Delning", "Common.Views.ReviewChanges.txtSpelling": "Stavningskontroll", "Common.Views.ReviewChanges.txtTurnon": "Spåra ändringar", "Common.Views.ReviewChanges.txtView": "Visningsläge", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON> till", "Common.Views.ReviewPopover.textAddReply": "Lägg till svar", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Stäng", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+omnämnande ger åtkomst till dokumentet och skickar ett e-postmeddelande", "Common.Views.ReviewPopover.textMentionNotify": "+omnämning meddelar användaren via e-post", "Common.Views.ReviewPopover.textOpenAgain": "Öppna igen", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Du har inte behörighet att öppna kommentaren igen", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Rediger<PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Mapp att spara i", "Common.Views.SearchPanel.textCaseSensitive": "Skiftlägeskänslig", "Common.Views.SearchPanel.textCloseSearch": "Stäng sökning", "Common.Views.SearchPanel.textContentChanged": "Do<PERSON><PERSON> ändrat.", "Common.Views.SearchPanel.textFind": "<PERSON>ö<PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON><PERSON> och er<PERSON>t", "Common.Views.SearchPanel.textMatchUsingRegExp": "Matcha med regulj<PERSON><PERSON>", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON> träffar", "Common.Views.SearchPanel.textNoSearchResults": "Inga sökresultat", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON><PERSON><PERSON><PERSON> alla", "Common.Views.SearchPanel.textReplaceWith": "<PERSON><PERSON>ätt med", "Common.Views.SearchPanel.textSearchHasStopped": "Sökningen har stoppats", "Common.Views.SearchPanel.textTooManyResults": "Det finns för många resultat för att visa här", "Common.Views.SearchPanel.textWholeWords": "Endast hela ord", "Common.Views.SearchPanel.tipNextResult": "Nästa resultat", "Common.Views.SearchPanel.tipPreviousResult": "Föregående resultat", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textBold": "Fet", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Infoga undertecknares namn", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Underteck<PERSON>e får inte vara tom.", "Common.Views.SignDialog.textPurpose": "Syfte för att underteckna det här dokumentet", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> bild", "Common.Views.SignDialog.textSignature": "Signaturen ser ut som", "Common.Views.SignDialog.textTitle": "Underteckna dokument", "Common.Views.SignDialog.textUseImage": "eller klicka på 'Välj bild' för att använda en bild som signatur", "Common.Views.SignDialog.textValid": "Giltig från %1 till %2", "Common.Views.SignDialog.tipFontName": "Fontnamn", "Common.Views.SignDialog.tipFontSize": "Fontstorlek", "Common.Views.SignSettingsDialog.textAllowComment": "Tillåt undertecknare att lägga till kommentar i signaturdialogrutan", "Common.Views.SignSettingsDialog.textInfoEmail": "E-post", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Undertecknare titel", "Common.Views.SignSettingsDialog.textInstructions": "Instruktioner för <PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "Visa datum för signatur på signaturraden", "Common.Views.SignSettingsDialog.textTitle": "Skapa signatur", "Common.Views.SignSettingsDialog.txtEmpty": "Detta fält är obligatoriskt", "Common.Views.SymbolTableDialog.textCharacter": "Tecken", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX värde", "Common.Views.SymbolTableDialog.textCopyright": "Upphovsrättstecken", "Common.Views.SymbolTableDialog.textDCQuote": "Avslutande dubbelt citattecken", "Common.Views.SymbolTableDialog.textDOQuote": "Opening Double Quote", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON><PERSON> ellips", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Em Space", "Common.Views.SymbolTableDialog.textEnDash": "En Dash", "Common.Views.SymbolTableDialog.textEnSpace": "En Space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON>-brytande bindestreck", "Common.Views.SymbolTableDialog.textNBSpace": "No-break Space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow tecken", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em Space", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON>r<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Tid<PERSON>re använda symboler", "Common.Views.SymbolTableDialog.textRegistered": "Registrerat tecken", "Common.Views.SymbolTableDialog.textSCQuote": "Avslutande enkelt citattecken", "Common.Views.SymbolTableDialog.textSection": "Avsnittstecken", "Common.Views.SymbolTableDialog.textShortcut": "Snabbtangent", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Opening Single Quote", "Common.Views.SymbolTableDialog.textSpecial": "Specialtecken", "Common.Views.SymbolTableDialog.textSymbols": "Symboler", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Varumärkesymbol", "Common.Views.UserNameDialog.textDontShow": "Fråga inte igen", "Common.Views.UserNameDialog.textLabel": "Etikett:", "Common.Views.UserNameDialog.textLabelError": "<PERSON><PERSON><PERSON><PERSON> får inte vara tom.", "PE.Controllers.LeftMenu.leavePageText": "Alla ändringar som inte sparats i detta dokument kommer att gå förlorade. <br> <PERSON><PERSON><PERSON> <PERSON>å \"Avbryt\" och sedan \"Spara\" för att spara dem. Klicka på \"OK\" för att kasta alla ändringar som inte sparats.", "PE.Controllers.LeftMenu.newDocumentTitle": "<PERSON>j nanmngiven Presentation", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Varning", "PE.Controllers.LeftMenu.requestEditRightsText": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>t att editera...", "PE.Controllers.LeftMenu.textLoadHistory": "Laddar versionshistorik...", "PE.Controllers.LeftMenu.textNoTextFound": "De data som du har letat efter kunde inte hittas. Ändra dina sökalternativ.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Ersättningen har gjorts. {0} händelser hoppades över.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Sökningen har gjorts. Förekomster som ersatts: {0}", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.applyChangesTextText": "Laddar data...", "PE.Controllers.Main.applyChangesTitleText": "Laddar data", "PE.Controllers.Main.convertationTimeoutText": "Konverteringstiden har överskridits.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> på \"OK\" för att komma tillbaka till dokumentlistan.", "PE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "PE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON><PERSON> misslyckades.", "PE.Controllers.Main.downloadTextText": "Laddar ner presentationen...", "PE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON> ner <PERSON>en", "PE.Controllers.Main.errorAccessDeny": "Du försöker utföra en åtgärd som du inte har rättighet till.<br>Vänligen kontakta din systemadministratör.", "PE.Controllers.Main.errorBadImageUrl": "Bildens URL är felaktig", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Serveranslutning förlorade. Dokumentet kan inte redigeras just nu.", "PE.Controllers.Main.errorComboSeries": "Välj minst två serier med data för att skapa ett kombinationsschema.", "PE.Controllers.Main.errorConnectToServer": "Dokumentet kunde inte sparas. . Kontrollera anslutningsinställningarna eller kontakta administratören <br> <PERSON><PERSON><PERSON> du klickar på \"OK\" -knap<PERSON>, kommer du att bli ombedd att ladda ner dokumentet.", "PE.Controllers.Main.errorDatabaseConnection": "Externt fel.<br>Databasanslutningsfel. Kontakta support om felet kvarstår.", "PE.Controllers.Main.errorDataEncrypted": "Krypterade ändringar har tagits emot, de kan inte avkodas.", "PE.Controllers.Main.errorDataRange": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.errorDefaultMessage": "Felkod: %1", "PE.Controllers.Main.errorEditingDownloadas": "Ett fel har inträffat.<br><PERSON><PERSON><PERSON><PERSON> \"Ladda ned som\" för att spara en säkerhetskopia på din dator.", "PE.Controllers.Main.errorEditingSaveas": "Ett fel har inträffat.<br><PERSON><PERSON><PERSON><PERSON> \"Ladda ned som...\" för att spara en säkerhetskopia på din dator.", "PE.Controllers.Main.errorEmailClient": "Ingen e-postklient kunde hittas.", "PE.Controllers.Main.errorFilePassProtect": "Dokumentet är lösenordsskyddat och kunde inte öppnas. ", "PE.Controllers.Main.errorFileSizeExceed": "Filstorleken överskrider gränsen för din server.<br>Var snäll och kontakta administratören för dokumentservern för mer information.", "PE.Controllers.Main.errorForceSave": "Ett fel uppstod när filen sparades. Använd alternativet \"Spara som\" för att spara filen till din lokala hårddisk eller försök igen senare.", "PE.Controllers.Main.errorKeyEncrypt": "Okänd nyckelbeskrivare", "PE.Controllers.Main.errorKeyExpire": "Nyckelns beskrivning har utgått", "PE.Controllers.Main.errorLoadingFont": "Typsnittet är inte tillgängligt.<br>Vänligen kontakta dokumentserverns administratör.", "PE.Controllers.Main.errorProcessSaveResult": "Fungerade ej att spara.", "PE.Controllers.Main.errorServerVersion": "Textredigerarens version har uppdaterats. <PERSON><PERSON> kommer att laddas om för att verkställa ändringarna.", "PE.Controllers.Main.errorSessionAbsolute": "Dokumentet redigeringssession har löpt ut. Vänligen ladda om sidan.", "PE.Controllers.Main.errorSessionIdle": "Dokumentet har inte redigerats under en ganska lång tid. Var vänlig att ladda om sidan.", "PE.Controllers.Main.errorSessionToken": "Anslutningen till servern har avbrutits. Vänligen ladda om sidan.", "PE.Controllers.Main.errorSetPassword": "Lösenord kunde inte ställas in.", "PE.Controllers.Main.errorStockChart": "Felaktig ordningsföljd. F<PERSON>r att bygga ett aktiediagram placerar du uppgifterna på arket i följande ordning:<br> öppning<PERSON>ris, maxpris, minipris, slutkurs.", "PE.Controllers.Main.errorToken": "Dokumentets säkerhetstoken är inte korrekt. <br>Vänligen kontakta din dokumentserver administratör.", "PE.Controllers.Main.errorTokenExpire": "Dokumentets säkerhetstoken har upphört att gälla.<br>Var vänlig och Kontakta din dokumentserver administratör.", "PE.Controllers.Main.errorUpdateVersion": "Filens version har ändrats. Sidan kommer laddas om.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Internetanslutningen har återställts och filversionen har ändrats.<br><PERSON><PERSON> du fortsätter arbeta, ladda ner filen eller kopiera innehållet för att försäkra dig om att inte förlora något, ladda sedan om denna sida.", "PE.Controllers.Main.errorUserDrop": "<PERSON>n kan inte nås för till<PERSON>. ", "PE.Controllers.Main.errorUsersExceed": "Antalet användare som tillåts av licensen överskreds", "PE.Controllers.Main.errorViewerDisconnect": "Anslutningen bröts. Du kan fortfarande se dokumentet<br>men kommer inte att kunna ladda ner eller skriva ut tills anslutningen är återställd.", "PE.Controllers.Main.leavePageText": "Du har osparade ändringar i presentatioen. <PERSON><PERSON><PERSON> på \"Stanna på den här sidan\" och sedan på \"Spara\" för att spara. <PERSON><PERSON><PERSON> på \"Lämna den här sidan\" för att ta bort alla ändringar som inte har sparats.", "PE.Controllers.Main.leavePageTextOnClose": "Alla ej sparade ändringar i presentationen kommer att gå förlorade. <br> <PERSON><PERSON><PERSON> på \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" och sedan på \"Spara\" för att spara dem. Klicka på \"OK\" för att kassera alla ändringar som inte sparats.", "PE.Controllers.Main.loadFontsTextText": "Laddar data...", "PE.Controllers.Main.loadFontsTitleText": "Laddar data", "PE.Controllers.Main.loadFontTextText": "Laddar data...", "PE.Controllers.Main.loadFontTitleText": "Laddar data", "PE.Controllers.Main.loadImagesTextText": "Laddar bilder...", "PE.Controllers.Main.loadImagesTitleText": "<PERSON>dd<PERSON> bilder", "PE.Controllers.Main.loadImageTextText": "Laddar bild...", "PE.Controllers.Main.loadImageTitleText": "Laddar bild", "PE.Controllers.Main.loadingDocumentTextText": "Laddar presentationen...", "PE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON>en", "PE.Controllers.Main.loadThemeTextText": "Laddar tema...", "PE.Controllers.Main.loadThemeTitleText": "Ladda tema", "PE.Controllers.Main.notcriticalErrorTitle": "Varning", "PE.Controllers.Main.openErrorText": "<PERSON>tt fel uppstod när filen skulle ö<PERSON>", "PE.Controllers.Main.openTextText": "Öppnar presentationen...", "PE.Controllers.Main.openTitleText": "Ö<PERSON><PERSON> presentation", "PE.Controllers.Main.printTextText": "Skriver ut presentation...", "PE.Controllers.Main.printTitleText": "Skriv ut presentation", "PE.Controllers.Main.reloadButtonText": "Ladda om sidan", "PE.Controllers.Main.requestEditFailedMessageText": "Någon redigerar denna presentation. Försök igen senare.", "PE.Controllers.Main.requestEditFailedTitleText": "Ingen behörighet", "PE.Controllers.Main.saveErrorText": "<PERSON>tt fel uppstod när filen skulle sparas", "PE.Controllers.Main.saveErrorTextDesktop": "Den här filen kan inte sparas eller skapas. <br> Möjliga orsaker är: <br> 1. Filen är skrivskyddad. <br> 2. Filen red<PERSON> av andra använda<PERSON>. <br> 3. Disken är full eller skadad.", "PE.Controllers.Main.saveTextText": "<PERSON><PERSON> presentationen...", "PE.Controllers.Main.saveTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.scriptLoadError": "Anslutningen är för <PERSON>, vissa av komponenterna kunde inte laddas. Vänligen ladda om sidan.", "PE.Controllers.Main.splitDividerErrorText": "Antalet rader måste vara en divisor av %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Antalet kolumner måste vara mindre än% 1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Antalet rader måste vara mindre än %1.", "PE.Controllers.Main.textAnonymous": "Anonym", "PE.Controllers.Main.textApplyAll": "<PERSON>ämp<PERSON> alla ekvationer", "PE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON><PERSON> webbp<PERSON>s", "PE.Controllers.Main.textChangesSaved": "Alla ändringar sparade", "PE.Controllers.Main.textClose": "Stäng", "PE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON> för att stänga tipset", "PE.Controllers.Main.textContactUs": "Kontakta <PERSON>jare", "PE.Controllers.Main.textConvertEquation": "Denna ekvation skapades med en gammal version av ekvationsredigeraren som inte längre stöds. Om du vill redigera den konverterar du ekvationen till Office Math ML-format. <br> Konvertera nu?", "PE.Controllers.Main.textCustomLoader": "Observera att enligt licensvillkoren har du inte rätt att byta laddaren. <br> Kontakta vår försäljningsavdelning för att få en offert.", "PE.Controllers.Main.textDisconnect": "Anslutningen förlorades", "PE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "<PERSON>n innehåller automatiska makron. <br> Vill du köra makron?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON> dig mer", "PE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON>en", "PE.Controllers.Main.textLongName": "<PERSON><PERSON> na<PERSON>n (max 128 tecken).", "PE.Controllers.Main.textNoLicenseTitle": "Licensens gräns är nådd", "PE.Controllers.Main.textPaidFeature": "Betald funktion", "PE.Controllers.Main.textReconnect": "Anslutningen återställdes", "PE.Controllers.Main.textRemember": "<PERSON><PERSON> ih<PERSON>g mitt val för alla filer", "PE.Controllers.Main.textRememberMacros": "<PERSON><PERSON> ih<PERSON>g mitt val för alla makron", "PE.Controllers.Main.textRenameError": "Användarnamn får inte var tomt.", "PE.Controllers.Main.textRenameLabel": "<PERSON><PERSON> namn för <PERSON>e", "PE.Controllers.Main.textShape": "Form", "PE.Controllers.Main.textStrict": "Strikt läge", "PE.Controllers.Main.textText": "Text", "PE.Controllers.Main.textTryUndoRedo": "Ångra/Återställ-funktionerna är inaktiva i snabbt samredigeringsläget.<br><PERSON><PERSON><PERSON> på knappen 'Strikt läge' för att växla till strikt samredigeringsläge och redigera filen utan andra användares påverkan och skicka dina ändringar först efter att du har sparat dem. Du kan växla mellan samredigeringslägena med hjälp av avancerade inställningar.", "PE.Controllers.Main.textTryUndoRedoWarn": "Ångra-funktionerna är inaktiverade för snabb samredigeringsläge.", "PE.Controllers.Main.titleLicenseExp": "Licensen har gått ut", "PE.Controllers.Main.titleServerVersion": "Editor uppdaterad", "PE.Controllers.Main.txtAddFirstSlide": "<PERSON>licka för att lägga till den första bilden", "PE.Controllers.Main.txtAddNotes": "<PERSON><PERSON><PERSON> för att lägga till anteckning", "PE.Controllers.Main.txtArt": "Din text här", "PE.Controllers.Main.txtBasicShapes": "Former", "PE.Controllers.Main.txtButtons": "Knappar", "PE.Controllers.Main.txtCallouts": "Pratbubbla", "PE.Controllers.Main.txtCharts": "Diagram", "PE.Controllers.Main.txtClipArt": "ClipArt", "PE.Controllers.Main.txtDateTime": "Datum och tid", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Diagramtitel", "PE.Controllers.Main.txtEditingMode": "St<PERSON>ll in redigeringsläge...", "PE.Controllers.Main.txtErrorLoadHistory": "Gick inte ladda historik", "PE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtFooter": "Sidfot", "PE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "Bild", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Laddar...", "PE.Controllers.Main.txtMath": "<PERSON><PERSON>", "PE.Controllers.Main.txtMedia": "Media", "PE.Controllers.Main.txtNeedSynchronize": "Du har upp<PERSON><PERSON>ar", "PE.Controllers.Main.txtNone": "ingen", "PE.Controllers.Main.txtPicture": "Bild", "PE.Controllers.Main.txtRectangles": "Rektanglar", "PE.Controllers.Main.txtSeries": "Serier", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Line Callout 1 (Border and Accent Bar)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Line Callout 2 (Border and Accent Bar)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Line Callout 3 (Border and Accent Bar)", "PE.Controllers.Main.txtShape_accentCallout1": "Line Callout 1 (Accent Bar)", "PE.Controllers.Main.txtShape_accentCallout2": "Line Callout 2 (Accent Bar)", "PE.Controllers.Main.txtShape_accentCallout3": "Line Callout 3 (Accent Bar)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tillbaka eller föregående knapp", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Startknapp", "PE.Controllers.Main.txtShape_actionButtonBlank": "Tömknapp", "PE.Controllers.Main.txtShape_actionButtonDocument": "Dokumentknapp", "PE.Controllers.Main.txtShape_actionButtonEnd": "Slutknapp", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON><PERSON> eller nästa knapp", "PE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_actionButtonInformation": "Informationsknapp", "PE.Controllers.Main.txtShape_actionButtonMovie": "Film knapp", "PE.Controllers.Main.txtShape_actionButtonReturn": "Återknapp", "PE.Controllers.Main.txtShape_actionButtonSound": "Ljudknapp", "PE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>ge", "PE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON> pil", "PE.Controllers.Main.txtShape_bentConnector5": "Armbågskontakt", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Armbågspilkontakt", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Armbågsdubbelpil", "PE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "<PERSON> båge", "PE.Controllers.Main.txtShape_borderCallout1": "Line Callout 1", "PE.Controllers.Main.txtShape_borderCallout2": "Line Callout 2", "PE.Controllers.Main.txtShape_borderCallout3": "Line Callout 3", "PE.Controllers.Main.txtShape_bracePair": "Dubbelklammer", "PE.Controllers.Main.txtShape_callout1": "Line Callout 1 (No Border)", "PE.Controllers.Main.txtShape_callout2": "Line Callout 2 (No Border)", "PE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No Border)", "PE.Controllers.Main.txtShape_can": "Bur<PERSON>", "PE.Controllers.Main.txtShape_chevron": "Chevron", "PE.Controllers.Main.txtShape_chord": "Chord", "PE.Controllers.Main.txtShape_circularArrow": "C<PERSON><PERSON>är pil", "PE.Controllers.Main.txtShape_cloud": "Mo<PERSON>", "PE.Controllers.Main.txtShape_cloudCallout": "Pratbubbla moln", "PE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "B<PERSON>jd p<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Böjd vänsterpil", "PE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_decagon": "Decagon", "PE.Controllers.Main.txtShape_diagStripe": "Diagon<PERSON> rand", "PE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "PE.Controllers.Main.txtShape_donut": "Donut", "PE.Controllers.Main.txtShape_doubleWave": "Double Wave", "PE.Controllers.Main.txtShape_downArrow": "Nedåtpil", "PE.Controllers.Main.txtShape_downArrowCallout": "Pratbubbla nedåtpil", "PE.Controllers.Main.txtShape_ellipse": "Ellips", "PE.Controllers.Main.txtShape_ellipseRibbon": "Curved Down Ribbon", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Curved Up Ribbon", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Födesschema: Alternativ process", "PE.Controllers.Main.txtShape_flowChartCollate": "Flödesschema: Collate", "PE.Controllers.Main.txtShape_flowChartConnector": "Flödesschema: Anslutning", "PE.Controllers.Main.txtShape_flowChartDecision": "Flödesschema: Decision", "PE.Controllers.Main.txtShape_flowChartDelay": "Flödesschema: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartDisplay": "Flödesschema: Display", "PE.Controllers.Main.txtShape_flowChartDocument": "Flödesschema: Dokument", "PE.Controllers.Main.txtShape_flowChartExtract": "Flödesschema: Uttag", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Flödesschema:Data", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Flödesschema: Intern lagring", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flödesschema: Magnetisk disk", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flödesschema: Direktansluten lagring", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Flödesschema: <PERSON><PERSON><PERSON><PERSON><PERSON> lagring", "PE.Controllers.Main.txtShape_flowChartManualInput": "Flödesschema: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Flödesschema: Manuell operation", "PE.Controllers.Main.txtShape_flowChartMerge": "Flödesschema: <PERSON><PERSON><PERSON> samman", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Flödesschema: Multipla dokument", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flödesschema: <PERSON><PERSON><PERSON><PERSON> utanför sidan", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flödesschema: Lagrad data", "PE.Controllers.Main.txtShape_flowChartOr": "Flödesschema: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flödesschema: Fördefinierad process", "PE.Controllers.Main.txtShape_flowChartPreparation": "Flödesschema: Förberedelse", "PE.Controllers.Main.txtShape_flowChartProcess": "Flödesschema: Process", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Flödesschema: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Flödesschema: <PERSON><PERSON><PERSON> tejp", "PE.Controllers.Main.txtShape_flowChartSort": "Flödesschema: Soretera", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Flödesschema: Summering", "PE.Controllers.Main.txtShape_flowChartTerminator": "Flödesschema: Avslutning", "PE.Controllers.Main.txtShape_foldedCorner": "Skyddat hörn", "PE.Controllers.Main.txtShape_frame": "Ram", "PE.Controllers.Main.txtShape_halfFrame": "Hall of Fame", "PE.Controllers.Main.txtShape_heart": "Hjärta", "PE.Controllers.Main.txtShape_heptagon": "Heptagon", "PE.Controllers.Main.txtShape_hexagon": "Sexhörning", "PE.Controllers.Main.txtShape_homePlate": "Pentagon", "PE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON><PERSON> rullning", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "PE.Controllers.Main.txtShape_leftArrow": "Vänsterpil", "PE.Controllers.Main.txtShape_leftArrowCallout": "Pratbubbla vänsterpil", "PE.Controllers.Main.txtShape_leftBrace": "Vänsterklammer", "PE.Controllers.Main.txtShape_leftBracket": "Vänster hakparentes", "PE.Controllers.Main.txtShape_leftRightArrow": "Vänster-höger-pil", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Pratbubbla vänster-höger-pil", "PE.Controllers.Main.txtShape_leftRightUpArrow": "V<PERSON>nst<PERSON> höger uppåtpil", "PE.Controllers.Main.txtShape_leftUpArrow": "Vänster uppåtpil", "PE.Controllers.Main.txtShape_lightningBolt": "Blixt", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON>l", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON> pil", "PE.Controllers.Main.txtShape_mathDivide": "Division", "PE.Controllers.Main.txtShape_mathEqual": "Lika med", "PE.Controllers.Main.txtShape_mathMinus": "Minus", "PE.Controllers.Main.txtShape_mathMultiply": "Multiplicera", "PE.Controllers.Main.txtShape_mathNotEqual": "Inte lika", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "\"Ingen\" symbol", "PE.Controllers.Main.txtShape_notchedRightArrow": "Skårad högerpil", "PE.Controllers.Main.txtShape_octagon": "Oktagon", "PE.Controllers.Main.txtShape_parallelogram": "Parallellogram", "PE.Controllers.Main.txtShape_pentagon": "Pentagon", "PE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_plaque": "Underteckna", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline2": "Fri form", "PE.Controllers.Main.txtShape_quadArrow": "Rundad rektangel", "PE.Controllers.Main.txtShape_quadArrowCallout": "Fyrdelad pil med pratbubbla", "PE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "Down Ribbon", "PE.Controllers.Main.txtShape_ribbon2": "Uppåt band", "PE.Controllers.Main.txtShape_rightArrow": "Högerpil", "PE.Controllers.Main.txtShape_rightArrowCallout": "Höger<PERSON><PERSON>", "PE.Controllers.Main.txtShape_rightBrace": "Högerklammer", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_round1Rect": "Rektangel med ett rundat hörn", "PE.Controllers.Main.txtShape_round2DiagRect": "Rektangel med rundade hörn diagonalt", "PE.Controllers.Main.txtShape_round2SameRect": "Re<PERSON><PERSON>el med rundade hörn samma sida", "PE.Controllers.Main.txtShape_roundRect": "Rektangel med avrundade hörn", "PE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON> triangel", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON> gubbe", "PE.Controllers.Main.txtShape_snip1Rect": "Rektangel med ett klippt hörn", "PE.Controllers.Main.txtShape_snip2DiagRect": "Rektangel med klippta hörn diagonalt", "PE.Controllers.Main.txtShape_snip2SameRect": "Rektangel med klippta hörn på en sida", "PE.Controllers.Main.txtShape_snipRoundRect": "Rektangel med ett klippt och rundat hörn", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON> stjärna", "PE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON> stjärna", "PE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON> stjärna", "PE.Controllers.Main.txtShape_star24": "24-<PERSON>ts stjärna", "PE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON> stjärna", "PE.Controllers.Main.txtShape_star4": "4-<PERSON>ts stjärna", "PE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON> stjärna", "PE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON> stjä<PERSON>", "PE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON> stjärna", "PE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON> stjärna", "PE.Controllers.Main.txtShape_stripedRightArrow": "Genomstruken högerpil", "PE.Controllers.Main.txtShape_sun": "Sol", "PE.Controllers.Main.txtShape_teardrop": "T<PERSON>r", "PE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_trapezoid": "Trapes", "PE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_upArrow": "Pil upp", "PE.Controllers.Main.txtShape_upArrowCallout": "Pratbubbla uppåtpil", "PE.Controllers.Main.txtShape_upDownArrow": "Upp-<PERSON>er pil", "PE.Controllers.Main.txtShape_uturnArrow": "U-sväng pil", "PE.Controllers.Main.txtShape_verticalScroll": "Vertikal rullning", "PE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Callout", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Pratbubbla rektangel", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rundad rektangel pratbubbla", "PE.Controllers.Main.txtSldLtTBlank": "Töm", "PE.Controllers.Main.txtSldLtTChart": "Diagram", "PE.Controllers.Main.txtSldLtTChartAndTx": "Diagram och text", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art och text", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art och vertikal text", "PE.Controllers.Main.txtSldLtTCust": "Anpassad", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "Fyra objekt", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media och text", "PE.Controllers.Main.txtSldLtTObj": "Titel och Objekt", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objekt och två objket", "PE.Controllers.Main.txtSldLtTObjAndTx": "Objekt och text", "PE.Controllers.Main.txtSldLtTObjOnly": "Objekt", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objekt över text", "PE.Controllers.Main.txtSldLtTObjTx": "T<PERSON><PERSON>, Objekt och Rubrik", "PE.Controllers.Main.txtSldLtTPicTx": "Bild och bildtext", "PE.Controllers.Main.txtSldLtTSecHead": "Sektion rubrik", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Titel", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON> en<PERSON>t", "PE.Controllers.Main.txtSldLtTTwoColTx": "Två kolumner text", "PE.Controllers.Main.txtSldLtTTwoObj": "Två objekt", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Två objekt och Objekt", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Två objekt och text", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Två objekt över text", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Två text och Objekt", "PE.Controllers.Main.txtSldLtTTx": "Text", "PE.Controllers.Main.txtSldLtTTxAndChart": "Text och Diagram", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Text och Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Text och Media", "PE.Controllers.Main.txtSldLtTTxAndObj": "Text och Objekt", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Text och 2 objekt", "PE.Controllers.Main.txtSldLtTTxOverObj": "Text över objekt", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Vertikal Titel och text", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Vertikal Titel över diagram", "PE.Controllers.Main.txtSldLtTVertTx": "Vertikal text", "PE.Controllers.Main.txtSlideNumber": "Bild nummer", "PE.Controllers.Main.txtSlideSubtitle": "Underrubrik", "PE.Controllers.Main.txtSlideText": "Bildtext", "PE.Controllers.Main.txtSlideTitle": "Titel", "PE.Controllers.Main.txtStarsRibbons": "Stjärnor & banner", "PE.Controllers.Main.txtTheme_basic": "Grundläggande", "PE.Controllers.Main.txtTheme_blank": "Töm", "PE.Controllers.Main.txtTheme_classic": "Klassisk", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_dotted": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office_theme": "Office tema", "PE.Controllers.Main.txtTheme_official": "Officiell", "PE.Controllers.Main.txtTheme_pixel": "Pixel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Sköldpadda", "PE.Controllers.Main.txtXAxis": "X-axel", "PE.Controllers.Main.txtYAxis": "Y-axel", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON>nt fel.", "PE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>e stöds ej.", "PE.Controllers.Main.uploadImageExtMessage": "Okänt bildformat.", "PE.Controllers.Main.uploadImageFileCountMessage": "Inga bilder uppladdade.", "PE.Controllers.Main.uploadImageSizeMessage": "Bilden är för stor. Den maximala storleken är 25 MB.", "PE.Controllers.Main.uploadImageTextText": "Laddar upp bild...", "PE.Controllers.Main.uploadImageTitleText": "Laddar upp bild", "PE.Controllers.Main.waitText": "Vänta...", "PE.Controllers.Main.warnBrowserIE9": "Fungerar dåligt med Internet Explorer 9. Använd version 10 eller högre.", "PE.Controllers.Main.warnBrowserZoom": "<PERSON> webbläsares nuvarande zoominställningar stöds inte fullt ut. Återställ till standard zoom genom att trycka på Ctrl + 0.", "PE.Controllers.Main.warnLicenseExceeded": "Gränsen är nådd för antalet %1 samtidiga anslutna redigerare. Dokumentet öppnas som skrivskyddat.<br>Kontakta administratören för mer information.", "PE.Controllers.Main.warnLicenseExp": "Din licens har gått ut.<br>Förnya din licens och uppdatera sidan.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licensen ogiltig. <br> Ingen access till redigeringsfunktioner.<br>Kontakta din administratör.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Licensen måste förnyas.<br>Endast begränsad funktionalitet.<br>Kontakta din administratör för full funktionalitet.", "PE.Controllers.Main.warnLicenseUsersExceeded": "Gränsen är nådd för antalet %1 redigerare.<br>Kontakta administratören för mer information.", "PE.Controllers.Main.warnNoLicense": "Gränsen är nådd för antalet %1 samtidiga anslutna redigerare. Dokumentet öppnas som skrivskyddat.<br>Kontakta %1 försäljningsteamet för personliga uppgraderings villkor.", "PE.Controllers.Main.warnNoLicenseUsers": "Gränsen är nådd för antalet %1 redigerare.<br>Kontakta %1 försäljningsteamet för personliga uppgraderings villkor.", "PE.Controllers.Main.warnProcessRightsChange": "Du har nekats rätten att redigera filen.", "PE.Controllers.Search.notcriticalErrorTitle": "Varning", "PE.Controllers.Search.textNoTextFound": "De data som du har letat efter kunde inte hittas. Ändra dina sökalternativ.", "PE.Controllers.Search.textReplaceSkipped": "Ersättningen har gjorts. {0} händelser hoppades över.", "PE.Controllers.Search.textReplaceSuccess": "Sök<PERSON> har gjorts. {0} förekomster har ersatts", "PE.Controllers.Statusbar.textDisconnect": "<br>Anslutningen förlorades</br><br>Försöker återansluta. Vänligen kontroller anslutningens inställningar.", "PE.Controllers.Statusbar.zoomText": "Zooma {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "Teckensnittet du kommer att spara finns inte på den aktuella enheten. <br>Textstilen kommer att visas med ett av systemets teckensnitt, sparade teckensnitt kommer att användas när det är tillgängligt. <br> Vill du fortsätta ?", "PE.Controllers.Toolbar.textAccent": "Accenter", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textEmptyImgUrl": "Du behöver ange en URL för bilden.", "PE.Controllers.Toolbar.textFontSizeErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett numeriskt värde mellan 0 och 300", "PE.Controllers.Toolbar.textFraction": "Frak<PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textInsert": "Infoga", "PE.Controllers.Toolbar.textIntegral": "Inte<PERSON><PERSON>", "PE.Controllers.Toolbar.textLargeOperator": "Stora operatorer", "PE.Controllers.Toolbar.textLimitAndLog": "Begränsningar och logaritmer", "PE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textOperator": "Operatorer", "PE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textScript": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "Symboler", "PE.Controllers.Toolbar.textWarning": "Varning", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Höger-vänster pil ovanför", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Vänster pil ovanför", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Högerriktad pil ovanför", "PE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "PE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "PE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON> formel (med hållare)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Boxad formel (exempel)", "PE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Oberbrace", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC med overbar", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y med overbar", "PE.Controllers.Toolbar.txtAccent_DDDot": "Trippel punkt", "PE.Controllers.Toolbar.txtAccent_DDot": "Dubbelpunkt", "PE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON> overbar", "PE.Controllers.Toolbar.txtAccent_Grave": "Grav", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Gruppera tecken under", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Gruppera tecken ovanför", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON> harp<PERSON> o<PERSON>", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Högerrik<PERSON> harp<PERSON>l ovanför", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parentes med avgränsare", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parentes med avgränsare", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON> gaffling", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON> gaffling", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parentes med avgränsare", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON> gaffling", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Fall (tv<PERSON> villkor)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Fall (tre villkor)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Stapla objekt", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Stapla objekt", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Fall exempel", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial koefficient", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial koefficient", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentes med avgränsare", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Enkel parentes", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Enkel parentes", "PE.Controllers.Toolbar.txtFractionDiagonal": "Skev fraktion", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Differentiell", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Differentiell", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Differentiell", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Differentiell", "PE.Controllers.Toolbar.txtFractionHorizontal": "Linjär fraktion", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi över 2", "PE.Controllers.Toolbar.txtFractionSmall": "Liten fraktion", "PE.Controllers.Toolbar.txtFractionVertical": "Staplade fraktioner", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Inverterad cosine funktion", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperboliska inverterad cosine funktion", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Inverterad cotangent funktion", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperboliska inverterad cotangent funktion", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Inverterad cosecant funktion", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperboliska inverterad cosecant funktion", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Inverterad secant funktion", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperboliska inverterad secant funktion", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Inverterad sine funktion", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperboliska inverterad sine funktion", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Inverterad tangent funktion", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperboliska inverterad tangent funktion", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON>sin<PERSON> funktion", "PE.Controllers.Toolbar.txtFunction_Cosh": "Hyperboliska cosine funktion", "PE.Controllers.Toolbar.txtFunction_Cot": "Cotangens funktion", "PE.Controllers.Toolbar.txtFunction_Coth": "Hyperboliska cotangent funktion", "PE.Controllers.Toolbar.txtFunction_Csc": "Cosekant funktion", "PE.Controllers.Toolbar.txtFunction_Csch": "Hyperboliska cosekanten funktion", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formel", "PE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sech": "Hyperboliska secant funktion", "PE.Controllers.Toolbar.txtFunction_Sin": "Sine funktion", "PE.Controllers.Toolbar.txtFunction_Sinh": "Hyperboliska sine funktion", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangent funktion", "PE.Controllers.Toolbar.txtFunction_Tanh": "Hyperboliska tangent function", "PE.Controllers.Toolbar.txtIntegral": "Integral", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Differentiell theta", "PE.Controllers.Toolbar.txtIntegral_dx": "Differentiell x", "PE.Controllers.Toolbar.txtIntegral_dy": "Differentiell y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralDouble": "Dubbel integral", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dubbel integral", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dubbelintegral", "PE.Controllers.Toolbar.txtIntegralOriented": "Konturintegral", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Konturintegral", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Ytliga integraler", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Ytliga integraler", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Ytliga integraler", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Konturintegral", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volymintegral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volymintegral", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volymintegral", "PE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "PE.Controllers.Toolbar.txtIntegralTriple": "Trippel integral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Trippel integral", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Trippel integral", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Samprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Samprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Samprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Samprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Samprodukt", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Koppla ihop", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Genomskärning", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Genomskärning", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Genomskärning", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Genomskärning", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Genomskärning", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summering", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Koppla ihop", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Koppla ihop", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Koppla ihop", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Koppla ihop", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Koppla ihop", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON><PERSON> exempel", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Max exempel", "PE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Naturlig logaritm", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritm", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritm", "PE.Controllers.Toolbar.txtLimitLog_Max": "Max", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minst", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 tom matris", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 tom matris", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 tom matris", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 tom matris", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON> matris med parenteser", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON> matris med parenteser", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON> matris med parenteser", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON> matris med parenteser", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 tom matris", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 tom matris", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 tom matris", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 tom matris", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON> i mitten", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonala punkter", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON> punkter", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON>les matris", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON>les matris", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identitetsmatris", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 identitetsmatris", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identitetsmatris", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identitetsmatris", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Höger vänsterpil under", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Höger-vänster pil ovanför", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Vänsterpil under", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Vänster pil ovanför", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Högerriktad pil nedanför", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Högerriktad pil ovanför", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Kolon lika med", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Avkastning", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta-utbyten", "PE.Controllers.Toolbar.txtOperator_Definition": "Lika med enligt definition", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta lika med", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Höger vänsterpil under", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Höger-vänster pil ovanför", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Vänsterpil under", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Vänsterpil ovan", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Högerriktad pil nedanför", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Högerriktad pil ovanför", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Lika med lika", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus lika med", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus lika med", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Mätt av", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Roten ur med grad", "PE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radikal med grad", "PE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> ur", "PE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSubSup": "Nedsänkt-upphöjd", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Vänster subskript-superscript", "PE.Controllers.Toolbar.txtScriptSup": "Upph<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_about": "Ungefär", "PE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON> lika med", "PE.Controllers.Toolbar.txtSymbol_ast": "Asterisk operator", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Slå vad", "PE.Controllers.Toolbar.txtSymbol_bullet": "Punktlista typ", "PE.Controllers.Toolbar.txtSymbol_cap": "Genomskärning", "PE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON><PERSON><PERSON> elips i mitten", "PE.Controllers.Toolbar.txtSymbol_celsius": "Grader celsius", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Ungefär lika med", "PE.Controllers.Toolbar.txtSymbol_cup": "Koppla ihop", "PE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON> <PERSON> diagonal ellips", "PE.Controllers.Toolbar.txtSymbol_degree": "Grader", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Divisionstecken", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Nedåtpil", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Lika med", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identisk med", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Det existerar", "PE.Controllers.Toolbar.txtSymbol_factorial": "Faktoriell", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grader fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON><PERSON>a", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> än eller lika med", "PE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON> s<PERSON>n", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_in": "Element av", "PE.Controllers.Toolbar.txtSymbol_inc": "Öka", "PE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Vänsterpil", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Vänster-höger pil", "PE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> än eller lika med", "PE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON> än", "PE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> mindre än", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Inte lika med", "PE.Controllers.Toolbar.txtSymbol_ni": "Fortsätt som medlem", "PE.Controllers.Toolbar.txtSymbol_not": "Inte signerad", "PE.Controllers.Toolbar.txtSymbol_notexists": "Det existerar inte", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Partiell differential", "PE.Controllers.Toolbar.txtSymbol_percent": "Procentsats", "PE.Controllers.Toolbar.txtSymbol_phi": "Phi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Proportionell mot", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON> roten", "PE.Controllers.Toolbar.txtSymbol_qed": "Slut på bevis", "PE.Controllers.Toolbar.txtSymbol_rddots": "Diagonal ellips", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Högerpil", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Ra<PERSON><PERSON> skylt", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Därför", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "Multiplikationstecken", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Pil upp", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "PE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "PE.Controllers.Toolbar.txtSymbol_vdots": "Vertikal ellips", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Anpassa till bild", "PE.Controllers.Viewport.textFitWidth": "Anpassa till bredd", "PE.Views.Animation.strDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strDuration": "Varaktighet", "PE.Views.Animation.strRepeat": "Upprepa", "PE.Views.Animation.strRewind": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strStart": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.strTrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textMoreEffects": "Visa fler effekter", "PE.Views.Animation.textMoveEarlier": "<PERSON>tta tidigare", "PE.Views.Animation.textMoveLater": "<PERSON><PERSON> senare", "PE.Views.Animation.textMultiple": "Flera", "PE.Views.Animation.textNone": "Inga", "PE.Views.Animation.textNoRepeat": "(inget)", "PE.Views.Animation.textOnClickOf": "Vid klick på", "PE.Views.Animation.textOnClickSequence": "<PERSON>id klick sekvens", "PE.Views.Animation.textStartAfterPrevious": "<PERSON><PERSON>", "PE.Views.Animation.textStartOnClick": "<PERSON>id klick", "PE.Views.Animation.textStartWithPrevious": "Med fö<PERSON>", "PE.Views.Animation.textUntilEndOfSlide": "Till slutet av bilden", "PE.Views.Animation.textUntilNextClick": "Till nästa klick", "PE.Views.Animation.txtAddEffect": "Lägg till animation", "PE.Views.Animation.txtAnimationPane": "Animationsrutan", "PE.Views.Animation.txtParameters": "Parametrar", "PE.Views.Animation.txtPreview": "Förhandsgranska", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Förhandsgranska effekt", "PE.Views.AnimationDialog.textTitle": "<PERSON><PERSON> effekter", "PE.Views.ChartSettings.textAdvanced": "Visa avancerade inställningar", "PE.Views.ChartSettings.textChartType": "<PERSON><PERSON> diagramtyp", "PE.Views.ChartSettings.textEditData": "Redigera data", "PE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textKeepRatio": "Konstanta proportioner", "PE.Views.ChartSettings.textSize": "Storlek", "PE.Views.ChartSettings.textStyle": "Stil", "PE.Views.ChartSettings.textWidth": "Bredd", "PE.Views.ChartSettingsAdvanced.textAlt": "Alternativ text", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Beskrivning", "PE.Views.ChartSettingsAdvanced.textAltTip": "Den alternativa textbaserad förekomsten av visuell objektinformation som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar för att hjälpa dem att bättre förstå vilken information som finns i bilden, figuren, diagrammet eller tabellen.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ChartSettingsAdvanced.textCenter": "Centrera", "PE.Views.ChartSettingsAdvanced.textFrom": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Konstanta proportioner", "PE.Views.ChartSettingsAdvanced.textPlacement": "Placering", "PE.Views.ChartSettingsAdvanced.textPosition": "Position", "PE.Views.ChartSettingsAdvanced.textSize": "Storlek", "PE.Views.ChartSettingsAdvanced.textTitle": "Diagram - avancerade inställningar", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Övre vänstra hörnet", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ChartSettingsAdvanced.textWidth": "Bredd", "PE.Views.DateTimeDialog.confirmDefault": "Ange standardformat för {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Ange som standard", "PE.Views.DateTimeDialog.textFormat": "Format", "PE.Views.DateTimeDialog.textLang": "Språk", "PE.Views.DateTimeDialog.textUpdate": "Uppdatera automatiskt", "PE.Views.DateTimeDialog.txtTitle": "Datum & Tid", "PE.Views.DocumentHolder.aboveText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.addCommentText": "Lägg till kommentar", "PE.Views.DocumentHolder.addToLayoutText": "Lägg till i layout", "PE.Views.DocumentHolder.advancedImageText": "Bild avancerade inställningar", "PE.Views.DocumentHolder.advancedParagraphText": "Avsnitt avancerade inställningar", "PE.Views.DocumentHolder.advancedShapeText": "Form - Avancerade inställningar", "PE.Views.DocumentHolder.advancedTableText": "Tabell avancerade inställningar", "PE.Views.DocumentHolder.alignmentText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.belowText": "Under", "PE.Views.DocumentHolder.cellAlignText": "Cell vertikal justering", "PE.Views.DocumentHolder.cellText": "Cell", "PE.Views.DocumentHolder.centerText": "Centrera", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON> kol<PERSON>n", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> rad", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>a tabell", "PE.Views.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.direct270Text": "Rotera text uppåt", "PE.Views.DocumentHolder.direct90Text": "Rotera text nedåt", "PE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.directionText": "Textriktning", "PE.Views.DocumentHolder.editChartText": "Redigera data", "PE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON><PERSON> länk", "PE.Views.DocumentHolder.hyperlinkText": "Hyperlänk", "PE.Views.DocumentHolder.ignoreAllSpellText": "Ignorera alla", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Kolumn vänster", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnText": "Infoga kolumn", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowBelowText": "Rad under", "PE.Views.DocumentHolder.insertRowText": "Infoga rad", "PE.Views.DocumentHolder.insertText": "Infoga", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.leftText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.loadSpellText": "Laddar varianter...", "PE.Views.DocumentHolder.mergeCellsText": "Slå ihop celler", "PE.Views.DocumentHolder.mniCustomTable": "Infoga egen tabell", "PE.Views.DocumentHolder.moreText": "Fler varianter...", "PE.Views.DocumentHolder.noSpellVariantsText": "Inga varianter", "PE.Views.DocumentHolder.originalSizeText": "Faktisk storlek", "PE.Views.DocumentHolder.removeHyperlinkText": "<PERSON> bort länk", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.spellcheckText": "Rättstavning", "PE.Views.DocumentHolder.splitCellsText": "Dela cell...", "PE.Views.DocumentHolder.splitCellTitleText": "Dela cell", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> l<PERSON> bak", "PE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON> b<PERSON>", "PE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON> f<PERSON>", "PE.Views.DocumentHolder.textArrangeFront": "Flytta till förgrund", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "Beskär", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Passa", "PE.Views.DocumentHolder.textCut": "<PERSON><PERSON><PERSON> ut", "PE.Views.DocumentHolder.textDistributeCols": "Distribuera kolumner", "PE.Views.DocumentHolder.textDistributeRows": "Distribuera rader", "PE.Views.DocumentHolder.textEditPoints": "Redigera punkter", "PE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON><PERSON> ho<PERSON>", "PE.Views.DocumentHolder.textFlipV": "Vänd vertikalt", "PE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON><PERSON> fil", "PE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "PE.Views.DocumentHolder.textFromUrl": "Från URL", "PE.Views.DocumentHolder.textNextPage": "<PERSON>ästa bild", "PE.Views.DocumentHolder.textPaste": "Klistra in", "PE.Views.DocumentHolder.textPrevPage": "Föregående bild", "PE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON><PERSON> bild", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Rotera 90° moturs", "PE.Views.DocumentHolder.textRotate90": "Rotera 90° medsols", "PE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textShapeAlignCenter": "Centrera", "PE.Views.DocumentHolder.textShapeAlignLeft": "Vänsterjustera", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Centrera", "PE.Views.DocumentHolder.textShapeAlignRight": "Högerjustera", "PE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON> toppen", "PE.Views.DocumentHolder.textSlideSettings": "Bild inställningar", "PE.Views.DocumentHolder.textUndo": "Å<PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "Detta element redigeras för närvarande av en annan användare.", "PE.Views.DocumentHolder.toDictionaryText": "Lägg till i ordlista", "PE.Views.DocumentHolder.txtAddBottom": "Lägg till bottenlinje", "PE.Views.DocumentHolder.txtAddFractionBar": "Lägg till fraktionbar", "PE.Views.DocumentHolder.txtAddHor": "Lägg till horisontell linje", "PE.Views.DocumentHolder.txtAddLB": "Lägg till bottenlinje", "PE.Views.DocumentHolder.txtAddLeft": "Lägg till vänster ram", "PE.Views.DocumentHolder.txtAddLT": "Lägg till vänster övre linje", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON> till höger ram", "PE.Views.DocumentHolder.txtAddTop": "Lägg till övre ram", "PE.Views.DocumentHolder.txtAddVer": "Lägg till horisontell linje", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON> te<PERSON>n", "PE.Views.DocumentHolder.txtArrange": "Ordna", "PE.Views.DocumentHolder.txtBackground": "Bakgrund", "PE.Views.DocumentHolder.txtBorderProps": "Ramegenskaper", "PE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtChangeLayout": "<PERSON><PERSON>", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON> te<PERSON>", "PE.Views.DocumentHolder.txtColumnAlign": "Kolumnjustering", "PE.Views.DocumentHolder.txtDecreaseArg": "Minska <PERSON>", "PE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> argument", "PE.Views.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> manuell brytning", "PE.Views.DocumentHolder.txtDeleteChars": "<PERSON><PERSON>a o<PERSON><PERSON>ande tecken", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Radera omslutande tecken och separatorer", "PE.Views.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON>a tecken", "PE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> radikal", "PE.Views.DocumentHolder.txtDeleteSlide": "<PERSON><PERSON><PERSON> bild", "PE.Views.DocumentHolder.txtDistribHor": "Distribuera horisontellt", "PE.Views.DocumentHolder.txtDistribVert": "Distribuera vertikalt", "PE.Views.DocumentHolder.txtDuplicateSlide": "Dup<PERSON><PERSON> bild", "PE.Views.DocumentHolder.txtFractionLinear": "Byt till linjärt bråk", "PE.Views.DocumentHolder.txtFractionSkewed": "Byt till skev fraktion", "PE.Views.DocumentHolder.txtFractionStacked": "Byt till staplad fraktion", "PE.Views.DocumentHolder.txtGroup": "Grupp", "PE.Views.DocumentHolder.txtGroupCharOver": "Cha<PERSON> ö<PERSON> text", "PE.Views.DocumentHolder.txtGroupCharUnder": "Char under text", "PE.Views.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON> nedre ramen", "PE.Views.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideCloseBracket": "G<PERSON><PERSON> stängningsparantes", "PE.Views.DocumentHolder.txtHideDegree": "Göm grad", "PE.Views.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>je", "PE.Views.DocumentHolder.txtHideLB": "Dölj vänstra bottenlinjen", "PE.Views.DocumentHolder.txtHideLeft": "Dölj vänster ram", "PE.Views.DocumentHolder.txtHideLT": "Dölj vänstra topplinjen", "PE.Views.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON> ram", "PE.Views.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON> toppramen", "PE.Views.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON> vertikal linje", "PE.Views.DocumentHolder.txtIncreaseArg": "Öka argumentets storlek", "PE.Views.DocumentHolder.txtInsertArgAfter": "Infoga argument efter", "PE.Views.DocumentHolder.txtInsertArgBefore": "Infoga argument före", "PE.Views.DocumentHolder.txtInsertBreak": "Infoga manuell brytning", "PE.Views.DocumentHolder.txtInsertEqAfter": "Infoga ekvation efter", "PE.Views.DocumentHolder.txtInsertEqBefore": "Infoga ekvation före", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON><PERSON> endast text", "PE.Views.DocumentHolder.txtLimitChange": "<PERSON><PERSON> gr<PERSON> plats", "PE.Views.DocumentHolder.txtLimitOver": "Begränsa över text", "PE.Views.DocumentHolder.txtLimitUnder": "Begr<PERSON><PERSON><PERSON> under text", "PE.Views.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON> parenteser till argumentets höjd", "PE.Views.DocumentHolder.txtMatrixAlign": "Matrisjustering", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Flytta bilden till slutet", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Flytta bilden till början", "PE.Views.DocumentHolder.txtNewSlide": "<PERSON><PERSON> bild", "PE.Views.DocumentHolder.txtOverbar": "Stapel över text", "PE.Views.DocumentHolder.txtPasteDestFormat": "Använd <PERSON>tema", "PE.Views.DocumentHolder.txtPastePicture": "Bild", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Behåll ursprungsformatering", "PE.Views.DocumentHolder.txtPressLink": "Tryck {0} och klicka på länken", "PE.Views.DocumentHolder.txtPreview": "<PERSON>a visning", "PE.Views.DocumentHolder.txtPrintSelection": "Skriv ut markering", "PE.Views.DocumentHolder.txtRemFractionBar": "Ta bort fraktionslinje", "PE.Views.DocumentHolder.txtRemLimit": "Ta bort begr<PERSON>", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Ta bort tecken med accenter", "PE.Views.DocumentHolder.txtRemoveBar": "Ta bort stapel", "PE.Views.DocumentHolder.txtRemScripts": "Ta bort script", "PE.Views.DocumentHolder.txtRemSubscript": "Ta bort nedsä<PERSON>t", "PE.Views.DocumentHolder.txtRemSuperscript": "<PERSON> bort upphöjt", "PE.Views.DocumentHolder.txtResetLayout": "Återställ bild", "PE.Views.DocumentHolder.txtScriptsAfter": "<PERSON><PERSON><PERSON><PERSON> efter text", "PE.Views.DocumentHolder.txtScriptsBefore": "Skript före text", "PE.Views.DocumentHolder.txtSelectAll": "<PERSON><PERSON> allt", "PE.Views.DocumentHolder.txtShowBottomLimit": "Visa nedre gräns", "PE.Views.DocumentHolder.txtShowCloseBracket": "Visa avslutande haken", "PE.Views.DocumentHolder.txtShowDegree": "Visa grad", "PE.Views.DocumentHolder.txtShowOpenBracket": "Visa öppingsfästning", "PE.Views.DocumentHolder.txtShowPlaceholder": "Visa platshållare", "PE.Views.DocumentHolder.txtShowTopLimit": "Visa övre gräns", "PE.Views.DocumentHolder.txtSlide": "Bild", "PE.Views.DocumentHolder.txtSlideHide": "<PERSON><PERSON><PERSON><PERSON> bild", "PE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "PE.Views.DocumentHolder.txtTop": "Ö<PERSON><PERSON>", "PE.Views.DocumentHolder.txtUnderbar": "Stapel under text", "PE.Views.DocumentHolder.txtUngroup": "Dela upp", "PE.Views.DocumentHolder.txtWarnUrl": "Att klicka på denna länk kan skada din utrustning och dess innehåll.<br>Är du säker på att du vill fortsätta?", "PE.Views.DocumentHolder.vertAlignText": "Vertikal anpassning", "PE.Views.DocumentPreview.goToSlideText": "Gå till bild", "PE.Views.DocumentPreview.slideIndexText": "Bild {0} of {1}", "PE.Views.DocumentPreview.txtClose": "A<PERSON><PERSON><PERSON><PERSON> visning", "PE.Views.DocumentPreview.txtEndSlideshow": "<PERSON><PERSON><PERSON><PERSON>a visning", "PE.Views.DocumentPreview.txtExitFullScreen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtFinalMessage": "Presentationen färdig. Klicka för att avsluta.", "PE.Views.DocumentPreview.txtFullScreen": "Fullskärm", "PE.Views.DocumentPreview.txtNext": "<PERSON>ästa bild", "PE.Views.DocumentPreview.txtPageNumInvalid": "Ogiltigt bildnummer", "PE.Views.DocumentPreview.txtPause": "Pausa presentationen", "PE.Views.DocumentPreview.txtPlay": "<PERSON><PERSON>en", "PE.Views.DocumentPreview.txtPrev": "Föregående bild", "PE.Views.DocumentPreview.txtReset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnAboutCaption": "Om", "PE.Views.FileMenu.btnBackCaption": "Gå till dokument", "PE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON>ng meny", "PE.Views.FileMenu.btnCreateNewCaption": "Skapa ny", "PE.Views.FileMenu.btnDownloadCaption": "Ladda ner som", "PE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "Öppna", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Versionshistorik", "PE.Views.FileMenu.btnInfoCaption": "Presentation info", "PE.Views.FileMenu.btnPrintCaption": "Skriv ut", "PE.Views.FileMenu.btnProtectCaption": "<PERSON>dda", "PE.Views.FileMenu.btnRecentFilesCaption": "Ö<PERSON>na senaste", "PE.Views.FileMenu.btnRenameCaption": "Döp om", "PE.Views.FileMenu.btnReturnCaption": "Tillbaka till presentationen", "PE.Views.FileMenu.btnRightsCaption": "Behörigheter", "PE.Views.FileMenu.btnSaveAsCaption": "Spara som", "PE.Views.FileMenu.btnSaveCaption": "Spara", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Spara kopia som", "PE.Views.FileMenu.btnSettingsCaption": "Avancerade inställningar", "PE.Views.FileMenu.btnToEditCaption": "Redigera presentation", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Tom presentation", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Skapa ny", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Tillämpa", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Lägg till författare", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON>gg till text", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Program", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Författare", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Skapad ", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Senast ändrad av", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Ägare", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Plats", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON>er som har beh<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Ämne", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titel", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uppladdad", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON>er som har beh<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Varning", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Med <PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON>en", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Med signatur", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Redigera presentation", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Vid ändring kommer signaturer tas bort från presentationen.<br>Är du säker på att fortsätta?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Presentationen är skyddad av lösenord", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Gil<PERSON>ga signaturer har lagts till presentationen. Presentationen är skyddad mot ändring.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Vissa av de digitala signaturerna i presentationen är ogiltiga eller kunde inte verifieras. Presentationen är skyddad från redigering.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Visa signaturer", "PE.Views.FileMenuPanels.Settings.okButtonText": "Tillämpa", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON>bb", "PE.Views.FileMenuPanels.Settings.strFontRender": "Fontförslag", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorera ord med VERSALER", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignorera ord med siffror", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Makroinställningar", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Visa knappen Klistra in alternativ när innehållet klistras in", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Visa ändringar från andra anv<PERSON>", "PE.Views.FileMenuPanels.Settings.strStrict": "Strikt", "PE.Views.FileMenuPanels.Settings.strTheme": "Gränssnittstema", "PE.Views.FileMenuPanels.Settings.strUnit": "Måttenhet", "PE.Views.FileMenuPanels.Settings.strZoom": "Standard zoomvärde", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Var 10:e minut", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Var 30:e minut", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Var 5:e minut", "PE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON><PERSON> timme", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Återskapa automatiskt", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Autospara", "PE.Views.FileMenuPanels.Settings.textDisabled": "Inaktiverad", "PE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON><PERSON> me<PERSON>ggande versioner", "PE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON><PERSON>ut", "PE.Views.FileMenuPanels.Settings.txtAll": "Visa alla", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Inställningar autokorrigering", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Standard cache-läge", "PE.Views.FileMenuPanels.Settings.txtCm": "Centimeter", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Samarbete", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Redigera och spara", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Samredigering i realtid. Alla ändringar sparas automatiskt", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Anpassa till bild", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Anpassa till bredd", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyfer", "PE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "Visa senaste", "PE.Views.FileMenuPanels.Settings.txtMac": "som OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Orginal", "PE.Views.FileMenuPanels.Settings.txtProofing": "Korrektur", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Aktivera alla", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Aktivera alla makron utan avisering", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Stavningskontroll", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Inaktivera allt", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Inaktivera alla makron utan avisering", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "<PERSON><PERSON><PERSON><PERSON> knap<PERSON> \"<PERSON>ra\" för att synkronisera ändringarna du och andra gör", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Använd Alt-tangenten för att navigera i användargränssnittet med tangentbordet", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Använd Option-tangenten för att navigera i användargränssnittet med tangentbordet", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Visa meddelanden", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Inaktivera alla makron med en avisering", "PE.Views.FileMenuPanels.Settings.txtWin": "som Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "A<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.applyAllText": "Tillämpa på alla", "PE.Views.HeaderFooterDialog.applyText": "Tillämpa", "PE.Views.HeaderFooterDialog.diffLanguage": "du kan inte använda ett datumformat på ett annat språk än bildmastern. <br> <PERSON><PERSON><PERSON> att ändra mastern, klicka på \"Apply to all\" istället för \"Apply\"", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Varning", "PE.Views.HeaderFooterDialog.textDateTime": "Datum och tid", "PE.Views.HeaderFooterDialog.textFixed": "Fast", "PE.Views.HeaderFooterDialog.textFooter": "Text i sidfot", "PE.Views.HeaderFooterDialog.textFormat": "Format", "PE.Views.HeaderFooterDialog.textLang": "Språk", "PE.Views.HeaderFooterDialog.textNotTitle": "Visas inte på titelsida", "PE.Views.HeaderFooterDialog.textPreview": "Förhandsgranska", "PE.Views.HeaderFooterDialog.textSlideNum": "Bild nummer", "PE.Views.HeaderFooterDialog.textTitle": "Inställningar sidfot", "PE.Views.HeaderFooterDialog.textUpdate": "Uppdatera automatiskt", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Visa", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON> till", "PE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON><PERSON><PERSON> del av text", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON> rubrik här", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON> länk här", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Ange tooltip här", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Extern länk", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Bild i <PERSON>en", "PE.Views.HyperlinkSettingsDialog.textSlides": "Bilder", "PE.Views.HyperlinkSettingsDialog.textTipText": "Skärmtext", "PE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlänkinställningar", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Detta fält är obligatoriskt", "PE.Views.HyperlinkSettingsDialog.txtFirst": "<PERSON><PERSON><PERSON><PERSON> bilden", "PE.Views.HyperlinkSettingsDialog.txtLast": "<PERSON><PERSON> bilden", "PE.Views.HyperlinkSettingsDialog.txtNext": "<PERSON>ästa bild", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON>ta fält bör vara en URL i formatet \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Föregående bild", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Detta fält är begränsat till 2083 tecken", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Bild", "PE.Views.ImageSettings.textAdvanced": "Visa avancerade inställningar", "PE.Views.ImageSettings.textCrop": "Beskär", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Passa", "PE.Views.ImageSettings.textCropToShape": "Beskära till form", "PE.Views.ImageSettings.textEdit": "Rediger<PERSON>", "PE.Views.ImageSettings.textEditObject": "Redigera objekt", "PE.Views.ImageSettings.textFitSlide": "Anpassa till bild", "PE.Views.ImageSettings.textFlip": "Vänd", "PE.Views.ImageSettings.textFromFile": "<PERSON><PERSON><PERSON> fil", "PE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "PE.Views.ImageSettings.textFromUrl": "Från URL", "PE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textHint270": "Rotera 90° moturs", "PE.Views.ImageSettings.textHint90": "Rotera 90° medsols", "PE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON><PERSON> ho<PERSON>", "PE.Views.ImageSettings.textHintFlipV": "Vänd vertikalt", "PE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON><PERSON> bild", "PE.Views.ImageSettings.textOriginalSize": "Faktisk storlek", "PE.Views.ImageSettings.textRecentlyUsed": "Nyligen använda", "PE.Views.ImageSettings.textRotate90": "Rotera 90°", "PE.Views.ImageSettings.textRotation": "Rotation", "PE.Views.ImageSettings.textSize": "Storlek", "PE.Views.ImageSettings.textWidth": "Bredd", "PE.Views.ImageSettingsAdvanced.textAlt": "Alternativ text", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Beskrivning", "PE.Views.ImageSettingsAdvanced.textAltTip": "Den alternativa textbaserad förekomsten av visuell objektinformation som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar för att hjälpa dem att bättre förstå vilken information som finns i bilden, figuren, diagrammet eller tabellen.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Centrera", "PE.Views.ImageSettingsAdvanced.textFlipped": "Vänd", "PE.Views.ImageSettingsAdvanced.textFrom": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Konstanta proportioner", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Faktisk storlek", "PE.Views.ImageSettingsAdvanced.textPlacement": "Placering", "PE.Views.ImageSettingsAdvanced.textPosition": "Position", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "PE.Views.ImageSettingsAdvanced.textSize": "Storlek", "PE.Views.ImageSettingsAdvanced.textTitle": "Bild - avancerade inställningar", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Övre vänstra hörnet", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textWidth": "Bredd", "PE.Views.LeftMenu.tipAbout": "Om", "PE.Views.LeftMenu.tipChat": "<PERSON><PERSON>", "PE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipPlugins": "Plugins", "PE.Views.LeftMenu.tipSearch": "<PERSON>ö<PERSON>", "PE.Views.LeftMenu.tipSlides": "Bilder", "PE.Views.LeftMenu.tipSupport": "Feedback & support", "PE.Views.LeftMenu.tipTitles": "Titlar", "PE.Views.LeftMenu.txtDeveloper": "Utvecklarläge", "PE.Views.LeftMenu.txtLimit": "Begränsad åtkomst", "PE.Views.LeftMenu.txtTrial": "TESTLÄGE", "PE.Views.LeftMenu.txtTrialDev": "Testutvecklarläge", "PE.Views.ParagraphSettings.strLineHeight": "Radavstånd", "PE.Views.ParagraphSettings.strParagraphSpacing": "Styckets avstånd", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "Visa avancerade inställningar", "PE.Views.ParagraphSettings.textAt": "på", "PE.Views.ParagraphSettings.textAtLeast": "minst", "PE.Views.ParagraphSettings.textAuto": "flera", "PE.Views.ParagraphSettings.textExact": "Exakt", "PE.Views.ParagraphSettings.txtAutoText": "auto", "PE.Views.ParagraphSettingsAdvanced.noTabs": "De angivna flikarna kommer att visas i det här fältet", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON>a versaler", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Dubbel genomstrykning", "PE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Radavstånd", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indrag & avstånd", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Gemener", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Avstånd", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Genomstruken", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Upph<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Ta<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textAuto": "flera", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Teckenavstånd", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Standardflik", "PE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textExact": "exakt", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>n", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Hängande", "PE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ingen)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON> bort", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Ta bort alla", "PE.Views.ParagraphSettingsAdvanced.textSet": "Specificera", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centrera", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tabb position", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Stycke - avancerade inställningar", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "auto", "PE.Views.RightMenu.txtChartSettings": "Diagraminställningar", "PE.Views.RightMenu.txtImageSettings": "Bildinställningar", "PE.Views.RightMenu.txtParagraphSettings": "Avsnitt inställningar", "PE.Views.RightMenu.txtShapeSettings": "Form inställningar", "PE.Views.RightMenu.txtSignatureSettings": "Signaturinställningar", "PE.Views.RightMenu.txtSlideSettings": "Bild inställningar", "PE.Views.RightMenu.txtTableSettings": "Tabell inställningar", "PE.Views.RightMenu.txtTextArtSettings": "Text Art inställningar", "PE.Views.ShapeSettings.strBackground": "Bakgrundsfärg", "PE.Views.ShapeSettings.strChange": "Ändra autoform", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Förgrundsfärg", "PE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strShadow": "Visa skugga", "PE.Views.ShapeSettings.strSize": "Storlek", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Opacitet", "PE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textAdvanced": "Visa avancerade inställningar", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett värde mellan 0 och 1584 pt.", "PE.Views.ShapeSettings.textColor": "Färgfyllnad", "PE.Views.ShapeSettings.textDirection": "Rik<PERSON>ning", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON> m<PERSON>", "PE.Views.ShapeSettings.textFlip": "Vänd", "PE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON><PERSON> fil", "PE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "PE.Views.ShapeSettings.textFromUrl": "Från URL", "PE.Views.ShapeSettings.textGradient": "Triangulära punkter", "PE.Views.ShapeSettings.textGradientFill": "F<PERSON><PERSON>ing", "PE.Views.ShapeSettings.textHint270": "Rotera 90° moturs", "PE.Views.ShapeSettings.textHint90": "Rotera 90° medsols", "PE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON><PERSON> ho<PERSON>", "PE.Views.ShapeSettings.textHintFlipV": "Vänd vertikalt", "PE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON><PERSON> el<PERSON> m<PERSON>", "PE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textNoFill": "Ingen fyllning", "PE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textPosition": "Position", "PE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textRecentlyUsed": "Nyligen använda", "PE.Views.ShapeSettings.textRotate90": "Rotera 90°", "PE.Views.ShapeSettings.textRotation": "Rotation", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> bild", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "Stil", "PE.Views.ShapeSettings.textTexture": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "PE.Views.ShapeSettings.tipAddGradientPoint": "Lägg till lutningspunkt", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Ta bort lutningspunkten", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papper", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "Mörkt tyg", "PE.Views.ShapeSettings.txtGrain": "Gryn", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> papper", "PE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "Ingen rad", "PE.Views.ShapeSettings.txtPapyrus": "Papyrus", "PE.Views.ShapeSettings.txtWood": "Trä", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "Text padding", "PE.Views.ShapeSettingsAdvanced.textAlt": "Alternativ text", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Beskrivning", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Den alternativa textbaserad förekomsten av visuell objektinformation som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar för att hjälpa dem att bättre förstå vilken information som finns i bilden, figuren, diagrammet eller tabellen.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "Titel", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Anpassa automatiskt", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON> s<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Startstil", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textCapType": "Typ av skiftläge", "PE.Views.ShapeSettingsAdvanced.textCenter": "Centrera", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Slutstorlek", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Avslutningsstil", "PE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Vänd", "PE.Views.ShapeSettingsAdvanced.textFrom": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Typ av sammanfogning", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Konstanta proportioner", "PE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "Linjestil", "PE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textNofit": "Anpassa inte automatiskt", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Placering", "PE.Views.ShapeSettingsAdvanced.textPosition": "Position", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON> storlek så den passar texten", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "PE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textShrink": "Krymp text vid överflöd", "PE.Views.ShapeSettingsAdvanced.textSize": "Storlek", "PE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON> mellan kolumner", "PE.Views.ShapeSettingsAdvanced.textSquare": "Fyrkant", "PE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTitle": "Form - avancerade inställningar", "PE.Views.ShapeSettingsAdvanced.textTop": "Ö<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Övre vänstra hörnet", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertikal", "PE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "<PERSON><PERSON><PERSON> & pilar", "PE.Views.ShapeSettingsAdvanced.textWidth": "Bredd", "PE.Views.ShapeSettingsAdvanced.txtNone": "Inga", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Varning", "PE.Views.SignatureSettings.strDelete": "Ta bort signatur", "PE.Views.SignatureSettings.strDetails": "Signaturdetaljer", "PE.Views.SignatureSettings.strInvalid": "Felaktiga signaturer", "PE.Views.SignatureSettings.strSign": "Underteckna", "PE.Views.SignatureSettings.strSignature": "Signatur", "PE.Views.SignatureSettings.strValid": "Giltiga signaturer", "PE.Views.SignatureSettings.txtContinueEditing": "Redigera ändå", "PE.Views.SignatureSettings.txtEditWarning": "Vid ändring kommer signaturer tas bort från presentationen.<br>Är du säker på att fortsätta?", "PE.Views.SignatureSettings.txtRemoveWarning": "Vill du ta bort den här signaturen? <br> <PERSON>n inte å<PERSON>.", "PE.Views.SignatureSettings.txtSigned": "Gil<PERSON>ga signaturer har lagts till presentationen. Presentationen är skyddad mot ändring.", "PE.Views.SignatureSettings.txtSignedInvalid": "Vissa av de digitala signaturerna i presentationen är ogiltiga eller kunde inte verifieras. Presentationen är skyddad från redigering.", "PE.Views.SlideSettings.strBackground": "Bakgrundsfärg", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strDateTime": "Visa datum och tid", "PE.Views.SlideSettings.strFill": "Bakgrund", "PE.Views.SlideSettings.strForeground": "Förgrundsfärg", "PE.Views.SlideSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strSlideNum": "Visa bildnummer", "PE.Views.SlideSettings.strTransparency": "Opacitet", "PE.Views.SlideSettings.textAdvanced": "Visa avancerade inställningar", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "Färgfyllnad", "PE.Views.SlideSettings.textDirection": "Rik<PERSON>ning", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON> m<PERSON>", "PE.Views.SlideSettings.textFromFile": "<PERSON><PERSON><PERSON> fil", "PE.Views.SlideSettings.textFromStorage": "<PERSON><PERSON><PERSON> lagring", "PE.Views.SlideSettings.textFromUrl": "Från URL", "PE.Views.SlideSettings.textGradient": "Triangulära punkter", "PE.Views.SlideSettings.textGradientFill": "F<PERSON><PERSON>ing", "PE.Views.SlideSettings.textImageTexture": "<PERSON><PERSON><PERSON> el<PERSON> m<PERSON>", "PE.Views.SlideSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textNoFill": "Ingen fyllning", "PE.Views.SlideSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textPosition": "Position", "PE.Views.SlideSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textReset": "Återställ ändringar", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> bild", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "Stil", "PE.Views.SlideSettings.textTexture": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textTile": "<PERSON><PERSON>", "PE.Views.SlideSettings.tipAddGradientPoint": "Lägg till lutningspunkt", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Ta bort lutningspunkten", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papper", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "Mörkt tyg", "PE.Views.SlideSettings.txtGrain": "Gryn", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> papper", "PE.Views.SlideSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "Papyrus", "PE.Views.SlideSettings.txtWood": "Trä", "PE.Views.SlideshowSettings.textLoop": "<PERSON><PERSON> tills 'Es<PERSON>' trycks ner", "PE.Views.SlideshowSettings.textTitle": "Visa inställningar", "PE.Views.SlideSizeSettings.strLandscape": "Landskap", "PE.Views.SlideSizeSettings.strPortrait": "Portr<PERSON><PERSON>", "PE.Views.SlideSizeSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideOrientation": "Bild orientering", "PE.Views.SlideSizeSettings.textSlideSize": "<PERSON><PERSON><PERSON> storlek", "PE.Views.SlideSizeSettings.textTitle": "<PERSON><PERSON> inställningar", "PE.Views.SlideSizeSettings.textWidth": "Bredd", "PE.Views.SlideSizeSettings.txt35": "35 mm diabilder", "PE.Views.SlideSizeSettings.txtA3": "A3 Papper (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Papper (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Papper (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Papper (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "<PERSON><PERSON>", "PE.Views.SlideSizeSettings.txtCustom": "Anpassad", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON> (11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Letter Papper (8.5x11 in)", "PE.Views.SlideSizeSettings.txtOverhead": "Overhead", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Bredbild", "PE.Views.Statusbar.goToPageText": "Gå till bild", "PE.Views.Statusbar.pageIndexText": "Bild {0} of {1}", "PE.Views.Statusbar.textShowBegin": "Visa presentation fr<PERSON><PERSON> b<PERSON>", "PE.Views.Statusbar.textShowCurrent": "Visa presentation från aktuell bild", "PE.Views.Statusbar.textShowPresenterView": "Presentatörsvy", "PE.Views.Statusbar.tipAccessRights": "Hantera åtkomsträttighet för dokument", "PE.Views.Statusbar.tipFitPage": "Anpassa till bild", "PE.Views.Statusbar.tipFitWidth": "Anpassa till bredd", "PE.Views.Statusbar.tipPreview": "<PERSON>a visning", "PE.Views.Statusbar.tipSetLang": "<PERSON><PERSON><PERSON> in text spr<PERSON>k", "PE.Views.Statusbar.tipZoomFactor": "Zooma", "PE.Views.Statusbar.tipZoomIn": "Zooma in", "PE.Views.Statusbar.tipZoomOut": "Zooma ut", "PE.Views.Statusbar.txtPageNumInvalid": "Ogiltigt bildnummer", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON> kol<PERSON>n", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> rad", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON>a tabell", "PE.Views.TableSettings.insertColumnLeftText": "Infoga kolumn till vänster", "PE.Views.TableSettings.insertColumnRightText": "Infoga kolumn till höger", "PE.Views.TableSettings.insertRowAboveText": "Infoga rad ovan", "PE.Views.TableSettings.insertRowBelowText": "Infoga rad under", "PE.Views.TableSettings.mergeCellsText": "Slå ihop celler", "PE.Views.TableSettings.selectCellText": "Välj cell", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> kol<PERSON>n", "PE.Views.TableSettings.selectRowText": "Vä<PERSON>j rad", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabell", "PE.Views.TableSettings.splitCellsText": "Dela cell...", "PE.Views.TableSettings.splitCellTitleText": "Dela cell", "PE.Views.TableSettings.textAdvanced": "Visa avancerade inställningar", "PE.Views.TableSettings.textBackColor": "Bakgrundsfärg", "PE.Views.TableSettings.textBanded": "Banded", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "Ramutseende", "PE.Views.TableSettings.textCellSize": "Cell storlek", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "Distribuera kolumner", "PE.Views.TableSettings.textDistributeRows": "Distribuera rader", "PE.Views.TableSettings.textEdit": "Ra<PERSON> & kolumner", "PE.Views.TableSettings.textEmptyTemplate": "<PERSON>ga mallar", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textLast": "Senaste", "PE.Views.TableSettings.textRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> hur kantlinjer ska tillämpas", "PE.Views.TableSettings.textTemplate": "Välj från mall", "PE.Views.TableSettings.textTotal": "Totalt", "PE.Views.TableSettings.textWidth": "Bredd", "PE.Views.TableSettings.tipAll": "<PERSON><PERSON>kant och alla inre linjer", "PE.Views.TableSettings.tipBottom": "Endast yttre ram i botten", "PE.Views.TableSettings.tipInner": "Endast inre linjer", "PE.Views.TableSettings.tipInnerHor": "<PERSON><PERSON><PERSON> ho<PERSON> inre linjer", "PE.Views.TableSettings.tipInnerVert": "Endast inre vertikala linjer", "PE.Views.TableSettings.tipLeft": "Yttre vänstra ram endast", "PE.Views.TableSettings.tipNone": "<PERSON><PERSON> ramar", "PE.Views.TableSettings.tipOuter": "Endast ytterkant", "PE.Views.TableSettings.tipRight": "Endast yttre höger ram", "PE.Views.TableSettings.tipTop": "Endast yttre övre ram", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON> ramar", "PE.Views.TableSettings.txtTable_Accent": "Accent", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON><PERSON> stil", "PE.Views.TableSettings.txtTable_LightStyle": "Light Style", "PE.Views.TableSettings.txtTable_MediumStyle": "Medium stil", "PE.Views.TableSettings.txtTable_NoGrid": "Ingen tabell", "PE.Views.TableSettings.txtTable_NoStyle": "Ingen stil", "PE.Views.TableSettings.txtTable_TableGrid": "Tabellnät", "PE.Views.TableSettings.txtTable_ThemedStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textAlt": "Alternativ text", "PE.Views.TableSettingsAdvanced.textAltDescription": "Beskrivning", "PE.Views.TableSettingsAdvanced.textAltTip": "Den alternativa textbaserad förekomsten av visuell objektinformation som kommer att läsas för personer med syn eller kognitiva funktionsnedsättningar för att hjälpa dem att bättre förstå vilken information som finns i bilden, figuren, diagrammet eller tabellen.", "PE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "PE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textCenter": "Centrera", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Använd standardmarginaler", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Standardmarginaler", "PE.Views.TableSettingsAdvanced.textFrom": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Konstanta proportioner", "PE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textMargins": "Cell marginal", "PE.Views.TableSettingsAdvanced.textPlacement": "Placering", "PE.Views.TableSettingsAdvanced.textPosition": "Position", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "Storlek", "PE.Views.TableSettingsAdvanced.textTitle": "Tabell - avancerade inställningar", "PE.Views.TableSettingsAdvanced.textTop": "Ö<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Övre vänstra hörnet", "PE.Views.TableSettingsAdvanced.textVertical": "Vertikal", "PE.Views.TableSettingsAdvanced.textWidth": "Bredd", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Marginaler", "PE.Views.TextArtSettings.strBackground": "Bakgrundsfärg", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "Förgrundsfärg", "PE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strSize": "Storlek", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Opacitet", "PE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "Det angivna värdet är inkorrekt.<br> Vänligen ange ett värde mellan 0 och 1584 pt.", "PE.Views.TextArtSettings.textColor": "Färgfyllnad", "PE.Views.TextArtSettings.textDirection": "Rik<PERSON>ning", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON> m<PERSON>", "PE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON><PERSON> fil", "PE.Views.TextArtSettings.textFromUrl": "Från URL", "PE.Views.TextArtSettings.textGradient": "Triangulära punkter", "PE.Views.TextArtSettings.textGradientFill": "F<PERSON><PERSON>ing", "PE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON><PERSON> el<PERSON> m<PERSON>", "PE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textNoFill": "Ingen fyllning", "PE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textPosition": "Position", "PE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "Stil", "PE.Views.TextArtSettings.textTemplate": "Mall", "PE.Views.TextArtSettings.textTexture": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTransform": "Omvandla", "PE.Views.TextArtSettings.tipAddGradientPoint": "Lägg till lutningspunkt", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Ta bort lutningspunkten", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papper", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "Mörkt tyg", "PE.Views.TextArtSettings.txtGrain": "Gryn", "PE.Views.TextArtSettings.txtGranite": "Granit", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON> papper", "PE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtNoBorders": "Ingen rad", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "Trä", "PE.Views.Toolbar.capAddSlide": "Lägg till ny bild", "PE.Views.Toolbar.capBtnAddComment": "Lägg till kommentar", "PE.Views.Toolbar.capBtnComment": "Kommentar", "PE.Views.Toolbar.capBtnDateTime": "Datum & Tid", "PE.Views.Toolbar.capBtnInsHeader": "Sidfot", "PE.Views.Toolbar.capBtnInsSymbol": "Symbol", "PE.Views.Toolbar.capBtnSlideNum": "Bild nummer", "PE.Views.Toolbar.capInsertAudio": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertChart": "Diagram", "PE.Views.Toolbar.capInsertEquation": "Ekvation", "PE.Views.Toolbar.capInsertHyperlink": "Hyperlänk", "PE.Views.Toolbar.capInsertImage": "Bild", "PE.Views.Toolbar.capInsertShape": "Form", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertVideo": "Video", "PE.Views.Toolbar.capTabFile": "Arkiv", "PE.Views.Toolbar.capTabHome": "<PERSON><PERSON>", "PE.Views.Toolbar.capTabInsert": "Infoga", "PE.Views.Toolbar.mniCapitalizeWords": "Stor bokstav varje ord", "PE.Views.Toolbar.mniCustomTable": "Infoga egen tabell", "PE.Views.Toolbar.mniImageFromFile": "Bild fr<PERSON><PERSON> fil", "PE.Views.Toolbar.mniImageFromStorage": "<PERSON>ild fr<PERSON><PERSON> lagring", "PE.Views.Toolbar.mniImageFromUrl": "Bild från URL", "PE.Views.Toolbar.mniInsertSSE": "Infoga kalkylblad", "PE.Views.Toolbar.mniLowerCase": "Inte<PERSON><PERSON>", "PE.Views.Toolbar.mniSentenceCase": "<PERSON><PERSON> bokstav varje mening", "PE.Views.Toolbar.mniSlideAdvanced": "Avancerade inställningar", "PE.Views.Toolbar.mniSlideStandard": "Standard (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.mniToggleCase": "vÄXLA vERSALER", "PE.Views.Toolbar.mniUpperCase": "VERSALER", "PE.Views.Toolbar.strMenuNoFill": "Ingen fyllning", "PE.Views.Toolbar.textAlignBottom": "Justera text till botten", "PE.Views.Toolbar.textAlignCenter": "Centrera text", "PE.Views.Toolbar.textAlignJust": "<PERSON><PERSON>", "PE.Views.Toolbar.textAlignLeft": "Vänsterjustera texten", "PE.Views.Toolbar.textAlignMiddle": "Justera text till mitten", "PE.Views.Toolbar.textAlignRight": "Högerjustera texten", "PE.Views.Toolbar.textAlignTop": "Justera texten till toppen", "PE.Views.Toolbar.textArrangeBack": "<PERSON><PERSON><PERSON> l<PERSON> bak", "PE.Views.Toolbar.textArrangeBackward": "<PERSON><PERSON> b<PERSON>", "PE.Views.Toolbar.textArrangeForward": "<PERSON><PERSON> f<PERSON>", "PE.Views.Toolbar.textArrangeFront": "Flytta till förgrund", "PE.Views.Toolbar.textBold": "Fet", "PE.Views.Toolbar.textColumnsCustom": "Anpassade kolumner", "PE.Views.Toolbar.textColumnsOne": "En kolumn", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON> k<PERSON>umner", "PE.Views.Toolbar.textColumnsTwo": "Två kolumner", "PE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textListSettings": "Listinställningar", "PE.Views.Toolbar.textRecentlyUsed": "Nyligen använda", "PE.Views.Toolbar.textShapeAlignBottom": "<PERSON><PERSON>", "PE.Views.Toolbar.textShapeAlignCenter": "Centrera", "PE.Views.Toolbar.textShapeAlignLeft": "Vänsterjustera", "PE.Views.Toolbar.textShapeAlignMiddle": "Centrera", "PE.Views.Toolbar.textShapeAlignRight": "Högerjustera", "PE.Views.Toolbar.textShapeAlignTop": "<PERSON><PERSON> toppen", "PE.Views.Toolbar.textShowBegin": "Visa presentation fr<PERSON><PERSON> b<PERSON>", "PE.Views.Toolbar.textShowCurrent": "Visa presentation från aktuell bild", "PE.Views.Toolbar.textShowPresenterView": "Presentatörsvy", "PE.Views.Toolbar.textShowSettings": "Visa inställningar", "PE.Views.Toolbar.textStrikeout": "Genomstruken", "PE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSuperscript": "Upph<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabAnimation": "Animation", "PE.Views.Toolbar.textTabCollaboration": "Samarbeta", "PE.Views.Toolbar.textTabFile": "Arkiv", "PE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "PE.Views.Toolbar.textTabInsert": "Infoga", "PE.Views.Toolbar.textTabProtect": "<PERSON><PERSON>", "PE.Views.Toolbar.textTabTransitions": "Övergångar", "PE.Views.Toolbar.textTabView": "Visa", "PE.Views.Toolbar.textTitleError": "<PERSON><PERSON>", "PE.Views.Toolbar.textUnderline": "Understrykning", "PE.Views.Toolbar.tipAddSlide": "Lägg till ny bild", "PE.Views.Toolbar.tipBack": "Tillbaka", "PE.Views.Toolbar.tipChangeCase": "<PERSON><PERSON>", "PE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON> diagramtyp", "PE.Views.Toolbar.tipChangeSlide": "<PERSON><PERSON>", "PE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON> stil", "PE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON>", "PE.Views.Toolbar.tipColumns": "In<PERSON>ga kolumner", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> stil", "PE.Views.Toolbar.tipCut": "<PERSON><PERSON><PERSON> ut", "PE.Views.Toolbar.tipDateTime": "Infoga aktuellt datum och tid", "PE.Views.Toolbar.tipDecFont": "Minska typsnittstorlek", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON> in<PERSON>g", "PE.Views.Toolbar.tipEditHeader": "<PERSON><PERSON>a foten", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipFontName": "Font", "PE.Views.Toolbar.tipFontSize": "Fontstorlek", "PE.Views.Toolbar.tipHAligh": "<PERSON><PERSON><PERSON><PERSON> justering", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON> f<PERSON>", "PE.Views.Toolbar.tipIncFont": "<PERSON><PERSON>", "PE.Views.Toolbar.tipIncPrLeft": "<PERSON><PERSON> indrag", "PE.Views.Toolbar.tipInsertAudio": "Infoga ljud", "PE.Views.Toolbar.tipInsertChart": "Infoga diagram", "PE.Views.Toolbar.tipInsertEquation": "Infoga ekvation", "PE.Views.Toolbar.tipInsertHyperlink": "Lägg till länk", "PE.Views.Toolbar.tipInsertImage": "Infoga bild", "PE.Views.Toolbar.tipInsertShape": "Infoga autoform", "PE.Views.Toolbar.tipInsertSymbol": "Infoga symbol", "PE.Views.Toolbar.tipInsertTable": "Infoga tabell", "PE.Views.Toolbar.tipInsertText": "Infoga textruta", "PE.Views.Toolbar.tipInsertTextArt": "Infoga Text Art", "PE.Views.Toolbar.tipInsertVideo": "Infoga video", "PE.Views.Toolbar.tipLineSpace": "Radavstånd", "PE.Views.Toolbar.tipMarkers": "Punktlista", "PE.Views.Toolbar.tipMarkersArrow": "Pil punkter", "PE.Views.Toolbar.tipMarkersCheckmark": "<PERSON>ck punkt", "PE.Views.Toolbar.tipMarkersDash": "Sträck punkter", "PE.Views.Toolbar.tipMarkersFRhombus": "<PERSON><PERSON><PERSON> romb punkter", "PE.Views.Toolbar.tipMarkersFRound": "<PERSON><PERSON><PERSON> runda <PERSON>ter", "PE.Views.Toolbar.tipMarkersFSquare": "<PERSON><PERSON><PERSON> k<PERSON>dratiska punkter", "PE.Views.Toolbar.tipMarkersHRound": "<PERSON><PERSON><PERSON> runda <PERSON>ter", "PE.Views.Toolbar.tipMarkersStar": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNone": "Ingen", "PE.Views.Toolbar.tipNumbers": "Numrering", "PE.Views.Toolbar.tipPaste": "Klistra in", "PE.Views.Toolbar.tipPreview": "<PERSON>a visning", "PE.Views.Toolbar.tipPrint": "Skriv ut", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON> om", "PE.Views.Toolbar.tipSave": "Spara", "PE.Views.Toolbar.tipSaveCoauth": "Spara ä<PERSON>na så att andra användare ska se dem.", "PE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON> allt", "PE.Views.Toolbar.tipShapeAlign": "Justera form", "PE.Views.Toolbar.tipShapeArrange": "Arrangera form", "PE.Views.Toolbar.tipSlideNum": "Infoga bild nummer", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSlideTheme": "Bild tema", "PE.Views.Toolbar.tipUndo": "Å<PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Verikal anpassning", "PE.Views.Toolbar.tipViewSettings": "Visa inställningar", "PE.Views.Toolbar.txtDistribHor": "Distribuera horisontellt", "PE.Views.Toolbar.txtDistribVert": "Distribuera vertikalt", "PE.Views.Toolbar.txtDuplicateSlide": "Dup<PERSON><PERSON> bild", "PE.Views.Toolbar.txtGroup": "Grupp", "PE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON> valda objekt", "PE.Views.Toolbar.txtScheme1": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme10": "Median", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Ursprung", "PE.Views.Toolbar.txtScheme16": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme17": "Solstånd", "PE.Views.Toolbar.txtScheme18": "Teknik", "PE.Views.Toolbar.txtScheme19": "<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme2": "Gråskala", "PE.Views.Toolbar.txtScheme20": "Urban", "PE.Views.Toolbar.txtScheme21": "Fart", "PE.Views.Toolbar.txtScheme22": "New Office", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Aspekt", "PE.Views.Toolbar.txtScheme5": "Medborgerlig", "PE.Views.Toolbar.txtScheme6": "Öppen plats", "PE.Views.Toolbar.txtScheme7": "Rimlighet", "PE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme9": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtSlideAlign": "Anpassa till bild", "PE.Views.Toolbar.txtUngroup": "Dela upp", "PE.Views.Transitions.strDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.strDuration": "Varaktighet", "PE.Views.Transitions.strStartOnClick": "Börja vid klick", "PE.Views.Transitions.textBlack": "<PERSON><PERSON> svart", "PE.Views.Transitions.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textBottomLeft": "Nederst-vänster", "PE.Views.Transitions.textBottomRight": "<PERSON>ers<PERSON>-h<PERSON>ger", "PE.Views.Transitions.textClock": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "Medurs", "PE.Views.Transitions.textCounterclockwise": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textCover": "Omslag", "PE.Views.Transitions.textFade": "Blekna", "PE.Views.Transitions.textHorizontalIn": "<PERSON><PERSON><PERSON><PERSON> in", "PE.Views.Transitions.textHorizontalOut": "<PERSON><PERSON><PERSON>ll ut", "PE.Views.Transitions.textLeft": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textNone": "Inga", "PE.Views.Transitions.textPush": "<PERSON><PERSON>", "PE.Views.Transitions.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSplit": "Dela", "PE.Views.Transitions.textTop": "Ö<PERSON><PERSON>", "PE.Views.Transitions.textTopLeft": "Överst till vänster", "PE.Views.Transitions.textTopRight": "Överst till höger", "PE.Views.Transitions.textUnCover": "Avtä<PERSON>a", "PE.Views.Transitions.textVerticalIn": "Vertikal in", "PE.Views.Transitions.textVerticalOut": "Vertikal ut", "PE.Views.Transitions.textWedge": "<PERSON><PERSON>", "PE.Views.Transitions.textWipe": "Ren<PERSON>", "PE.Views.Transitions.textZoom": "Förstora", "PE.Views.Transitions.textZoomIn": "Zooma in", "PE.Views.Transitions.textZoomOut": "Zooma ut", "PE.Views.Transitions.textZoomRotate": "Zooma och rotera", "PE.Views.Transitions.txtApplyToAll": "Applicera på alla bilder", "PE.Views.Transitions.txtParameters": "Parametrar", "PE.Views.Transitions.txtPreview": "Förhandsgranska", "PE.Views.Transitions.txtSec": "s", "PE.Views.ViewTab.textAlwaysShowToolbar": "Visa alltid verktygsfältet", "PE.Views.ViewTab.textFitToSlide": "Passa till bild", "PE.Views.ViewTab.textFitToWidth": "Passa till bredd", "PE.Views.ViewTab.textInterfaceTheme": "Gränssnittstema", "PE.Views.ViewTab.textNotes": "Anteckningar", "PE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textStatusBar": "Statusmätare", "PE.Views.ViewTab.textZoom": "Förstora", "PE.Views.ViewTab.tipFitToSlide": "Anpassa till bild", "PE.Views.ViewTab.tipFitToWidth": "Anpassa till bredd", "PE.Views.ViewTab.tipInterfaceTheme": "Gränssnittstema"}