{"Common.Controllers.Chat.notcriticalErrorTitle": "Ostrzeżenie", "Common.Controllers.Chat.textEnterMessage": "Wprowadź swoją wiadomość tutaj", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonimowy użytkownik ", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "Obiekt jest wył<PERSON><PERSON><PERSON>, ponieważ jest edytowany przez innego użytkownika.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Ostrzeżenie", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "Zamknij", "Common.Controllers.ExternalOleEditor.warningText": "Obiekt jest wył<PERSON><PERSON><PERSON>, ponieważ jest edytowany przez innego użytkownika.", "Common.Controllers.ExternalOleEditor.warningTitle": "Ostrzeżenie", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Skumulowany warstwowy", "Common.define.chartData.textAreaStackedPer": "100% skumulowany warstwowy", "Common.define.chartData.textBar": "Pasek", "Common.define.chartData.textBarNormal": "Kolumnowy grupowany", "Common.define.chartData.textBarNormal3d": "Kolumnowy grupowany 3D", "Common.define.chartData.textBarNormal3dPerspective": "Kolumnowy 3D", "Common.define.chartData.textBarStacked": "Skumulowany kolumnowy", "Common.define.chartData.textBarStacked3d": "Skumulowany kolumnowy 3D", "Common.define.chartData.textBarStackedPer": "100% skumulowany kolumnowy", "Common.define.chartData.textBarStackedPer3d": "100% skumulowany kolumnowy 3D", "Common.define.chartData.textCharts": "Wykresy", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textComboAreaBar": "Skumulowany warstwowy - kolumnowy grupowany", "Common.define.chartData.textComboBarLine": "Kolumnowy grupowany - liniowy", "Common.define.chartData.textComboBarLineSecondary": "Kolumnowy grupowany - liniowy na osi pomocniczej", "Common.define.chartData.textComboCustom": "Niestandardowy złożony", "Common.define.chartData.textDoughnut": "Pierś<PERSON>nio<PERSON>", "Common.define.chartData.textHBarNormal": "Słupkowy grupowany", "Common.define.chartData.textHBarNormal3d": "Słupkowy grupowany 3D", "Common.define.chartData.textHBarStacked": "Skumulowany słupkowy", "Common.define.chartData.textHBarStacked3d": "Skumulowany słupkowy 3D", "Common.define.chartData.textHBarStackedPer": "100% skumulowany słupkowy", "Common.define.chartData.textHBarStackedPer3d": "100% skumulowany słupkowy 3D", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "Liniowy 3D", "Common.define.chartData.textLineMarker": "Liniowy ze znacznikami", "Common.define.chartData.textLineStacked": "Skumulowany liniowy", "Common.define.chartData.textLineStackedMarker": "Skumulowany liniowy ze znacznikami", "Common.define.chartData.textLineStackedPer": "100% skumulowany liniowy", "Common.define.chartData.textLineStackedPerMarker": "100% skumulowany liniowy ze znacznikami", "Common.define.chartData.textPie": "Kołowe", "Common.define.chartData.textPie3d": "Kołowy 3D", "Common.define.chartData.textPoint": "XY (Punktowy)", "Common.define.chartData.textScatter": "Punktowy", "Common.define.chartData.textScatterLine": "Punktowy z prostymi liniami", "Common.define.chartData.textScatterLineMarker": "Punktowy z prostymi liniami i znacznikami", "Common.define.chartData.textScatterSmooth": "Punktowy z wygładzonymi liniami", "Common.define.chartData.textScatterSmoothMarker": "Punktowy z wygładzonymi liniami i znacznikami", "Common.define.chartData.textStock": "Zbiory", "Common.define.chartData.textSurface": "Powierzchnia", "Common.define.effectData.textAcross": "W<PERSON><PERSON>ł<PERSON><PERSON>", "Common.define.effectData.textBasic": "Podstawowy", "Common.define.effectData.textCircle": "Okrąg", "Common.define.effectData.textCollapse": "Zwiń", "Common.define.effectData.textCompress": "Skompresuj", "Common.define.effectData.textContrast": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textContrastingColor": "Kontrastują<PERSON> kolor", "Common.define.effectData.textCustomPath": "Własna ścieżka", "Common.define.effectData.textDarken": "Przyciemnij", "Common.define.effectData.textDiamond": "Romb", "Common.define.effectData.textDown": "<PERSON> dół", "Common.define.effectData.textExciting": "Ekscytujące", "Common.define.effectData.textExpand": "Rozwiń", "Common.define.effectData.textFade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFillColor": "<PERSON><PERSON> wypełnienia", "Common.define.effectData.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textFlyIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFlyOut": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFontColor": "<PERSON><PERSON>", "Common.define.effectData.textFromBottom": "Z dołu", "Common.define.effectData.textFromLeft": "<PERSON><PERSON>", "Common.define.effectData.textFromRight": "<PERSON><PERSON>", "Common.define.effectData.textFromTop": "Z góry", "Common.define.effectData.textHeart": "<PERSON><PERSON>", "Common.define.effectData.textHeartbeat": "Bicie serca", "Common.define.effectData.textHexagon": "Sześciokąt", "Common.define.effectData.textHorizontal": "Poziomy", "Common.define.effectData.textHorizontalIn": "W poziomie do środka", "Common.define.effectData.textHorizontalOut": "W poziomie na zewnątrz", "Common.define.effectData.textIn": "w", "Common.define.effectData.textLeft": "<PERSON><PERSON>", "Common.define.effectData.textLighten": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textLines": "<PERSON><PERSON>", "Common.define.effectData.textModerate": "Umiarkowany", "Common.define.effectData.textOctagon": "Ośmiokąt", "Common.define.effectData.textOut": "Wyjście", "Common.define.effectData.textParallelogram": "Równoległobok", "Common.define.effectData.textPentagon": "Pięcioką<PERSON>", "Common.define.effectData.textPlus": "Plus", "Common.define.effectData.textRight": "Do <PERSON>", "Common.define.effectData.textRightTriangle": "Trójkąt równoramienny", "Common.define.effectData.textShape": "Kształt", "Common.define.effectData.textShapes": "Kształty", "Common.define.effectData.textSpecial": "Specialny", "Common.define.effectData.textSplit": "Podziel", "Common.define.effectData.textSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textStretch": "Roz<PERSON>ą<PERSON><PERSON>", "Common.define.effectData.textTeardrop": "Ł<PERSON>", "Common.define.effectData.textTrapezoid": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textUnderline": "Podkreślenie", "Common.define.effectData.textUp": "W górę", "Common.define.effectData.textVertical": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textVerticalIn": "W pionie", "Common.define.effectData.textVerticalOut": "W pionie na zewnątrz", "Common.define.effectData.textWave": "Fala", "Common.define.effectData.textWedge": "Zak<PERSON><PERSON>j", "Common.define.effectData.textWheel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textWipe": "Wytrzyj", "Common.define.effectData.textZoom": "Powiększenie", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON><PERSON>j", "Common.Translation.warnFileLocked": "Plik jest edytowany w innej aplikacji. Możesz kontynuować edycję i zapisać go jako kopię.", "Common.Translation.warnFileLockedBtnEdit": "Utwórz kopię", "Common.Translation.warnFileLockedBtnView": "Otwarte do oglądania", "Common.UI.ButtonColored.textAutoColor": "Automatyczny", "Common.UI.ButtonColored.textNewColor": "Nowy niestandardowy kolor", "Common.UI.ComboBorderSize.txtNoBorders": "Bez k<PERSON>ędzi", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Bez k<PERSON>ędzi", "Common.UI.ComboDataView.emptyComboText": "Brak styli", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Obecny", "Common.UI.ExtendedColorDialog.textHexErr": "Wprowadzon<PERSON> wartość jest nieprawidłowa.<br>W<PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 000000 do FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "nowy", "Common.UI.ExtendedColorDialog.textRGBErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość liczbową w zakresie od 0 do 255.", "Common.UI.HSBColorPicker.textNoColor": "Bez koloru", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON> hasło", "Common.UI.SearchBar.textFind": "Znajdź", "Common.UI.SearchBar.tipCloseSearch": "Zamknij wyszukiwanie", "Common.UI.SearchBar.tipNextResult": "Następny wynik", "Common.UI.SearchBar.tipPreviousResult": "Poprzedni wynik", "Common.UI.SearchDialog.textHighlight": "Podświetl wyniki", "Common.UI.SearchDialog.textMatchCase": "Rozróżniana wielkość liter", "Common.UI.SearchDialog.textReplaceDef": "Wprowadź tekst zastępczy", "Common.UI.SearchDialog.textSearchStart": "Wprowadź tekst tutaj", "Common.UI.SearchDialog.textTitle": "Znajdź i zamień", "Common.UI.SearchDialog.textTitle2": "Znajdź", "Common.UI.SearchDialog.textWholeWords": "Tylko całe słowa", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Zamień wszystko", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.UI.SynchronizeTip.textSynchronize": "Dokument został zmieniony przez innego użytkownika.<br><PERSON><PERSON><PERSON> k<PERSON>, aby zap<PERSON>ć swoje zmiany i ponownie załadować zmiany.", "Common.UI.ThemeColorPalette.textRecentColors": "Ostatnie kolory", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>e", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON> moty<PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasyczny Jasny", "Common.UI.Themes.txtThemeDark": "Ciemny", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Potwierdzenie", "Common.UI.Window.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.UI.Window.textError": "Błąd", "Common.UI.Window.textInformation": "Informacja", "Common.UI.Window.textWarning": "Ostrzeżenie", "Common.UI.Window.yesButtonText": "Tak", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "adres:", "Common.Views.About.txtLicensee": "LICENCJOBIORCA", "Common.Views.About.txtLicensor": "LICENCJODAWCA", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "zasilany przez", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Zastos<PERSON>j <PERSON>", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorekta tekstu", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formatuj automatycznie podczas pisania", "Common.Views.AutoCorrectDialog.textBulleted": "Listy punktowane automatycznie", "Common.Views.AutoCorrectDialog.textBy": "Na", "Common.Views.AutoCorrectDialog.textDelete": "Usuń", "Common.Views.AutoCorrectDialog.textFLSentence": "Zamień pierwszą literę w zdaniach na wielką", "Common.Views.AutoCorrectDialog.textHyperlink": "Ścieżki internetowe i sieciowe z hiperłączami", "Common.Views.AutoCorrectDialog.textHyphens": "Łączniki (--) na pauzy (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autokorekta matematyczna", "Common.Views.AutoCorrectDialog.textNumbered": "Listy numerowane automatycznie", "Common.Views.AutoCorrectDialog.textQuotes": "Cudzysłowy \"proste\" na \"drukarskie\"", "Common.Views.AutoCorrectDialog.textRecognized": "Rozpoznawane funkcje", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Następujące wyrażenia są rozpoznawanymi wyrażeniami matematycznymi. Nie będą one automatycznie pisane kursywą.", "Common.Views.AutoCorrectDialog.textReplace": "Zamień", "Common.Views.AutoCorrectDialog.textReplaceText": "Zamień podczas pisania", "Common.Views.AutoCorrectDialog.textReplaceType": "Zamień tekst podczas pisania", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ustawienia domyślne", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Autokorekta", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Rozpoznawane funkcje muszą zawierać tylko litery od A do Z, wielkie lub małe.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Każde dodane wyrażenie zostanie usunięte, a te, które były usunięte zostaną przywrócone. <PERSON><PERSON> kont<PERSON>uo<PERSON>?", "Common.Views.AutoCorrectDialog.warnReplace": "Wpis autokorekty dla %1 już istnieje. <PERSON><PERSON> ch<PERSON>z go wymienić?", "Common.Views.AutoCorrectDialog.warnReset": "Wszelkie dodane autokorekty zostaną usunięte, a dla zmienionych zostaną przywrócone oryginalne wartości. <PERSON><PERSON> ch<PERSON> kont<PERSON>?", "Common.Views.AutoCorrectDialog.warnRestore": "Wpis autokorekty dla %1 zostanie zresetowany do pierwotnej wartości. <PERSON><PERSON> ch<PERSON>z kontynuować?", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Autor od A do Z", "Common.Views.Comments.mniAuthorDesc": "Autor od Z do A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniPositionAsc": "Z góry", "Common.Views.Comments.mniPositionDesc": "Z dołu", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Dodaj komentarz do dokumentu", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "Wszystkie", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Zamknij komentarze", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Wprowadź twój komentarz tutaj", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Otwórz ponownie", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Rozwiązany", "Common.Views.Comments.textSort": "Sort<PERSON>j komentarze", "Common.Views.Comments.textViewResolved": "Nie masz uprawnień do ponownego otwarcia komentarza", "Common.Views.Comments.txtEmpty": "Nie ma komentarzy w tym dokumencie.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, wycinanie i wklejanie za pomocą przycisków edytora i działań menu kontekstowego zostanie przeprowadzone tylko w tej karcie edytora.<br><br><PERSON><PERSON> s<PERSON><PERSON><PERSON>ć lub w<PERSON><PERSON> do lub z aplikacji poza kartą edytora, użyj następujących kombinacji klawiszy:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>, Wytnij i Wklej", "Common.Views.CopyWarningDialog.textToCopy": "dla kopiowania", "Common.Views.CopyWarningDialog.textToCut": "dla w<PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "do wklejenia", "Common.Views.DocumentAccessDialog.textLoading": "Ładowanie...", "Common.Views.DocumentAccessDialog.textTitle": "Ustaw uprawnienia dostępu", "Common.Views.ExternalDiagramEditor.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalOleEditor.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.Header.labelCoUsersDescr": "Dokument jest obecnie edytowany przez kilku użytkowników.", "Common.Views.Header.textAddFavorite": "Dodaj do ulubionych", "Common.Views.Header.textAdvSettings": "Ustawi<PERSON>e", "Common.Views.Header.textBack": "Przejdź do Dokumentów", "Common.Views.Header.textCompactView": "Ukryj pasek narzędzi", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON><PERSON><PERSON> pasek stanu", "Common.Views.Header.textRemoveFavorite": "Usuń z ulubionych", "Common.Views.Header.textSaveBegin": "Zapisywanie ...", "Common.Views.Header.textSaveChanged": "Zmodyfikowan<PERSON>", "Common.Views.Header.textSaveEnd": "Wszystkie zmiany zapisane", "Common.Views.Header.textSaveExpander": "Wszystkie zmiany zapisane", "Common.Views.Header.textShare": "Udostępnij", "Common.Views.Header.textZoom": "Powiększenie", "Common.Views.Header.tipAccessRights": "Zarządzaj prawami dostępu do dokumentu", "Common.Views.Header.tipDownload": "Pobierz plik", "Common.Views.Header.tipGoEdit": "Edytuj bieżący plik", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON> plik", "Common.Views.Header.tipRedo": "<PERSON>yk<PERSON><PERSON> pono<PERSON>ie", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "Szukaj", "Common.Views.Header.tipUndo": "Cof<PERSON>j", "Common.Views.Header.tipUndock": "Odepnij w osobnym oknie", "Common.Views.Header.tipUsers": "Zobacz użytkowników", "Common.Views.Header.tipViewSettings": "Wyświ<PERSON>l us<PERSON>wienia", "Common.Views.Header.tipViewUsers": "Wyświetl użytkowników i zarządzaj prawami dostępu do dokumentu", "Common.Views.Header.txtAccessRights": "Zmień prawa dostępu", "Common.Views.Header.txtRename": "Zmień nazwę", "Common.Views.History.textCloseHistory": "Zamknij Historię", "Common.Views.History.textHide": "Zwiń", "Common.Views.History.textHideAll": "Ukryj szczegółowe zmiany", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Rozwiń", "Common.Views.History.textShowAll": "Pokaż szczegółowe zmiany", "Common.Views.History.textVer": "wer.", "Common.Views.ImageFromUrlDialog.textUrl": "Wklej link URL do obrazu:", "Common.Views.ImageFromUrlDialog.txtEmpty": "To pole jest wymagane", "Common.Views.ImageFromUrlDialog.txtNotUrl": "To pole powinno by<PERSON> adresem URL w formacie \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Musisz określić poprawną liczbę wierszy i kolumn.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON><PERSON> kolumn", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON> dla tego pola to {0}", "Common.Views.InsertTableDialog.txtMinText": "<PERSON><PERSON><PERSON> dla tego pola to {0}", "Common.Views.InsertTableDialog.txtRows": "Liczba wierszy", "Common.Views.InsertTableDialog.txtTitle": "Rozmiar tablicy", "Common.Views.InsertTableDialog.txtTitleSplit": "Podziel komórkę", "Common.Views.LanguageDialog.labelSelect": "Wybierz język dokumentu", "Common.Views.ListSettingsDialog.textBulleted": "Punktowane", "Common.Views.ListSettingsDialog.textNumbering": "Numerowane", "Common.Views.ListSettingsDialog.tipChange": "Zmień znacznik", "Common.Views.ListSettingsDialog.txtBullet": "Znak punktora", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "Nowy znacznik", "Common.Views.ListSettingsDialog.txtNone": "Brak", "Common.Views.ListSettingsDialog.txtOfText": "% tekstu", "Common.Views.ListSettingsDialog.txtSize": "Rozmiar", "Common.Views.ListSettingsDialog.txtStart": "Zacznij w", "Common.Views.ListSettingsDialog.txtSymbol": "Symbole", "Common.Views.ListSettingsDialog.txtTitle": "Ustawienia listy", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "Zamknij <PERSON>", "Common.Views.OpenDialog.txtEncoding": "Kodowanie", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> jest nieprawidłowe.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło, aby otworzyć plik", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Po wprowadzeniu hasła i otwarciu pliku bieżące hasło do pliku zostanie zresetowane", "Common.Views.OpenDialog.txtTitle": "Wybierz %1 opcji", "Common.Views.OpenDialog.txtTitleProtected": "Plik chroniony", "Common.Views.PasswordDialog.txtDescription": "Ustaw hasło aby zabezpieczyć ten dokument", "Common.Views.PasswordDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON>zaj<PERSON>ce nie jest identyczne", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtTitle": "Ustaw hasło", "Common.Views.PasswordDialog.txtWarning": "Uwaga: <PERSON><PERSON><PERSON> zapomnisz lub zgubisz hasło, nie będzie możliwości odzyskania go. Zapisz go i nikomu nie udostępniaj.", "Common.Views.PluginDlg.textLoading": "Ładowanie", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "Ładowanie", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> lub usuń hasło", "Common.Views.Protection.hintSignature": "Dodaj podpis cyfrowy lub linię do podpisu", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON> hasło", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON>o", "Common.Views.Protection.txtEncrypt": "Szyfruj", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON> c<PERSON>y", "Common.Views.Protection.txtSignature": "Sygnatura", "Common.Views.Protection.txtSignatureLine": "Dodaj linię do podpisu", "Common.Views.RenameDialog.textName": "Nazwa pliku", "Common.Views.RenameDialog.txtInvalidName": "Nazwa pliku nie może zawierać żadnego z następujących znaków:", "Common.Views.ReviewChanges.hintNext": "Do następnej zmiany", "Common.Views.ReviewChanges.hintPrev": "Do poprzedniej zmiany", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Współedycja w czasie rzeczywistym. Wszystkie zmiany są zapisywane automatycznie.", "Common.Views.ReviewChanges.strStrict": "Ścisły", "Common.Views.ReviewChanges.strStrictDesc": "Użyj przycisku „Zapisz”, aby zsynchronizować zmiany wprowadzane przez Ciebie i innych.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Zaakceptuj bieżącą zmianę", "Common.Views.ReviewChanges.tipCoAuthMode": "Ustaw tryb współedycji", "Common.Views.ReviewChanges.tipCommentRem": "Usuń komentarze", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Usuń aktualne komentarze", "Common.Views.ReviewChanges.tipCommentResolve": "Rozwiąż komentarze", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Rozwiąż bieżące komentarze", "Common.Views.ReviewChanges.tipHistory": "Pokaż historię we<PERSON>ji", "Common.Views.ReviewChanges.tipRejectCurrent": "Odrzuć bieżącą zmianę", "Common.Views.ReviewChanges.tipReview": "Śledzenie zmian", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON><PERSON> tryb, w kt<PERSON>rym mają być wyświetlane zmiany", "Common.Views.ReviewChanges.tipSetDocLang": "Ustaw język dokumentu", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "Common.Views.ReviewChanges.tipSharing": "Zarządzaj prawami dostępu do dokumentu", "Common.Views.ReviewChanges.txtAccept": "Ak<PERSON>pt<PERSON>j", "Common.Views.ReviewChanges.txtAcceptAll": "Zaakceptuj wszystkie zmiany", "Common.Views.ReviewChanges.txtAcceptChanges": "Zaak<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "Zaakceptuj Bieżą<PERSON>ą <PERSON>", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Zamknij", "Common.Views.ReviewChanges.txtCoAuthMode": "Tryb współtworzenia", "Common.Views.ReviewChanges.txtCommentRemAll": "Usuń wszystkie komentarze", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Usuń aktualne komentarze", "Common.Views.ReviewChanges.txtCommentRemMy": "Usuń moje komentarze", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Usuń moje bieżące komentarze", "Common.Views.ReviewChanges.txtCommentRemove": "Usuń", "Common.Views.ReviewChanges.txtCommentResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Rozwiąż wszystkie komentarze", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Rozwiąż bieżące komentarze", "Common.Views.ReviewChanges.txtCommentResolveMy": "Rozwiąż moje komentarze", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Rozwiąż moje bieżące komentarze", "Common.Views.ReviewChanges.txtDocLang": "Język", "Common.Views.ReviewChanges.txtFinal": "Wszystkie zmiany zaakceptowane (Podgląd)", "Common.Views.ReviewChanges.txtFinalCap": "Ostateczny", "Common.Views.ReviewChanges.txtHistory": "<PERSON> wersji", "Common.Views.ReviewChanges.txtMarkup": "Wszystkie zmiany (Edycja)", "Common.Views.ReviewChanges.txtMarkupCap": "Zmiany", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Wszystkie zmiany odrzucone (Podgląd)", "Common.Views.ReviewChanges.txtOriginalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtPrev": "Poprzedni", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Odrzuć wszystkie zmiany", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Odrzuć bieżącą zmianę", "Common.Views.ReviewChanges.txtSharing": "Dostęp współdzielony", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "Common.Views.ReviewChanges.txtTurnon": "Śledzenie zmian", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON> w<PERSON>świ<PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Zamknij", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+wzmianka zapewni użytkownikowi dostęp do pliku i wyśle e-maila", "Common.Views.ReviewPopover.textMentionNotify": "+wzmianka powiadomi użytkownika e-mailem", "Common.Views.ReviewPopover.textOpenAgain": "Otwórz ponownie", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Nie masz uprawnień do ponownego otwarcia komentarza", "Common.Views.ReviewPopover.txtDeleteTip": "Usuń", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Ładowanie", "Common.Views.SaveAsDlg.textTitle": "Folder do zapisu", "Common.Views.SearchPanel.textCaseSensitive": "Rozróżniana wielkość liter", "Common.Views.SearchPanel.textCloseSearch": "Zamknij wyszukiwanie", "Common.Views.SearchPanel.textFind": "Znajdź", "Common.Views.SearchPanel.textFindAndReplace": "Znajdź i zastąp", "Common.Views.SearchPanel.textNoMatches": "Brak dopasowań", "Common.Views.SearchPanel.textNoSearchResults": "Brak wyników wyszukiwania", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Zamień wszystko", "Common.Views.SearchPanel.textTooManyResults": "Jest zbyt dużo wyników, aby je tutaj wyświ<PERSON>", "Common.Views.SearchPanel.textWholeWords": "Tylko całe słowa", "Common.Views.SearchPanel.tipNextResult": "Następny wynik", "Common.Views.SearchPanel.tipPreviousResult": "Poprzedni wynik", "Common.Views.SelectFileDlg.textLoading": "Ładowanie", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textBold": "Pogrubienie", "Common.Views.SignDialog.textCertificate": "Certy<PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Zmień", "Common.Views.SignDialog.textInputName": "Wpisz imię i nazwisko osoby podpisującej", "Common.Views.SignDialog.textItalic": "Ku<PERSON>ywa", "Common.Views.SignDialog.textNameError": "Imię i nazwisko osoby podpisującej nie może być puste.", "Common.Views.SignDialog.textPurpose": "Cel podpisywania tego dokumentu", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> obraz", "Common.Views.SignDialog.textSignature": "<PERSON><PERSON> wyglą<PERSON> podpis:", "Common.Views.SignDialog.textTitle": "Podpisz dokument", "Common.Views.SignDialog.textUseImage": "lub k<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON> obraz\", aby <PERSON><PERSON><PERSON><PERSON> obrazu jako pod<PERSON>u", "Common.Views.SignDialog.textValid": "Ważny od %1 do %2", "Common.Views.SignDialog.tipFontName": "Nazwa czcionki", "Common.Views.SignDialog.tipFontSize": "Rozmiar <PERSON>ki", "Common.Views.SignSettingsDialog.textAllowComment": "Zezwól podpisującemu na dodanie komentarza w oknie podpisu", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nazwa", "Common.Views.SignSettingsDialog.textInfoTitle": "Stanowisko osoby podpisującej", "Common.Views.SignSettingsDialog.textInstructions": "Instrukcje dla osoby podpisującej", "Common.Views.SignSettingsDialog.textShowDate": "Pokaż datę wykonania podpisu", "Common.Views.SignSettingsDialog.textTitle": "Konfiguracja podpisu", "Common.Views.SignSettingsDialog.txtEmpty": "To pole jest wymagane", "Common.Views.SymbolTableDialog.textCharacter": "Znak", "Common.Views.SymbolTableDialog.textCode": "Nazwa Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Znak zastrzeżenia prawa autorskiego", "Common.Views.SymbolTableDialog.textDCQuote": "Podwójny cudzysłów zamykający", "Common.Views.SymbolTableDialog.textDOQuote": "Podwójny cudzysłów otwierający", "Common.Views.SymbolTableDialog.textEllipsis": "Wielokropek", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Długa spacja", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnSpace": "Krótka spacja", "Common.Views.SymbolTableDialog.textFont": "Czcionka", "Common.Views.SymbolTableDialog.textNBHyphen": "Łącznik nierozdzielający", "Common.Views.SymbolTableDialog.textNBSpace": "Spacja nierozdzielająca", "Common.Views.SymbolTableDialog.textPilcrow": "Aka<PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 d<PERSON>ug<PERSON><PERSON>ji", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Ostatnio używane symbole", "Common.Views.SymbolTableDialog.textRegistered": "Zastrzeżony znak towarowy", "Common.Views.SymbolTableDialog.textSCQuote": "Pojedynczy cudzysłów zamykający", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "Łącznik miękki", "Common.Views.SymbolTableDialog.textSOQuote": "Pojedynczy cudzysłów otwierający", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Symbole", "Common.Views.SymbolTableDialog.textTitle": "Symbole", "Common.Views.SymbolTableDialog.textTradeMark": "Znak towarowy", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON> pytaj mnie ponownie", "Common.Views.UserNameDialog.textLabel": "Etykieta:", "Common.Views.UserNameDialog.textLabelError": "Etykieta nie może być pusta.", "PE.Controllers.LeftMenu.leavePageText": "Wszystkie niezapisane zmiany w tym dokumencie zostaną utracone. Kliknij przycisk \"Anuluj\", a następnie \"Zapisz\", aby je zapisa<PERSON>. Kliknij \"OK\", aby usun<PERSON><PERSON> wszystkie niezapisane zmiany.", "PE.Controllers.LeftMenu.newDocumentTitle": "Nienazwana prezentacja", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Ostrzeżenie", "PE.Controllers.LeftMenu.requestEditRightsText": "Żądanie praw do edycji...", "PE.Controllers.LeftMenu.textLoadHistory": "Wczytywanie historii wersji...", "PE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON> znale<PERSON>, k<PERSON><PERSON><PERSON><PERSON> s<PERSON>sz. <PERSON><PERSON><PERSON> dos<PERSON>uj opcje wyszukiwania.", "PE.Controllers.LeftMenu.textReplaceSkipped": "Zastąpiono. {0} zdarzenia zostały pominięte.", "PE.Controllers.LeftMenu.textReplaceSuccess": "Wyszukiwanie zakończone. Zastąpiono {0}", "PE.Controllers.LeftMenu.txtUntitled": "Bez Nazwy", "PE.Controllers.Main.applyChangesTextText": "Ładowanie danych...", "PE.Controllers.Main.applyChangesTitleText": "Łado<PERSON><PERSON> danych", "PE.Controllers.Main.convertationTimeoutText": "Przekroczono limit czasu konwersji.", "PE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"OK\", aby p<PERSON><PERSON><PERSON><PERSON><PERSON> do listy dokumentów.", "PE.Controllers.Main.criticalErrorTitle": "Błąd", "PE.Controllers.Main.downloadErrorText": "Pobieranie ni<PERSON>.", "PE.Controllers.Main.downloadTextText": "Pobieranie pre<PERSON>...", "PE.Controllers.Main.downloadTitleText": "Pobieranie <PERSON>", "PE.Controllers.Main.errorAccessDeny": "Próbujesz wykonać działanie, na które nie masz uprawnień.<br><PERSON><PERSON><PERSON> skontaktować się z <PERSON>em serwera dokumentów.", "PE.Controllers.Main.errorBadImageUrl": "Adres URL obrazu jest błędny", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Połączenie z serwerem zostało utracone. Nie można teraz edytować dokumentu.", "PE.Controllers.Main.errorComboSeries": "<PERSON>by utworz<PERSON>ć wykres złożony, wybierz co najmniej dwie serie danych.", "PE.Controllers.Main.errorConnectToServer": "Nie można zapisać dokumentu. Sprawdź ustawienia połączenia lub skontaktuj się z <PERSON>em.<br>Po kliknięciu przycisku \"OK\" zostanie wyświetlony monit o pobranie dokumentu.", "PE.Controllers.Main.errorDatabaseConnection": "Błąd zewnętrzny.<br>Błąd połączenia z bazą danych. W przypadku wystąpienia błędu należy skontaktować się z pomocą techniczną.", "PE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>, nie można ich odszyfrować.", "PE.Controllers.Main.errorDataRange": "Błędny zakres danych.", "PE.Controllers.Main.errorDefaultMessage": "Kod błędu: %1", "PE.Controllers.Main.errorEditingDownloadas": "Wystąpił błąd podczas pracy z dokumentem.<br><PERSON><PERSON><PERSON><PERSON> op<PERSON> \"Pobierz jako\", aby zapisać kopię zapasową pliku na dysku twardym komputera.", "PE.Controllers.Main.errorEditingSaveas": "Wystą<PERSON>ł błąd podczas pracy z dokumentem.<br><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> \"Zapisz kopię jako...\", aby zapisać kopię zapasową pliku na dysku twardym komputera.", "PE.Controllers.Main.errorEmailClient": "Nie znaleziono klienta poczty email.", "PE.Controllers.Main.errorFilePassProtect": "Dokument jest chroniony hasłem i nie można go otworzyć.", "PE.Controllers.Main.errorFileSizeExceed": "Rozmiar pliku przekracza ustalony limit dla twojego serwera.<br><PERSON><PERSON><PERSON> skontaktowa<PERSON> z administratorem twojego Serwera Dokumentów w celu uzyskania szczegółowych informacji.", "PE.Controllers.Main.errorForceSave": "Wystąpił błąd podczas zapisywania pliku. Użyj opcji \"Pobierz jako\", aby zapisać plik na dysku twardym komputera lub spróbuj zapisać plik ponownie poźniej.", "PE.Controllers.Main.errorKeyEncrypt": "Nieznany deskryptor klu<PERSON>a", "PE.Controllers.Main.errorKeyExpire": "Okres ważności deskryptora klucza wygasł", "PE.Controllers.Main.errorLoadingFont": "Czcionki nie zostały załadowane.<br>Skontaktuj się z administratorem Serwera Dokumentów.", "PE.Controllers.Main.errorProcessSaveResult": "Zapisywanie nie powiodło się.", "PE.Controllers.Main.errorServerVersion": "Wersja edytora została zaktualizowana. Strona zostanie ponownie załadowana, aby z<PERSON><PERSON><PERSON><PERSON> z<PERSON>.", "PE.Controllers.Main.errorSessionAbsolute": "Sesja edycji dokumentu wygasła. Proszę ponownie załadować stronę.", "PE.Controllers.Main.errorSessionIdle": "Dokument nie był edytowany przez długi czas. Proszę ponownie załadować stronę.", "PE.Controllers.Main.errorSessionToken": "Połączenie z serwerem zostało przerwane. Proszę ponownie załadować stronę.", "PE.Controllers.Main.errorSetPassword": "<PERSON><PERSON> m<PERSON><PERSON> hasła.", "PE.Controllers.Main.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "PE.Controllers.Main.errorToken": "Token bezpieczeństwa dokumentu jest nieprawidłowo uformowany.<br>Prosimy o kontakt z <PERSON>em serwera dokumentów.", "PE.Controllers.Main.errorTokenExpire": "Token zabezpieczeń dokumentu wygasł.<br>Prosimy o kontakt z <PERSON>em serwera dokumentów.", "PE.Controllers.Main.errorUpdateVersion": "Wersja pliku została zmieniona. Strona zostanie ponownie załadowana.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "Połączenie z internetem zostało odzyskane, a wersja pliku uległa zmianie.<br><PERSON><PERSON><PERSON> b<PERSON> mógł kont<PERSON> pracę, mus<PERSON>z pobrać plik albo skopiować jego <PERSON>, aby <PERSON><PERSON><PERSON>, że nic nie zostało utracone, a następnie odświeżyć stronę.", "PE.Controllers.Main.errorUserDrop": "Nie można uzyskać dostępu do tego pliku.", "PE.Controllers.Main.errorUsersExceed": "Przekroczono liczbę dozwolonych użytkowników określonych przez plan cenowy wersji", "PE.Controllers.Main.errorViewerDisconnect": "Połączenie zostało utracone. Nadal moż<PERSON><PERSON> p<PERSON> dokument, ale nie będziesz mógł pobrać ani wydrukować dokumentu do momentu przywrócenia połączenia.", "PE.Controllers.Main.leavePageText": "W tej prezentacji masz niezapisane zmiany. Kliknij przycisk \"Zostań na te stronie\", a następnie \"Zapisz\", aby je zapisa<PERSON>. Kliknij przycisk \"Pozostaw tę stronę\", aby usun<PERSON>ć wszystkie niezapisane zmiany.", "PE.Controllers.Main.leavePageTextOnClose": "Wszystkie niezapisane zmiany zostaną utracone.<br> <PERSON><PERSON><PERSON><PERSON> \"An<PERSON><PERSON>\", a nastę<PERSON>nie \"Zapisz\" aby je zapisać. Kliknij \"OK\" aby o<PERSON><PERSON><PERSON>ć wszystkie niezapisane zmiany.", "PE.Controllers.Main.loadFontsTextText": "Ładowanie danych...", "PE.Controllers.Main.loadFontsTitleText": "Łado<PERSON><PERSON> danych", "PE.Controllers.Main.loadFontTextText": "Ładowanie danych...", "PE.Controllers.Main.loadFontTitleText": "Łado<PERSON><PERSON> danych", "PE.Controllers.Main.loadImagesTextText": "Ładowanie obrazów...", "PE.Controllers.Main.loadImagesTitleText": "Ładowanie obrazów", "PE.Controllers.Main.loadImageTextText": "Ładowanie obrazu...", "PE.Controllers.Main.loadImageTitleText": "Ładowanie obrazu", "PE.Controllers.Main.loadingDocumentTextText": "Ładowanie prezentacji...", "PE.Controllers.Main.loadingDocumentTitleText": "Ładowanie <PERSON>", "PE.Controllers.Main.loadThemeTextText": "Ładowanie motywu...", "PE.Controllers.Main.loadThemeTitleText": "<PERSON><PERSON><PERSON><PERSON> moty<PERSON>", "PE.Controllers.Main.notcriticalErrorTitle": "Ostrzeżenie", "PE.Controllers.Main.openErrorText": "W<PERSON><PERSON><PERSON><PERSON>ł błąd podczas otwierania pliku", "PE.Controllers.Main.openTextText": "Otwi<PERSON>ie pre<PERSON>...", "PE.Controllers.Main.openTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.printTextText": "Drukowanie prezentacji...", "PE.Controllers.Main.printTitleText": "Druk<PERSON><PERSON> pre<PERSON>", "PE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON>ś edytuje teraz tą prezentację. Proszę spróbuj później.", "PE.Controllers.Main.requestEditFailedTitleText": "Odmo<PERSON> dos<PERSON>ępu", "PE.Controllers.Main.saveErrorText": "Wys<PERSON>ą<PERSON>ł błąd podczas zapisywania pliku", "PE.Controllers.Main.saveErrorTextDesktop": "<PERSON>e można zapisać lub utwo<PERSON><PERSON>ć tego pliku.<br>Możliwe przyczyny to: <br>1. <PERSON>lik jest tylko do odczytu. <br>2. Plik jest edytowany przez innych użytkowników. <br>3. <PERSON><PERSON><PERSON> jest pełny lub uszkodzony.", "PE.Controllers.Main.saveTextText": "Zapisywan<PERSON>...", "PE.Controllers.Main.saveTitleText": "<PERSON>apisy<PERSON><PERSON>", "PE.Controllers.Main.scriptLoadError": "Połączenie jest zbyt wolne, niektóre komponenty mogły nie zostać załadowane. Proszę odświeżyć stronę.", "PE.Controllers.Main.splitDividerErrorText": "Liczba wierszy musi być dzielnikiem %1.", "PE.Controllers.Main.splitMaxColsErrorText": "Liczba kolumn musi być mniejsza niż %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "Liczba wierszy musi być mniejsza niż %1.", "PE.Controllers.Main.textAnonymous": "Anonimowy użytkownik ", "PE.Controllers.Main.textApplyAll": "Zastosuj do wszystkich równań", "PE.Controllers.Main.textBuyNow": "Od<PERSON><PERSON><PERSON> stronę web", "PE.Controllers.Main.textChangesSaved": "Wszystkie zmiany zapisane", "PE.Controllers.Main.textClose": "Zamknij", "PE.Controllers.Main.textCloseTip": "Kliknij, żeby zamknąć wskazówkę", "PE.Controllers.Main.textContactUs": "Skontaktuj się z działem sprzedaży", "PE.Controllers.Main.textConvertEquation": "To równanie zostało utworzone za pomocą starej wersji edytora równań, kt<PERSON>ra nie jest już obsługiwana. <PERSON><PERSON> je edyt<PERSON>, przekonwertuj równanie na format Office Math ML.<br>Przekonwertować teraz?", "PE.Controllers.Main.textCustomLoader": "<PERSON><PERSON><PERSON><PERSON>, że zgodnie z warunkami licencji nie jesteś uprawniony do zmiany ładowania. <br> W celu uzyskania wyceny prosimy o kontakt z naszym Działem Sprzedaży.", "PE.Controllers.Main.textDisconnect": "Połączenie zostało utracone", "PE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textHasMacros": "Plik zawiera makra automatyczne.<br><PERSON><PERSON><PERSON> uruchomić te makra?", "PE.Controllers.Main.textLearnMore": "Dowiedz się więcej", "PE.Controllers.Main.textLoadingDocument": "Ładowanie <PERSON>", "PE.Controllers.Main.textLongName": "Wpisz nazwę krótszą niż 128 znaków.", "PE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE wersja open source", "PE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textReconnect": "Połączenie zostało przywrócone", "PE.Controllers.Main.textRemember": "Zapamiętaj mój wybór dla wszystkich plików", "PE.Controllers.Main.textRenameError": "Nazwa użytkownika nie może być pusta.", "PE.Controllers.Main.textRenameLabel": "Wpisz nazwę, która ma być używana do współpracy", "PE.Controllers.Main.textShape": "Kształt", "PE.Controllers.Main.textStrict": "<PERSON><PERSON>", "PE.Controllers.Main.textText": "Tekst", "PE.Controllers.Main.textTryUndoRedo": "Funkcje Cofnij/Ponów są wyłączone w trybie \"<PERSON><PERSON>b<PERSON>\" współtworzenia.<br><PERSON><PERSON><PERSON><PERSON> przy<PERSON> \"Tryb ścisły\", aby przejść do trybu ścisłego edytowania, aby edytować plik bez ingerencji innych użytkowników i wysyłać zmiany tylko po zapisaniu. Możesz przełączać się między trybami współtworzenia, używając edytora Ustawienia zaawansowane.", "PE.Controllers.Main.textTryUndoRedoWarn": "Funkcje Cofnij/Ponów są wyłączone w trybie Szybkim współtworzenia.", "PE.Controllers.Main.titleLicenseExp": "Upłynął okres ważności licencji", "PE.Controllers.Main.titleServerVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtAddFirstSlide": "<PERSON><PERSON><PERSON><PERSON>, aby do<PERSON><PERSON> pierwszy slajd", "PE.Controllers.Main.txtAddNotes": "<PERSON><PERSON><PERSON><PERSON> aby do<PERSON> notatki", "PE.Controllers.Main.txtArt": "Twój tekst tutaj", "PE.Controllers.Main.txtBasicShapes": "Kształty podstawowe", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "Objaśnienia", "PE.Controllers.Main.txtCharts": "Wykresy", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Data i czas", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtEditingMode": "Ustaw tryb edycji...", "PE.Controllers.Main.txtErrorLoadHistory": "Ładowanie historii nie powiodło się", "PE.Controllers.Main.txtFiguredArrows": "Strz<PERSON>ł<PERSON>", "PE.Controllers.Main.txtFooter": "Stopka", "PE.Controllers.Main.txtHeader": "Nagłówek", "PE.Controllers.Main.txtImage": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "Ładowanie...", "PE.Controllers.Main.txtMath": "Matematyka", "PE.Controllers.Main.txtMedia": "<PERSON><PERSON>", "PE.Controllers.Main.txtNeedSynchronize": "Masz aktualizacje", "PE.Controllers.Main.txtNone": "Brak", "PE.Controllers.Main.txtPicture": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtRectangles": "Prostokąty", "PE.Controllers.Main.txtSeries": "Serie", "PE.Controllers.Main.txtShape_accentCallout1": "Objaśnienie: linia z paskiem wyróżniającym", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "Przycisk wstecz lub wcześniej", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Przycisk Rozpoczęcia", "PE.Controllers.Main.txtShape_actionButtonBlank": "Pusty Przycisk", "PE.Controllers.Main.txtShape_actionButtonDocument": "Przycisk dokument", "PE.Controllers.Main.txtShape_actionButtonEnd": "Przycisk zakończenia ", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Przycisk następny", "PE.Controllers.Main.txtShape_actionButtonHelp": "Przycisk pomocy", "PE.Controllers.Main.txtShape_actionButtonHome": "Przycisk dom", "PE.Controllers.Main.txtShape_actionButtonInformation": "Przycisk informacji", "PE.Controllers.Main.txtShape_actionButtonMovie": "Przycisk wideo", "PE.Controllers.Main.txtShape_actionButtonReturn": "Przycisk powrotu", "PE.Controllers.Main.txtShape_actionButtonSound": "Przycisk dźwięku", "PE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_bentArrow": "Strzałka Wygięta", "PE.Controllers.Main.txtShape_bentConnector5": "Łącznik: łamany", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Łącznik: łamany ze strzałką", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Łącznik: łamany z podwójną strzałką", "PE.Controllers.Main.txtShape_bentUpArrow": "Strzałka Wygięta w Górę", "PE.Controllers.Main.txtShape_bevel": "U<PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Łuk Blokowy", "PE.Controllers.Main.txtShape_bracePair": "Para nawiasów klamrowych", "PE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_chevron": "Strzałka Pagon", "PE.Controllers.Main.txtShape_chord": "Odcinek koła", "PE.Controllers.Main.txtShape_circularArrow": "Strzałka Kolista", "PE.Controllers.Main.txtShape_cloud": "Chmura", "PE.Controllers.Main.txtShape_cloudCallout": "Dymek <PERSON>śli: chmurka", "PE.Controllers.Main.txtShape_corner": "Narożnik", "PE.Controllers.Main.txtShape_cube": "S<PERSON>ś<PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Łącznik: zakrzywiony", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Łącznik: zakrzywiony ze strzałką", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Łącznik: zakrzywiony z podwójną strzałką", "PE.Controllers.Main.txtShape_curvedDownArrow": "Strzałka: zakrzywiona w dół", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Strzałka: zakrzywiona w lewo", "PE.Controllers.Main.txtShape_curvedRightArrow": "Strzałka: zakrzywiona w prawo", "PE.Controllers.Main.txtShape_curvedUpArrow": "Strzałka: zakrzywiona w górę", "PE.Controllers.Main.txtShape_decagon": "Dziesięciokąt", "PE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_diamond": "Romb", "PE.Controllers.Main.txtShape_dodecagon": "Dwunastoką<PERSON>", "PE.Controllers.Main.txtShape_donut": "Okrąg: pusty", "PE.Controllers.Main.txtShape_doubleWave": "Podwójna fala", "PE.Controllers.Main.txtShape_downArrow": "Strzałka w dół", "PE.Controllers.Main.txtShape_downArrowCallout": "Objaśnienie: strzałka w dół", "PE.Controllers.Main.txtShape_ellipse": "Elipsa", "PE.Controllers.Main.txtShape_ellipseRibbon": "Wstęga: zakrzywiona i nachylona w dół", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Wstęga: zakrzywiona i nachylona w górę", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Schemat blokowy: proces alternatywny", "PE.Controllers.Main.txtShape_flowChartCollate": "Schemat blokowy: zestawienie", "PE.Controllers.Main.txtShape_flowChartConnector": "Schemat blokowy: łącznik", "PE.Controllers.Main.txtShape_flowChartDecision": "Schemat blokowy: decyzja", "PE.Controllers.Main.txtShape_flowChartDelay": "Schemat blokowy: opóźnienie", "PE.Controllers.Main.txtShape_flowChartDisplay": "Schemat blokowy: ekran", "PE.Controllers.Main.txtShape_flowChartDocument": "Schemat blokowy: dokument", "PE.Controllers.Main.txtShape_flowChartExtract": "Schemat blokowy: wyodrębnianie", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Schemat blokowy: decyzja", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Schemat blokowy: p<PERSON><PERSON><PERSON> wewnętrzna", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Schemat blokowy: dysk magnetyczny", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Schemat blokowy: pamięć o dostępie bezpośrednim", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Schemat blokowy: pamięć o dostępie sekwencyjnym", "PE.Controllers.Main.txtShape_flowChartManualInput": "Schemat blokowy: ręczne wprowadzenie danych", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Schemat blokowy: operacja ręczna", "PE.Controllers.Main.txtShape_flowChartMerge": "Schemat blokowy: scalanie", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Schemat blokowy: wiele dokumentów", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Schemat blokowy: łącznik międzystronicowy", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Schemat blokowy: przechowywane dane", "PE.Controllers.Main.txtShape_flowChartOr": "Schemat blokowy: lub", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Schemat blokowy: proces uprzednio zdefiniowany", "PE.Controllers.Main.txtShape_flowChartPreparation": "Schemat blokowy: przygotowanie", "PE.Controllers.Main.txtShape_flowChartProcess": "Schemat blokowy: proces", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Schemat blokowy: karta", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Schemat blokowy: taśma dziurkowana", "PE.Controllers.Main.txtShape_flowChartSort": "Schemat blokowy: sortowanie", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Schemat blokowy: operacja sumowania", "PE.Controllers.Main.txtShape_flowChartTerminator": "<PERSON>hemat blokowy: terminator", "PE.Controllers.Main.txtShape_foldedCorner": "Prostokąt: zagięty narożnik", "PE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "Połowa ramki", "PE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_heptagon": "Siedmiokąt", "PE.Controllers.Main.txtShape_hexagon": "Sześciokąt", "PE.Controllers.Main.txtShape_homePlate": "Pięcioką<PERSON>", "PE.Controllers.Main.txtShape_horizontalScroll": "Zwój: poziomy", "PE.Controllers.Main.txtShape_irregularSeal1": "Eksplozja 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Eksplozja 2", "PE.Controllers.Main.txtShape_leftArrow": "Lewa strzałka", "PE.Controllers.Main.txtShape_leftArrowCallout": "Objaśnienie: strzałka w lewo", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON> otwierający", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftRightArrow": "Strzałka: w lewo i w prawo", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Objaśnienie: strzałka w lewo i w prawo", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Strzałka: w lewo wprawo i w górę", "PE.Controllers.Main.txtShape_leftUpArrow": "Strzałka: w lewo i w górę", "PE.Controllers.Main.txtShape_lightningBolt": "Błyskawica", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "Strzałka", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "Strzałka liniowa: podwójna", "PE.Controllers.Main.txtShape_mathDivide": "Znak dzielenia", "PE.Controllers.Main.txtShape_mathEqual": "Równy", "PE.Controllers.Main.txtShape_mathMinus": "Minus", "PE.Controllers.Main.txtShape_mathMultiply": "Znak mnożenia", "PE.Controllers.Main.txtShape_mathNotEqual": "Różny od", "PE.Controllers.Main.txtShape_mathPlus": "Plus", "PE.Controllers.Main.txtShape_moon": "Księżyc", "PE.Controllers.Main.txtShape_noSmoking": "Symbol \"nie\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Strzałka: w prawo z wcięciem", "PE.Controllers.Main.txtShape_octagon": "Ośmiokąt", "PE.Controllers.Main.txtShape_parallelogram": "Równoległobok", "PE.Controllers.Main.txtShape_pentagon": "Pięcioką<PERSON>", "PE.Controllers.Main.txtShape_pie": "Kołowe", "PE.Controllers.Main.txtShape_plaque": "Podpisz", "PE.Controllers.Main.txtShape_plus": "Plus", "PE.Controllers.Main.txtShape_polyline1": "Dowolny kształt: bazgroły", "PE.Controllers.Main.txtShape_polyline2": "Dowolny kształt: kształt", "PE.Controllers.Main.txtShape_quadArrow": "Strzałka: w cztery strony", "PE.Controllers.Main.txtShape_quadArrowCallout": "Objaśnienie: strzałka w cztery strony", "PE.Controllers.Main.txtShape_rect": "Prostokąt", "PE.Controllers.Main.txtShape_ribbon": "Wstęga: nachylona w dół", "PE.Controllers.Main.txtShape_ribbon2": "Wstęga: nachylona w górę", "PE.Controllers.Main.txtShape_rightArrow": "Strzałka w prawo", "PE.Controllers.Main.txtShape_rightArrowCallout": "Objaśnienie: strzałka w prawo", "PE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON>rowy zamykający", "PE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_round1Rect": "Prostokąt: jeden zaokrąglony róg", "PE.Controllers.Main.txtShape_round2DiagRect": "Prostokąt: zaokrąglone rogi po przekątnej", "PE.Controllers.Main.txtShape_round2SameRect": "Prostokąt: zaokrąglone rogi u góry", "PE.Controllers.Main.txtShape_roundRect": "Prostokąt: zaokrąglone rogi", "PE.Controllers.Main.txtShape_rtTriangle": "Trójkąt równoramienny", "PE.Controllers.Main.txtShape_smileyFace": "Uśmiechnię<PERSON> buźka", "PE.Controllers.Main.txtShape_snip1Rect": "Prostokąt: jeden <PERSON> róg ", "PE.Controllers.Main.txtShape_snip2DiagRect": "Prostokąt: ścięte rogi po przekątnej", "PE.Controllers.Main.txtShape_snip2SameRect": "Prostokąt: ścięte rogi u góry", "PE.Controllers.Main.txtShape_snipRoundRect": "Prostokąt: zaokrąglony róg i ścięty róg u góry", "PE.Controllers.Main.txtShape_spline": "Krzywa", "PE.Controllers.Main.txtShape_star10": "Gwiazda 10-<PERSON><PERSON>na", "PE.Controllers.Main.txtShape_star12": "Gwiazda 12-<PERSON><PERSON>na", "PE.Controllers.Main.txtShape_star16": "Gwiazda 16-<PERSON><PERSON>na", "PE.Controllers.Main.txtShape_star24": "Gwiazda 24-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star32": "Gwiazda 32-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star4": "Gwiazda 4-ramienna", "PE.Controllers.Main.txtShape_star5": "Gwiazda 5-ramienna", "PE.Controllers.Main.txtShape_star6": "Gwiazda 6-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star7": "Gwiazda 7-<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star8": "Gwiazda 8-<PERSON><PERSON>na", "PE.Controllers.Main.txtShape_stripedRightArrow": "Strzałka: prążkowana w prawo", "PE.Controllers.Main.txtShape_sun": "Sł<PERSON>cz<PERSON>", "PE.Controllers.Main.txtShape_teardrop": "Ł<PERSON>", "PE.Controllers.Main.txtShape_textRect": "Pole tekstowe", "PE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_triangle": "Trójkąt równoramienny", "PE.Controllers.Main.txtShape_upArrow": "Strzałka w górę", "PE.Controllers.Main.txtShape_upArrowCallout": "Objaśnienie: strzałka w górę", "PE.Controllers.Main.txtShape_upDownArrow": "Strzałka: w górę i w dół", "PE.Controllers.Main.txtShape_uturnArrow": "Strzałka: zawracanie", "PE.Controllers.Main.txtShape_verticalScroll": "Zwój: pionowy", "PE.Controllers.Main.txtShape_wave": "Fala", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Dymek mowy: owalny", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Dymek mowy: prostokąt", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Dymek mowy: prostokąt z zaokrąglonymi rogami", "PE.Controllers.Main.txtSldLtTBlank": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChart": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTChartAndTx": "Wykres i tekst", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art and Text", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Grafika i tekst pionowy", "PE.Controllers.Main.txtSldLtTCust": "Własny", "PE.Controllers.Main.txtSldLtTDgm": "Diagram", "PE.Controllers.Main.txtSldLtTFourObj": "Cztery obiekty", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Media i tekst", "PE.Controllers.Main.txtSldLtTObj": "Tytuł i obiekt", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Obiekt i dwa obiekty", "PE.Controllers.Main.txtSldLtTObjAndTx": "Obiekt i tekst", "PE.Controllers.Main.txtSldLtTObjOnly": "Obiekt", "PE.Controllers.Main.txtSldLtTObjOverTx": "Obiekt nad tekstem", "PE.Controllers.Main.txtSldLtTObjTx": "Tytuł, Obiekt i Podpis", "PE.Controllers.Main.txtSldLtTPicTx": "Obraz i napis", "PE.Controllers.Main.txtSldLtTSecHead": "Nagłówek sekcji", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON> t<PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Dwie kolumny tekstowe", "PE.Controllers.Main.txtSldLtTTwoObj": "Dwa obiekty", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Dwa obiekty i obiekt", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Dwa obiekty i tekst", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Dwa obiekty nad tekstem", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Dwa teksty i dwa obiekty", "PE.Controllers.Main.txtSldLtTTx": "Tekst", "PE.Controllers.Main.txtSldLtTTxAndChart": "Tekst i wykres", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Tekst i obrazek", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Tekst i media", "PE.Controllers.Main.txtSldLtTTxAndObj": "Tekst i obiekt", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Tekst i dwa obiekty", "PE.Controllers.Main.txtSldLtTTxOverObj": "Tekst nad obiektem", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Pionowy tytuł i tekst", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Pionowy tytuł i tekst nad wykresem", "PE.Controllers.Main.txtSldLtTVertTx": "Pionowy tekst", "PE.Controllers.Main.txtSlideNumber": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSlideSubtitle": "Podt<PERSON><PERSON>ł slajdu", "PE.Controllers.Main.txtSlideText": "<PERSON><PERSON><PERSON><PERSON> slajdu", "PE.Controllers.Main.txtSlideTitle": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtStarsRibbons": "Gwiazdy i wstążki", "PE.Controllers.Main.txtTheme_basic": "Podstawowy", "PE.Controllers.Main.txtTheme_blank": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_classic": "Klasyczny", "PE.Controllers.Main.txtTheme_corner": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green_leaf": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "Biuro", "PE.Controllers.Main.txtXAxis": "Oś X", "PE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "PE.Controllers.Main.unknownErrorText": "Nieznany błąd.", "PE.Controllers.Main.unsupportedBrowserErrorText": "Twoja przeglądarka nie jest wspierana.", "PE.Controllers.Main.uploadImageExtMessage": "Nieznany format obrazu.", "PE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON>.", "PE.Controllers.Main.uploadImageSizeMessage": "Maksymalny rozmiar obrazu został przekroczony.", "PE.Controllers.Main.uploadImageTextText": "Wysyłanie obrazu...", "PE.Controllers.Main.uploadImageTitleText": "Wysyłanie obrazu", "PE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON> cze<PERSON>...", "PE.Controllers.Main.warnBrowserIE9": "Aplikacja ma małe możliwości w IE9. Użyj przeglądarki IE10 lub nowszej.", "PE.Controllers.Main.warnBrowserZoom": "Aktualne ustawienie powiększenia przeglądarki nie jest w pełni obsługiwane. Zresetuj domyślny zoom, naciskając Ctrl + 0.", "PE.Controllers.Main.warnLicenseExceeded": "Ta wersja edytorów ONLYOFFICE ma pewne ograniczenia dla użytkowników.Dokument zostanie otwarty tylko do odczytu.<b><PERSON><PERSON><PERSON> potrzebujesz więcej, roz<PERSON>ż zakupienie licencji komercyjnej.", "PE.Controllers.Main.warnLicenseExp": "Twoja licencja wygasła.<br>Zaktualizuj licencję i odśwież stronę.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licencja wygasła.<br><PERSON><PERSON> <PERSON>sz <PERSON>tę<PERSON> do edycji dokumentu.<br><PERSON><PERSON><PERSON> skontaktować się ze swoim administratorem.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Licencja musi zostać odnowiona.<br><PERSON><PERSON> dostęp do edycji dokumentu.<br>Skontaktuj się ze swoim administratorem, aby uzyskać pełny dostęp.", "PE.Controllers.Main.warnLicenseUsersExceeded": "Osiągnąłeś limit dla użytkownia. Skontaktuj się z <PERSON>em, aby dowiedzieć się więcej.", "PE.Controllers.Main.warnNoLicense": "Używasz wersji %1 w wersji open source. Wersja ma ograniczenia dla jednoczesnych połączeń z serwerem dokumentów (po 20 połączeń naraz). <PERSON><PERSON><PERSON> potrzebu<PERSON> więcej, rozważ zakup licencji komercyjnej.", "PE.Controllers.Main.warnNoLicenseUsers": "Osiągnąłeś limit dla użytkownika. Skontaktuj się z zespołem sprzedaży %1 w celu uzyskania osobistych warunków aktualizacji.", "PE.Controllers.Main.warnProcessRightsChange": "<PERSON>e masz prawa edytować tego pliku.", "PE.Controllers.Search.notcriticalErrorTitle": "Ostrzeżenie", "PE.Controllers.Search.textNoTextFound": "<PERSON><PERSON> znale<PERSON>, k<PERSON><PERSON><PERSON><PERSON> s<PERSON>sz. <PERSON><PERSON><PERSON> dos<PERSON>uj opcje wyszukiwania.", "PE.Controllers.Search.textReplaceSkipped": "Zastąpiono. {0} zdarzenia zostały pominięte.", "PE.Controllers.Statusbar.zoomText": "Powięks<PERSON><PERSON> {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> z<PERSON>, nie jest dostępna na bieżącym urządzeniu.<br>Styl tekstu zostanie wyświetlony przy użyciu jednej z czcionek systemowych, a zapisana czcionka będzie używana, jeśli będzie dostępna.<br><PERSON><PERSON> ch<PERSON> kont<PERSON>uo<PERSON>?", "PE.Controllers.Toolbar.textAccent": "Akcenty", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textEmptyImgUrl": "<PERSON><PERSON><PERSON> podać adres URL obrazu.", "PE.Controllers.Toolbar.textFontSizeErr": "Wp<PERSON><PERSON><PERSON><PERSON> wartość jest nieprawidłowa.<br>W<PERSON><PERSON><PERSON><PERSON> wartość numeryczną w zakresie od 1 do 300", "PE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textInsert": "Wstaw", "PE.Controllers.Toolbar.textIntegral": "Całki", "PE.Controllers.Toolbar.textLargeOperator": "Duże operatory", "PE.Controllers.Toolbar.textLimitAndLog": "Limity i algorytmy", "PE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textOperator": "Operatory", "PE.Controllers.Toolbar.textRadical": "radykały", "PE.Controllers.Toolbar.textScript": "Indeksy", "PE.Controllers.Toolbar.textSymbols": "Symbole", "PE.Controllers.Toolbar.textWarning": "Ostrzeżenie", "PE.Controllers.Toolbar.txtAccent_Accent": "Ostry", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Strzałka w lewo - w prawo powyżej", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Lewa strzałka powyżej", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Strzałka w prawo powyżej", "PE.Controllers.Toolbar.txtAccent_Bar": "Pasek", "PE.Controllers.Toolbar.txtAccent_BarBot": " Kreska na dole", "PE.Controllers.Toolbar.txtAccent_BarTop": "Kreska od góry", "PE.Controllers.Toolbar.txtAccent_BorderBox": "Formuła w ramce (z wypełnieniem)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Forma zamknięta (przykład)", "PE.Controllers.Toolbar.txtAccent_Check": "Sprawdź", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON><PERSON><PERSON> k<PERSON> na dole", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Wektor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Z Nadmiarem", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y z cechą góry", "PE.Controllers.Toolbar.txtAccent_DDDot": "Wielokropek", "PE.Controllers.Toolbar.txtAccent_DDot": "Podwójna kropka", "PE.Controllers.Toolbar.txtAccent_Dot": "Kropka", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Podwójna kreska od góry", "PE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Grupowanie znaków poniżej", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Grupowanie znaków powyżej", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Ha<PERSON>un w lewo do góry", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Prawy Harpun Powyżej", "PE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Nawiasy i dzielniki", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Nawiasy i dzielniki", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Nawiasy i dzielniki", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (dwa warunki)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (trzy warunki)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "Obiekt stosu", "PE.Controllers.Toolbar.txtBracket_Custom_4": "Obiekt stosu", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Przykłady przypadków", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Współczynnik dwumianowy", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Współczynnik dwumianowy", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Nawiasy i dzielniki", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Poje<PERSON><PERSON><PERSON> na<PERSON>", "PE.Controllers.Toolbar.txtFractionDiagonal": "Skrzywiona frakcja", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Zróżnicowany", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Zróżnicowany", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Zróżnicowany", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Zróżnicowany", "PE.Controllers.Toolbar.txtFractionHorizontal": "Ułamek liniowy", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi ponad 2", "PE.Controllers.Toolbar.txtFractionSmall": "Mała frakcja", "PE.Controllers.Toolbar.txtFractionVertical": "Sku<PERSON>lowany fragment", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Funkcja Inverse Cosine", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Funkcja Inverse Cotangent", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Hiper<PERSON><PERSON>na <PERSON> Inverse Cotangent", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Funkcja Odwrotna Cosecans", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperboliczny arcus cosecans", "PE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON><PERSON><PERSON>na", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperboliczna odwrotna funkcja se<PERSON>na", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Funkcja Inverse Sine", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Funkcja odwrotnej sinusoidy hiperbolicznej", "PE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON><PERSON><PERSON><PERSON> arcus tangens", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "<PERSON><PERSON>ja hiperboliczna odwrotna", "PE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON> cosinus", "PE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Cot": "Funkcja ctg", "PE.Controllers.Toolbar.txtFunction_Coth": "Cotangens hiperboliczny", "PE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Csch": "Funkcja hipotonii hiperbola", "PE.Controllers.Toolbar.txtFunction_Custom_1": "Sinus theta", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "Wzór styczny", "PE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sech": "<PERSON>er<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtFunction_Sinh": "<PERSON><PERSON>ja hiperboliczna <PERSON>", "PE.Controllers.Toolbar.txtFunction_Tan": "Tangens", "PE.Controllers.Toolbar.txtFunction_Tanh": "Tangens hiperboliczny", "PE.Controllers.Toolbar.txtIntegral": "Całka", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Mechanizm różnicowy", "PE.Controllers.Toolbar.txtIntegral_dx": "Mechanizm różnicowy x", "PE.Controllers.Toolbar.txtIntegral_dy": "Mechanizm różnicowy y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Całka", "PE.Controllers.Toolbar.txtIntegralDouble": "Całka podwójna", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Całka podwójna", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Całka podwójna", "PE.Controllers.Toolbar.txtIntegralOriented": "Całka krzywej", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Całka krzywej", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral powierzchni", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral powierzchni", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral powierzchni", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Całka krzywej", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integralna objętość", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integralna objętość", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integralna objętość", "PE.Controllers.Toolbar.txtIntegralSubSup": "Całka", "PE.Controllers.Toolbar.txtIntegralTriple": "Potr<PERSON>jny integral", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Potr<PERSON>jny integral", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Potr<PERSON>jny integral", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Zak<PERSON><PERSON>j", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Zak<PERSON><PERSON>j", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Zak<PERSON><PERSON>j", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Zak<PERSON><PERSON>j", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Zak<PERSON><PERSON>j", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "Współprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Współprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Współprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Współprodukt", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Współprodukt", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unia", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Przecię<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Przecię<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Przecię<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Przecię<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Przecię<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Podsumowanie", "PE.Controllers.Toolbar.txtLargeOperator_Union": "Unia", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unia", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unia", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unia", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unia", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Przykładowy limit", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maksymalny przykład", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logarytm naturalny", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logarytm", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarytm", "PE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "PE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Pusta macierz", "PE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Pusta mac<PERSON>z", "PE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Pusta mac<PERSON>z", "PE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Pusta macierz", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Pusta macierz z nawiasami", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Pusta macierz z nawiasami", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Pusta macierz z nawiasami", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Pusta macierz z nawiasami", "PE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Pusta mac<PERSON>z", "PE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Pusta macierz", "PE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Pusta macierz", "PE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Pusta mac<PERSON>z", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Kropki bazowe", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Punkty środkowe", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Punkty po przekątnej", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "Rozrzedzona matryca", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "Rozrzedzona matryca", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Macierz jednostkowa", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Macierz jednostkowa", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Macierz jednostkowa", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Macierz jednostkowa", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Strzałka w lewo - w prawo powyżej", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Strzałka w lewo - w prawo powyżej", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Lewa strzałka poniżej", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Lewa strzałka powyżej", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Strzałka w prawo Poniżej", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Strzałka w prawo powyżej", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Dwukropek jest równy", "PE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Delta wyjścia", "PE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON> def<PERSON>", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta równa się", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Strzałka w lewo - w prawo powyżej", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Strzałka w lewo - w prawo powyżej", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Lewa strzałka poniżej", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Lewa strzałka powyżej", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Strzałka w prawo Poniżej", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Strzałka w prawo powyżej", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Równy równy", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Równy", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Zmierzone przez", "PE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Pierwiastek drugiego <PERSON>", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Pierwiastek sześcienny", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radykał ze stopniem", "PE.Controllers.Toolbar.txtRadicalSqrt": "Pierwiastek kwadratowy", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSubSup": "Dolne i górne indeksy", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Le<PERSON> indeks dolny", "PE.Controllers.Toolbar.txtScriptSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_about": "W przybliżeniu", "PE.Controllers.Toolbar.txtSymbol_additional": "Uzupełnienie", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "Prawie równe do", "PE.Controllers.Toolbar.txtSymbol_ast": "Operator <PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Bet", "PE.Controllers.Toolbar.txtSymbol_bullet": "Operator-znacznik", "PE.Controllers.Toolbar.txtSymbol_cap": "Przecię<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Pierwiastek sześcienny", "PE.Controllers.Toolbar.txtSymbol_cdots": "Poziome kropki w środku", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>lcjusza", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "W przybliżeniu równe", "PE.Controllers.Toolbar.txtSymbol_cup": "Unia", "PE.Controllers.Toolbar.txtSymbol_ddots": "Przekątny wielokropek w dół w prawo", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>nie", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Znak dzielenia", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Strzałka w dół", "PE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> zestaw", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "PE.Controllers.Toolbar.txtSymbol_equals": "Równy", "PE.Controllers.Toolbar.txtSymbol_equiv": "Identyczny do", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "<PERSON> jest", "PE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "Stopnie Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON> wszystkich", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "PE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub równy niż", "PE.Controllers.Toolbar.txtSymbol_gg": "Dużo wię<PERSON>zy niż", "PE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "PE.Controllers.Toolbar.txtSymbol_in": "Element", "PE.Controllers.Toolbar.txtSymbol_inc": "Przyrost", "PE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Lewa strzałka", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Lewa-Prawa strzałka", "PE.Controllers.Toolbar.txtSymbol_leq": "Mniejszy lub równy niż", "PE.Controllers.Toolbar.txtSymbol_less": "Mniejszy niż", "PE.Controllers.Toolbar.txtSymbol_ll": "Dużo mniejszy niż", "PE.Controllers.Toolbar.txtSymbol_minus": "Minus", "PE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Różny od", "PE.Controllers.Toolbar.txtSymbol_ni": "Zawiera jako c<PERSON>k", "PE.Controllers.Toolbar.txtSymbol_not": "Znak negacji", "PE.Controllers.Toolbar.txtSymbol_notexists": "Tam nie ma", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omikron", "PE.Controllers.Toolbar.txtSymbol_omega": "Omega", "PE.Controllers.Toolbar.txtSymbol_partial": "Częściowe różnice", "PE.Controllers.Toolbar.txtSymbol_percent": "Procentowo", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "Plus", "PE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "PE.Controllers.Toolbar.txtSymbol_propto": "Proporcjonalny do", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Pierwiastek poczwórny", "PE.Controllers.Toolbar.txtSymbol_qed": "Koniec dowodu", "PE.Controllers.Toolbar.txtSymbol_rddots": "Przekątny wielokropek w górę w prawo", "PE.Controllers.Toolbar.txtSymbol_rho": "Rho", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Strzałka w prawo", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Znak grupy funkcyjnej", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "W związku z tym", "PE.Controllers.Toolbar.txtSymbol_theta": "Theta", "PE.Controllers.Toolbar.txtSymbol_times": "Znak mnożenia", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Strzałka w górę", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon (opcja)", "PE.Controllers.Toolbar.txtSymbol_varphi": "Phi <PERSON>", "PE.Controllers.Toolbar.txtSymbol_varpi": "Pi <PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "Wariant Rho", "PE.Controllers.Toolbar.txtSymbol_varsigma": "Wariacja Sigma", "PE.Controllers.Toolbar.txtSymbol_vartheta": "Wariant Theta", "PE.Controllers.Toolbar.txtSymbol_vdots": "Pionowe wyskakiwanie", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Dopasuj do slajdu", "PE.Controllers.Viewport.textFitWidth": "Dopasuj do szerokości", "PE.Views.Animation.str0_5": "0,5 s (<PERSON><PERSON><PERSON>)", "PE.Views.Animation.str1": "1 s (Szybko)", "PE.Views.Animation.str2": "2s (<PERSON><PERSON><PERSON>)", "PE.Views.Animation.str20": "20 s (Ekstremalnie powoli)", "PE.Views.Animation.str3": "3 s (Wolno)", "PE.Views.Animation.str5": "5 s (<PERSON><PERSON><PERSON> p<PERSON>)", "PE.Views.Animation.strDelay": "Opóźnienie", "PE.Views.Animation.strDuration": "Czas trwania", "PE.Views.Animation.textMultiple": "Mnożnik", "PE.Views.Animation.textNone": "Brak", "PE.Views.Animation.textNoRepeat": "(brak)", "PE.Views.Animation.txtAddEffect": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.txtParameters": "Parametry", "PE.Views.Animation.txtPreview": "Podgląd", "PE.Views.Animation.txtSec": "S", "PE.Views.ChartSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "PE.Views.ChartSettings.textChartType": "Zmień typ wykresu", "PE.Views.ChartSettings.textEditData": "<PERSON><PERSON><PERSON><PERSON> dane", "PE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textKeepRatio": "Stałe proporcje", "PE.Views.ChartSettings.textSize": "Rozmiar", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON>", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textAlt": "Tekst alternatywny", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Opis", "PE.Views.ChartSettingsAdvanced.textAltTip": "Alternatywna prezentacja wizualnych informacji o obiektach, które będą czytane osobom z wadami wzroku lub zmysłu poznawczego, aby le<PERSON><PERSON>, jakie informacje znajdują się na obrazie, ksz<PERSON>łtach, wykresie lub tabeli.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textCenter": "Wyśrodkuj", "PE.Views.ChartSettingsAdvanced.textFrom": "Z", "PE.Views.ChartSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Poziomy", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Stałe proporcje", "PE.Views.ChartSettingsAdvanced.textPlacement": "Umieszczenie", "PE.Views.ChartSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>ja", "PE.Views.ChartSettingsAdvanced.textSize": "Rozmiar", "PE.Views.ChartSettingsAdvanced.textTitle": "Wykres - <PERSON><PERSON><PERSON><PERSON>wane us<PERSON>wi<PERSON>", "PE.Views.ChartSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Ustaw domyślny format dla {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Ustaw jako <PERSON>", "PE.Views.DateTimeDialog.textFormat": "Formaty", "PE.Views.DateTimeDialog.textLang": "Język", "PE.Views.DateTimeDialog.textUpdate": "Aktualizuj automatycznie", "PE.Views.DateTimeDialog.txtTitle": "Data i czas", "PE.Views.DocumentHolder.aboveText": "Nad", "PE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.addToLayoutText": "Dodaj do rozkładu", "PE.Views.DocumentHolder.advancedImageText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON> obrazu", "PE.Views.DocumentHolder.advancedParagraphText": "<PERSON><PERSON><PERSON><PERSON><PERSON>e ustawienia tekstu", "PE.Views.DocumentHolder.advancedShapeText": "Zaawansowane ustawienia kształtu", "PE.Views.DocumentHolder.advancedTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON> tabeli", "PE.Views.DocumentHolder.alignmentText": "Wyrównanie", "PE.Views.DocumentHolder.belowText": "Poniżej", "PE.Views.DocumentHolder.cellAlignText": "Wyrównanie pionowe komórki", "PE.Views.DocumentHolder.cellText": "Komórka", "PE.Views.DocumentHolder.centerText": "Środek", "PE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON>ń kolumnę", "PE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> wiersz", "PE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON> ta<PERSON>", "PE.Views.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.direct270Text": "Obróć tekst w górę", "PE.Views.DocumentHolder.direct90Text": "Obróć tekst w dół", "PE.Views.DocumentHolder.directHText": "Poziomy", "PE.Views.DocumentHolder.directionText": "Kierunek tekstu", "PE.Views.DocumentHolder.editChartText": "<PERSON><PERSON><PERSON><PERSON> dane", "PE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.hyperlinkText": "Hiperlink", "PE.Views.DocumentHolder.ignoreAllSpellText": "Ignoruj wszystko", "PE.Views.DocumentHolder.ignoreSpellText": "Ignoruj", "PE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> lewa", "PE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> p<PERSON>a", "PE.Views.DocumentHolder.insertColumnText": "Wstaw kolumnę", "PE.Views.DocumentHolder.insertRowAboveText": "Wiersz powyżej", "PE.Views.DocumentHolder.insertRowBelowText": "Wiersz poniżej", "PE.Views.DocumentHolder.insertRowText": "Wstaw wiersz", "PE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.langText": "<PERSON><PERSON>bierz język", "PE.Views.DocumentHolder.leftText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.loadSpellText": "Ładowanie wariantów...", "PE.Views.DocumentHolder.mergeCellsText": "Połącz komórki", "PE.Views.DocumentHolder.mniCustomTable": "Wstaw tabelę niestandardową", "PE.Views.DocumentHolder.moreText": "<PERSON><PERSON><PERSON><PERSON>j wariantów...", "PE.Views.DocumentHolder.noSpellVariantsText": "Brak wariantów", "PE.Views.DocumentHolder.originalSizeText": "Domyślny rozmiar", "PE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON> hiperlink", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "PE.Views.DocumentHolder.splitCellsText": "Podziel komórkę...", "PE.Views.DocumentHolder.splitCellTitleText": "Podziel komórkę", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textArrangeBack": "Wyślij do tła", "PE.Views.DocumentHolder.textArrangeBackward": "Przenieś do tyłu", "PE.Views.DocumentHolder.textArrangeForward": "Przenieś do przodu", "PE.Views.DocumentHolder.textArrangeFront": "Przejdź na pierwszy plan", "PE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCrop": "Przytnij", "PE.Views.DocumentHolder.textCropFill": "Wypełnij", "PE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textDistributeCols": "Rozłóż kolumny", "PE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipH": "Odwróć w poziomie", "PE.Views.DocumentHolder.textFlipV": "Przerzuć w pionie", "PE.Views.DocumentHolder.textFromFile": "Z pliku", "PE.Views.DocumentHolder.textFromStorage": "Z magazynu", "PE.Views.DocumentHolder.textFromUrl": "Z adresu URL", "PE.Views.DocumentHolder.textNextPage": "Następny slajd", "PE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textPrevPage": "Poprzedni slajd", "PE.Views.DocumentHolder.textReplace": "Zamień obraz", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "PE.Views.DocumentHolder.textRotate90": "Obróć w prawo o 90°", "PE.Views.DocumentHolder.textShapeAlignBottom": "Wyrównaj do dołu", "PE.Views.DocumentHolder.textShapeAlignCenter": "Wyrównaj do środka", "PE.Views.DocumentHolder.textShapeAlignLeft": "Wyrównaj do lewej", "PE.Views.DocumentHolder.textShapeAlignMiddle": "Wyrównaj do środka", "PE.Views.DocumentHolder.textShapeAlignRight": "Wyrównaj do prawej", "PE.Views.DocumentHolder.textShapeAlignTop": "Wyrównaj do góry", "PE.Views.DocumentHolder.textSlideSettings": "Ustawienia slajdu", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipIsLocked": "Ten element jest obecnie edytowany przez innego użytkownika.", "PE.Views.DocumentHolder.toDictionaryText": "Dodaj do słownika", "PE.Views.DocumentHolder.txtAddBottom": "Dodaj dolną krawędź", "PE.Views.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON> pasek ułamka", "PE.Views.DocumentHolder.txtAddHor": "Dodaj poziomą linie", "PE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON><PERSON> lewy dolny wiersz", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> k<PERSON>", "PE.Views.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> lewy górny w<PERSON>z", "PE.Views.DocumentHolder.txtAddRight": "Dodaj p<PERSON>ą krawędź", "PE.Views.DocumentHolder.txtAddTop": "Dodaj górną krawędź", "PE.Views.DocumentHolder.txtAddVer": "Dodaj pionowy wiersz", "PE.Views.DocumentHolder.txtAlign": "Wyrównaj", "PE.Views.DocumentHolder.txtAlignToChar": "Wyrównaj do znaku", "PE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtBackground": "Tło", "PE.Views.DocumentHolder.txtBorderProps": "Ustawienia obra<PERSON>ia", "PE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtChangeLayout": "Zmień układ", "PE.Views.DocumentHolder.txtChangeTheme": "<PERSON><PERSON><PERSON>ć motyw", "PE.Views.DocumentHolder.txtColumnAlign": "Wyrównanie kolumny", "PE.Views.DocumentHolder.txtDecreaseArg": "Zmniejsz rozmiar argumentu", "PE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> argument", "PE.Views.DocumentHolder.txtDeleteBreak": "Usuń ręczną przerwę", "PE.Views.DocumentHolder.txtDeleteChars": "Usuń zamknięte znaki", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Usuń zamknięte znaki i separatory", "PE.Views.DocumentHolder.txtDeleteEq": "Usuń równanie", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Usuń znak", "PE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> radykał", "PE.Views.DocumentHolder.txtDeleteSlide": "Us<PERSON>ń slajd", "PE.Views.DocumentHolder.txtDistribHor": "Rozdziel poziomo", "PE.Views.DocumentHolder.txtDistribVert": "Rozdziel pionowo", "PE.Views.DocumentHolder.txtDuplicateSlide": "Duplikat slajdu", "PE.Views.DocumentHolder.txtFractionLinear": "Zmień na ułamek liniowy", "PE.Views.DocumentHolder.txtFractionSkewed": "Zmienić na ukośny prosty ułamek", "PE.Views.DocumentHolder.txtFractionStacked": "Zmień na ułożone ułamki", "PE.Views.DocumentHolder.txtGroup": "Grupa", "PE.Views.DocumentHolder.txtGroupCharOver": "Znak nad tekstem", "PE.Views.DocumentHolder.txtGroupCharUnder": "Znak pod tekstem", "PE.Views.DocumentHolder.txtHideBottom": "Ukryj dolną krawędź", "PE.Views.DocumentHolder.txtHideBottomLimit": "Ukryj dolny limit", "PE.Views.DocumentHolder.txtHideCloseBracket": "Ukryj uchwyt zamykający", "PE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> poziomy w<PERSON>z", "PE.Views.DocumentHolder.txtHideLB": "<PERSON><PERSON><PERSON><PERSON> lewy dolny wiersz", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ęd<PERSON>", "PE.Views.DocumentHolder.txtHideLT": "<PERSON><PERSON><PERSON><PERSON> lewy górny wiersz", "PE.Views.DocumentHolder.txtHideOpenBracket": "Ukryj uchwyt otwierający", "PE.Views.DocumentHolder.txtHidePlaceholder": "Ukryj <PERSON> zastępczy", "PE.Views.DocumentHolder.txtHideRight": "Uk<PERSON>j prawą krawędź", "PE.Views.DocumentHolder.txtHideTop": "Ukryj górną krawędź", "PE.Views.DocumentHolder.txtHideTopLimit": "Ukryj górny limit", "PE.Views.DocumentHolder.txtHideVer": "Ukryj pionowy wiersz", "PE.Views.DocumentHolder.txtIncreaseArg": "Zwiększ rozmiar argumentu", "PE.Views.DocumentHolder.txtInsertArgAfter": "Wstaw argument po", "PE.Views.DocumentHolder.txtInsertArgBefore": "Wstaw argument przed", "PE.Views.DocumentHolder.txtInsertBreak": "Wstaw ręczną przerwę", "PE.Views.DocumentHolder.txtInsertEqAfter": "Wstaw równanie po", "PE.Views.DocumentHolder.txtInsertEqBefore": "Wstaw równanie przed", "PE.Views.DocumentHolder.txtKeepTextOnly": "Zachowaj tylko tekst", "PE.Views.DocumentHolder.txtLimitChange": "Zmień lokalizację limitu", "PE.Views.DocumentHolder.txtLimitOver": "Ograniczenie tekstu", "PE.Views.DocumentHolder.txtLimitUnder": "Limit w tekście", "PE.Views.DocumentHolder.txtMatchBrackets": "Dopasuj nawiasy do wysokości argumentu", "PE.Views.DocumentHolder.txtMatrixAlign": "Wyrównanie macierzy", "PE.Views.DocumentHolder.txtNewSlide": "<PERSON><PERSON> slajd", "PE.Views.DocumentHolder.txtOverbar": "Pasek nad tekstem", "PE.Views.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Zachowaj formatowanie źródłowe", "PE.Views.DocumentHolder.txtPressLink": "Naciśnij {0} i kliknij link", "PE.Views.DocumentHolder.txtPreview": "Rozpocznij pokaz slajdów", "PE.Views.DocumentHolder.txtPrintSelection": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON> belkę ułamka", "PE.Views.DocumentHolder.txtRemLimit": "Usuń limit", "PE.Views.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "PE.Views.DocumentHolder.txtRemoveBar": "Us<PERSON>ń pasek", "PE.Views.DocumentHolder.txtRemScripts": "Usuń indeksy", "PE.Views.DocumentHolder.txtRemSubscript": "Usuń indeks dolny", "PE.Views.DocumentHolder.txtRemSuperscript": "Usuń górny indeks", "PE.Views.DocumentHolder.txtScriptsAfter": "Indeksy po tekście", "PE.Views.DocumentHolder.txtScriptsBefore": "Indeksy przed tekstem", "PE.Views.DocumentHolder.txtSelectAll": "Zaznacz wszystko", "PE.Views.DocumentHolder.txtShowBottomLimit": "Pokaż dolny limit", "PE.Views.DocumentHolder.txtShowCloseBracket": "Pokaż klamrę zamyk<PERSON>ącą", "PE.Views.DocumentHolder.txtShowDegree": "Pokaż stopień", "PE.Views.DocumentHolder.txtShowOpenBracket": "Pokaż klamrę otwierającą", "PE.Views.DocumentHolder.txtShowPlaceholder": "Pokaż symbol zastępczy", "PE.Views.DocumentHolder.txtShowTopLimit": "Pokaż górny limit", "PE.Views.DocumentHolder.txtSlide": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtSlideHide": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>jd", "PE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nawi<PERSON>y", "PE.Views.DocumentHolder.txtTop": "Góra", "PE.Views.DocumentHolder.txtUnderbar": "Pasek pod tekstem", "PE.Views.DocumentHolder.txtUngroup": "Rozgrupuj", "PE.Views.DocumentHolder.txtWarnUrl": "Kliknięcie tego linku może być szkodliwe dla urządzenia i danych.<br><PERSON><PERSON> na pewno chcesz kontynuować?", "PE.Views.DocumentHolder.vertAlignText": "Wyrównaj pionowo", "PE.Views.DocumentPreview.goToSlideText": "Idź do slajdu", "PE.Views.DocumentPreview.slideIndexText": "Slajd {0} z {1}", "PE.Views.DocumentPreview.txtClose": "Zamknij pokaz slajdów", "PE.Views.DocumentPreview.txtEndSlideshow": "Zakończyć pokaz slajdów", "PE.Views.DocumentPreview.txtExitFullScreen": "Opuść pełny ekran", "PE.Views.DocumentPreview.txtFinalMessage": "Koniec podglądu slajdów. Kliknij aby wyjść.", "PE.Views.DocumentPreview.txtFullScreen": "Pełny ekran", "PE.Views.DocumentPreview.txtNext": "Następny slajd", "PE.Views.DocumentPreview.txtPageNumInvalid": "Błędny numer slajdu", "PE.Views.DocumentPreview.txtPause": "Zatrzymaj <PERSON>", "PE.Views.DocumentPreview.txtPlay": "Rozpocznij <PERSON>", "PE.Views.DocumentPreview.txtPrev": "Poprzedni slajd", "PE.Views.DocumentPreview.txtReset": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnAboutCaption": "O programie", "PE.Views.FileMenu.btnBackCaption": "Przejdź do Dokumentów", "PE.Views.FileMenu.btnCloseMenuCaption": "Zamknij menu", "PE.Views.FileMenu.btnCreateNewCaption": "Utwórz nowy", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> jako", "PE.Views.FileMenu.btnExitCaption": "Zamknij", "PE.Views.FileMenu.btnFileOpenCaption": "Otwórz", "PE.Views.FileMenu.btnHelpCaption": "Pomoc", "PE.Views.FileMenu.btnHistoryCaption": "<PERSON> wersji", "PE.Views.FileMenu.btnInfoCaption": "Informacje o prezentacji", "PE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnProtectCaption": "Zabezpiecz", "PE.Views.FileMenu.btnRecentFilesCaption": "Otwórz ostatnie", "PE.Views.FileMenu.btnRenameCaption": "Zmień nazwę", "PE.Views.FileMenu.btnReturnCaption": "Powrót do prezentacji", "PE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveAsCaption": "<PERSON>ap<PERSON>z jako", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Zapisz kopię jako", "PE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Utwórz nowy", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Dodaj autora", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON>j te<PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikacja", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Zmień prawa dostępu", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentarz", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Utworzono", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Ostatnie modyfikowane przez", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Ostatnio zmodyfikowany", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Właściciel", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokalizacja", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON>, które mają prawa", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Przesłano", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Zmień prawa dostępu", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON>, które mają prawa", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Ostrzeżenie", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>a p<PERSON><PERSON><PERSON> hasła", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Z s<PERSON>gnaturą", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Tryb współtworzenia", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Podpowiedź czcionki", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignoruj słowa pisane WIELKIMI LITERAMI", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignoruj słowa z liczbami", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Ustawienia Makr", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Pokaż przycisk opcji wklejania po wklejeniu zawartości", "PE.Views.FileMenuPanels.Settings.strStrict": "Ścisły", "PE.Views.FileMenuPanels.Settings.strTheme": "Motyw interfejsu", "PE.Views.FileMenuPanels.Settings.strUnit": "Jednostka miary", "PE.Views.FileMenuPanels.Settings.strZoom": "Domyślna wartość powiększenia", "PE.Views.FileMenuPanels.Settings.text10Minutes": "Każde 10 minut", "PE.Views.FileMenuPanels.Settings.text30Minutes": "Każde 30 minut", "PE.Views.FileMenuPanels.Settings.text5Minutes": "Każde 5 minut", "PE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "Prowadnice wyrównania", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Automatyczne odzyskiwanie", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Automatyczny zapis", "PE.Views.FileMenuPanels.Settings.textDisabled": "Wyłączony", "PE.Views.FileMenuPanels.Settings.textForceSave": "Zapisz na serwer", "PE.Views.FileMenuPanels.Settings.textMinute": "Każda minuta", "PE.Views.FileMenuPanels.Settings.txtAll": "Pokaż wszystkie", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opcje Autokorekty...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Domyślny tryb pamięci podręcznej", "PE.Views.FileMenuPanels.Settings.txtCm": "Centymetr", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Współpraca", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Dopasuj do slajdu", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Dopasuj do szerokości", "PE.Views.FileMenuPanels.Settings.txtInch": "<PERSON>", "PE.Views.FileMenuPanels.Settings.txtLast": "Pokaż ostatni", "PE.Views.FileMenuPanels.Settings.txtMac": "jako OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtProofing": "Sprawdzanie", "PE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Włącz wszystkie", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Włącz wszystkie makra bez powiadomienia", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Wyłącz Wszystkie", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Wyłącz wszystkie makra bez powiadomienia", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Użyj przycisku \"Zapisz\", aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, które ty i inni dokonują", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Pokaż powiadomienie", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Wyłącz wszystkie makra z powiadomieniem", "PE.Views.FileMenuPanels.Settings.txtWin": "jako <PERSON>", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON><PERSON> rob<PERSON>", "PE.Views.HeaderFooterDialog.applyAllText": "Zastosuj do wszystkich", "PE.Views.HeaderFooterDialog.applyText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Ostrzeżenie", "PE.Views.HeaderFooterDialog.textDateTime": "Data i czas", "PE.Views.HeaderFooterDialog.textFixed": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textFormat": "Formaty", "PE.Views.HeaderFooterDialog.textLang": "Język", "PE.Views.HeaderFooterDialog.textPreview": "Podgląd", "PE.Views.HeaderFooterDialog.textSlideNum": "<PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textTitle": "Ustawi<PERSON> stopki", "PE.Views.HeaderFooterDialog.textUpdate": "Aktualizuj automatycznie", "PE.Views.HyperlinkSettingsDialog.strDisplay": "Po<PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Link do", "PE.Views.HyperlinkSettingsDialog.textDefault": "Wybrany fragment tekstu", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON>z tutaj podpis", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Wprowadź tutaj link", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON>j tutaj <PERSON>", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Link zew<PERSON>ętrzny", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Slajd w tej prezentacji", "PE.Views.HyperlinkSettingsDialog.textSlides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.textTipText": "Tekst wskazówki na ekranie", "PE.Views.HyperlinkSettingsDialog.textTitle": "Ustawi<PERSON>link<PERSON>", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "To pole jest wymagane", "PE.Views.HyperlinkSettingsDialog.txtFirst": "<PERSON><PERSON><PERSON> s<PERSON>jd", "PE.Views.HyperlinkSettingsDialog.txtLast": "Ostatni slajd", "PE.Views.HyperlinkSettingsDialog.txtNext": "Następny slajd", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "To pole powinno by<PERSON> adresem URL w formacie \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Poprzedni slajd", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "To pole jest ogranicz<PERSON> do 2083 znaków", "PE.Views.HyperlinkSettingsDialog.txtSlide": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "PE.Views.ImageSettings.textCrop": "Przytnij", "PE.Views.ImageSettings.textCropFill": "Wypełnij", "PE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropToShape": "Przytnij do kształtu", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "Ed<PERSON><PERSON>j obiekt", "PE.Views.ImageSettings.textFitSlide": "Dopasuj do slajdu", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "Z pliku", "PE.Views.ImageSettings.textFromStorage": "Z magazynu", "PE.Views.ImageSettings.textFromUrl": "Z adresu URL", "PE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textHint270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "PE.Views.ImageSettings.textHint90": "Obróć w prawo o 90°", "PE.Views.ImageSettings.textHintFlipH": "Odwróć w poziomie", "PE.Views.ImageSettings.textHintFlipV": "Przerzuć w pionie", "PE.Views.ImageSettings.textInsert": "Zamień obraz", "PE.Views.ImageSettings.textOriginalSize": "Domyślny rozmiar", "PE.Views.ImageSettings.textRecentlyUsed": "Ostatnio używane", "PE.Views.ImageSettings.textRotate90": "O<PERSON><PERSON>óć o 90°", "PE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textSize": "Rozmiar", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Tekst alternatywny", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Opis", "PE.Views.ImageSettingsAdvanced.textAltTip": "Alternatywna prezentacja wizualnych informacji o obiektach, które będą czytane osobom z wadami wzroku lub zmysłu poznawczego, aby le<PERSON><PERSON>, jakie informacje znajdują się na obrazie, ksz<PERSON>łtach, wykresie lub tabeli.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Wyśrodkuj", "PE.Views.ImageSettingsAdvanced.textFlipped": "Odwrócony ", "PE.Views.ImageSettingsAdvanced.textFrom": "Od", "PE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Poziomy", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Poziomo ", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Stałe proporcje", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Domyślny rozmiar", "PE.Views.ImageSettingsAdvanced.textPlacement": "Umieszczenie", "PE.Views.ImageSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>ja", "PE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textSize": "Rozmiar", "PE.Views.ImageSettingsAdvanced.textTitle": "Obraz - <PERSON><PERSON><PERSON><PERSON>wane us<PERSON>wi<PERSON>", "PE.Views.ImageSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON> ", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "O programie", "PE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSearch": "Wyszukiwanie", "PE.Views.LeftMenu.tipSlides": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipSupport": "Opinie i wsparcie", "PE.Views.LeftMenu.tipTitles": "Tytuły", "PE.Views.LeftMenu.txtDeveloper": "TRYB DEWELOPERA", "PE.Views.LeftMenu.txtLimit": "Ograniczony dostęp", "PE.Views.LeftMenu.txtTrial": "PRÓBNY TRYB", "PE.Views.LeftMenu.txtTrialDev": "Próbny tryb programisty", "PE.Views.ParagraphSettings.strLineHeight": "Rozstaw wierszy", "PE.Views.ParagraphSettings.strParagraphSpacing": "Odstępy akapitu", "PE.Views.ParagraphSettings.strSpacingAfter": "po", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "PE.Views.ParagraphSettings.textAt": "w", "PE.Views.ParagraphSettings.textAtLeast": "Co najmniej", "PE.Views.ParagraphSettings.textAuto": "Mnożnik", "PE.Views.ParagraphSettings.textExact": "Dokładnie", "PE.Views.ParagraphSettings.txtAutoText": "Automatyczny", "PE.Views.ParagraphSettingsAdvanced.noTabs": "W tym polu zostaną wyświetlone określone karty", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "Wszystkie duże litery", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Podwójne przekreślenie", "PE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON>ę<PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Rozstaw wierszy", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Po", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Specialny", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Czcionka", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Wcięcia i miejsca docelowe", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Małe litery", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Rozstaw", "PE.Views.ParagraphSettingsAdvanced.strStrike": "Przekreślony", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strTabs": "tabulacja", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Wyrównanie", "PE.Views.ParagraphSettingsAdvanced.textAuto": "Mnożnik", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Rozstaw znaków", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Domyślna zakładka", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Efekty", "PE.Views.ParagraphSettingsAdvanced.textExact": "Dokładnie", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON> w<PERSON>", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Wysunięcie", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Wyjustowany", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(brak)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Usuń", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Us<PERSON>ń wszystko", "PE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Wyśrodkuj", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON>ja", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Akapit - Ustawienia zaawansowane", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automatyczny", "PE.Views.RightMenu.txtChartSettings": "Ustawienia wykresu", "PE.Views.RightMenu.txtImageSettings": "Ustawi<PERSON> obrazu", "PE.Views.RightMenu.txtParagraphSettings": "Ustawienia tekstu", "PE.Views.RightMenu.txtShapeSettings": "Ustawienia kształtu", "PE.Views.RightMenu.txtSignatureSettings": "Ustawienia podpisów", "PE.Views.RightMenu.txtSlideSettings": "Ustawienia slajdu", "PE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON><PERSON> tabeli", "PE.Views.RightMenu.txtTextArtSettings": "Ustawienia tekstu", "PE.Views.ShapeSettings.strBackground": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strChange": "Zmień kształt", "PE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strFill": "Wypełnij", "PE.Views.ShapeSettings.strForeground": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strPattern": "Wzór", "PE.Views.ShapeSettings.strShadow": "Pokaż cień ", "PE.Views.ShapeSettings.strSize": "Rozmiar", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 0 do 1584 pt.", "PE.Views.ShapeSettings.textColor": "<PERSON><PERSON> wypełnienia", "PE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textEmptyPattern": "Brak wzorca", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "Z pliku", "PE.Views.ShapeSettings.textFromStorage": "Z magazynu", "PE.Views.ShapeSettings.textFromUrl": "Z adresu URL", "PE.Views.ShapeSettings.textGradient": "Gradient", "PE.Views.ShapeSettings.textGradientFill": "Wypełnienie gradientem", "PE.Views.ShapeSettings.textHint270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "PE.Views.ShapeSettings.textHint90": "Obróć w prawo o 90°", "PE.Views.ShapeSettings.textHintFlipH": "Odwróć w poziomie", "PE.Views.ShapeSettings.textHintFlipV": "Przerzuć w pionie", "PE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON>z lub teks<PERSON>", "PE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textNoFill": "Brak wypełnienia", "PE.Views.ShapeSettings.textPatternFill": "Wzór", "PE.Views.ShapeSettings.textPosition": "<PERSON><PERSON><PERSON>ja", "PE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textRecentlyUsed": "Ostatnio używane", "PE.Views.ShapeSettings.textRotate90": "O<PERSON><PERSON>óć o 90°", "PE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>rz zd<PERSON>", "PE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textStretch": "Roz<PERSON>ą<PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "Z tekstury", "PE.Views.ShapeSettings.textTile": "Płytka", "PE.Views.ShapeSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> pakowy", "PE.Views.ShapeSettings.txtCanvas": "Płótno", "PE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtGrain": "Ziarno", "PE.Views.ShapeSettings.txtGranite": "Granit", "PE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> papier", "PE.Views.ShapeSettings.txtKnit": "Szydełkowanie", "PE.Views.ShapeSettings.txtLeather": "Skórzany", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "Drew<PERSON>", "PE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.strMargins": "Wypełnienie tekstem", "PE.Views.ShapeSettingsAdvanced.textAlt": "Tekst alternatywny", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Opis", "PE.Views.ShapeSettingsAdvanced.textAltTip": "Alternatywna prezentacja wizualnych informacji o obiektach, które będą czytane osobom z wadami wzroku lub zmysłu poznawczego, aby le<PERSON><PERSON>, jakie informacje znajdują się na obrazie, ksz<PERSON>łtach, wykresie lub tabeli.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "Strz<PERSON>ł<PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Autodopasowanie", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "Początkowy rozmiar", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "Styl p<PERSON>z<PERSON>kowy", "PE.Views.ShapeSettingsAdvanced.textBevel": "U<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textCapType": "Typ zakończenia", "PE.Views.ShapeSettingsAdvanced.textCenter": "Wyśrodkuj", "PE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON><PERSON> kolumn", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Rozmiar k<PERSON>ńcowy", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Styl końcowy", "PE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Odwrócony ", "PE.Views.ShapeSettingsAdvanced.textFrom": "Od", "PE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Poziomy", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Poziomo ", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Dołącz typ", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Stałe proporcje", "PE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON> w<PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "prosty", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Umieszczenie", "PE.Views.ShapeSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>ja", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Dopasuj rozmiar kształtu do tekstu", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRound": "Zaokrąglij", "PE.Views.ShapeSettingsAdvanced.textSize": "Rozmiar", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Przerwa między kolumnami", "PE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Pole tekstowe", "PE.Views.ShapeSettingsAdvanced.textTitle": "Kształt - Zaawansowane ustawienia", "PE.Views.ShapeSettingsAdvanced.textTop": "Góra", "PE.Views.ShapeSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON> ", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Wagi i strzałki", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Ostrzeżenie", "PE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strDetails": "Szczegóły sygnatury", "PE.Views.SignatureSettings.strInvalid": "Nieprawidłowe podpisy", "PE.Views.SignatureSettings.strSign": "Podpisz", "PE.Views.SignatureSettings.strSignature": "Sygnatura", "PE.Views.SignatureSettings.strValid": "Ważne podpisy", "PE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON><PERSON><PERSON> mimo to", "PE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON> ch<PERSON> us<PERSON> ten podpis? <br> <PERSON><PERSON> można tego co<PERSON>.", "PE.Views.SlideSettings.strBackground": "<PERSON><PERSON>", "PE.Views.SlideSettings.strColor": "<PERSON><PERSON>", "PE.Views.SlideSettings.strFill": "Tło", "PE.Views.SlideSettings.strForeground": "<PERSON><PERSON>", "PE.Views.SlideSettings.strPattern": "Wzór", "PE.Views.SlideSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "<PERSON><PERSON> wypełnienia", "PE.Views.SlideSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textEmptyPattern": "Brak wzorca", "PE.Views.SlideSettings.textFromFile": "Z pliku", "PE.Views.SlideSettings.textFromStorage": "Z magazynu", "PE.Views.SlideSettings.textFromUrl": "Z adresu URL", "PE.Views.SlideSettings.textGradient": "Gradient", "PE.Views.SlideSettings.textGradientFill": "Wypełnienie gradientem", "PE.Views.SlideSettings.textImageTexture": "<PERSON><PERSON>z lub teks<PERSON>", "PE.Views.SlideSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textNoFill": "Brak wypełnienia", "PE.Views.SlideSettings.textPatternFill": "Wzór", "PE.Views.SlideSettings.textPosition": "Położenie", "PE.Views.SlideSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textReset": "Zresetuj zmiany", "PE.Views.SlideSettings.textSelectImage": "<PERSON><PERSON><PERSON>rz zd<PERSON>", "PE.Views.SlideSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textStretch": "Roz<PERSON>ą<PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "Z tekstury", "PE.Views.SlideSettings.textTile": "Płytka", "PE.Views.SlideSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> pakowy", "PE.Views.SlideSettings.txtCanvas": "Płótno", "PE.Views.SlideSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtGrain": "Ziarno", "PE.Views.SlideSettings.txtGranite": "Granit", "PE.Views.SlideSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> papier", "PE.Views.SlideSettings.txtKnit": "Szydełkowanie", "PE.Views.SlideSettings.txtLeather": "Skórzany", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "Drew<PERSON>", "PE.Views.SlideshowSettings.textLoop": "Zapętl do momentu, aż zostanie naciśnięty klawisz \"Esc\"", "PE.Views.SlideshowSettings.textTitle": "<PERSON><PERSON>ż us<PERSON>wi<PERSON>", "PE.Views.SlideSizeSettings.strLandscape": "Pozioma", "PE.Views.SlideSizeSettings.strPortrait": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideOrientation": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textSlideSize": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.textTitle": "Ustawienia rozmiaru s<PERSON>du", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Szkiełka 35 mm", "PE.Views.SlideSizeSettings.txtA3": "A3 Papier (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "A4 Papier (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "B4 (ICO) Papier (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "B5 (ICO) Papier (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Własny", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON>r k<PERSON>ęgi głównej (11 x 17 cali)", "PE.Views.SlideSizeSettings.txtLetter": "<PERSON><PERSON><PERSON> Letter (8.5x11 cali)", "PE.Views.SlideSizeSettings.txtOverhead": "Folia", "PE.Views.SlideSizeSettings.txtStandard": "Standard (4: 3)", "PE.Views.Statusbar.goToPageText": "Idź do slajdu", "PE.Views.Statusbar.pageIndexText": "Slajd {0} z {1}", "PE.Views.Statusbar.textShowBegin": "Pokaż od początku", "PE.Views.Statusbar.textShowCurrent": "Pokaż z aktualnego slajdu", "PE.Views.Statusbar.textShowPresenterView": "Pokaz slajdów w trybie prezentera", "PE.Views.Statusbar.tipAccessRights": "Zarządzaj prawami dostępu do dokumentu", "PE.Views.Statusbar.tipFitPage": "Dopasuj do slajdu", "PE.Views.Statusbar.tipFitWidth": "Dopasuj do szerokości", "PE.Views.Statusbar.tipPreview": "Rozpocznij pokaz slajdów", "PE.Views.Statusbar.tipSetLang": "Ustaw język tekstu", "PE.Views.Statusbar.tipZoomFactor": "Powiększenie", "PE.Views.Statusbar.tipZoomIn": "Powię<PERSON><PERSON>", "PE.Views.Statusbar.tipZoomOut": "Po<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Statusbar.txtPageNumInvalid": "Błędny numer slajdu", "PE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>ń kolumnę", "PE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> wiersz", "PE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON> ta<PERSON>", "PE.Views.TableSettings.insertColumnLeftText": "Wstaw kolumnę z lewej", "PE.Views.TableSettings.insertColumnRightText": "Wstaw kolumnę z prawej", "PE.Views.TableSettings.insertRowAboveText": "Wstaw wiersz powyżej", "PE.Views.TableSettings.insertRowBelowText": "Wstaw wiersz poniżej", "PE.Views.TableSettings.mergeCellsText": "Połącz komórki", "PE.Views.TableSettings.selectCellText": "Wybierz komórkę", "PE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> kol<PERSON>", "PE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>z", "PE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.splitCellsText": "Podziel komórkę...", "PE.Views.TableSettings.splitCellTitleText": "Podziel komórkę", "PE.Views.TableSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "PE.Views.TableSettings.textBackColor": "<PERSON><PERSON>", "PE.Views.TableSettings.textBanded": "Na przemian", "PE.Views.TableSettings.textBorderColor": "<PERSON><PERSON>", "PE.Views.TableSettings.textBorders": "Style obramowań", "PE.Views.TableSettings.textCellSize": "Roz<PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textDistributeCols": "Rozłóż kolumny", "PE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textEdit": "Wiersze i Kolumny", "PE.Views.TableSettings.textEmptyTemplate": "Brak szablonów", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "Nagłówek", "PE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textLast": "Ostatni", "PE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON>, które chcesz zmienić stosując styl wybrany powyżej", "PE.Views.TableSettings.textTemplate": "Wybierz z szablonu", "PE.Views.TableSettings.textTotal": "Razem", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Ustaw krawędź zewnętrzną i wszystkie wewnętrzne linie", "PE.Views.TableSettings.tipBottom": "Ustaw tylko obramowanie dolnej krawędzi", "PE.Views.TableSettings.tipInner": "Us<PERSON>wić tylko linie wewnętrzne", "PE.Views.TableSettings.tipInnerHor": "<PERSON><PERSON><PERSON><PERSON> tylko poziome linie wewnętrzne", "PE.Views.TableSettings.tipInnerVert": "Ustaw tylko wewnętrzne pionowe linie", "PE.Views.TableSettings.tipLeft": "Ustaw tylko obramowanie lewej krawędzi", "PE.Views.TableSettings.tipNone": "Wyłącz krawędzie", "PE.Views.TableSettings.tipOuter": "Ustaw tylko obramowanie zewnętrzne", "PE.Views.TableSettings.tipRight": "Ustaw tylko obramowanie prawej krawędzi", "PE.Views.TableSettings.tipTop": "Ustaw tylko obramowanie górnej krawędzi", "PE.Views.TableSettings.txtNoBorders": "Bez k<PERSON>ędzi", "PE.Views.TableSettings.txtTable_Accent": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_MediumStyle": "Średni styl", "PE.Views.TableSettings.txtTable_TableGrid": "Tabela - siatka", "PE.Views.TableSettingsAdvanced.textAlt": "Tekst alternatywny", "PE.Views.TableSettingsAdvanced.textAltDescription": "Opis", "PE.Views.TableSettingsAdvanced.textAltTip": "Alternatywna prezentacja wizualnych informacji o obiektach, które będą czytane osobom z wadami wzroku lub zmysłu poznawczego, aby le<PERSON><PERSON>, jakie informacje znajdują się na obrazie, ksz<PERSON>łtach, wykresie lub tabeli.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textCenter": "Wyśrodkuj", "PE.Views.TableSettingsAdvanced.textCheckMargins": "Użyj domyślnych marginesów", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Domyślne marginesy", "PE.Views.TableSettingsAdvanced.textFrom": "Z", "PE.Views.TableSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textHorizontal": "Poziomy", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Stałe proporcje", "PE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textMargins": "Marginesy komórki", "PE.Views.TableSettingsAdvanced.textPlacement": "Umieszczenie", "PE.Views.TableSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>ja", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "Rozmiar", "PE.Views.TableSettingsAdvanced.textTitle": "Tabel<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON>", "PE.Views.TableSettingsAdvanced.textTop": "Góra", "PE.Views.TableSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Mar<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strBackground": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strFill": "Wypełnij", "PE.Views.TextArtSettings.strForeground": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strPattern": "Wzór", "PE.Views.TextArtSettings.strSize": "Rozmiar", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 0 do 1584 pt.", "PE.Views.TextArtSettings.textColor": "<PERSON><PERSON> wypełnienia", "PE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textEmptyPattern": "Brak wzorca", "PE.Views.TextArtSettings.textFromFile": "Z pliku", "PE.Views.TextArtSettings.textFromUrl": "Z adresu URL", "PE.Views.TextArtSettings.textGradient": "Gradient", "PE.Views.TextArtSettings.textGradientFill": "Wypełnienie gradientem", "PE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON>z lub teks<PERSON>", "PE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textNoFill": "Brak wypełnienia", "PE.Views.TextArtSettings.textPatternFill": "Wzór", "PE.Views.TextArtSettings.textPosition": "Położenie", "PE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textStretch": "Roz<PERSON>ą<PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "Szablon", "PE.Views.TextArtSettings.textTexture": "Z tekstury", "PE.Views.TextArtSettings.textTile": "Płytka", "PE.Views.TextArtSettings.textTransform": "Przekształcenie", "PE.Views.TextArtSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "PE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> pakowy", "PE.Views.TextArtSettings.txtCanvas": "Płótno", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtGrain": "Ziarno", "PE.Views.TextArtSettings.txtGranite": "Granit", "PE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> papier", "PE.Views.TextArtSettings.txtKnit": "Szydełkowanie", "PE.Views.TextArtSettings.txtLeather": "Skórzany", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.txtWood": "Drew<PERSON>", "PE.Views.Toolbar.capAddSlide": "<PERSON><PERSON><PERSON> s<PERSON>", "PE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnComment": "Komentarz", "PE.Views.Toolbar.capBtnDateTime": "Data i czas", "PE.Views.Toolbar.capBtnInsHeader": "Stopka", "PE.Views.Toolbar.capBtnInsSymbol": "Symbole", "PE.Views.Toolbar.capBtnSlideNum": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertAudio": "Dźwięk", "PE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertEquation": "Równanie", "PE.Views.Toolbar.capInsertHyperlink": "Hiperlink", "PE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertShape": "Kształt", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Pole tekstowe", "PE.Views.Toolbar.capInsertVideo": "Wideo", "PE.Views.Toolbar.capTabFile": "Plik", "PE.Views.Toolbar.capTabHome": "Narzędzia główne", "PE.Views.Toolbar.capTabInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON>zy Z Dużej Litery", "PE.Views.Toolbar.mniCustomTable": "Wstaw tabelę niestandardową", "PE.Views.Toolbar.mniImageFromFile": "Obraz z pliku", "PE.Views.Toolbar.mniImageFromStorage": "Obraz z magazynu", "PE.Views.Toolbar.mniImageFromUrl": "Obraz z URL", "PE.Views.Toolbar.mniInsertSSE": "Wstaw arkusz kalkulacyjny", "PE.Views.Toolbar.mniLowerCase": "małe litery", "PE.Views.Toolbar.mniSentenceCase": "Jak w zdaniu.", "PE.Views.Toolbar.mniSlideAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.mniSlideStandard": "Standard (4: 3)", "PE.Views.Toolbar.mniSlideWide": "<PERSON><PERSON><PERSON> (16:9)", "PE.Views.Toolbar.mniUpperCase": "WIELKIE LITERY", "PE.Views.Toolbar.strMenuNoFill": "Brak wypełnienia", "PE.Views.Toolbar.textAlignBottom": "Wyrównaj tekst do dołu", "PE.Views.Toolbar.textAlignCenter": "Wyśrodkowanie tekstu", "PE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textAlignLeft": "Wyrównaj tekst do lewej", "PE.Views.Toolbar.textAlignMiddle": "Wyrównaj tekst do środka", "PE.Views.Toolbar.textAlignRight": "Wyrównaj tekst do prawej", "PE.Views.Toolbar.textAlignTop": "Wyrównaj tekst do góry", "PE.Views.Toolbar.textArrangeBack": "Wyślij do tła", "PE.Views.Toolbar.textArrangeBackward": "Przenieś do tyłu", "PE.Views.Toolbar.textArrangeForward": "Przenieś do przodu", "PE.Views.Toolbar.textArrangeFront": "Przejdź na pierwszy plan", "PE.Views.Toolbar.textBold": "Pogrubione", "PE.Views.Toolbar.textColumnsCustom": "Niestandardowe kolumny", "PE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON><PERSON> kol<PERSON>", "PE.Views.Toolbar.textItalic": "Ku<PERSON>ywa", "PE.Views.Toolbar.textListSettings": "Ustawienia listy", "PE.Views.Toolbar.textRecentlyUsed": "Ostatnio używane", "PE.Views.Toolbar.textShapeAlignBottom": "Wyrównaj do dołu", "PE.Views.Toolbar.textShapeAlignCenter": "Wyrównaj do środka", "PE.Views.Toolbar.textShapeAlignLeft": "Wyrównaj do lewej", "PE.Views.Toolbar.textShapeAlignMiddle": "Wyrównaj do środka", "PE.Views.Toolbar.textShapeAlignRight": "Wyrównaj do prawej", "PE.Views.Toolbar.textShapeAlignTop": "Wyrównaj do góry", "PE.Views.Toolbar.textShowBegin": "Pokaż od początku", "PE.Views.Toolbar.textShowCurrent": "Pokaż z aktualnego slajdu", "PE.Views.Toolbar.textShowPresenterView": "Pokaz slajdów w trybie prezentera", "PE.Views.Toolbar.textShowSettings": "<PERSON><PERSON>ż us<PERSON>wi<PERSON>", "PE.Views.Toolbar.textStrikeout": "Skreślenie", "PE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSuperscript": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabAnimation": "Animacja", "PE.Views.Toolbar.textTabCollaboration": "Współpraca", "PE.Views.Toolbar.textTabFile": "Plik", "PE.Views.Toolbar.textTabHome": "Narzędzia główne", "PE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabProtect": "Ochrona", "PE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTitleError": "Błąd", "PE.Views.Toolbar.textUnderline": "Podkreśl", "PE.Views.Toolbar.tipAddSlide": "<PERSON><PERSON><PERSON> s<PERSON>", "PE.Views.Toolbar.tipBack": "Powró<PERSON>", "PE.Views.Toolbar.tipChangeCase": "Zmień wielkość liter", "PE.Views.Toolbar.tipChangeChart": "Zmień typ wykresu", "PE.Views.Toolbar.tipChangeSlide": "Zmień układ slajdów", "PE.Views.Toolbar.tipClearStyle": "Wy<PERSON><PERSON><PERSON><PERSON> style", "PE.Views.Toolbar.tipColorSchemas": "Zmień schemat kolorów", "PE.Views.Toolbar.tipColumns": "Wstaw kolumny", "PE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON><PERSON> styl", "PE.Views.Toolbar.tipCut": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipDateTime": "Wstaw aktualną datę i godzinę", "PE.Views.Toolbar.tipDecFont": "Zmniejsz rozmiar czcionki", "PE.Views.Toolbar.tipDecPrLeft": "Zmniejsz wcięcie", "PE.Views.Toolbar.tipEditHeader": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "PE.Views.Toolbar.tipFontName": "Czcionka", "PE.Views.Toolbar.tipFontSize": "Rozmiar <PERSON>ki", "PE.Views.Toolbar.tipHAligh": "Wyrównaj poziomo", "PE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON>", "PE.Views.Toolbar.tipIncFont": "Zwiększ rozmiar czcionki", "PE.Views.Toolbar.tipIncPrLeft": "Zwięks<PERSON> wcięcie", "PE.Views.Toolbar.tipInsertAudio": "Wstaw dźwięk", "PE.Views.Toolbar.tipInsertChart": "Wstaw wykres", "PE.Views.Toolbar.tipInsertEquation": "Wstaw równanie", "PE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertImage": "Wstaw obraz", "PE.Views.Toolbar.tipInsertShape": "Wstaw kształt", "PE.Views.Toolbar.tipInsertSymbol": "Wstaw symbol", "PE.Views.Toolbar.tipInsertTable": "Wstaw tabelę", "PE.Views.Toolbar.tipInsertText": "Wstaw tekst", "PE.Views.Toolbar.tipInsertTextArt": "Wstaw tekst Art", "PE.Views.Toolbar.tipInsertVideo": "Wstaw film", "PE.Views.Toolbar.tipLineSpace": "Rozstaw wierszy", "PE.Views.Toolbar.tipMarkers": "Lista punktowa", "PE.Views.Toolbar.tipNone": "Brak", "PE.Views.Toolbar.tipNumbers": "Lista numeryczna", "PE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipPreview": "Rozpocznij pokaz slajdów", "PE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipRedo": "Ponów", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "Zapisz swoje zmiany, aby inni użytkownicy mogli je zobaczyć.", "PE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON> wszystkich", "PE.Views.Toolbar.tipShapeAlign": "Wyrównaj kształt", "PE.Views.Toolbar.tipShapeArrange": "Uformuj kształt", "PE.Views.Toolbar.tipSlideNum": "Wstaw numer slajdu", "PE.Views.Toolbar.tipSlideSize": "<PERSON><PERSON><PERSON><PERSON> roz<PERSON>r s<PERSON>j<PERSON>", "PE.Views.Toolbar.tipSlideTheme": "<PERSON><PERSON><PERSON> s<PERSON>", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Wyrównaj pionowo", "PE.Views.Toolbar.tipViewSettings": "Wyświ<PERSON>l us<PERSON>wienia", "PE.Views.Toolbar.txtDistribHor": "Rozdziel poziomo", "PE.Views.Toolbar.txtDistribVert": "Rozdziel pionowo", "PE.Views.Toolbar.txtDuplicateSlide": "<PERSON><PERSON><PERSON><PERSON><PERSON> slajd", "PE.Views.Toolbar.txtGroup": "Grupa", "PE.Views.Toolbar.txtObjectsAlign": "Wyrównaj Zaznaczone Obiekty", "PE.Views.Toolbar.txtScheme1": "Biuro", "PE.Views.Toolbar.txtScheme10": "Mediana", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "Zasob<PERSON>", "PE.Views.Toolbar.txtScheme14": "Oriel", "PE.Views.Toolbar.txtScheme15": "Początek", "PE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme17": "Przesilenie", "PE.Views.Toolbar.txtScheme18": "Techniczny", "PE.Views.Toolbar.txtScheme19": "Trek", "PE.Views.Toolbar.txtScheme2": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme20": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme21": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme22": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme3": "Apex", "PE.Views.Toolbar.txtScheme4": "Aspekt", "PE.Views.Toolbar.txtScheme5": "Obywatelski", "PE.Views.Toolbar.txtScheme6": "Zbiegowisko", "PE.Views.Toolbar.txtScheme7": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme8": "Przepływ", "PE.Views.Toolbar.txtScheme9": "Odlewnia", "PE.Views.Toolbar.txtSlideAlign": "Wyrównaj do slajdu", "PE.Views.Toolbar.txtUngroup": "Rozgrupuj", "PE.Views.Transitions.strDelay": "Opóźnienie", "PE.Views.Transitions.strDuration": "Czas trwania", "PE.Views.Transitions.strStartOnClick": "Zacznij od kliknięcia", "PE.Views.Transitions.textBlack": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textBottom": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textBottomLeft": "Na dole po lewej", "PE.Views.Transitions.textBottomRight": "<PERSON><PERSON><PERSON> dolny", "PE.Views.Transitions.textClock": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "Zgodnie z ruchem wskazówek zegara", "PE.Views.Transitions.textCounterclockwise": "Przeciwnie do ruchu wskazówek zegara", "PE.Views.Transitions.textCover": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textFade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textHorizontalIn": "W poziomie do środka", "PE.Views.Transitions.textHorizontalOut": "W poziomie na zewnątrz", "PE.Views.Transitions.textLeft": "<PERSON><PERSON>", "PE.Views.Transitions.textNone": "Brak", "PE.Views.Transitions.textPush": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "Płynnie", "PE.Views.Transitions.textSplit": "Podziel", "PE.Views.Transitions.textUnCover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textVerticalIn": "W pionie", "PE.Views.Transitions.textVerticalOut": "W pionie na zewnątrz", "PE.Views.Transitions.textWedge": "Zak<PERSON><PERSON>j", "PE.Views.Transitions.textWipe": "Wytrzyj", "PE.Views.Transitions.textZoom": "Powiększenie", "PE.Views.Transitions.textZoomIn": "Powię<PERSON><PERSON>", "PE.Views.Transitions.textZoomOut": "<PERSON><PERSON>", "PE.Views.Transitions.textZoomRotate": "Powiększenie i obrót", "PE.Views.Transitions.txtApplyToAll": "Zatwierdź dla wszystkich slajdów", "PE.Views.Transitions.txtParameters": "Parametry", "PE.Views.Transitions.txtPreview": "Podgląd", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.textAlwaysShowToolbar": "Zawsze pokazuj pasek narzędzi", "PE.Views.ViewTab.textFitToSlide": "Dopasuj do slajdu", "PE.Views.ViewTab.textFitToWidth": "Dopasuj do szerokości", "PE.Views.ViewTab.textInterfaceTheme": "Motyw interfejsu", "PE.Views.ViewTab.textNotes": "Notatki", "PE.Views.ViewTab.textRulers": "Zasady", "PE.Views.ViewTab.textStatusBar": "Pasek stanu", "PE.Views.ViewTab.textZoom": "Powiększenie"}