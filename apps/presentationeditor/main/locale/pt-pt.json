{"Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Inserir sua mensagem aqui", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "O objeto foi desativado porque está a ser editado por outro utilizador.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Aviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "O objeto está desativado porque está a ser editado por outro utilizador.", "Common.Controllers.ExternalOleEditor.warningTitle": "Aviso", "Common.define.chartData.textArea": "Gráfico da Área", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> empi<PERSON>", "Common.define.chartData.textAreaStackedPer": "Área 100% alinhada", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "Coluna agrupada", "Common.define.chartData.textBarNormal3d": "Coluna 3-D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Coluna 3-D", "Common.define.chartData.textBarStacked": "Coluna empilhada", "Common.define.chartData.textBarStacked3d": "Coluna 3-D agrupada", "Common.define.chartData.textBarStackedPer": "Coluna 100% alinhada", "Common.define.chartData.textBarStackedPer3d": "Coluna 3-D 100% alinhada", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Coluna", "Common.define.chartData.textCombo": "Combinação", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> empi<PERSON> – coluna agrupada", "Common.define.chartData.textComboBarLine": "Coluna agrupada – linha", "Common.define.chartData.textComboBarLineSecondary": "Coluna agrupada – linha num eixo secundário", "Common.define.chartData.textComboCustom": "Combinação personalizada", "Common.define.chartData.textDoughnut": "Rosquin<PERSON>", "Common.define.chartData.textHBarNormal": "Barra Agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3-D agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON> empilhada", "Common.define.chartData.textHBarStacked3d": "Barra 3-D agrupada", "Common.define.chartData.textHBarStackedPer": "Barra 100% alinhada", "Common.define.chartData.textHBarStackedPer3d": "Barra 3-D 100% alinhada", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Linha 3-D", "Common.define.chartData.textLineMarker": "Linha com marcadores", "Common.define.chartData.textLineStacked": "<PERSON><PERSON> em<PERSON>", "Common.define.chartData.textLineStackedMarker": "Linha empilhada com marcadores", "Common.define.chartData.textLineStackedPer": "100% Ali<PERSON><PERSON>", "Common.define.chartData.textLineStackedPerMarker": "Alinhado com 100%", "Common.define.chartData.textPie": "Tarte", "Common.define.chartData.textPie3d": "Tarte 3-D", "Common.define.chartData.textPoint": "XY (gráfico de dispersão)", "Common.define.chartData.textScatter": "Di<PERSON>são", "Common.define.chartData.textScatterLine": "Dispersão com Linhas Retas", "Common.define.chartData.textScatterLineMarker": "Dispersão com Linhas e Marcadores Retos", "Common.define.chartData.textScatterSmooth": "Dispersão com Linhas Suaves", "Common.define.chartData.textScatterSmoothMarker": "Dispersão com Linhas Suaves e Marcadores", "Common.define.chartData.textStock": "Gráfico de ações", "Common.define.chartData.textSurface": "Superfície", "Common.define.effectData.textAcross": "Horizontalmente", "Common.define.effectData.textAppear": "Mostrar", "Common.define.effectData.textArcDown": "Arco para Baixo", "Common.define.effectData.textArcLeft": "Arco para a Esquerda", "Common.define.effectData.textArcRight": "Arco para a Direita", "Common.define.effectData.textArcs": "Arcos", "Common.define.effectData.textArcUp": "Arco para Cima", "Common.define.effectData.textBasic": "Básico", "Common.define.effectData.textBasicSwivel": "Swivel Básico", "Common.define.effectData.textBasicZoom": "Zoom Básico", "Common.define.effectData.textBean": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textBlinds": "Persianas", "Common.define.effectData.textBlink": "Piscar", "Common.define.effectData.textBoldFlash": "Piscar em Negrito ", "Common.define.effectData.textBoldReveal": "Aparecer em Negrito", "Common.define.effectData.textBoomerang": "Boomerang", "Common.define.effectData.textBounce": "<PERSON><PERSON>", "Common.define.effectData.textBounceLeft": "Pular para a Esquerda", "Common.define.effectData.textBounceRight": "Pular para a Direita", "Common.define.effectData.textBox": "Caixa", "Common.define.effectData.textBrushColor": "<PERSON><PERSON> <PERSON>", "Common.define.effectData.textCenterRevolve": "Girar em Torno do Centro", "Common.define.effectData.textCheckerboard": "Quadro bicolor", "Common.define.effectData.textCircle": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textCollapse": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textColorPulse": "Cor Intermitente", "Common.define.effectData.textComplementaryColor": "Cor Complementar", "Common.define.effectData.textComplementaryColor2": "Cor Complementar 2", "Common.define.effectData.textCompress": "Comprimir", "Common.define.effectData.textContrast": "Contraste", "Common.define.effectData.textContrastingColor": "Cor Contrastante", "Common.define.effectData.textCredits": "C<PERSON>dit<PERSON>", "Common.define.effectData.textCrescentMoon": "Quarto <PERSON>e", "Common.define.effectData.textCurveDown": "<PERSON><PERSON><PERSON> para Baixo", "Common.define.effectData.textCurvedSquare": "Quadrado Encurvado", "Common.define.effectData.textCurvedX": "X Encurvado", "Common.define.effectData.textCurvyLeft": "Curva para a Esquerda", "Common.define.effectData.textCurvyRight": "Curva para a Direita", "Common.define.effectData.textCurvyStar": "Estrela Curvada", "Common.define.effectData.textCustomPath": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textCuverUp": "<PERSON><PERSON><PERSON> para Cima", "Common.define.effectData.textDarken": "Escurecer", "Common.define.effectData.textDecayingWave": "Onda Esbatida", "Common.define.effectData.textDesaturate": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textDiagonalDownRight": "Diagonal Baixo Direita", "Common.define.effectData.textDiagonalUpRight": "Diagonal Cima Direita", "Common.define.effectData.textDiamond": "Diamante", "Common.define.effectData.textDisappear": "Desapa<PERSON>cer", "Common.define.effectData.textDissolveIn": "Dissolver na Entrada", "Common.define.effectData.textDissolveOut": "Dissolver na Saída", "Common.define.effectData.textDown": "Para baixo", "Common.define.effectData.textDrop": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textEmphasis": "Efeitos de Ênfase", "Common.define.effectData.textEntrance": "Efeitos de Entrada", "Common.define.effectData.textEqualTriangle": "Triân<PERSON><PERSON>", "Common.define.effectData.textExciting": "Apelativo", "Common.define.effectData.textExit": "Efeitos de Saída", "Common.define.effectData.textExpand": "Expandir", "Common.define.effectData.textFade": "Desvanecimento", "Common.define.effectData.textFigureFour": "Figura 8 Quatro", "Common.define.effectData.textFillColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "Common.define.effectData.textFlip": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textFloat": "Flut<PERSON><PERSON>", "Common.define.effectData.textFloatDown": "Flutuar para Baixo", "Common.define.effectData.textFloatIn": "Flutuar na Entrada", "Common.define.effectData.textFloatOut": "Flutuar na Saída", "Common.define.effectData.textFloatUp": "Flutuar pra Cima", "Common.define.effectData.textFlyIn": "Voar na Entrada", "Common.define.effectData.textFlyOut": "Voar na Saída", "Common.define.effectData.textFontColor": "Cor do tipo de letra", "Common.define.effectData.textFootball": "Futebol", "Common.define.effectData.textFromBottom": "Do fundo", "Common.define.effectData.textFromBottomLeft": "Do canto inferior esquerdo", "Common.define.effectData.textFromBottomRight": "Do canto inferior direito", "Common.define.effectData.textFromLeft": "Da Esquerda", "Common.define.effectData.textFromRight": "Da Direita", "Common.define.effectData.textFromTop": "De cima", "Common.define.effectData.textFromTopLeft": "Do canto superior esquerdo", "Common.define.effectData.textFromTopRight": "Do canto superior direito", "Common.define.effectData.textFunnel": "Funil", "Common.define.effectData.textGrowShrink": "Aumentar/Diminuir", "Common.define.effectData.textGrowTurn": "Aumentar e Virar", "Common.define.effectData.textGrowWithColor": "Aumentar com Cor", "Common.define.effectData.textHeart": "Coração", "Common.define.effectData.textHeartbeat": "Batimento cardíaco", "Common.define.effectData.textHexagon": "Hexágono", "Common.define.effectData.textHorizontal": "Horizontal", "Common.define.effectData.textHorizontalFigure": "Figura 8 Horizontal", "Common.define.effectData.textHorizontalIn": "Horizontal para dentro", "Common.define.effectData.textHorizontalOut": "Horizontal para fora", "Common.define.effectData.textIn": "Em", "Common.define.effectData.textInFromScreenCenter": "Ampliar a Partir do Centro do Ecrã", "Common.define.effectData.textInSlightly": "Ampliar Ligeiramente", "Common.define.effectData.textInToScreenBottom": "Para dentro a iniciar no fundo do ecrã", "Common.define.effectData.textInvertedSquare": "Quadrado Invertido", "Common.define.effectData.textInvertedTriangle": "Triângulo Invertido", "Common.define.effectData.textLeft": "E<PERSON>rda", "Common.define.effectData.textLeftDown": "Esquerda em baixo", "Common.define.effectData.textLeftUp": "Esquerda em cima", "Common.define.effectData.textLighten": "<PERSON><PERSON>", "Common.define.effectData.textLineColor": "<PERSON><PERSON> <PERSON> l<PERSON><PERSON>", "Common.define.effectData.textLines": "<PERSON><PERSON>", "Common.define.effectData.textLinesCurves": "<PERSON><PERSON> e <PERSON>vas", "Common.define.effectData.textLoopDeLoop": "Laço", "Common.define.effectData.textLoops": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textModerate": "Moderado", "Common.define.effectData.textNeutron": "Neutrão", "Common.define.effectData.textObjectCenter": "Centro do Objeto", "Common.define.effectData.textObjectColor": "Cor do Objeto", "Common.define.effectData.textOctagon": "Octógono", "Common.define.effectData.textOut": "Fora", "Common.define.effectData.textOutFromScreenBottom": "Para fora a iniciar no fundo do ecrã", "Common.define.effectData.textOutSlightly": "Reduzir Ligeiramente", "Common.define.effectData.textOutToScreenCenter": "De Fora Para a Parte Central do Ecrã", "Common.define.effectData.textParallelogram": "Paralelograma", "Common.define.effectData.textPath": "Trajetórias de Movimento ", "Common.define.effectData.textPathCurve": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPathLine": "<PERSON><PERSON>", "Common.define.effectData.textPathScribble": "Rabisco", "Common.define.effectData.textPeanut": "Amendoim", "Common.define.effectData.textPeekIn": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textPeekOut": "<PERSON><PERSON>", "Common.define.effectData.textPentagon": "Pentágono", "Common.define.effectData.textPinwheel": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textPlus": "<PERSON><PERSON>", "Common.define.effectData.textPointStar": "Estrela de Pontas", "Common.define.effectData.textPointStar4": "Estrela de 4 pontos", "Common.define.effectData.textPointStar5": "Estrela de 5 pontos", "Common.define.effectData.textPointStar6": "Estrela de 6 pontos", "Common.define.effectData.textPointStar8": "Estrela de 8 pontos", "Common.define.effectData.textPulse": "Intermitente", "Common.define.effectData.textRandomBars": "<PERSON><PERSON>", "Common.define.effectData.textRight": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textRightDown": "Direita em Baixo", "Common.define.effectData.textRightTriangle": "Triângulo à Direita", "Common.define.effectData.textRightUp": "Direita em cima", "Common.define.effectData.textRiseUp": "Revelar para Baixo", "Common.define.effectData.textSCurve1": "Curva em S 1", "Common.define.effectData.textSCurve2": "Curva em S 2", "Common.define.effectData.textShape": "Forma", "Common.define.effectData.textShapes": "Formas", "Common.define.effectData.textShimmer": "Bril<PERSON>", "Common.define.effectData.textShrinkTurn": "Diminuir e Virar", "Common.define.effectData.textSineWave": "Onda Sinusoidal", "Common.define.effectData.textSinkDown": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textSlideCenter": "Centro do Diapositivo", "Common.define.effectData.textSpecial": "Especial", "Common.define.effectData.textSpin": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpinner": "Controlo giratório", "Common.define.effectData.textSpiralIn": "Entrada em espiral", "Common.define.effectData.textSpiralLeft": "Espiral para a Esquerda", "Common.define.effectData.textSpiralOut": "Saída em Espiral", "Common.define.effectData.textSpiralRight": "Espiral para a Direita", "Common.define.effectData.textSplit": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSpoke1": "1 Raio", "Common.define.effectData.textSpoke2": "2 Raios", "Common.define.effectData.textSpoke3": "3 Raios", "Common.define.effectData.textSpoke4": "4 Raios", "Common.define.effectData.textSpoke8": "8 Raios", "Common.define.effectData.textSpring": "Primavera", "Common.define.effectData.textSquare": "Quadrado", "Common.define.effectData.textStairsDown": "<PERSON><PERSON><PERSON> a<PERSON>", "Common.define.effectData.textStretch": "<PERSON><PERSON>", "Common.define.effectData.textStrips": "Faixas", "Common.define.effectData.textSubtle": "<PERSON><PERSON>", "Common.define.effectData.textSwivel": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textSwoosh": "Laço", "Common.define.effectData.textTeardrop": "Lágrima", "Common.define.effectData.textTeeter": "Balançar", "Common.define.effectData.textToBottom": "Para baixo", "Common.define.effectData.textToBottomLeft": "Para esquerda e baixo", "Common.define.effectData.textToBottomRight": "Para direita e baixo", "Common.define.effectData.textToLeft": "Para a esquerda", "Common.define.effectData.textToRight": "Para a Direita", "Common.define.effectData.textToTop": "Para cima", "Common.define.effectData.textToTopLeft": "Para cima e esquerda", "Common.define.effectData.textToTopRight": "Para cima e direita", "Common.define.effectData.textTransparency": "Transparência", "Common.define.effectData.textTrapezoid": "Trapé<PERSON>", "Common.define.effectData.textTurnDown": "Virar para Baixo", "Common.define.effectData.textTurnDownRight": "Virar para Baixo e Direita", "Common.define.effectData.textTurns": "Voltas", "Common.define.effectData.textTurnUp": "Para Cima", "Common.define.effectData.textTurnUpRight": "Para Cima e para a Direita", "Common.define.effectData.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.effectData.textUp": "Para cima", "Common.define.effectData.textVertical": "Vertical", "Common.define.effectData.textVerticalFigure": "Figura Vertical 8", "Common.define.effectData.textVerticalIn": "Vertical para dentro", "Common.define.effectData.textVerticalOut": "Vertical para fora", "Common.define.effectData.textWave": "On<PERSON>", "Common.define.effectData.textWedge": "Triangular", "Common.define.effectData.textWheel": "Roda", "Common.define.effectData.textWhip": "Chicote", "Common.define.effectData.textWipe": "<PERSON><PERSON><PERSON>", "Common.define.effectData.textZigzag": "Zigzag", "Common.define.effectData.textZoom": "Zoom", "Common.define.gridlineData.txtCm": "cm", "Common.define.gridlineData.txtPt": "pt", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.warnFileLocked": "O ficheiro está a ser editado por outra aplicação. Pode continuar a trabalhar mas terá que guardar uma cópia.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON> uma c<PERSON>pia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Adicionar nova cor personalizada", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboDataView.emptyComboText": "Sem estilos", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Atual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor inserido não está correto.<br>Introduza um valor entre 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor inserido não está correto.<br>Introduza um valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sem cor", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar palavra-passe", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostrar palavra-passe", "Common.UI.SearchBar.textFind": "Localizar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir opções avançadas", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Destacar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto de substituição", "Common.UI.SearchDialog.textSearchStart": "Inserir seu texto aqui", "Common.UI.SearchDialog.textTitle": "Localizar e substituir", "Common.UI.SearchDialog.textTitle2": "Localizar", "Common.UI.SearchDialog.textWholeWords": "Palavras inteiras apenas", "Common.UI.SearchDialog.txtBtnHideReplace": "Ocultar Substituição", "Common.UI.SearchDialog.txtBtnReplace": "Substituir", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituir tudo", "Common.UI.SynchronizeTip.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.SynchronizeTip.textSynchronize": "O documento foi alterado por outro utilizador.<br>Clique para guardar as suas alterações e recarregar o documento.", "Common.UI.ThemeColorPalette.textRecentColors": "Cores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores do tema", "Common.UI.Themes.txtThemeClassicLight": "Clássico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste escuro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "O mesmo que o sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Não", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmação", "Common.UI.Window.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Informações", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "<PERSON>m", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "Pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "endereço:", "Common.Views.About.txtLicensee": "LICENÇA", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Aplicar ao escrever", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorreção de Texto", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formatação automática ao escrever", "Common.Views.AutoCorrectDialog.textBulleted": "Lista automática com marcas", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Adicionar parágrafo com espaçamento duplo", "Common.Views.AutoCorrectDialog.textFLCells": "Maiúscula na primeira letra das células da tabela", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalizar a primeira letra das frases", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e locais de rede com hiperligações", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON>s (--) com traço (-)", "Common.Views.AutoCorrectDialog.textMathCorrect": " Correção automática de matemática", "Common.Views.AutoCorrectDialog.textNumbered": "Lista automática com números", "Common.Views.AutoCorrectDialog.textQuotes": "\"Aspas retas\" com \"aspas inteligentes\"", "Common.Views.AutoCorrectDialog.textRecognized": "Funções Reconhecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expressões são expressões matemáticas reconhecidas. Não serão colocadas automaticamente em itálico.", "Common.Views.AutoCorrectDialog.textReplace": "Substituir", "Common.Views.AutoCorrectDialog.textReplaceText": "Substituir à medida que digita", "Common.Views.AutoCorrectDialog.textReplaceType": "Substitua o texto à medida que digita", "Common.Views.AutoCorrectDialog.textReset": "Repor", "Common.Views.AutoCorrectDialog.textResetAll": "Repor predefinição", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Correção automática", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funções reconhecidas devem conter apenas as letras de A a Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Qualquer expressão que tenha acrescentado será removida e as expressões removidas serão restauradas. Quer continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A correção automática para %1 já existe. Quer substituí-la?", "Common.Views.AutoCorrectDialog.warnReset": "Qualquer correção automática que tenha adicionado será removida e as alterações serão restauradas aos seus valores originais. Quer continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A correção automática para %1 já existe. Quer substituí-la?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> anti<PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por Grupo", "Common.Views.Comments.mniPositionAsc": "De cima", "Common.Views.Comments.mniPositionDesc": "Do fundo", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Adicionar comentário ao documento", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.Comments.textAll": "Todos", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textComments": "Comentários", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Inserir seu coment<PERSON>rio aqui", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolvido", "Common.Views.Comments.textSort": "Ordenar comentá<PERSON>s", "Common.Views.Comments.textViewResolved": "Não tem permissão para reabrir o comentário", "Common.Views.Comments.txtEmpty": "Não há comentários no documento.", "Common.Views.CopyWarningDialog.textDontShow": "Não exibir esta mensagem novamente", "Common.Views.CopyWarningDialog.textMsg": "As ações copiar, cortar e colar através dos botões da barra de ferramentas ou através do menu de contexto apenas serão executadas neste separador.<br><br>Para copiar ou colar de outras aplicações deve utilizar estas teclas de atalho:", "Common.Views.CopyWarningDialog.textTitle": "Ações <PERSON>, cortar e colar", "Common.Views.CopyWarningDialog.textToCopy": "para copiar", "Common.Views.CopyWarningDialog.textToCut": "para cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Colar", "Common.Views.DocumentAccessDialog.textLoading": "A carregar...", "Common.Views.DocumentAccessDialog.textTitle": "Definições de partilha", "Common.Views.ExternalDiagramEditor.textTitle": "Editor de gráfico", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Guardar e sair", "Common.Views.ExternalOleEditor.textTitle": "Editor de folhas de cálculo", "Common.Views.Header.labelCoUsersDescr": "Utilizadores a editar o ficheiro:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Definições avançadas", "Common.Views.Header.textBack": "Abrir localização", "Common.Views.Header.textCompactView": "Ocultar barra de ferramentas", "Common.Views.Header.textHideLines": "Ocultar r<PERSON>", "Common.Views.Header.textHideNotes": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "Ocultar barra de estado", "Common.Views.Header.textRemoveFavorite": "Remover dos favoritos", "Common.Views.Header.textSaveBegin": "A guardar...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON> as alterações foram guardadas", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON> as alterações foram guardadas", "Common.Views.Header.textShare": "Partilhar", "Common.Views.Header.textZoom": "Ampliação", "Common.Views.Header.tipAccessRights": "Gerenciar direitos de acesso ao documento", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "<PERSON><PERSON> atual", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Guardar", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Desacoplar em janela separada", "Common.Views.Header.tipUsers": "Ver utilizadores", "Common.Views.Header.tipViewSettings": "Definições de visualização", "Common.Views.Header.tipViewUsers": "Ver utilizadores e gerir direitos de acesso", "Common.Views.Header.txtAccessRights": "Alterar direitos de acesso", "Common.Views.Header.txtRename": "<PERSON>dar nome", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar alterações detalhadas", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Mostrar alterações detalhadas", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Colar URL de uma imagem:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Você precisa especificar números de linhas e colunas válidos.", "Common.Views.InsertTableDialog.txtColumns": "Número de <PERSON>nas", "Common.Views.InsertTableDialog.txtMaxText": "O valor máximo para este campo é {0}.", "Common.Views.InsertTableDialog.txtMinText": "O valor mínimo para este campo é {0}.", "Common.Views.InsertTableDialog.txtRows": "Número de l<PERSON>has", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Selecionar idioma do documento", "Common.Views.ListSettingsDialog.textBulleted": "Com Marcas de Lista", "Common.Views.ListSettingsDialog.textFromFile": "Do fi<PERSON>iro", "Common.Views.ListSettingsDialog.textFromStorage": "Do armazenamento", "Common.Views.ListSettingsDialog.textFromUrl": "De um URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.textSelect": "Selecionar de", "Common.Views.ListSettingsDialog.tipChange": "Alterar lista", "Common.Views.ListSettingsDialog.txtBullet": "Marcador", "Common.Views.ListSettingsDialog.txtColor": "Cor", "Common.Views.ListSettingsDialog.txtImage": "Imagem", "Common.Views.ListSettingsDialog.txtImport": "Importar", "Common.Views.ListSettingsDialog.txtNewBullet": "Nova marca", "Common.Views.ListSettingsDialog.txtNewImage": "Nova imagem", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% do texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Iniciar em", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Definições da lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Codificação", "Common.Views.OpenDialog.txtIncorrectPwd": "Palavra-passe inválida.", "Common.Views.OpenDialog.txtOpenFile": "Introduza a palavra-passe para abrir o ficheiro", "Common.Views.OpenDialog.txtPassword": "Palavra-passe", "Common.Views.OpenDialog.txtProtected": "Assim que introduzir a palavra-passe e abrir o ficheiro, a palavra-passe atual será reposta.", "Common.Views.OpenDialog.txtTitle": "Escolher opções %1", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON> protegido", "Common.Views.PasswordDialog.txtDescription": "Defina uma palavra-passe para proteger este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "Disparidade nas palavras-passe introduzidas", "Common.Views.PasswordDialog.txtPassword": "Palavra-passe", "Common.Views.PasswordDialog.txtRepeat": "Repetição de palavra-passe", "Common.Views.PasswordDialog.txtTitle": "Definir palavra-passe", "Common.Views.PasswordDialog.txtWarning": "Aviso: Se perder ou esquecer a palavra-passe, não será possível recuperá-la. Guarde-a num local seguro.", "Common.Views.PluginDlg.textLoading": "A carregar", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Fechar plugin", "Common.Views.Plugins.textLoading": "A carregar", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Cifrar com palavra-passe", "Common.Views.Protection.hintDelPwd": "Eliminar palavra-passe", "Common.Views.Protection.hintPwd": "Alt<PERSON>r ou eliminar palavra-passe", "Common.Views.Protection.hintSignature": "Inserir assinatura digital ou linha de assinatura", "Common.Views.Protection.txtAddPwd": "Adicionar pala<PERSON>-passe", "Common.Views.Protection.txtChangePwd": "Alterar palavra-passe", "Common.Views.Protection.txtDeletePwd": "Eliminar palavra-passe", "Common.Views.Protection.txtEncrypt": "Encriptar", "Common.Views.Protection.txtInvisibleSignature": "Inserir assinatura digital", "Common.Views.Protection.txtSignature": "Assinatura", "Common.Views.Protection.txtSignatureLine": "Adicionar linha de assinatura", "Common.Views.RenameDialog.textName": "Nome do ficheiro", "Common.Views.RenameDialog.txtInvalidName": "O nome do ficheiro não pode ter qualquer um dos seguintes caracteres:", "Common.Views.ReviewChanges.hintNext": "Para a próxima alteração", "Common.Views.ReviewChanges.hintPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Edição em tempo real. <PERSON><PERSON> as alterações foram guardadas.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Utilize o botão 'Guardar' para sincronizar as alterações efetuadas ao documento.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceito estas mudanças", "Common.Views.ReviewChanges.tipCoAuthMode": "Definir modo de coedição", "Common.Views.ReviewChanges.tipCommentRem": "Remover comentários", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentários", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.tipHistory": "<PERSON>rar histó<PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Selecione o modo em que pretende que as alterações sejam apresentadas", "Common.Views.ReviewChanges.tipSetDocLang": "Definir idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.tipSharing": "<PERSON><PERSON><PERSON> direitos de acesso ao documento", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON> to<PERSON>", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceito a mudança", "Common.Views.ReviewChanges.txtChat": "Conversa", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de co-edição", "Common.Views.ReviewChanges.txtCommentRemAll": "Remover todos os comentários", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Remover os meus comentários", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Remover os meus comentários atuais", "Common.Views.ReviewChanges.txtCommentRemove": "Remover", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentários", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver meus comentários", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver meus comentários atuais", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> as alteraç<PERSON>es ace<PERSON> (Pré-visualizar)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Hist<PERSON><PERSON><PERSON> <PERSON> ve<PERSON>ão", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> (Editar)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcação", "Common.Views.ReviewChanges.txtNext": "Próximo", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> as alteraç<PERSON>es recusadas (Pré-visualizar)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeitar alterações", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeitar alteração atual", "Common.Views.ReviewChanges.txtSharing": "Partilhar", "Common.Views.ReviewChanges.txtSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Modo de exibição", "Common.Views.ReviewPopover.textAdd": "Adiciona", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textMention": "+mencionar dar-lhe-á acesso ao documento e enviar-lhe-á um e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+menção notifica o utilizador por e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Abrir novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Não tem permissão para reabrir o comentário", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "A carregar", "Common.Views.SaveAsDlg.textTitle": "Pasta para guardar", "Common.Views.SearchPanel.textCaseSensitive": "Diferenciar maiús<PERSON>s de minúsculas", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.Views.SearchPanel.textContentChanged": "Documento alterado.", "Common.Views.SearchPanel.textFind": "Localizar", "Common.Views.SearchPanel.textFindAndReplace": "Localizar e substituir", "Common.Views.SearchPanel.textMatchUsingRegExp": "Correspondência ao usar expressões regulares", "Common.Views.SearchPanel.textNoMatches": "Sem correspondência", "Common.Views.SearchPanel.textNoSearchResults": "Sem resultados de pesquisa", "Common.Views.SearchPanel.textReplace": "Substituir", "Common.Views.SearchPanel.textReplaceAll": "Substituir tudo", "Common.Views.SearchPanel.textReplaceWith": "Substituir com", "Common.Views.SearchPanel.textSearchAgain": "{0}Executar nova pesquisa{1} para resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "A pesquisa parou", "Common.Views.SearchPanel.textSearchResults": "Resultados da pesquisa: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Existem demasiados resultados para poderem ser mostrados aqui", "Common.Views.SearchPanel.textWholeWords": "Palavras inteiras apenas", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "A carregar", "Common.Views.SelectFileDlg.textTitle": "Selecionar fonte de dados", "Common.Views.SignDialog.textBold": "Negrito", "Common.Views.SignDialog.textCertificate": " Certificado", "Common.Views.SignDialog.textChange": "Alterar", "Common.Views.SignDialog.textInputName": "Inserir nome do assinante", "Common.Views.SignDialog.textItalic": "Itálico", "Common.Views.SignDialog.textNameError": "O nome do assinante não pode estar vazio", "Common.Views.SignDialog.textPurpose": "Objetivo para assinar este documento", "Common.Views.SignDialog.textSelect": "Selecionar", "Common.Views.SignDialog.textSelectImage": "Selecionar imagem", "Common.Views.SignDialog.textSignature": "A assinatura parece ser", "Common.Views.SignDialog.textTitle": "Assinar o documento", "Common.Views.SignDialog.textUseImage": "ou clique \"Selecionar imagem\" para a utilizar como assinatura", "Common.Views.SignDialog.textValid": "Válida de %1 até %2", "Common.Views.SignDialog.tipFontName": "Nome do tipo de letra", "Common.Views.SignDialog.tipFontSize": "Tamanho do tipo de letra", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir ao signatário inserir comentários no diálogo de assinatura", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de assinar este documento, verifique se o conteúdo que está a assinar está correto.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "Título do Assinante", "Common.Views.SignSettingsDialog.textInstructions": "Instruções para o Assinante", "Common.Views.SignSettingsDialog.textShowDate": "Mostrar data na linha de assinatura", "Common.Views.SignSettingsDialog.textTitle": "Definições de Assinatura", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Assinatura Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "Aspas Duplas de Fechamento", "Common.Views.SymbolTableDialog.textDOQuote": "Aspas de abertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Travessão", "Common.Views.SymbolTableDialog.textEmSpace": "Espaço", "Common.Views.SymbolTableDialog.textEnDash": "Travessão", "Common.Views.SymbolTableDialog.textEnSpace": "Espaço", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON> de letra", "Common.Views.SymbolTableDialog.textNBHyphen": "Hífen inseparável", "Common.Views.SymbolTableDialog.textNBSpace": "Espaço sem interrupção", "Common.Views.SymbolTableDialog.textPilcrow": "Sinal de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 de espaço", "Common.Views.SymbolTableDialog.textRange": "Intervalo", "Common.Views.SymbolTableDialog.textRecent": "Símbolos usados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Sinal Registado", "Common.Views.SymbolTableDialog.textSCQuote": "Aspas de Fechamento", "Common.Views.SymbolTableDialog.textSection": "Sinal de secção", "Common.Views.SymbolTableDialog.textShortcut": "Tecla de atalho", "Common.Views.SymbolTableDialog.textSHyphen": "Hífen virtual", "Common.Views.SymbolTableDialog.textSOQuote": "Apóstrofo de abertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de Marca Registada.", "Common.Views.UserNameDialog.textDontShow": "Não perguntar novamente", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "Etiqueta não deve estar em branco.", "PE.Controllers.LeftMenu.leavePageText": "<PERSON><PERSON> as alterações não guardadas serão perdidas.<br>Clique em \"Cancelar\" e depois em \"Guardar\" para as guardar. Clique em \"Ok\" para descartar todas as alterações não guardadas.", "PE.Controllers.LeftMenu.newDocumentTitle": "Apresentação sem nome", "PE.Controllers.LeftMenu.notcriticalErrorTitle": "Aviso", "PE.Controllers.LeftMenu.requestEditRightsText": "Solicitando direitos de edição...", "PE.Controllers.LeftMenu.textLoadHistory": "A carregar o histórico de versões...", "PE.Controllers.LeftMenu.textNoTextFound": "Não foi possível localizar os dados procurados. Por favor ajuste as opções de pesquisa.", "PE.Controllers.LeftMenu.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "PE.Controllers.LeftMenu.textReplaceSuccess": "A pesquisa foi realizada. Ocorrências substituídas: {0}", "PE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "PE.Controllers.Main.applyChangesTextText": "A carregar dados...", "PE.Controllers.Main.applyChangesTitleText": "A carregar dados", "PE.Controllers.Main.convertationTimeoutText": "Excedeu o tempo limite de conversão.", "PE.Controllers.Main.criticalErrorExtText": "Prima \"OK\" para voltar para a lista de documentos.", "PE.Controllers.Main.criticalErrorTitle": "Erro", "PE.Controllers.Main.downloadErrorText": "Download falhou.", "PE.Controllers.Main.downloadTextText": "A descarregar apresentação…", "PE.Controllers.Main.downloadTitleText": "A descarregar apresentação", "PE.Controllers.Main.errorAccessDeny": "Você está tentando executar uma ação para a qual não tem direitos.<br>Entre em contato com o administrador do Document Server.", "PE.Controllers.Main.errorBadImageUrl": "URL inválido", "PE.Controllers.Main.errorCoAuthoringDisconnect": "Conexão com servidor perdida. O documento não pode ser editado neste momento.", "PE.Controllers.Main.errorComboSeries": "Para criar um gráfico de combinação, selecione pelo menos duas séries de dados.", "PE.Controllers.Main.errorConnectToServer": "Não foi possível guardar o documento. Verifique a sua ligação de rede ou contacte o administrador.<br>Ao clicar em 'OK', surgirá uma caixa de diálogo para descarregar o documento.", "PE.Controllers.Main.errorDatabaseConnection": "Erro externo.<br><PERSON><PERSON> de conexão ao banco de dados. Entre em contato com o suporte caso o erro persista.", "PE.Controllers.Main.errorDataEncrypted": "Foram recebidas alterações cifradas que não puderam ser decifradas.", "PE.Controllers.Main.errorDataRange": "Intervalo de dados inválido.", "PE.Controllers.Main.errorDefaultMessage": "Código de erro: %1", "PE.Controllers.Main.errorDirectUrl": "Verifique a ligação ao documento.<br><PERSON>e ser uma ligação direta para o ficheiro a descarregar.", "PE.Controllers.Main.errorEditingDownloadas": "Ocorreu um erro ao trabalhar no documento.<br>Utilize a opção 'Descarregar como' para guardar a cópia de segurança do ficheiro numa unidade.", "PE.Controllers.Main.errorEditingSaveas": "Ocorreu um erro ao trabalhar no documento.<br>Utilize a opção 'Descarregar como...' para guardar a cópia de segurança do ficheiro numa unidade.", "PE.Controllers.Main.errorEmailClient": "Não foi possível encontrar nenhum cliente de e-mail.", "PE.Controllers.Main.errorFilePassProtect": "O ficheiro está protegido por palavra-passe e não pode ser aberto.", "PE.Controllers.Main.errorFileSizeExceed": "O tamanho do ficheiro excede o limite do servidor.<br>Contacte o administrador do servidor de documentos para mais detalhes.", "PE.Controllers.Main.errorForceSave": "Ocorreu um erro ao guardar o ficheiro. Utilize a opção 'Descarregar como' para guardar o ficheiro para uma unidade ou tente novamente mais tarde.", "PE.Controllers.Main.errorKeyEncrypt": "Descritor de chave desconhecido", "PE.Controllers.Main.errorKeyExpire": "Descritor de chave expirado", "PE.Controllers.Main.errorLoadingFont": "Os tipos de letra não foram carregados.<br>Contacte o administrador do servidor de documentos.", "PE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON>ha ao guardar.", "PE.Controllers.Main.errorServerVersion": "A versão do editor foi atualizada. A página será recarregada para aplicar as alterações.", "PE.Controllers.Main.errorSessionAbsolute": "A sessão de edição expirou. Tente recarregar a página.", "PE.Controllers.Main.errorSessionIdle": "Este documento não foi editado durante muito tempo. Tente recarregar a página.", "PE.Controllers.Main.errorSessionToken": "A ligação ao servidor foi interrompida. Tente recarregar a página.", "PE.Controllers.Main.errorSetPassword": "Não foi possível definir a palavra-passe.", "PE.Controllers.Main.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "PE.Controllers.Main.errorToken": "O token de segurança do documento não foi formado corretamente.<br>Entre em contato com o administrador do Document Server.", "PE.Controllers.Main.errorTokenExpire": "O token de segurança do documento expirou.<br>Entre em contato com o administrador do Document Server.", "PE.Controllers.Main.errorUpdateVersion": "A versão do ficheiro foi alterada. A página será recarregada.", "PE.Controllers.Main.errorUpdateVersionOnDisconnect": "A ligação foi restaurada e a versão do ficheiro foi alterada.<br><PERSON><PERSON> de poder continuar a trabalhar, é necessário descarregar o ficheiro ou copiar o seu conteúdo para garantir que nada se perde e depois voltar a carregar esta página.", "PE.Controllers.Main.errorUserDrop": "De momento, não é possível aceder ao ficheiro.", "PE.Controllers.Main.errorUsersExceed": "Excedeu o número máximo de utilizadores permitidos pelo seu plano", "PE.Controllers.Main.errorViewerDisconnect": "Ligação perdida. Ainda pode ver o documento mas<br>não o conseguirá descarregar até que a ligação seja restaurada e a página recarregada.", "PE.Controllers.Main.leavePageText": "Este documento tem alterações não guardadas. Clique 'Ficar na página' para que o documento seja guardado automaticamente. Clique 'Sair da página' para rejeitar todas as alterações.", "PE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON> as alterações não guardadas nesta apresentação serão perdidas.<br> Clique em \"Cancelar\" e depois em \"Guardar\" para as guardar. Clique em \"OK\" para descartar todas as alterações não guardadas.", "PE.Controllers.Main.loadFontsTextText": "A carregar dados...", "PE.Controllers.Main.loadFontsTitleText": "A carregar dados", "PE.Controllers.Main.loadFontTextText": "A carregar dados...", "PE.Controllers.Main.loadFontTitleText": "A carregar dados", "PE.Controllers.Main.loadImagesTextText": "A carregar imagens...", "PE.Controllers.Main.loadImagesTitleText": "A carregar imagens", "PE.Controllers.Main.loadImageTextText": "A carregar imagem...", "PE.Controllers.Main.loadImageTitleText": "A carregar imagem", "PE.Controllers.Main.loadingDocumentTextText": "A carregar apresentação...", "PE.Controllers.Main.loadingDocumentTitleText": "A carregar apresentação", "PE.Controllers.Main.loadThemeTextText": "A carregar tema...", "PE.Controllers.Main.loadThemeTitleText": "A carregar tema", "PE.Controllers.Main.notcriticalErrorTitle": "Aviso", "PE.Controllers.Main.openErrorText": "Ocorreu um erro ao abrir o ficheiro.", "PE.Controllers.Main.openTextText": "Abrindo apresentação...", "PE.Controllers.Main.openTitleText": "Abrindo apresentação", "PE.Controllers.Main.printTextText": "Imprimindo apresentação...", "PE.Controllers.Main.printTitleText": "Imprimindo apresentação", "PE.Controllers.Main.reloadButtonText": "Re<PERSON><PERSON><PERSON> p<PERSON>gina", "PE.Controllers.Main.requestEditFailedMessageText": "Alguém está editando esta apresentação neste momento. Tente novamente mais tarde.", "PE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>", "PE.Controllers.Main.saveErrorText": "Ocorreu um erro ao guardar o ficheiro.", "PE.Controllers.Main.saveErrorTextDesktop": "Este ficheiro não pode ser guardado ou criado.<br>Os motivos podem ser: <br>1. O ficheiro é apenas de leitura. <br>2. O ficheiro está a ser editado por outro utilizador.<br>3. O disco está cheio ou danificado.", "PE.Controllers.Main.saveTextText": "A guardar apresentação...", "PE.Controllers.Main.saveTitleText": "A guardar apresentação", "PE.Controllers.Main.scriptLoadError": "A ligação está muito lenta e alguns dos componentes não foram carregados. Tente recarregar a página.", "PE.Controllers.Main.splitDividerErrorText": "O número de linhas deve ser um divisor de %1.", "PE.Controllers.Main.splitMaxColsErrorText": "O número de colunas deve ser inferior a %1.", "PE.Controllers.Main.splitMaxRowsErrorText": "O número de linhas deve ser inferior a %1.", "PE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.textApplyAll": "Aplicar a todas as equações", "PE.Controllers.Main.textBuyNow": "Visitar site", "PE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> as alterações foram guardadas", "PE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.textCloseTip": "Clique para fechar a dica", "PE.Controllers.Main.textContactUs": "Contacte a equipa comercial", "PE.Controllers.Main.textConvertEquation": "Esta equação foi criada com uma versão anterior da aplicação e já não é suportada. Para a editar, tem que converter a equação para o formato Office Math ML.<br>Converter agora?", "PE.Controllers.Main.textCustomLoader": "Tenha em conta de que, de acordo com os termos da licença, não tem permissões para alterar o carregador.<br><PERSON>r favor contacte a equipa comercial.", "PE.Controllers.Main.textDisconnect": "A ligação está perdida", "PE.Controllers.Main.textGuest": "Convidado(a)", "PE.Controllers.Main.textHasMacros": "O ficheiro contém macros automáticas.<br><PERSON><PERSON><PERSON> executar as macros?", "PE.Controllers.Main.textLearnMore": "<PERSON><PERSON> mais", "PE.Controllers.Main.textLoadingDocument": "A carregar apresentação", "PE.Controllers.Main.textLongName": "Insira um nome com menos de 128 caracteres.", "PE.Controllers.Main.textNoLicenseTitle": "Atingiu o limite da licença", "PE.Controllers.Main.textPaidFeature": "Funcionalidade paga", "PE.Controllers.Main.textReconnect": "A ligação foi reposta", "PE.Controllers.Main.textRemember": "Memorizar a minha escolha", "PE.Controllers.Main.textRememberMacros": "Memor<PERSON>r escolha para todas as macros", "PE.Controllers.Main.textRenameError": "O nome de utilizador não pode estar em branco.", "PE.Controllers.Main.textRenameLabel": "Insira um nome a ser usado para colaboração", "PE.Controllers.Main.textRequestMacros": "Uma macro faz um pedido de URL. Quer permitir o pedido à %1?", "PE.Controllers.Main.textShape": "Forma", "PE.Controllers.Main.textStrict": "Modo estrito", "PE.Controllers.Main.textText": "Texto", "PE.Controllers.Main.textTryUndoRedo": "As funções Desfazer/<PERSON><PERSON><PERSON> foram desativadas para se poder co-editar o documento.<br>Clique no botão 'Modo estrito' para ativar este modo de edição e editar o ficheiro sem ser incomodado por outros utilizadores enviando apenas as suas alterações assim que terminar e guardar. Pode alternar entre modos de co-edição através das definições avançadas.", "PE.Controllers.Main.textTryUndoRedoWarn": "As funções Desfazer/Refazer estão desativadas no modo de co-edição rápida.", "PE.Controllers.Main.titleLicenseExp": "Licença expirada", "PE.Controllers.Main.titleServerVersion": "Editor atual<PERSON><PERSON>", "PE.Controllers.Main.txtAddFirstSlide": "Clique para adicionar o primeiro diapositivo", "PE.Controllers.Main.txtAddNotes": "Clique para adicionar notas", "PE.Controllers.Main.txtArt": "Your text here", "PE.Controllers.Main.txtBasicShapes": "Formas básicas", "PE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "PE.Controllers.Main.txtClipArt": "<PERSON><PERSON>", "PE.Controllers.Main.txtDateTime": "Data e Hora", "PE.Controllers.Main.txtDiagram": "SmartArt", "PE.Controllers.Main.txtDiagramTitle": "Título do <PERSON>a", "PE.Controllers.Main.txtEditingMode": "Definir modo de edição...", "PE.Controllers.Main.txtErrorLoadHistory": "Falha ao carregar histórico", "PE.Controllers.Main.txtFiguredArrows": "Setas figuradas", "PE.Controllers.Main.txtFooter": "Rodapé", "PE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtImage": "Imagem", "PE.Controllers.Main.txtLines": "<PERSON><PERSON>", "PE.Controllers.Main.txtLoading": "A carregar...", "PE.Controllers.Main.txtMath": "Matemática", "PE.Controllers.Main.txtMedia": "Multimédia", "PE.Controllers.Main.txtNeedSynchronize": "Você tem atualizações", "PE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtPicture": "Imagem", "PE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSeries": "Série", "PE.Controllers.Main.txtShape_accentBorderCallout1": "Chamada da linha 1 (borda e barra de destaque)", "PE.Controllers.Main.txtShape_accentBorderCallout2": "Chamada da linha 2 (borda e barra de destaque)", "PE.Controllers.Main.txtShape_accentBorderCallout3": "Texto explicativo da linha 3 (borda e barra de destaque)", "PE.Controllers.Main.txtShape_accentCallout1": "Chamada da linha 1 (barra de destaque)", "PE.Controllers.Main.txtShape_accentCallout2": "Chamada da linha 2 (barra de destaque)", "PE.Controllers.Main.txtShape_accentCallout3": "Texto explicativo da linha 3 (barra de destaque)", "PE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON><PERSON> ou Anterior", "PE.Controllers.Main.txtShape_actionButtonBeginning": "Botão Início", "PE.Controllers.Main.txtShape_actionButtonBlank": "Botão vazio", "PE.Controllers.Main.txtShape_actionButtonDocument": "Botão Documento", "PE.Controllers.Main.txtShape_actionButtonEnd": "Botão Final", "PE.Controllers.Main.txtShape_actionButtonForwardNext": "Bot<PERSON><PERSON> e/ou Avançar", "PE.Controllers.Main.txtShape_actionButtonHelp": "Botão Ajuda", "PE.Controllers.Main.txtShape_actionButtonHome": "Botão Base", "PE.Controllers.Main.txtShape_actionButtonInformation": "Botão Informação", "PE.Controllers.Main.txtShape_actionButtonMovie": "Botão Filme", "PE.Controllers.Main.txtShape_actionButtonReturn": "Botão de Voltar", "PE.Controllers.Main.txtShape_actionButtonSound": "Botão Som", "PE.Controllers.Main.txtShape_arc": "Arco", "PE.Controllers.Main.txtShape_bentArrow": "Seta curvada", "PE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "PE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector de seta angular", "PE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector de seta dupla angulada", "PE.Controllers.Main.txtShape_bentUpArrow": "Seta para cima dobrada", "PE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_blockArc": "Arco de bloco", "PE.Controllers.Main.txtShape_borderCallout1": "Chamada da linha 1", "PE.Controllers.Main.txtShape_borderCallout2": "Chamada da linha 2", "PE.Controllers.Main.txtShape_borderCallout3": "Chamada da linha 3", "PE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_callout1": "Chamada da linha 1 (sem borda)", "PE.Controllers.Main.txtShape_callout2": "Chamada da linha 2 (sem borda)", "PE.Controllers.Main.txtShape_callout3": "Texto explicativo da linha 3 (sem borda)", "PE.Controllers.Main.txtShape_can": "Pode", "PE.Controllers.Main.txtShape_chevron": "Divisa", "PE.Controllers.Main.txtShape_chord": "Acorde", "PE.Controllers.Main.txtShape_circularArrow": "Seta circular", "PE.Controllers.Main.txtShape_cloud": "Nuvem", "PE.Controllers.Main.txtShape_cloudCallout": "Texto explicativo na nuvem", "PE.Controllers.Main.txtShape_corner": "Canto", "PE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "PE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector de seta curvada", "PE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector de seta dupla curvado", "PE.Controllers.Main.txtShape_curvedDownArrow": "Seta curvada para baixo", "PE.Controllers.Main.txtShape_curvedLeftArrow": "Seta curvada para a esquerda", "PE.Controllers.Main.txtShape_curvedRightArrow": "Seta curvava para a direita", "PE.Controllers.Main.txtShape_curvedUpArrow": "Seta curvada para cima", "PE.Controllers.Main.txtShape_decagon": "Decágono", "PE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "PE.Controllers.Main.txtShape_diamond": "Diamante", "PE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "PE.Controllers.Main.txtShape_donut": "Donut", "PE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_downArrow": "Seta para baixo", "PE.Controllers.Main.txtShape_downArrowCallout": "Chamada com seta para baixo", "PE.Controllers.Main.txtShape_ellipse": "Elipse", "PE.Controllers.Main.txtShape_ellipseRibbon": "Faixa curvada para baixo", "PE.Controllers.Main.txtShape_ellipseRibbon2": "Faixa curvada para cima", "PE.Controllers.Main.txtShape_flowChartAlternateProcess": "Fluxograma: Processo alternativo", "PE.Controllers.Main.txtShape_flowChartCollate": "Fluxograma: Agrupar", "PE.Controllers.Main.txtShape_flowChartConnector": "Fluxograma: Conector", "PE.Controllers.Main.txtShape_flowChartDecision": "Fluxograma: Decisão", "PE.Controllers.Main.txtShape_flowChartDelay": "Fluxograma: Atraso", "PE.Controllers.Main.txtShape_flowChartDisplay": "Fluxograma: Exibir", "PE.Controllers.Main.txtShape_flowChartDocument": "Fluxograma: Documento", "PE.Controllers.Main.txtShape_flowChartExtract": "Fluxograma: Extrair", "PE.Controllers.Main.txtShape_flowChartInputOutput": "Fluxograma: <PERSON><PERSON>", "PE.Controllers.Main.txtShape_flowChartInternalStorage": "Fluxograma: Armazenamento interno", "PE.Controllers.Main.txtShape_flowChartMagneticDisk": "Fluxograma: Disco magnético", "PE.Controllers.Main.txtShape_flowChartMagneticDrum": "Fluxograma: Armazenamento de acesso direto", "PE.Controllers.Main.txtShape_flowChartMagneticTape": "Fluxograma: Armazenamento de acesso sequencial", "PE.Controllers.Main.txtShape_flowChartManualInput": "Fluxograma: Entrada manual", "PE.Controllers.Main.txtShape_flowChartManualOperation": "Fluxograma: Operação manual", "PE.Controllers.Main.txtShape_flowChartMerge": "Fluxograma: Unir", "PE.Controllers.Main.txtShape_flowChartMultidocument": "Fluxograma: <PERSON><PERSON><PERSON><PERSON> documentos", "PE.Controllers.Main.txtShape_flowChartOffpageConnector": "Fluxograma: Conector fora da página", "PE.Controllers.Main.txtShape_flowChartOnlineStorage": "Fluxograma: Dad<PERSON> armaz<PERSON>", "PE.Controllers.Main.txtShape_flowChartOr": "Fluxograma: Ou", "PE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Fluxograma: Processo predefinido", "PE.Controllers.Main.txtShape_flowChartPreparation": "Fluxograma: Preparação", "PE.Controllers.Main.txtShape_flowChartProcess": "Fluxograma: Processo", "PE.Controllers.Main.txtShape_flowChartPunchedCard": "Fluxograma: Cartão", "PE.Controllers.Main.txtShape_flowChartPunchedTape": "Fluxograma: <PERSON><PERSON> perfurada", "PE.Controllers.Main.txtShape_flowChartSort": "Fluxograma: Ordenar", "PE.Controllers.Main.txtShape_flowChartSummingJunction": "Fluxograma: Junção de Soma", "PE.Controllers.Main.txtShape_flowChartTerminator": "Fluxograma: Terminator", "PE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON> do<PERSON>", "PE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_heart": "Coração", "PE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_hexagon": "Hexágono", "PE.Controllers.Main.txtShape_homePlate": "Pentágono", "PE.Controllers.Main.txtShape_horizontalScroll": "Deslocação horizontal", "PE.Controllers.Main.txtShape_irregularSeal1": "Explosão 1", "PE.Controllers.Main.txtShape_irregularSeal2": "Explosão 2", "PE.Controllers.Main.txtShape_leftArrow": "Seta para a esquerda", "PE.Controllers.Main.txtShape_leftArrowCallout": "Chamada com seta para a esquerda", "PE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_leftRightArrow": "Seta para a esquerda e para a direita", "PE.Controllers.Main.txtShape_leftRightArrowCallout": "Chamada com seta para a esquerda e direita", "PE.Controllers.Main.txtShape_leftRightUpArrow": "Seta para cima, para a direita e para a esquerda", "PE.Controllers.Main.txtShape_leftUpArrow": "Seta para a esquerda e para cima", "PE.Controllers.Main.txtShape_lightningBolt": "Relâmpago", "PE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON>a dupla", "PE.Controllers.Main.txtShape_mathDivide": "Divisão", "PE.Controllers.Main.txtShape_mathEqual": "Igual", "PE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "PE.Controllers.Main.txtShape_mathNotEqual": "Não é igual", "PE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_noSmoking": "Símbolo \"Não\"", "PE.Controllers.Main.txtShape_notchedRightArrow": "Seta entalhada para a direita", "PE.Controllers.Main.txtShape_octagon": "Octógono", "PE.Controllers.Main.txtShape_parallelogram": "Paralelograma", "PE.Controllers.Main.txtShape_pentagon": "Pentágono", "PE.Controllers.Main.txtShape_pie": "Tarte", "PE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "PE.Controllers.Main.txtShape_polyline1": "Rabisco", "PE.Controllers.Main.txtShape_polyline2": "Forma livre", "PE.Controllers.Main.txtShape_quadArrow": "<PERSON>a cruzada", "PE.Controllers.Main.txtShape_quadArrowCallout": "Chamada com seta cruzada", "PE.Controllers.Main.txtShape_rect": "Re<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_ribbon": "Faixa para baixo", "PE.Controllers.Main.txtShape_ribbon2": "Faixa para cima", "PE.Controllers.Main.txtShape_rightArrow": "Seta para a direita", "PE.Controllers.Main.txtShape_rightArrowCallout": "Chamada com seta para a direita", "PE.Controllers.Main.txtShape_rightBrace": "Chaveta à Direita", "PE.Controllers.Main.txtShape_rightBracket": "Parê<PERSON><PERSON> direito", "PE.Controllers.Main.txtShape_round1Rect": "Retângulo de Apenas Um Canto Redondo", "PE.Controllers.Main.txtShape_round2DiagRect": "Retângulo Diagonal Redondo", "PE.Controllers.Main.txtShape_round2SameRect": "Retângulo do Mesmo Lado Redondo", "PE.Controllers.Main.txtShape_roundRect": "Retângulo de Cantos Redondos", "PE.Controllers.Main.txtShape_rtTriangle": "Triângulo à Direita", "PE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_snip1Rect": "Retângulo de Canto Cortado", "PE.Controllers.Main.txtShape_snip2DiagRect": "Retângulo de Cantos Diagonais Cortados", "PE.Controllers.Main.txtShape_snip2SameRect": "Retângulo de Cantos Cortados No Mesmo Lado", "PE.Controllers.Main.txtShape_snipRoundRect": "Retângulo Com Canto Arredondado e Canto Cortado", "PE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtShape_star10": "Estrela de 10 pontos", "PE.Controllers.Main.txtShape_star12": "Estrela de 12 pontos", "PE.Controllers.Main.txtShape_star16": "Estrela de 16 pontos", "PE.Controllers.Main.txtShape_star24": "Estrela de 24 pontos", "PE.Controllers.Main.txtShape_star32": "Estrela de 32 pontos", "PE.Controllers.Main.txtShape_star4": "Estrela de 4 pontos", "PE.Controllers.Main.txtShape_star5": "Estrela de 5 pontos", "PE.Controllers.Main.txtShape_star6": "Estrela de 6 pontos", "PE.Controllers.Main.txtShape_star7": "Estrela de 7 pontos", "PE.Controllers.Main.txtShape_star8": "Estrela de 8 pontos", "PE.Controllers.Main.txtShape_stripedRightArrow": "Seta riscada para a direita", "PE.Controllers.Main.txtShape_sun": "Sol", "PE.Controllers.Main.txtShape_teardrop": "Lágrima", "PE.Controllers.Main.txtShape_textRect": "Caixa de texto", "PE.Controllers.Main.txtShape_trapezoid": "Trapé<PERSON>", "PE.Controllers.Main.txtShape_triangle": "Triângulo", "PE.Controllers.Main.txtShape_upArrow": "Seta para cima", "PE.Controllers.Main.txtShape_upArrowCallout": "Chamada com seta para cima", "PE.Controllers.Main.txtShape_upDownArrow": "Seta para cima e para baixo", "PE.Controllers.Main.txtShape_uturnArrow": "Seta em forma de U", "PE.Controllers.Main.txtShape_verticalScroll": "Deslocação vertical", "PE.Controllers.Main.txtShape_wave": "On<PERSON>", "PE.Controllers.Main.txtShape_wedgeEllipseCallout": "Texto explicativo oval", "PE.Controllers.Main.txtShape_wedgeRectCallout": "Texto explicativo retangular", "PE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON> retangular arredondada", "PE.Controllers.Main.txtSldLtTBlank": "Branco", "PE.Controllers.Main.txtSldLtTChart": "Gráfico", "PE.Controllers.Main.txtSldLtTChartAndTx": "Gráfico e Texto", "PE.Controllers.Main.txtSldLtTClipArtAndTx": "Clip Art e Texto", "PE.Controllers.Main.txtSldLtTClipArtAndVertTx": "Clip Art e Texto vertical", "PE.Controllers.Main.txtSldLtTCust": "Personalizado", "PE.Controllers.Main.txtSldLtTDgm": "Diagrama", "PE.Controllers.Main.txtSldLtTFourObj": "Quatro objetos", "PE.Controllers.Main.txtSldLtTMediaAndTx": "Multimédia e texto", "PE.Controllers.Main.txtSldLtTObj": "Título e Objeto", "PE.Controllers.Main.txtSldLtTObjAndTwoObj": "Objeto e Dois objetos", "PE.Controllers.Main.txtSldLtTObjAndTx": "Objeto e Texto", "PE.Controllers.Main.txtSldLtTObjOnly": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTObjOverTx": "Objeto sobre o texto", "PE.Controllers.Main.txtSldLtTObjTx": "<PERSON><PERSON><PERSON><PERSON>, objeto e legenda", "PE.Controllers.Main.txtSldLtTPicTx": "Imagem e Legenda", "PE.Controllers.Main.txtSldLtTSecHead": "Cabeçalho da seção", "PE.Controllers.Main.txtSldLtTTbl": "<PERSON><PERSON><PERSON>", "PE.Controllers.Main.txtSldLtTTitle": "Titulo", "PE.Controllers.Main.txtSldLtTTitleOnly": "<PERSON><PERSON><PERSON> tí<PERSON>", "PE.Controllers.Main.txtSldLtTTwoColTx": "Texto em duas colulas", "PE.Controllers.Main.txtSldLtTTwoObj": "<PERSON><PERSON> ob<PERSON>", "PE.Controllers.Main.txtSldLtTTwoObjAndObj": "Dois objetos e Objeto", "PE.Controllers.Main.txtSldLtTTwoObjAndTx": "Dois objetos e Texto", "PE.Controllers.Main.txtSldLtTTwoObjOverTx": "Dois objetos sobre Texto", "PE.Controllers.Main.txtSldLtTTwoTxTwoObj": "Dois textos e Dois objetos", "PE.Controllers.Main.txtSldLtTTx": "Тexto", "PE.Controllers.Main.txtSldLtTTxAndChart": "Texto e Gráfico", "PE.Controllers.Main.txtSldLtTTxAndClipArt": "Texto e Clip Art", "PE.Controllers.Main.txtSldLtTTxAndMedia": "Texto e multimédia", "PE.Controllers.Main.txtSldLtTTxAndObj": "Texto e Objeto", "PE.Controllers.Main.txtSldLtTTxAndTwoObj": "Texto e Dois objetos", "PE.Controllers.Main.txtSldLtTTxOverObj": "Texto sobre Objeto", "PE.Controllers.Main.txtSldLtTVertTitleAndTx": "Título vertical e Texto", "PE.Controllers.Main.txtSldLtTVertTitleAndTxOverChart": "Título vertical e Texto sobre gráfico", "PE.Controllers.Main.txtSldLtTVertTx": "Texto vertical", "PE.Controllers.Main.txtSlideNumber": "Número do diapositivo", "PE.Controllers.Main.txtSlideSubtitle": "Subtítulo do diapositivo", "PE.Controllers.Main.txtSlideText": "Texto do diapositivo", "PE.Controllers.Main.txtSlideTitle": "Título do diapositivo", "PE.Controllers.Main.txtStarsRibbons": "Estrelas e Faixas", "PE.Controllers.Main.txtTheme_basic": "Básico", "PE.Controllers.Main.txtTheme_blank": "Branco", "PE.Controllers.Main.txtTheme_classic": "Clássico", "PE.Controllers.Main.txtTheme_corner": "Canto", "PE.Controllers.Main.txtTheme_dotted": "Pontil<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_green": "Verde", "PE.Controllers.Main.txtTheme_green_leaf": "Folha verde", "PE.Controllers.Main.txtTheme_lines": "<PERSON><PERSON>", "PE.Controllers.Main.txtTheme_office": "Office", "PE.Controllers.Main.txtTheme_office_theme": "Tema do office", "PE.Controllers.Main.txtTheme_official": "Oficial", "PE.Controllers.Main.txtTheme_pixel": "Píxel", "PE.Controllers.Main.txtTheme_safari": "Safari", "PE.Controllers.Main.txtTheme_turtle": "Tartaruga", "PE.Controllers.Main.txtXAxis": "Eixo X", "PE.Controllers.Main.txtYAxis": "Eixo Y", "PE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> desconhecido.", "PE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>u navegador não é suportado.", "PE.Controllers.Main.uploadImageExtMessage": "Formato desconhecido.", "PE.Controllers.Main.uploadImageFileCountMessage": "<PERSON>enhuma imagem foi carregada.", "PE.Controllers.Main.uploadImageSizeMessage": "A imagem é demasiado grande. O tamanho máximo é de 25 MB.", "PE.Controllers.Main.uploadImageTextText": "A carregar imagem...", "PE.Controllers.Main.uploadImageTitleText": "A carregar imagem", "PE.Controllers.Main.waitText": "Aguarde...", "PE.Controllers.Main.warnBrowserIE9": "A aplicação não funciona corretamente com IE9. Deve utilizar IE10 ou superior", "PE.Controllers.Main.warnBrowserZoom": "A definição 'zoom' do seu navegador não é totalmente suportada. Prima Ctrl+0 para repor o valor padrão.", "PE.Controllers.Main.warnLicenseExceeded": "Atingiu o limite de ligações simultâneas a %1 editores. Este documento será aberto no modo de leitura.<br>Contacte o administrador para obter mais de<PERSON>hes.", "PE.Controllers.Main.warnLicenseExp": "A sua licença expirou.<br>Deve atualizar a licença e recarregar a página.", "PE.Controllers.Main.warnLicenseLimitedNoAccess": "Licença expirada.<br>Não pode editar o documento.<br><PERSON>r favor contacte o administrador de sistemas.", "PE.Controllers.Main.warnLicenseLimitedRenewed": "Tem que renovar a sua licença.<br>A edição de documentos está limitada.<br>Contacte o administrador de sistemas para obter acesso completo.", "PE.Controllers.Main.warnLicenseUsersExceeded": "Atingiu o limite de %1 editores. Contacte o seu administrador para obter detalhes.", "PE.Controllers.Main.warnNoLicense": "Atingiu o limite de ligações simultâneas a %1 editores. Este documento será aberto no modo de leitura.<br>Contacte a equipa comercial %1 para saber mais sobre os termos de licenciamento.", "PE.Controllers.Main.warnNoLicenseUsers": "Atingiu o limite de %1 editores. Contacte a equipa comercial %1 para obter mais informações.", "PE.Controllers.Main.warnProcessRightsChange": "Você não tem permissões para editar o ficheiro.", "PE.Controllers.Search.notcriticalErrorTitle": "Aviso", "PE.Controllers.Search.textNoTextFound": "Não foi possível localizar os dados procurados. Ajuste as opções de pesquisa.", "PE.Controllers.Search.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "PE.Controllers.Search.textReplaceSuccess": "A pesquisa foi concluída. {0} ocorrências foram substituídas", "PE.Controllers.Search.warnReplaceString": "{0} não é um carácter especial válido para a janela Substituir com.", "PE.Controllers.Statusbar.textDisconnect": "<b>Sem Ligação</b><br>A tentar ligar. Por favor, verifique as definições de ligação.", "PE.Controllers.Statusbar.zoomText": "Zoom {0}%", "PE.Controllers.Toolbar.confirmAddFontName": "O tipo de letra que vai guardar não está disponível no dispositivo atual.<br>O estilo de texto será apresentado utilizando um dos tipos de letra do sistema, o tipo de letra guardado será utilizado quando estiver disponível.<br>Quer continuar?", "PE.Controllers.Toolbar.textAccent": "Destaques", "PE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textEmptyImgUrl": "Tem que especificar o URL da imagem.", "PE.Controllers.Toolbar.textFontSizeErr": "O valor inserido não está correto.<br>Introduza um valor numérico entre 1 e 300.", "PE.Controllers.Toolbar.textFraction": "Frações", "PE.Controllers.Toolbar.textFunction": "Funções", "PE.Controllers.Toolbar.textInsert": "Inserir", "PE.Controllers.Toolbar.textIntegral": "Inteiros", "PE.Controllers.Toolbar.textLargeOperator": "Grandes operadores", "PE.Controllers.Toolbar.textLimitAndLog": "Limites e logaritmos", "PE.Controllers.Toolbar.textMatrix": "Matrizes", "PE.Controllers.Toolbar.textOperator": "Operadores", "PE.Controllers.Toolbar.textRadical": "Radicais", "PE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.textWarning": "Aviso", "PE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_ArrowD": "Seta para direita-esquerda acima", "PE.Controllers.Toolbar.txtAccent_ArrowL": "Seta adiante para cima", "PE.Controllers.Toolbar.txtAccent_ArrowR": "Seta para direita acima", "PE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "PE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "PE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "PE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON> embal<PERSON> (com Placeholder)", "PE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "F<PERSON>rmula embalada(Exemplo)", "PE.Controllers.Toolbar.txtAccent_Check": "Verificar", "PE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave <PERSON>", "PE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Chave Superior", "PE.Controllers.Toolbar.txtAccent_Custom_1": "Vetor A", "PE.Controllers.Toolbar.txtAccent_Custom_2": "Barra superior com ABC", "PE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y com barra superior", "PE.Controllers.Toolbar.txtAccent_DDDot": "Ponto <PERSON>lo", "PE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtAccent_Dot": "Ponto", "PE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra superior dupla", "PE.Controllers.Toolbar.txtAccent_Grave": "Grave", "PE.Controllers.Toolbar.txtAccent_GroupBot": "Agrupamento de caracteres abaixo", "PE.Controllers.Toolbar.txtAccent_GroupTop": "Agrupamento de caracteres acima", "PE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpão adiante para cima", "PE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpão para direita acima", "PE.Controllers.Toolbar.txtAccent_Hat": "Acento circunflexo", "PE.Controllers.Toolbar.txtAccent_Smile": "Breve", "PE.Controllers.Toolbar.txtAccent_Tilde": "Til", "PE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parênteses com separadores", "PE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parênteses com separadores", "PE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parênteses com separadores", "PE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (Duas Condições)", "PE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (Três Condições)", "PE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "PE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente binominal", "PE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binominal", "PE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parênteses com separadores", "PE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Colchete Simples", "PE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Colchete Simples", "PE.Controllers.Toolbar.txtFractionDiagonal": "Fração inclinada", "PE.Controllers.Toolbar.txtFractionDifferential_1": "Diferencial", "PE.Controllers.Toolbar.txtFractionDifferential_2": "Diferencial", "PE.Controllers.Toolbar.txtFractionDifferential_3": "Diferencial", "PE.Controllers.Toolbar.txtFractionDifferential_4": "Diferencial", "PE.Controllers.Toolbar.txtFractionHorizontal": "Fração linear", "PE.Controllers.Toolbar.txtFractionPi_2": "Pi sobre 2", "PE.Controllers.Toolbar.txtFractionSmall": "Fração pequena", "PE.Controllers.Toolbar.txtFractionVertical": "Fração Empilhada", "PE.Controllers.Toolbar.txtFunction_1_Cos": "Função cosseno inverso", "PE.Controllers.Toolbar.txtFunction_1_Cosh": "Função cosseno inverso hiperbólico", "PE.Controllers.Toolbar.txtFunction_1_Cot": "Função cotangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Coth": "Função cotangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Csc": "Função cossecante inversa", "PE.Controllers.Toolbar.txtFunction_1_Csch": "Função cossecante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sec": "Função secante inversa", "PE.Controllers.Toolbar.txtFunction_1_Sech": "Função secante inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_1_Sin": "Função seno inverso", "PE.Controllers.Toolbar.txtFunction_1_Sinh": "Função seno inverso hiperbólico", "PE.Controllers.Toolbar.txtFunction_1_Tan": "Função tangente inversa", "PE.Controllers.Toolbar.txtFunction_1_Tanh": "Função tangente inversa hiperbólica", "PE.Controllers.Toolbar.txtFunction_Cos": "Função cosseno", "PE.Controllers.Toolbar.txtFunction_Cosh": "Função cosseno hiperbólico", "PE.Controllers.Toolbar.txtFunction_Cot": "Função cotangente", "PE.Controllers.Toolbar.txtFunction_Coth": "Função cotangente hiperbólica", "PE.Controllers.Toolbar.txtFunction_Csc": "Função cossecante", "PE.Controllers.Toolbar.txtFunction_Csch": "Função co-secante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON>ta seno", "PE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "PE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON> da tangente", "PE.Controllers.Toolbar.txtFunction_Sec": "Função secante", "PE.Controllers.Toolbar.txtFunction_Sech": "Função secante hiperbólica", "PE.Controllers.Toolbar.txtFunction_Sin": "Função de seno", "PE.Controllers.Toolbar.txtFunction_Sinh": "Função seno hiperbólico", "PE.Controllers.Toolbar.txtFunction_Tan": "Função da tangente", "PE.Controllers.Toolbar.txtFunction_Tanh": "Função tangente hiperbólica", "PE.Controllers.Toolbar.txtIntegral": "Inteiro", "PE.Controllers.Toolbar.txtIntegral_dtheta": "Teta diferencial", "PE.Controllers.Toolbar.txtIntegral_dx": "Diferencial x", "PE.Controllers.Toolbar.txtIntegral_dy": "Diferencial y", "PE.Controllers.Toolbar.txtIntegralCenterSubSup": "Inteiro", "PE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtIntegralOriented": "Contorno integral", "PE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contorno integral", "PE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de Superfície", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de Superfície", "PE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de Superfície", "PE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contorno integral", "PE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de Volume", "PE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de Volume", "PE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de Volume", "PE.Controllers.Toolbar.txtIntegralSubSup": "Inteiro", "PE.Controllers.Toolbar.txtIntegralTriple": "Inteiro <PERSON>", "PE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Inteiro <PERSON>", "PE.Controllers.Toolbar.txtIntegralTripleSubSup": "Inteiro <PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Triangular", "PE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_1": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_2": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_3": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Custom_5": "União", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Intersection": "Interseção", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Interseção", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Interseção", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Interseção", "PE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Interseção", "PE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produ<PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "So<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtLargeOperator_Union": "União", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "União", "PE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "União", "PE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "União", "PE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "União", "PE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo limite", "PE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo máximo", "PE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "PE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "PE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "PE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "PE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON><PERSON> 1x2", "PE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON><PERSON> 1x3", "PE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON><PERSON> 2x1", "PE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON><PERSON> 2x2", "PE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz vazia com parênteses", "PE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Matriz vazia com parênteses", "PE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz vazia com parênteses", "PE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz vazia com parênteses", "PE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON><PERSON> 2x3", "PE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON><PERSON> Vazi<PERSON> 3x1", "PE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON><PERSON> 3x2", "PE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON><PERSON> 3x3", "PE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pontos de linha de base", "PE.Controllers.Toolbar.txtMatrix_Dots_Center": "Pontos de linha média", "PE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Pontos diagonais", "PE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Pontos verticais", "PE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa", "PE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa", "PE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON>riz da identidade 2x2", "PE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz da identidade 3x3", "PE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz da identidade 3x3", "PE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz da identidade 3x3", "PE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Seta para direita esquerda abaixo", "PE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Seta para direita-esquerda acima", "PE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Seta adiante para baixo", "PE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Seta adiante para cima", "PE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Seta para direita abaixo", "PE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Seta para direita acima", "PE.Controllers.Toolbar.txtOperator_ColonEquals": "Dois-pontos-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "PE.Controllers.Toolbar.txtOperator_Custom_2": "Resultados de Delta", "PE.Controllers.Toolbar.txtOperator_Definition": "Igual a por definição", "PE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Seta para direita esquerda abaixo", "PE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Seta para direita-esquerda acima", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Seta adiante para baixo", "PE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Seta adiante para cima", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Seta para direita abaixo", "PE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Seta para direita acima", "PE.Controllers.Toolbar.txtOperator_EqualsEquals": "Sinal de Igual-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_MinusEquals": "Sinal de Menos-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_PlusEquals": "Sinal de Mais-Sinal de Igual", "PE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "PE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "PE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "PE.Controllers.Toolbar.txtRadicalRoot_2": "Raiz quadrada com grau", "PE.Controllers.Toolbar.txtRadicalRoot_3": "Raiz cúbica", "PE.Controllers.Toolbar.txtRadicalRoot_n": "Radical com grau", "PE.Controllers.Toolbar.txtRadicalSqrt": "Raiz quadrada", "PE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtScriptSub": "Subscrito", "PE.Controllers.Toolbar.txtScriptSubSup": "Subscrito-Sobrescrito", "PE.Controllers.Toolbar.txtScriptSubSupLeft": "Subscrito-Sobrescrito Esquerdo", "PE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "PE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "PE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "PE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "PE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "PE.Controllers.Toolbar.txtSymbol_approx": "Quase igual a", "PE.Controllers.Toolbar.txtSymbol_ast": "Operador de asterisco", "PE.Controllers.Toolbar.txtSymbol_beta": "Beta", "PE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "PE.Controllers.Toolbar.txtSymbol_bullet": "Operador de marcador", "PE.Controllers.Toolbar.txtSymbol_cap": "Interseção", "PE.Controllers.Toolbar.txtSymbol_cbrt": "Raiz cúbica", "PE.Controllers.Toolbar.txtSymbol_cdots": "Reticências horizontais de linha média", "PE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "PE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "PE.Controllers.Toolbar.txtSymbol_cup": "União", "PE.Controllers.Toolbar.txtSymbol_ddots": "Reticências diagonal para baixo à direita", "PE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_delta": "Delta", "PE.Controllers.Toolbar.txtSymbol_div": "Sinal de divisão", "PE.Controllers.Toolbar.txtSymbol_downarrow": "Seta para baixo", "PE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vazio", "PE.Controllers.Toolbar.txtSymbol_epsilon": "Epsílon", "PE.Controllers.Toolbar.txtSymbol_equals": "Igual", "PE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON><PERSON> a", "PE.Controllers.Toolbar.txtSymbol_eta": "Eta", "PE.Controllers.Toolbar.txtSymbol_exists": "Existe", "PE.Controllers.Toolbar.txtSymbol_factorial": "Fatorial", "PE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON>us Fahrenheit", "PE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "PE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "PE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "PE.Controllers.Toolbar.txtSymbol_gg": "Muito superior a", "PE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "PE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "PE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "PE.Controllers.Toolbar.txtSymbol_infinity": "Infinidade", "PE.Controllers.Toolbar.txtSymbol_iota": "Iota", "PE.Controllers.Toolbar.txtSymbol_kappa": "Capa", "PE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "PE.Controllers.Toolbar.txtSymbol_leftarrow": "Seta para esquerda", "PE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Seta esquerda-direita", "PE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "PE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "PE.Controllers.Toolbar.txtSymbol_ll": "Muito inferior a", "PE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_mp": "Sinal de Menos-Sinal de <PERSON>", "PE.Controllers.Toolbar.txtSymbol_mu": "Mu", "PE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "PE.Controllers.Toolbar.txtSymbol_neq": "Não igual a", "PE.Controllers.Toolbar.txtSymbol_ni": "Contém como membro", "PE.Controllers.Toolbar.txtSymbol_not": "Não entrar", "PE.Controllers.Toolbar.txtSymbol_notexists": "Não existe", "PE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "PE.Controllers.Toolbar.txtSymbol_o": "Omicron", "PE.Controllers.Toolbar.txtSymbol_omega": "Ômega", "PE.Controllers.Toolbar.txtSymbol_partial": "Diferencial parcial", "PE.Controllers.Toolbar.txtSymbol_percent": "Porcentagem", "PE.Controllers.Toolbar.txtSymbol_phi": "Fi", "PE.Controllers.Toolbar.txtSymbol_pi": "Pi", "PE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_pm": "Sinal de Menos-Sinal de Igual", "PE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "PE.Controllers.Toolbar.txtSymbol_psi": "Psi", "PE.Controllers.Toolbar.txtSymbol_qdrt": "Raiz quadrada", "PE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> da prova", "PE.Controllers.Toolbar.txtSymbol_rddots": "Reticências diagonal direitas para cima", "PE.Controllers.Toolbar.txtSymbol_rho": "Rô", "PE.Controllers.Toolbar.txtSymbol_rightarrow": "Seta para direita", "PE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "PE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de Radical", "PE.Controllers.Toolbar.txtSymbol_tau": "Tau", "PE.Controllers.Toolbar.txtSymbol_therefore": "Portanto", "PE.Controllers.Toolbar.txtSymbol_theta": "Teta", "PE.Controllers.Toolbar.txtSymbol_times": "Sinal de multiplicação", "PE.Controllers.Toolbar.txtSymbol_uparrow": "Seta para cima", "PE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "PE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON> de Epsílon", "PE.Controllers.Toolbar.txtSymbol_varphi": "Variante de fi", "PE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "PE.Controllers.Toolbar.txtSymbol_vdots": "Reticências verticais", "PE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "PE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "PE.Controllers.Viewport.textFitPage": "Ajustar ao diapositivo", "PE.Controllers.Viewport.textFitWidth": "Ajustar à Largura", "PE.Views.Animation.str0_5": "0.5 s (<PERSON><PERSON>)", "PE.Views.Animation.str1": "1 s (<PERSON><PERSON><PERSON><PERSON>)", "PE.Views.Animation.str2": "2 s (M<PERSON><PERSON>)", "PE.Views.Animation.str20": "20 s (Extremamente Lenta)", "PE.Views.Animation.str3": "3 s (<PERSON><PERSON>)", "PE.Views.Animation.str5": "5 s (<PERSON><PERSON>)", "PE.Views.Animation.strDelay": "Atraso", "PE.Views.Animation.strDuration": "Duração", "PE.Views.Animation.strRepeat": "<PERSON><PERSON>r", "PE.Views.Animation.strRewind": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.strStart": "Iniciar", "PE.Views.Animation.strTrigger": "Acionador", "PE.Views.Animation.textAutoPreview": "Pré-visualização automática", "PE.Views.Animation.textMoreEffects": "<PERSON><PERSON>", "PE.Views.Animation.textMoveEarlier": "Mover mais cedo", "PE.Views.Animation.textMoveLater": "Mover mais tarde", "PE.Views.Animation.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Animation.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Animation.textNoRepeat": "(nenhum)", "PE.Views.Animation.textOnClickOf": "Ao clicar em", "PE.Views.Animation.textOnClickSequence": "Sequência ao Clicar", "PE.Views.Animation.textStartAfterPrevious": "<PERSON><PERSON><PERSON> da Anterior", "PE.Views.Animation.textStartOnClick": "Ao clicar", "PE.Views.Animation.textStartWithPrevious": "Com o anterior", "PE.Views.Animation.textUntilEndOfSlide": "Até ao Fim do Diapositivo", "PE.Views.Animation.textUntilNextClick": "Até ao Próximo Clique", "PE.Views.Animation.txtAddEffect": "Adicionar animação", "PE.Views.Animation.txtAnimationPane": "Painel da Animação", "PE.Views.Animation.txtParameters": "Parâmetros", "PE.Views.Animation.txtPreview": "Pré-visualizar", "PE.Views.Animation.txtSec": "s", "PE.Views.AnimationDialog.textPreviewEffect": "Pré-visualiza<PERSON>", "PE.Views.AnimationDialog.textTitle": "<PERSON><PERSON>", "PE.Views.ChartSettings.text3dDepth": "Profundidade (% da base)", "PE.Views.ChartSettings.text3dHeight": "Altura (% da base)", "PE.Views.ChartSettings.text3dRotation": "Rotação 3D", "PE.Views.ChartSettings.textAdvanced": "Mostrar definições avançadas", "PE.Views.ChartSettings.textAutoscale": "Ajuste automático", "PE.Views.ChartSettings.textChartType": "Alterar tipo de gráfico", "PE.Views.ChartSettings.textDefault": "Rotação padrão", "PE.Views.ChartSettings.textDown": "Para baixo", "PE.Views.ChartSettings.textEditData": "<PERSON><PERSON> dad<PERSON>", "PE.Views.ChartSettings.textHeight": "Altura", "PE.Views.ChartSettings.textKeepRatio": "Proporções constantes", "PE.Views.ChartSettings.textLeft": "E<PERSON>rda", "PE.Views.ChartSettings.textNarrow": "Campo de visão estreito", "PE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textRightAngle": "Eixos de ângulo reto", "PE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textUp": "Para cima", "PE.Views.ChartSettings.textWiden": "Ampliar o campo de visão", "PE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettings.textX": "Rotação X", "PE.Views.ChartSettings.textY": "Rotação Y", "PE.Views.ChartSettingsAdvanced.textAlt": "Texto Alternativo", "PE.Views.ChartSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.ChartSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "PE.Views.ChartSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textCenter": "Centro", "PE.Views.ChartSettingsAdvanced.textFrom": "De", "PE.Views.ChartSettingsAdvanced.textHeight": "Altura", "PE.Views.ChartSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ChartSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.ChartSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.ChartSettingsAdvanced.textPosition": "Posição", "PE.Views.ChartSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ChartSettingsAdvanced.textTitle": "Gráfico - Definições avançadas", "PE.Views.ChartSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.ChartSettingsAdvanced.textVertical": "Vertical", "PE.Views.ChartSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.DateTimeDialog.confirmDefault": "Definir formato predefinido para {0}: \"{1}\"", "PE.Views.DateTimeDialog.textDefault": "Definir como predefinido", "PE.Views.DateTimeDialog.textFormat": "Formatos", "PE.Views.DateTimeDialog.textLang": "Idioma", "PE.Views.DateTimeDialog.textUpdate": "Atualizar automaticamente", "PE.Views.DateTimeDialog.txtTitle": "Data e hora", "PE.Views.DocumentHolder.aboveText": "Acima", "PE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.addToLayoutText": "Adicionar à disposição", "PE.Views.DocumentHolder.advancedChartText": "Definições avançadas de gráfico", "PE.Views.DocumentHolder.advancedImageText": "Definições avançadas de imagem", "PE.Views.DocumentHolder.advancedParagraphText": "Configurações avançadas de parágrafo", "PE.Views.DocumentHolder.advancedShapeText": "Definições avançadas de forma", "PE.Views.DocumentHolder.advancedTableText": "Definições avançadas de tabela", "PE.Views.DocumentHolder.alignmentText": "Alinhamento", "PE.Views.DocumentHolder.belowText": "Abaixo", "PE.Views.DocumentHolder.cellAlignText": "Alinhamento vertical da célula", "PE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.centerText": "Centro", "PE.Views.DocumentHolder.columnText": "Coluna", "PE.Views.DocumentHolder.deleteColumnText": "Excluir coluna", "PE.Views.DocumentHolder.deleteRowText": "Excluir linha", "PE.Views.DocumentHolder.deleteTableText": "Eliminar tabela", "PE.Views.DocumentHolder.deleteText": "Excluir", "PE.Views.DocumentHolder.direct270Text": "Rodar texto para cima", "PE.Views.DocumentHolder.direct90Text": "Rotate at 90°", "PE.Views.DocumentHolder.directHText": "Horizontal", "PE.Views.DocumentHolder.directionText": "Text Direction", "PE.Views.DocumentHolder.editChartText": "<PERSON><PERSON> dad<PERSON>", "PE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.hyperlinkText": "Hiperligação", "PE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> tudo", "PE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "PE.Views.DocumentHolder.insertColumnRightText": "Coluna direita", "PE.Views.DocumentHolder.insertColumnText": "Inserir coluna", "PE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON> linha", "PE.Views.DocumentHolder.insertText": "Inserir", "PE.Views.DocumentHolder.langText": "Selecionar idioma", "PE.Views.DocumentHolder.leftText": "E<PERSON>rda", "PE.Views.DocumentHolder.loadSpellText": "A carregar variantes...", "PE.Views.DocumentHolder.mergeCellsText": "Unir <PERSON>", "PE.Views.DocumentHolder.mniCustomTable": "Inserir tabela personalizada", "PE.Views.DocumentHolder.moreText": "Mais variantes...", "PE.Views.DocumentHolder.noSpellVariantsText": "Sem varientes", "PE.Views.DocumentHolder.originalSizeText": "Tamanho real", "PE.Views.DocumentHolder.removeHyperlinkText": "Remover hiperligação", "PE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "PE.Views.DocumentHolder.selectText": "Selecionar", "PE.Views.DocumentHolder.spellcheckText": "Verificação ortográfica", "PE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON>...", "PE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textAddHGuides": "Adici<PERSON>r g<PERSON>", "PE.Views.DocumentHolder.textAddVGuides": "Adicionar gui<PERSON> vertical", "PE.Views.DocumentHolder.textArrangeBack": "Enviar para plano de fundo", "PE.Views.DocumentHolder.textArrangeBackward": "Enviar para trás", "PE.Views.DocumentHolder.textArrangeForward": "Trazer para frente", "PE.Views.DocumentHolder.textArrangeFront": "Trazer para primeiro plano", "PE.Views.DocumentHolder.textClearGuides": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textCm": "cm", "PE.Views.DocumentHolder.textCopy": "Copiar", "PE.Views.DocumentHolder.textCrop": "Cortar", "PE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textCropFit": "Ajustar", "PE.Views.DocumentHolder.textCustom": "Personalizado", "PE.Views.DocumentHolder.textCut": "Cortar", "PE.Views.DocumentHolder.textDeleteGuide": "Eliminar guia", "PE.Views.DocumentHolder.textDistributeCols": "Distribuir colunas", "PE.Views.DocumentHolder.textDistributeRows": "Distribuir linhas", "PE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "PE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "PE.Views.DocumentHolder.textFromFile": "De um ficheiro", "PE.Views.DocumentHolder.textFromStorage": "De um armazenamento", "PE.Views.DocumentHolder.textFromUrl": "De um URL", "PE.Views.DocumentHolder.textGridlines": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textGuides": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.textNextPage": "Diapositivo seguinte", "PE.Views.DocumentHolder.textPaste": "Colar", "PE.Views.DocumentHolder.textPrevPage": "Diapositivo anterior", "PE.Views.DocumentHolder.textReplace": "Substituir imagem", "PE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>", "PE.Views.DocumentHolder.textRotate270": "Rodar 90º à esquerda", "PE.Views.DocumentHolder.textRotate90": "Rodar 90º à direita", "PE.Views.DocumentHolder.textRulers": "Réguas", "PE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON><PERSON> em baixo", "PE.Views.DocumentHolder.textShapeAlignCenter": "Alinhar ao centro", "PE.Views.DocumentHolder.textShapeAlignLeft": "Alinhar à esquerda", "PE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON>ar ao meio", "PE.Views.DocumentHolder.textShapeAlignRight": "Alinhar à direita", "PE.Views.DocumentHolder.textShapeAlignTop": "Alinhar em cima", "PE.Views.DocumentHolder.textShowGridlines": "<PERSON><PERSON> lin<PERSON> da g<PERSON>ha", "PE.Views.DocumentHolder.textShowGuides": "<PERSON>rar guias", "PE.Views.DocumentHolder.textSlideSettings": "Definições de diapositivo", "PE.Views.DocumentHolder.textSmartGuides": "<PERSON><PERSON><PERSON> intelige<PERSON>", "PE.Views.DocumentHolder.textSnapObjects": "Ajustar objeto à grelha", "PE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.tipGuides": "<PERSON>rar guias", "PE.Views.DocumentHolder.tipIsLocked": "Este elemento está a ser editado por outro utilizador.", "PE.Views.DocumentHolder.toDictionaryText": "Incluir no Dicionário", "PE.Views.DocumentHolder.txtAddBottom": "Adicionar contorno inferior", "PE.Views.DocumentHolder.txtAddFractionBar": "Adicionar barra de fração", "PE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "PE.Views.DocumentHolder.txtAddLB": "Adicionar linha inferior esquerda", "PE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON> borda es<PERSON>", "PE.Views.DocumentHolder.txtAddLT": "Adicionar linha superior esquerda", "PE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>r borda direita", "PE.Views.DocumentHolder.txtAddTop": "Adicionar contorno superior", "PE.Views.DocumentHolder.txtAddVer": "Adicionar lin<PERSON>", "PE.Views.DocumentHolder.txtAlign": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> ao caractere", "PE.Views.DocumentHolder.txtArrange": "Dispor", "PE.Views.DocumentHolder.txtBackground": "Plano de fundo", "PE.Views.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON>a", "PE.Views.DocumentHolder.txtBottom": "Baixo", "PE.Views.DocumentHolder.txtChangeLayout": "Alterar disposição", "PE.Views.DocumentHolder.txtChangeTheme": "Alterar tema", "PE.Views.DocumentHolder.txtColumnAlign": "Alinhamento de colunas", "PE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> tamanho do argumento", "PE.Views.DocumentHolder.txtDeleteArg": "Excluir argumento", "PE.Views.DocumentHolder.txtDeleteBreak": "Excluir quebra manual", "PE.Views.DocumentHolder.txtDeleteChars": "Excluir caracteres anexos ", "PE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Excluir separadores e caracteres anexos", "PE.Views.DocumentHolder.txtDeleteEq": "Excluir equação", "PE.Views.DocumentHolder.txtDeleteGroupChar": "Excluir caractere", "PE.Views.DocumentHolder.txtDeleteRadical": "Excluir radical", "PE.Views.DocumentHolder.txtDeleteSlide": "Eliminar diapositivo", "PE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "PE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "PE.Views.DocumentHolder.txtDuplicateSlide": "Duplicar diapositivo", "PE.Views.DocumentHolder.txtFractionLinear": "Alterar para fração linear", "PE.Views.DocumentHolder.txtFractionSkewed": "Alterar para fração inclinada", "PE.Views.DocumentHolder.txtFractionStacked": "Alterar para fração empilhada", "PE.Views.DocumentHolder.txtGroup": "Grupo", "PE.Views.DocumentHolder.txtGroupCharOver": "Caractere sobre texto", "PE.Views.DocumentHolder.txtGroupCharUnder": "Caractere sob texto", "PE.Views.DocumentHolder.txtHideBottom": "Ocultar contorno inferior", "PE.Views.DocumentHolder.txtHideBottomLimit": "Ocultar limite inferior", "PE.Views.DocumentHolder.txtHideCloseBracket": "Ocultar colchete de fechamento", "PE.Views.DocumentHolder.txtHideDegree": "Ocultar grau", "PE.Views.DocumentHolder.txtHideHor": "Ocultar linha horizontal", "PE.Views.DocumentHolder.txtHideLB": "Ocultar linha inferior esquerda", "PE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON>r borda esquer<PERSON>", "PE.Views.DocumentHolder.txtHideLT": "Ocultar linha superior esquerda", "PE.Views.DocumentHolder.txtHideOpenBracket": "Ocultar colchete de abertura", "PE.Views.DocumentHolder.txtHidePlaceholder": "Ocultar espaço reservado", "PE.Views.DocumentHolder.txtHideRight": "O<PERSON>ltar borda direita", "PE.Views.DocumentHolder.txtHideTop": "Ocultar contorno superior", "PE.Views.DocumentHolder.txtHideTopLimit": "Ocultar limite superior", "PE.Views.DocumentHolder.txtHideVer": "Ocultar linha vertical", "PE.Views.DocumentHolder.txtIncreaseArg": "Aumentar tamanho do argumento", "PE.Views.DocumentHolder.txtInsertArgAfter": "Inserir argumento após", "PE.Views.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "PE.Views.DocumentHolder.txtInsertBreak": "Inserir quebra manual", "PE.Views.DocumentHolder.txtInsertEqAfter": "Inserir equação a seguir", "PE.Views.DocumentHolder.txtInsertEqBefore": "Inserir equação à frente", "PE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> apenas texto", "PE.Views.DocumentHolder.txtLimitChange": "Alterar localização de limites", "PE.Views.DocumentHolder.txtLimitOver": "Limite sobre o texto", "PE.Views.DocumentHolder.txtLimitUnder": "Limite sob o texto", "PE.Views.DocumentHolder.txtMatchBrackets": "Combinar parênteses com a altura do argumento", "PE.Views.DocumentHolder.txtMatrixAlign": "Alinhamento de matriz", "PE.Views.DocumentHolder.txtMoveSlidesToEnd": "Mover Diapositivo para o Fim", "PE.Views.DocumentHolder.txtMoveSlidesToStart": "Mover Diapositivo para o Início", "PE.Views.DocumentHolder.txtNewSlide": "Novo diapositivo", "PE.Views.DocumentHolder.txtOverbar": "Barra sobre texto", "PE.Views.DocumentHolder.txtPasteDestFormat": "U<PERSON><PERSON>r tema de destino", "PE.Views.DocumentHolder.txtPastePicture": "Imagem", "PE.Views.DocumentHolder.txtPasteSourceFormat": "Manter formatação original", "PE.Views.DocumentHolder.txtPressLink": "Prima {0} e clique na ligação", "PE.Views.DocumentHolder.txtPreview": "Iniciar ap<PERSON>ent<PERSON>", "PE.Views.DocumentHolder.txtPrintSelection": "Imprimir <PERSON>", "PE.Views.DocumentHolder.txtRemFractionBar": "Remover barra de fração", "PE.Views.DocumentHolder.txtRemLimit": "Remover limite", "PE.Views.DocumentHolder.txtRemoveAccentChar": "Remover caractere destacado", "PE.Views.DocumentHolder.txtRemoveBar": "Remover barra", "PE.Views.DocumentHolder.txtRemScripts": "Remover scripts", "PE.Views.DocumentHolder.txtRemSubscript": "Remover subscrito", "PE.Views.DocumentHolder.txtRemSuperscript": "Remover sobrescrito", "PE.Views.DocumentHolder.txtResetLayout": "Repor diapositivo", "PE.Views.DocumentHolder.txtScriptsAfter": "Scripts após o texto", "PE.Views.DocumentHolder.txtScriptsBefore": "Scripts antes do texto", "PE.Views.DocumentHolder.txtSelectAll": "Selecionar tudo", "PE.Views.DocumentHolder.txtShowBottomLimit": "Mostrar limite inferior", "PE.Views.DocumentHolder.txtShowCloseBracket": "Mostrar colchetes de fechamento", "PE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> gra<PERSON>", "PE.Views.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON> co<PERSON> de abertura", "PE.Views.DocumentHolder.txtShowPlaceholder": "Exibir espaço reservado", "PE.Views.DocumentHolder.txtShowTopLimit": "Mostrar limite superior", "PE.Views.DocumentHolder.txtSlide": "Diapositivo", "PE.Views.DocumentHolder.txtSlideHide": "Ocultar diapositivo", "PE.Views.DocumentHolder.txtStretchBrackets": "Esticar colchetes", "PE.Views.DocumentHolder.txtTop": "Cima", "PE.Views.DocumentHolder.txtUnderbar": "Barra abaixo de texto", "PE.Views.DocumentHolder.txtUngroup": "Desagrupar", "PE.Views.DocumentHolder.txtWarnUrl": "Clicar nesta ligação pode ser prejudicial ao seu dispositivo e dados.<br>Deseja continuar?", "PE.Views.DocumentHolder.vertAlignText": "Alinhamento vertical", "PE.Views.DocumentPreview.goToSlideText": "Ir para o diapositivo", "PE.Views.DocumentPreview.slideIndexText": "Diapositivo {0} de {1}", "PE.Views.DocumentPreview.txtClose": "<PERSON><PERSON><PERSON>", "PE.Views.DocumentPreview.txtEndSlideshow": "Terminar apresentação", "PE.Views.DocumentPreview.txtExitFullScreen": "Exit Full Screen", "PE.Views.DocumentPreview.txtFinalMessage": "Pré-visualização terminada. Clique para sair.", "PE.Views.DocumentPreview.txtFullScreen": "Full Screen", "PE.Views.DocumentPreview.txtNext": "Diapositivo seguinte", "PE.Views.DocumentPreview.txtPageNumInvalid": "Número <PERSON>", "PE.Views.DocumentPreview.txtPause": "Pausar apresentação", "PE.Views.DocumentPreview.txtPlay": "Iniciar ap<PERSON>ent<PERSON>", "PE.Views.DocumentPreview.txtPrev": "Diapositivo anterior", "PE.Views.DocumentPreview.txtReset": "Redefinir", "PE.Views.FileMenu.btnAboutCaption": "Sobre", "PE.Views.FileMenu.btnBackCaption": "Abrir localização", "PE.Views.FileMenu.btnCloseMenuCaption": "Fechar menu", "PE.Views.FileMenu.btnCreateNewCaption": "Criar novo", "PE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON><PERSON> como", "PE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnFileOpenCaption": "Abrir", "PE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenu.btnHistoryCaption": "Hist<PERSON><PERSON><PERSON> <PERSON> ve<PERSON>ão", "PE.Views.FileMenu.btnInfoCaption": "Informações de apresentação", "PE.Views.FileMenu.btnPrintCaption": "Imprimir", "PE.Views.FileMenu.btnProtectCaption": "Proteger", "PE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "PE.Views.FileMenu.btnRenameCaption": "<PERSON>dar nome", "PE.Views.FileMenu.btnReturnCaption": "Voltar para a apresentação", "PE.Views.FileMenu.btnRightsCaption": "Direitos de Acesso", "PE.Views.FileMenu.btnSaveAsCaption": "Save as", "PE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "PE.Views.FileMenu.btnSaveCopyAsCaption": "Guardar cópia como", "PE.Views.FileMenu.btnSettingsCaption": "Definições avançadas", "PE.Views.FileMenu.btnToEditCaption": "Editar apresent<PERSON>", "PE.Views.FileMenuPanels.CreateNew.txtBlank": "Apresentação em branco", "PE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Criar novo", "PE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Inclui o Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicação", "PE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "PE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Alterar direitos de acesso", "PE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificação por", "PE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificação", "PE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Localização", "PE.Views.FileMenuPanels.DocumentInfo.txtRights": "Pessoas que têm direitos", "PE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Carregado", "PE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Alterar direitos de acesso", "PE.Views.FileMenuPanels.DocumentRights.txtRights": "Pessoas que têm direitos", "PE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "PE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Com palavra-passe", "PE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger a apresentação", "PE.Views.FileMenuPanels.ProtectDoc.strSignature": "Com assinatura", "PE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar apresent<PERSON>", "PE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "A edição irá remover as assinaturas da apresentação.<br><PERSON><PERSON><PERSON><PERSON>?", "PE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "A apresentação está protegida com uma palavra-passe", "PE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Assinaturas adicionadas ao documento. Esta apresentação não pode ser editada.", "PE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algumas das assinaturas digitais são inválidas ou não puderam ser verificadas. Esta apresentação não pode ser editada.", "PE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver assinaturas", "PE.Views.FileMenuPanels.Settings.okButtonText": "Aplicar", "PE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modo de co-edição", "PE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.FileMenuPanels.Settings.strFontRender": "Dicas de tipo de letra", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorar palavras em MAÍSCULAS", "PE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignorar palavras com números", "PE.Views.FileMenuPanels.Settings.strMacrosSettings": "Definições de macros", "PE.Views.FileMenuPanels.Settings.strPasteButton": "Mostrar botão Opções de colagem ao colar conteúdo", "PE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Mostrar alterações de outros utilizadores", "PE.Views.FileMenuPanels.Settings.strStrict": "Strict", "PE.Views.FileMenuPanels.Settings.strTheme": "Tema de interface", "PE.Views.FileMenuPanels.Settings.strUnit": "Unidade de medida", "PE.Views.FileMenuPanels.Settings.strZoom": "Valor de zoom padrão", "PE.Views.FileMenuPanels.Settings.text10Minutes": "A cada 10 minutos", "PE.Views.FileMenuPanels.Settings.text30Minutes": "A cada 30 minutos", "PE.Views.FileMenuPanels.Settings.text5Minutes": "A cada 5 minutos", "PE.Views.FileMenuPanels.Settings.text60Minutes": "A cada hora", "PE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON> <PERSON>", "PE.Views.FileMenuPanels.Settings.textAutoRecover": "Recuperação automática", "PE.Views.FileMenuPanels.Settings.textAutoSave": "Salvamento automático", "PE.Views.FileMenuPanels.Settings.textDisabled": "Desabilitado", "PE.Views.FileMenuPanels.Settings.textForceSave": "Guardar versões intermédias", "PE.Views.FileMenuPanels.Settings.textMinute": "A cada minuto", "PE.Views.FileMenuPanels.Settings.txtAll": "Ver tudo", "PE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opções de correção automática...", "PE.Views.FileMenuPanels.Settings.txtCacheMode": "Modo de cache padrão", "PE.Views.FileMenuPanels.Settings.txtCm": "Centímetro", "PE.Views.FileMenuPanels.Settings.txtCollaboration": "Colaboração", "PE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editar e Guardar", "PE.Views.FileMenuPanels.Settings.txtFastTip": "Co-edição em tempo real. Todas as alterações são guardadas automaticamente", "PE.Views.FileMenuPanels.Settings.txtFitSlide": "Ajustar ao diapositivo", "PE.Views.FileMenuPanels.Settings.txtFitWidth": "Ajustar à Largura", "PE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hierogli<PERSON>s", "PE.Views.FileMenuPanels.Settings.txtInch": "Polegada", "PE.Views.FileMenuPanels.Settings.txtLast": "Ver último", "PE.Views.FileMenuPanels.Settings.txtMac": "como OS X", "PE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "PE.Views.FileMenuPanels.Settings.txtProofing": "Correção", "PE.Views.FileMenuPanels.Settings.txtPt": "Ponto", "PE.Views.FileMenuPanels.Settings.txtRunMacros": "Ativar tudo", "PE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros sem uma notificação", "PE.Views.FileMenuPanels.Settings.txtSpellCheck": "Verificação ortográfica", "PE.Views.FileMenuPanels.Settings.txtStopMacros": "Desativar tudo", "PE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "<PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "PE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilize o bot<PERSON> \"Guardar\" para sincronizar as alterações efetuadas", "PE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utilize a tecla 'Alt' para navegar na interface através do teclado", "PE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utilize a tecla 'Opção' para navegar na interface através do teclado", "PE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostrar notificação", "PE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros com uma notificação", "PE.Views.FileMenuPanels.Settings.txtWin": "como Windows", "PE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON>", "PE.Views.GridSettings.textCm": "cm", "PE.Views.GridSettings.textCustom": "Personalizado", "PE.Views.GridSettings.textSpacing": "Espaçamento", "PE.Views.GridSettings.textTitle": "Definições da grelha", "PE.Views.HeaderFooterDialog.applyAllText": "Aplicar a todos", "PE.Views.HeaderFooterDialog.applyText": "Aplicar", "PE.Views.HeaderFooterDialog.diffLanguage": "Não pode utilizar um formato de data que seja diferente do formato utilizado no modelo global.<br>Para alterar o modelo global, clique \"Aplicar a todos\" em vez de \"Aplicar\".", "PE.Views.HeaderFooterDialog.notcriticalErrorTitle": "Aviso", "PE.Views.HeaderFooterDialog.textDateTime": "Data e Hora", "PE.Views.HeaderFooterDialog.textFixed": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.HeaderFooterDialog.textFooter": "Texto no rodapé", "PE.Views.HeaderFooterDialog.textFormat": "Formatos", "PE.Views.HeaderFooterDialog.textLang": "Idioma", "PE.Views.HeaderFooterDialog.textNotTitle": "Não mostrar no diapositivo inicial", "PE.Views.HeaderFooterDialog.textPreview": "Pré-visualizar", "PE.Views.HeaderFooterDialog.textSlideNum": "Número do diapositivo", "PE.Views.HeaderFooterDialog.textTitle": "Definições de rodapé", "PE.Views.HeaderFooterDialog.textUpdate": "Atualizar automaticamente", "PE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON>", "PE.Views.HyperlinkSettingsDialog.strLinkTo": "Ligação a", "PE.Views.HyperlinkSettingsDialog.textDefault": "Fragmento de texto selecionado", "PE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Inserir legenda aqui", "PE.Views.HyperlinkSettingsDialog.textEmptyLink": "Introduzir ligação aqui", "PE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserir dica de ferramenta aqui", "PE.Views.HyperlinkSettingsDialog.textExternalLink": "Ligação externa", "PE.Views.HyperlinkSettingsDialog.textInternalLink": "Diapositivo nesta apresentação", "PE.Views.HyperlinkSettingsDialog.textSlides": "Diapositivos", "PE.Views.HyperlinkSettingsDialog.textTipText": "Texto de dica de tela:", "PE.Views.HyperlinkSettingsDialog.textTitle": "Definições de hiperligação", "PE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatório", "PE.Views.HyperlinkSettingsDialog.txtFirst": "Primeiro diapositivo", "PE.Views.HyperlinkSettingsDialog.txtLast": "Último diapositivo", "PE.Views.HyperlinkSettingsDialog.txtNext": "Diapositivo seguinte", "PE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "PE.Views.HyperlinkSettingsDialog.txtPrev": "Diapositivo anterior", "PE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres", "PE.Views.HyperlinkSettingsDialog.txtSlide": "Diapositivo", "PE.Views.ImageSettings.textAdvanced": "Mostrar definições avançadas", "PE.Views.ImageSettings.textCrop": "Cortar", "PE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textCropFit": "Ajustar", "PE.Views.ImageSettings.textCropToShape": "Recortar com Forma", "PE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "PE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "PE.Views.ImageSettings.textFitSlide": "Ajustar ao diapositivo", "PE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textFromFile": "De um ficheiro", "PE.Views.ImageSettings.textFromStorage": "De um armazenamento", "PE.Views.ImageSettings.textFromUrl": "De um URL", "PE.Views.ImageSettings.textHeight": "Altura", "PE.Views.ImageSettings.textHint270": "Rodar 90º à esquerda", "PE.Views.ImageSettings.textHint90": "Rodar 90º à direita", "PE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "PE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "PE.Views.ImageSettings.textInsert": "Substituir imagem", "PE.Views.ImageSettings.textOriginalSize": "Tamanho real", "PE.Views.ImageSettings.textRecentlyUsed": "Utilizado recentemente", "PE.Views.ImageSettings.textRotate90": "Rodar 90°", "PE.Views.ImageSettings.textRotation": "Rotação", "PE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAlt": "Texto Alternativo", "PE.Views.ImageSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.ImageSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "PE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textCenter": "Centro", "PE.Views.ImageSettingsAdvanced.textFlipped": "Invertido", "PE.Views.ImageSettingsAdvanced.textFrom": "De", "PE.Views.ImageSettingsAdvanced.textHeight": "Altura", "PE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.ImageSettingsAdvanced.textOriginalSize": "Tamanho real", "PE.Views.ImageSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.ImageSettingsAdvanced.textPosition": "Posição", "PE.Views.ImageSettingsAdvanced.textRotation": "Rotação", "PE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ImageSettingsAdvanced.textTitle": "Imagem - Definições avançadas", "PE.Views.ImageSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "PE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.tipAbout": "Sobre", "PE.Views.LeftMenu.tipChat": "Gráfico", "PE.Views.LeftMenu.tipComments": "Comentários", "PE.Views.LeftMenu.tipPlugins": "Plugins", "PE.Views.LeftMenu.tipSearch": "Pesquisa", "PE.Views.LeftMenu.tipSlides": "Diapositivos", "PE.Views.LeftMenu.tipSupport": "Feedback e Suporte", "PE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.LeftMenu.txtDeveloper": "MODO DE DESENVOLVEDOR", "PE.Views.LeftMenu.txtEditor": "Editor de apresentações", "PE.Views.LeftMenu.txtLimit": "Limitar o acesso", "PE.Views.LeftMenu.txtTrial": "MODO DE TESTE", "PE.Views.LeftMenu.txtTrialDev": "Versão de Avaliação do Modo de Programador", "PE.Views.ParagraphSettings.strLineHeight": "Espaçamento de linha", "PE.Views.ParagraphSettings.strParagraphSpacing": "Espaçamento", "PE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAdvanced": "Mostrar definições avançadas", "PE.Views.ParagraphSettings.textAt": "Em", "PE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "PE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettings.textExact": "Exatamente", "PE.Views.ParagraphSettings.txtAutoText": "Automático", "PE.Views.ParagraphSettingsAdvanced.noTabs": "Os separadores especificados aparecerão neste campo", "PE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON> ma<PERSON>", "PE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "PE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaçamento entre linhas", "PE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "após", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "PE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON> de letra", "PE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Avanços e espaçamento", "PE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSpacing": "Espaçamento", "PE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "PE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "PE.Views.ParagraphSettingsAdvanced.strTabs": "Separadores", "PE.Views.ParagraphSettingsAdvanced.textAlign": "Alinhamento", "PE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espaçamento entre caracteres", "PE.Views.ParagraphSettingsAdvanced.textDefault": "Separador predefinido", "PE.Views.ParagraphSettingsAdvanced.textEffects": "Efeitos", "PE.Views.ParagraphSettingsAdvanced.textExact": "Exatamente", "PE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira linha", "PE.Views.ParagraphSettingsAdvanced.textHanging": "Suspensão", "PE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "PE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nenhum)", "PE.Views.ParagraphSettingsAdvanced.textRemove": "Remover", "PE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remover todos", "PE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "PE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centro", "PE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "PE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posição da aba", "PE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "PE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Definições avançadas", "PE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "PE.Views.RightMenu.txtChartSettings": "Definições de gráfico", "PE.Views.RightMenu.txtImageSettings": "Definições de imagem", "PE.Views.RightMenu.txtParagraphSettings": "Configurações do parágrafo", "PE.Views.RightMenu.txtShapeSettings": "Definições de forma", "PE.Views.RightMenu.txtSignatureSettings": "Definições de assinatura", "PE.Views.RightMenu.txtSlideSettings": "Definições de diapositivo", "PE.Views.RightMenu.txtTableSettings": "Definições de tabela", "PE.Views.RightMenu.txtTextArtSettings": "Definições de texto artístico", "PE.Views.ShapeSettings.strBackground": "Cor de fundo", "PE.Views.ShapeSettings.strChange": "Alterar forma automática", "PE.Views.ShapeSettings.strColor": "Cor", "PE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strForeground": "Cor principal", "PE.Views.ShapeSettings.strPattern": "Padrão", "PE.Views.ShapeSettings.strShadow": "Mostrar sombra", "PE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "PE.Views.ShapeSettings.strTransparency": "Opacidade", "PE.Views.ShapeSettings.strType": "Tipo", "PE.Views.ShapeSettings.textAdvanced": "Mostrar definições avançadas", "PE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textBorderSizeErr": "O valor inserido não está correto.<br>Introduza um valor entre 0 pt e 1584 pt.", "PE.Views.ShapeSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "PE.Views.ShapeSettings.textDirection": "Direção", "PE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textFromFile": "De um ficheiro", "PE.Views.ShapeSettings.textFromStorage": "De um armazenamento", "PE.Views.ShapeSettings.textFromUrl": "De um URL", "PE.Views.ShapeSettings.textGradient": "Ponto de gradiente", "PE.Views.ShapeSettings.textGradientFill": "Preenchimento gradiente", "PE.Views.ShapeSettings.textHint270": "Rodar 90º à esquerda", "PE.Views.ShapeSettings.textHint90": "Rodar 90º à direita", "PE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "PE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "PE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "PE.Views.ShapeSettings.textLinear": "Linear", "PE.Views.ShapeSettings.textNoFill": "Sem preenchimento", "PE.Views.ShapeSettings.textPatternFill": "Padrão", "PE.Views.ShapeSettings.textPosition": "Posição", "PE.Views.ShapeSettings.textRadial": "Radial", "PE.Views.ShapeSettings.textRecentlyUsed": "Utilizado recentemente", "PE.Views.ShapeSettings.textRotate90": "Rodar 90°", "PE.Views.ShapeSettings.textRotation": "Rotação", "PE.Views.ShapeSettings.textSelectImage": "Selecionar imagem", "PE.Views.ShapeSettings.textSelectTexture": "Selecionar", "PE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "PE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.textTexture": "De uma textura", "PE.Views.ShapeSettings.textTile": "Lado a lado", "PE.Views.ShapeSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "PE.Views.ShapeSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "PE.Views.ShapeSettings.txtBrownPaper": "<PERSON>pel pardo", "PE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtCarton": "Papelão", "PE.Views.ShapeSettings.txtDarkFabric": "Tela escura", "PE.Views.ShapeSettings.txtGrain": "Granulação", "PE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtGreyPaper": "Papel cinza", "PE.Views.ShapeSettings.txtKnit": "Unir", "PE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> linha", "PE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettings.txtWood": "Madeira", "PE.Views.ShapeSettingsAdvanced.strColumns": "Colunas", "PE.Views.ShapeSettingsAdvanced.strMargins": "Preenchimento de texto", "PE.Views.ShapeSettingsAdvanced.textAlt": "Texto Alternativo", "PE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.ShapeSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "PE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textArrows": "Set<PERSON>", "PE.Views.ShapeSettingsAdvanced.textAutofit": "Ajuste automático", "PE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> inicial", "PE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "PE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textBottom": "Baixo", "PE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "PE.Views.ShapeSettingsAdvanced.textCenter": "Centro", "PE.Views.ShapeSettingsAdvanced.textColNumber": "Número de <PERSON>nas", "PE.Views.ShapeSettingsAdvanced.textEndSize": "Tamanho final", "PE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "PE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "PE.Views.ShapeSettingsAdvanced.textFlipped": "Invertido", "PE.Views.ShapeSettingsAdvanced.textFrom": "De", "PE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "PE.Views.ShapeSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "PE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de junção", "PE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.ShapeSettingsAdvanced.textLeft": "E<PERSON>rda", "PE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textMiter": "Malhete", "PE.Views.ShapeSettingsAdvanced.textNofit": "Não ajustar automaticamente.", "PE.Views.ShapeSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.ShapeSettingsAdvanced.textPosition": "Posição", "PE.Views.ShapeSettingsAdvanced.textResizeFit": "Redimensionar forma para se ajustar ao texto", "PE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textRotation": "Rotação", "PE.Views.ShapeSettingsAdvanced.textRound": "Rodada", "PE.Views.ShapeSettingsAdvanced.textShrink": "Reduzir o texto ao transbordar", "PE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.textSpacing": "Espaçamento entre colunas", "PE.Views.ShapeSettingsAdvanced.textSquare": "Quadrado", "PE.Views.ShapeSettingsAdvanced.textTextBox": "Caixa de texto", "PE.Views.ShapeSettingsAdvanced.textTitle": "Forma - Definições avançadas", "PE.Views.ShapeSettingsAdvanced.textTop": "Cima", "PE.Views.ShapeSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.ShapeSettingsAdvanced.textVertical": "Vertical", "PE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "PE.Views.ShapeSettingsAdvanced.textWeightArrows": "Pesos e Setas", "PE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.ShapeSettingsAdvanced.txtNone": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "PE.Views.SignatureSettings.strDelete": "Remover assinatura", "PE.Views.SignatureSettings.strDetails": "Detalhes da assinatura", "PE.Views.SignatureSettings.strInvalid": "Assinaturas inválidas", "PE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "PE.Views.SignatureSettings.strSignature": "Assinatura", "PE.Views.SignatureSettings.strValid": "Assinaturas válidas", "PE.Views.SignatureSettings.txtContinueEditing": "<PERSON>ar mesmo assim", "PE.Views.SignatureSettings.txtEditWarning": "A edição irá remover as assinaturas da apresentação.<br><PERSON><PERSON><PERSON><PERSON>?", "PE.Views.SignatureSettings.txtRemoveWarning": "Quer remover esta assinatura?<br><PERSON><PERSON> não pode ser anulado.", "PE.Views.SignatureSettings.txtSigned": "Assinaturas adicionadas ao documento. Esta apresentação não pode ser editada.", "PE.Views.SignatureSettings.txtSignedInvalid": "Algumas das assinaturas digitais são inválidas ou não puderam ser verificadas. Esta apresentação não pode ser editada.", "PE.Views.SlideSettings.strBackground": "Cor de fundo", "PE.Views.SlideSettings.strColor": "Cor", "PE.Views.SlideSettings.strDateTime": "Mostrar data e tempo", "PE.Views.SlideSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.strForeground": "Cor principal", "PE.Views.SlideSettings.strPattern": "Padrão", "PE.Views.SlideSettings.strSlideNum": "Mostrar número do diapositivo", "PE.Views.SlideSettings.strTransparency": "Opacidade", "PE.Views.SlideSettings.textAdvanced": "Mostrar definições avançadas", "PE.Views.SlideSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "PE.Views.SlideSettings.textDirection": "Direção", "PE.Views.SlideSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.SlideSettings.textFromFile": "De um ficheiro", "PE.Views.SlideSettings.textFromStorage": "De um armazenamento", "PE.Views.SlideSettings.textFromUrl": "De um URL", "PE.Views.SlideSettings.textGradient": "Ponto de gradiente", "PE.Views.SlideSettings.textGradientFill": "Preenchimento gradiente", "PE.Views.SlideSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "PE.Views.SlideSettings.textLinear": "Linear", "PE.Views.SlideSettings.textNoFill": "Sem preenchimento", "PE.Views.SlideSettings.textPatternFill": "Padrão", "PE.Views.SlideSettings.textPosition": "Posição", "PE.Views.SlideSettings.textRadial": "Radial", "PE.Views.SlideSettings.textReset": "Resetar alterações", "PE.Views.SlideSettings.textSelectImage": "Selecionar imagem", "PE.Views.SlideSettings.textSelectTexture": "Selecionar", "PE.Views.SlideSettings.textStretch": "<PERSON><PERSON>", "PE.Views.SlideSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.textTexture": "De uma textura", "PE.Views.SlideSettings.textTile": "Lado a lado", "PE.Views.SlideSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "PE.Views.SlideSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "PE.Views.SlideSettings.txtBrownPaper": "<PERSON>pel pardo", "PE.Views.SlideSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtCarton": "Papelão", "PE.Views.SlideSettings.txtDarkFabric": "Tela escura", "PE.Views.SlideSettings.txtGrain": "Granulação", "PE.Views.SlideSettings.txtGranite": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtGreyPaper": "Papel cinza", "PE.Views.SlideSettings.txtKnit": "Unir", "PE.Views.SlideSettings.txtLeather": "<PERSON><PERSON>", "PE.Views.SlideSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSettings.txtWood": "Madeira", "PE.Views.SlideshowSettings.textLoop": "Loop contínuo até \"Esc\" ser pressionado", "PE.Views.SlideshowSettings.textTitle": "Mostrar definições", "PE.Views.SlideSizeSettings.strLandscape": "Horizontal", "PE.Views.SlideSizeSettings.strPortrait": "Vertical", "PE.Views.SlideSizeSettings.textHeight": "Altura", "PE.Views.SlideSizeSettings.textSlideOrientation": "Orientação do diapositivo", "PE.Views.SlideSizeSettings.textSlideSize": "Tamanho do diapositivo", "PE.Views.SlideSizeSettings.textTitle": "Definições para o tamanho do diapositivo", "PE.Views.SlideSizeSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.SlideSizeSettings.txt35": "Diapositivos de 35 mm", "PE.Views.SlideSizeSettings.txtA3": "Papel A3 (297x420 mm)", "PE.Views.SlideSizeSettings.txtA4": "Papel A4 (210x297 mm)", "PE.Views.SlideSizeSettings.txtB4": "Papel B4 (ICO) (250x353 mm)", "PE.Views.SlideSizeSettings.txtB5": "Papel B5 (ICO) (176x250 mm)", "PE.Views.SlideSizeSettings.txtBanner": "Banner", "PE.Views.SlideSizeSettings.txtCustom": "Personalizado", "PE.Views.SlideSizeSettings.txtLedger": "<PERSON><PERSON>(11x17 in)", "PE.Views.SlideSizeSettings.txtLetter": "Papel carta (8,5x11 pol)", "PE.Views.SlideSizeSettings.txtOverhead": "Transparência", "PE.Views.SlideSizeSettings.txtStandard": "<PERSON><PERSON><PERSON> (4:3)", "PE.Views.SlideSizeSettings.txtWidescreen": "Panorâmico", "PE.Views.Statusbar.goToPageText": "Ir para o diapositivo", "PE.Views.Statusbar.pageIndexText": "Diapositivo {0} de {1}", "PE.Views.Statusbar.textShowBegin": "Mostrar do início", "PE.Views.Statusbar.textShowCurrent": "Mostrar a partir do diapositivo atual", "PE.Views.Statusbar.textShowPresenterView": "Mostrar vista de apresentador", "PE.Views.Statusbar.tipAccessRights": "Manage document access rights", "PE.Views.Statusbar.tipFitPage": "Ajustar ao diapositivo", "PE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON><PERSON> largura", "PE.Views.Statusbar.tipPreview": "Iniciar ap<PERSON>ent<PERSON>", "PE.Views.Statusbar.tipSetLang": "Definir idioma do texto", "PE.Views.Statusbar.tipZoomFactor": "Ampliação", "PE.Views.Statusbar.tipZoomIn": "Ampliar", "PE.Views.Statusbar.tipZoomOut": "Reduzir", "PE.Views.Statusbar.txtPageNumInvalid": "Número <PERSON>", "PE.Views.TableSettings.deleteColumnText": "Excluir coluna", "PE.Views.TableSettings.deleteRowText": "Excluir linha", "PE.Views.TableSettings.deleteTableText": "Eliminar tabela", "PE.Views.TableSettings.insertColumnLeftText": "Inserir coluna à esquerda", "PE.Views.TableSettings.insertColumnRightText": "Inserir coluna à direita", "PE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> linha acima", "PE.Views.TableSettings.insertRowBelowText": "<PERSON>ser<PERSON> linha a<PERSON>o", "PE.Views.TableSettings.mergeCellsText": "Unir <PERSON>", "PE.Views.TableSettings.selectCellText": "Selecionar célula", "PE.Views.TableSettings.selectColumnText": "Selecionar coluna", "PE.Views.TableSettings.selectRowText": "Selecionar linha", "PE.Views.TableSettings.selectTableText": "Selecionar tabela", "PE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON>...", "PE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textAdvanced": "Mostrar definições avançadas", "PE.Views.TableSettings.textBackColor": "Cor de fundo", "PE.Views.TableSettings.textBanded": "Em tiras", "PE.Views.TableSettings.textBorderColor": "Cor", "PE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textColumns": "Colunas", "PE.Views.TableSettings.textDistributeCols": "Distribuir colunas", "PE.Views.TableSettings.textDistributeRows": "Distribuir linhas", "PE.Views.TableSettings.textEdit": "Linhas e Colunas", "PE.Views.TableSettings.textEmptyTemplate": "Sem modelos", "PE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "PE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettings.textHeight": "Altura", "PE.Views.TableSettings.textLast": "Última", "PE.Views.TableSettings.textRows": "<PERSON><PERSON>", "PE.Views.TableSettings.textSelectBorders": "Selecione os contornos aos quais pretende aplicar o estilo escolhido", "PE.Views.TableSettings.textTemplate": "Selecionar de um modelo", "PE.Views.TableSettings.textTotal": "Total", "PE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.tipAll": "Definir contorno externo e todas as linhas internas", "PE.Views.TableSettings.tipBottom": "Definir apenas contorno inferior externo", "PE.Views.TableSettings.tipInner": "Definir apenas linhas internas", "PE.Views.TableSettings.tipInnerHor": "Definir apenas linhas internas horizontais", "PE.Views.TableSettings.tipInnerVert": "Definir apenas linhas internas verticais", "PE.Views.TableSettings.tipLeft": "Definir apenas contorno esquerdo externo", "PE.Views.TableSettings.tipNone": "Definir sem bordas", "PE.Views.TableSettings.tipOuter": "Definir apenas contorno externo", "PE.Views.TableSettings.tipRight": "Definir apenas contorno direito externo", "PE.Views.TableSettings.tipTop": "Definir apenas contorno superior externo", "PE.Views.TableSettings.txtGroupTable_Custom": "Personalizado", "PE.Views.TableSettings.txtGroupTable_Dark": "Escuro", "PE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtGroupTable_Medium": "Médio", "PE.Views.TableSettings.txtGroupTable_Optimal": "Melhor correspondência para documento", "PE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON> bordas", "PE.Views.TableSettings.txtTable_Accent": "Sotaque", "PE.Views.TableSettings.txtTable_DarkStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_LightStyle": "<PERSON><PERSON><PERSON> claro", "PE.Views.TableSettings.txtTable_MediumStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettings.txtTable_NoGrid": "<PERSON><PERSON> g<PERSON>", "PE.Views.TableSettings.txtTable_NoStyle": "Sem estilo", "PE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON><PERSON>a", "PE.Views.TableSettings.txtTable_ThemedStyle": "Estilo com Tema", "PE.Views.TableSettingsAdvanced.textAlt": "Texto Alternativo", "PE.Views.TableSettingsAdvanced.textAltDescription": "Descrição", "PE.Views.TableSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "PE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textBottom": "Baixo", "PE.Views.TableSettingsAdvanced.textCenter": "Centro", "PE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON>ar margens pad<PERSON>", "PE.Views.TableSettingsAdvanced.textDefaultMargins": "Margens padrão", "PE.Views.TableSettingsAdvanced.textFrom": "De", "PE.Views.TableSettingsAdvanced.textHeight": "Altura", "PE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "PE.Views.TableSettingsAdvanced.textKeepRatio": "Proporções constantes", "PE.Views.TableSettingsAdvanced.textLeft": "E<PERSON>rda", "PE.Views.TableSettingsAdvanced.textMargins": "Margens da célula", "PE.Views.TableSettingsAdvanced.textPlacement": "Posicionamento", "PE.Views.TableSettingsAdvanced.textPosition": "Posição", "PE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textTitle": "Tabela - Definições avançadas", "PE.Views.TableSettingsAdvanced.textTop": "Cima", "PE.Views.TableSettingsAdvanced.textTopLeftCorner": "Canto superior esquerdo", "PE.Views.TableSettingsAdvanced.textVertical": "Vertical", "PE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "PE.Views.TableSettingsAdvanced.textWidthSpaces": "Margens", "PE.Views.TextArtSettings.strBackground": "Cor de fundo", "PE.Views.TextArtSettings.strColor": "Cor", "PE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strForeground": "Cor principal", "PE.Views.TextArtSettings.strPattern": "Pattern", "PE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "PE.Views.TextArtSettings.strTransparency": "Opacidade", "PE.Views.TextArtSettings.strType": "Tipo", "PE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textBorderSizeErr": "O valor inserido não está correto.<br>Introduza um valor entre 0 pt e 1584 pt.", "PE.Views.TextArtSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "PE.Views.TextArtSettings.textDirection": "Direção", "PE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textFromFile": "De um ficheiro", "PE.Views.TextArtSettings.textFromUrl": "De um URL", "PE.Views.TextArtSettings.textGradient": "Ponto de gradiente", "PE.Views.TextArtSettings.textGradientFill": "Preenchimento gradiente", "PE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "PE.Views.TextArtSettings.textLinear": "Linear", "PE.Views.TextArtSettings.textNoFill": "Sem preenchimento", "PE.Views.TextArtSettings.textPatternFill": "Pattern", "PE.Views.TextArtSettings.textPosition": "Posição", "PE.Views.TextArtSettings.textRadial": "Radial", "PE.Views.TextArtSettings.textSelectTexture": "Selecionar", "PE.Views.TextArtSettings.textStretch": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "PE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON>", "PE.Views.TextArtSettings.textTexture": "De uma textura", "PE.Views.TextArtSettings.textTile": "Lado a lado", "PE.Views.TextArtSettings.textTransform": "Transform", "PE.Views.TextArtSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "PE.Views.TextArtSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "PE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "PE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "PE.Views.TextArtSettings.txtDarkFabric": "Tela escura", "PE.Views.TextArtSettings.txtGrain": "Grain", "PE.Views.TextArtSettings.txtGranite": "Granite", "PE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "PE.Views.TextArtSettings.txtKnit": "Unir", "PE.Views.TextArtSettings.txtLeather": "Leather", "PE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> linha", "PE.Views.TextArtSettings.txtPapyrus": "Papyrus", "PE.Views.TextArtSettings.txtWood": "<PERSON>", "PE.Views.Toolbar.capAddSlide": "Adicionar diapositivo", "PE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capBtnDateTime": "Data e hora", "PE.Views.Toolbar.capBtnInsHeader": "Rodapé", "PE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "PE.Views.Toolbar.capBtnSlideNum": "Número do diapositivo", "PE.Views.Toolbar.capInsertAudio": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertChart": "Gráfico", "PE.Views.Toolbar.capInsertEquation": "Equação", "PE.Views.Toolbar.capInsertHyperlink": "Hiperligação", "PE.Views.Toolbar.capInsertImage": "Imagem", "PE.Views.Toolbar.capInsertShape": "Forma", "PE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capInsertText": "Caixa de texto", "PE.Views.Toolbar.capInsertTextArt": "Lágrima", "PE.Views.Toolbar.capInsertVideo": "Vídeo", "PE.Views.Toolbar.capTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.capTabHome": "Base", "PE.Views.Toolbar.capTabInsert": "Inserir", "PE.Views.Toolbar.mniCapitalizeWords": "Capitalizar cada palavra", "PE.Views.Toolbar.mniCustomTable": "Inserir tabela personalizada", "PE.Views.Toolbar.mniImageFromFile": "Imagem de um ficheiro", "PE.Views.Toolbar.mniImageFromStorage": "Imagem de um armazenamento", "PE.Views.Toolbar.mniImageFromUrl": "Imagem de um URL", "PE.Views.Toolbar.mniInsertSSE": "Inserir <PERSON> de Cálculo", "PE.Views.Toolbar.mniLowerCase": "minúscula", "PE.Views.Toolbar.mniSentenceCase": "Maiúscula no Início da frase.", "PE.Views.Toolbar.mniSlideAdvanced": "Definições avançadas", "PE.Views.Toolbar.mniSlideStandard": "<PERSON><PERSON><PERSON> (4:3)", "PE.Views.Toolbar.mniSlideWide": "Widescreen (16:9)", "PE.Views.Toolbar.mniToggleCase": "iNVERTER mAIÚSCULAS/mINÚSCULAS", "PE.Views.Toolbar.mniUpperCase": "MAIÚSCULAS", "PE.Views.Toolbar.strMenuNoFill": "Sem preenchimento", "PE.Views.Toolbar.textAlignBottom": "Alinhar texto à parte inferior", "PE.Views.Toolbar.textAlignCenter": "Centralizar texto", "PE.Views.Toolbar.textAlignJust": "Justificar", "PE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON>ar texto à esquerda", "PE.Views.Toolbar.textAlignMiddle": "Alinhar texto ao meio", "PE.Views.Toolbar.textAlignRight": "Alinhar texto à direita", "PE.Views.Toolbar.textAlignTop": "Alinhar texto à parte superior", "PE.Views.Toolbar.textArrangeBack": "Enviar para plano de fundo", "PE.Views.Toolbar.textArrangeBackward": "Enviar para trás", "PE.Views.Toolbar.textArrangeForward": "Trazer para frente", "PE.Views.Toolbar.textArrangeFront": "Trazer para primeiro plano", "PE.Views.Toolbar.textBold": "Negrito", "PE.Views.Toolbar.textColumnsCustom": "Colunas personalizadas", "PE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON>", "PE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textColumnsTwo": "Duas <PERSON>", "PE.Views.Toolbar.textItalic": "Itálico", "PE.Views.Toolbar.textListSettings": "Definições da lista", "PE.Views.Toolbar.textRecentlyUsed": "Utilizado recentemente", "PE.Views.Toolbar.textShapeAlignBottom": "<PERSON><PERSON><PERSON> em baixo", "PE.Views.Toolbar.textShapeAlignCenter": "Alinhar ao centro", "PE.Views.Toolbar.textShapeAlignLeft": "Alinhar à esquerda", "PE.Views.Toolbar.textShapeAlignMiddle": "<PERSON><PERSON>ar ao meio", "PE.Views.Toolbar.textShapeAlignRight": "Alinhar à direita", "PE.Views.Toolbar.textShapeAlignTop": "Alinhar em cima", "PE.Views.Toolbar.textShowBegin": "Mostrar do início", "PE.Views.Toolbar.textShowCurrent": "Mostrar a partir do diapositivo atual", "PE.Views.Toolbar.textShowPresenterView": "Mostrar vista de apresentador", "PE.Views.Toolbar.textShowSettings": "Mostrar definições", "PE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textSubscript": "Subscrito", "PE.Views.Toolbar.textSuperscript": "Sobrescrito", "PE.Views.Toolbar.textTabAnimation": "Animação", "PE.Views.Toolbar.textTabCollaboration": "Colaboração", "PE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.textTabHome": "Base", "PE.Views.Toolbar.textTabInsert": "Inserir", "PE.Views.Toolbar.textTabProtect": "Proteção", "PE.Views.Toolbar.textTabTransitions": "Transições", "PE.Views.Toolbar.textTabView": "Visualizar", "PE.Views.Toolbar.textTitleError": "Erro", "PE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipAddSlide": "Adicionar diapositivo", "PE.Views.Toolbar.tipBack": "Voltar", "PE.Views.Toolbar.tipChangeCase": "Alternar maiúscula/minúscula", "PE.Views.Toolbar.tipChangeChart": "Alterar tipo de gráfico", "PE.Views.Toolbar.tipChangeSlide": "Alterar disposição do diapositivo", "PE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON> estilo", "PE.Views.Toolbar.tipColorSchemas": "Alterar esquema de cor", "PE.Views.Toolbar.tipColumns": "Inserir colunas", "PE.Views.Toolbar.tipCopy": "Copiar", "PE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "PE.Views.Toolbar.tipCut": "Cortar", "PE.Views.Toolbar.tipDateTime": "Insira a data e hora atual", "PE.Views.Toolbar.tipDecFont": "Di<PERSON><PERSON>r tamanho do tipo de letra", "PE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON> recuo", "PE.Views.Toolbar.tipEditHeader": "<PERSON><PERSON>", "PE.Views.Toolbar.tipFontColor": "Cor do tipo de letra", "PE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON> de letra", "PE.Views.Toolbar.tipFontSize": "Tamanho do tipo de letra", "PE.Views.Toolbar.tipHAligh": "Alinhamento horizontal", "PE.Views.Toolbar.tipHighlightColor": "<PERSON>r <PERSON>", "PE.Views.Toolbar.tipIncFont": "Aumentar tamanho do tipo de letra", "PE.Views.Toolbar.tipIncPrLeft": "Aumentar recuo", "PE.Views.Toolbar.tipInsertAudio": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "PE.Views.Toolbar.tipInsertEquation": "Inserir equação", "PE.Views.Toolbar.tipInsertHorizontalText": "Inserir caixa de texto horizontal", "PE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipInsertImage": "Inserir imagem", "PE.Views.Toolbar.tipInsertShape": "Inserir forma automática", "PE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "PE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> tabela", "PE.Views.Toolbar.tipInsertText": "Inserir caixa de texto", "PE.Views.Toolbar.tipInsertTextArt": "Inserir arte de texto", "PE.Views.Toolbar.tipInsertVerticalText": "Inserir caixa de texto vertical", "PE.Views.Toolbar.tipInsertVideo": "Inserir vídeo", "PE.Views.Toolbar.tipLineSpace": "Espaçamento de linha", "PE.Views.Toolbar.tipMarkers": "Marcadores", "PE.Views.Toolbar.tipMarkersArrow": "Marcas em <PERSON>a", "PE.Views.Toolbar.tipMarkersCheckmark": "Marcas de verificação", "PE.Views.Toolbar.tipMarkersDash": "Marcadores de traços", "PE.Views.Toolbar.tipMarkersFRhombus": "Listas Rômbicas Preenchidas", "PE.Views.Toolbar.tipMarkersFRound": "Listas Redondas Preenchidas", "PE.Views.Toolbar.tipMarkersFSquare": "Listas Quadradas Preenchidas", "PE.Views.Toolbar.tipMarkersHRound": "Marcas de lista redondas vazias", "PE.Views.Toolbar.tipMarkersStar": "Marcas em estrela", "PE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipNumbers": "Numeração", "PE.Views.Toolbar.tipPaste": "Colar", "PE.Views.Toolbar.tipPreview": "Iniciar ap<PERSON>ent<PERSON>", "PE.Views.Toolbar.tipPrint": "Imprimir", "PE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "PE.Views.Toolbar.tipSaveCoauth": "<PERSON>e as suas alterações para que os outros utilizadores as possam ver.", "PE.Views.Toolbar.tipSelectAll": "Selecionar tudo", "PE.Views.Toolbar.tipShapeAlign": "<PERSON><PERSON><PERSON> forma", "PE.Views.Toolbar.tipShapeArrange": "Dispor forma", "PE.Views.Toolbar.tipSlideNum": "Inserir número de diapositivo", "PE.Views.Toolbar.tipSlideSize": "Selecionar tamanho do diapositivo", "PE.Views.Toolbar.tipSlideTheme": "Tema do diapositivo", "PE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "PE.Views.Toolbar.tipVAligh": "Alinhamento vertical", "PE.Views.Toolbar.tipViewSettings": "Definições de visualização", "PE.Views.Toolbar.txtDistribHor": "Distribuir horizontalmente", "PE.Views.Toolbar.txtDistribVert": "Distribuir verticalmente", "PE.Views.Toolbar.txtDuplicateSlide": "Duplicar diapositivo", "PE.Views.Toolbar.txtGroup": "Grupo", "PE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON> objetos selecionados", "PE.Views.Toolbar.txtScheme1": "Office", "PE.Views.Toolbar.txtScheme10": "Mediana", "PE.Views.Toolbar.txtScheme11": "Metro", "PE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme13": "Opulento", "PE.Views.Toolbar.txtScheme14": "Balcão Envidraçado", "PE.Views.Toolbar.txtScheme15": "Origem", "PE.Views.Toolbar.txtScheme16": "Papel", "PE.Views.Toolbar.txtScheme17": "<PERSON>st<PERSON><PERSON>", "PE.Views.Toolbar.txtScheme18": "Técnica", "PE.Views.Toolbar.txtScheme19": "Viagem", "PE.Views.Toolbar.txtScheme2": "Escala de cinza", "PE.Views.Toolbar.txtScheme20": "Urbano", "PE.Views.Toolbar.txtScheme21": "Verve", "PE.Views.Toolbar.txtScheme22": "Novo Escritório", "PE.Views.Toolbar.txtScheme3": "Ápice", "PE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "PE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme6": "Concurso", "PE.Views.Toolbar.txtScheme7": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Toolbar.txtScheme8": "Fluxo", "PE.Views.Toolbar.txtScheme9": "Fundição", "PE.Views.Toolbar.txtSlideAlign": "Alinhar ao diapositivo", "PE.Views.Toolbar.txtUngroup": "Desagrupar", "PE.Views.Transitions.strDelay": "Atraso", "PE.Views.Transitions.strDuration": "Duração", "PE.Views.Transitions.strStartOnClick": "Iniciar ao clicar", "PE.Views.Transitions.textBlack": "Através preto", "PE.Views.Transitions.textBottom": "Baixo", "PE.Views.Transitions.textBottomLeft": "Esquerda inferior", "PE.Views.Transitions.textBottomRight": "Direita inferior", "PE.Views.Transitions.textClock": "<PERSON><PERSON><PERSON><PERSON>", "PE.Views.Transitions.textClockwise": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textCounterclockwise": "<PERSON><PERSON>do <PERSON>-<PERSON>", "PE.Views.Transitions.textCover": "Capa", "PE.Views.Transitions.textFade": "Desvanecimento", "PE.Views.Transitions.textHorizontalIn": "Horizontal para dentro", "PE.Views.Transitions.textHorizontalOut": "Horizontal para fora", "PE.Views.Transitions.textLeft": "E<PERSON>rda", "PE.Views.Transitions.textNone": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textPush": "Empurrar", "PE.Views.Transitions.textRight": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textSmoothly": "Suavemente", "PE.Views.Transitions.textSplit": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textTop": "Cima", "PE.Views.Transitions.textTopLeft": "Superior esquerda", "PE.Views.Transitions.textTopRight": "Superior direita", "PE.Views.Transitions.textUnCover": "Descobrir", "PE.Views.Transitions.textVerticalIn": "Vertical para dentro", "PE.Views.Transitions.textVerticalOut": "Vertical para fora", "PE.Views.Transitions.textWedge": "Triangular", "PE.Views.Transitions.textWipe": "<PERSON><PERSON><PERSON>", "PE.Views.Transitions.textZoom": "Zoom", "PE.Views.Transitions.textZoomIn": "Ampliar", "PE.Views.Transitions.textZoomOut": "Reduzir", "PE.Views.Transitions.textZoomRotate": "Zoom e Rotação", "PE.Views.Transitions.txtApplyToAll": "Aplicar a todos os diapositivos", "PE.Views.Transitions.txtParameters": "Parâmetros", "PE.Views.Transitions.txtPreview": "Pré-visualizar", "PE.Views.Transitions.txtSec": "S", "PE.Views.ViewTab.textAddHGuides": "Adici<PERSON>r g<PERSON>", "PE.Views.ViewTab.textAddVGuides": "Adicionar gui<PERSON> vertical", "PE.Views.ViewTab.textAlwaysShowToolbar": "Mostrar sempre a barra de ferramentas", "PE.Views.ViewTab.textClearGuides": "<PERSON><PERSON>", "PE.Views.ViewTab.textCm": "cm", "PE.Views.ViewTab.textCustom": "Personalizado", "PE.Views.ViewTab.textFitToSlide": "Ajustar ao diapositivo", "PE.Views.ViewTab.textFitToWidth": "Ajustar à Largura", "PE.Views.ViewTab.textGridlines": "<PERSON><PERSON>", "PE.Views.ViewTab.textGuides": "<PERSON><PERSON><PERSON>", "PE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON>", "PE.Views.ViewTab.textNotes": "Notas", "PE.Views.ViewTab.textRulers": "Réguas", "PE.Views.ViewTab.textShowGridlines": "<PERSON><PERSON> lin<PERSON> da g<PERSON>ha", "PE.Views.ViewTab.textShowGuides": "<PERSON>rar guias", "PE.Views.ViewTab.textSmartGuides": "<PERSON><PERSON><PERSON> intelige<PERSON>", "PE.Views.ViewTab.textSnapObjects": "Ajustar objeto à grelha", "PE.Views.ViewTab.textStatusBar": "Barra de estado", "PE.Views.ViewTab.textZoom": "Zoom", "PE.Views.ViewTab.tipFitToSlide": "Ajustar ao diapositivo", "PE.Views.ViewTab.tipFitToWidth": "Ajustar à Largura", "PE.Views.ViewTab.tipGridlines": "<PERSON><PERSON> lin<PERSON> da g<PERSON>ha", "PE.Views.ViewTab.tipGuides": "<PERSON>rar guias", "PE.Views.ViewTab.tipInterfaceTheme": "<PERSON><PERSON>"}