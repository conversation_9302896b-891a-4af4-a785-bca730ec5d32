name: check
on: [push]
jobs:
  project-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js 12
        uses: actions/setup-node@v1
        with:
            node-version: 12
      - name: Build using grunt
        run: |
          npm install --prefix build
          cd ..
          git clone --branch=develop --depth=1 https://github.com/ONLYOFFICE/sdkjs.git
          cd web-apps
          grunt --level=ADVANCED --base build --gruntfile build/Gruntfile.js
